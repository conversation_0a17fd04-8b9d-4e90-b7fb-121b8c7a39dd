<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.CourseReviewsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.CourseReviews">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="user_id" property="userId" />
        <result column="rating" property="rating" />
        <result column="content" property="content" />
        <result column="status" property="status" />
        <result column="is_del" property="isDel" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, user_id, rating, content, status, is_del, created_at, updated_at
    </sql>

</mapper>
