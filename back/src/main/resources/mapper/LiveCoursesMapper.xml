<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.LiveCoursesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.LiveCourses">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="cover_image" property="coverImage" />
        <result column="teacher_id" property="teacherId" />
        <result column="category_id" property="categoryId" />
        <result column="price" property="price" />
        <result column="room_id" property="roomId" />
        <result column="room_password" property="roomPassword" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="live_url" property="liveUrl" />
        <result column="status" property="status" />
        <result column="is_del" property="isDel" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, description, cover_image, teacher_id, category_id, price, room_id, room_password, start_time, end_time, live_url, status, is_del, created_at, updated_at
    </sql>

</mapper>
