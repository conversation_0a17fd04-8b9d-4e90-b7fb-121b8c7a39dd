<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.TeachersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.Teachers">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="avatar" property="avatar" />
        <result column="title" property="title" />
        <result column="introduction" property="introduction" />
        <result column="experience" property="experience" />
        <result column="expertise" property="expertise" />
        <result column="rating" property="rating" />
        <result column="student_count" property="studentCount" />
        <result column="course_count" property="courseCount" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="is_del" property="isDel" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, avatar, title, introduction, experience, expertise, rating, student_count, course_count, status, created_at, is_del, updated_at
    </sql>

</mapper>
