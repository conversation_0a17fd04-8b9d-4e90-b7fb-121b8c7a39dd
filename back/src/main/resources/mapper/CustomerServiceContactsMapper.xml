<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.CustomerServiceContactsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.CustomerServiceContacts">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="avatar" property="avatar" />
        <result column="phone" property="phone" />
        <result column="wechat" property="wechat" />
        <result column="email" property="email" />
        <result column="qq" property="qq" />
        <result column="remark" property="remark" />
        <result column="is_del" property="isDel" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, avatar, phone, wechat, email, qq, remark, is_del, status, created_at, updated_at
    </sql>

</mapper>
