<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.TagConfigsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.TagConfigs">
        <id column="id" property="id" />
        <result column="category" property="category" />
        <result column="label" property="label" />
        <result column="value" property="value" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_system" property="isSystem" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category, label, value, sort_order, is_system, status, remark, created_at, updated_at, is_del
    </sql>

</mapper> 