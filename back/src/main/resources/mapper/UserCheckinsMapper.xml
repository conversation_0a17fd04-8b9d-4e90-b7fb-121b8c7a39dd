<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.UserCheckinsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.UserCheckins">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="checkin_date" property="checkinDate" />
        <result column="checkin_time" property="checkinTime" />
        <result column="learn_duration" property="learnDuration" />
        <result column="remark" property="remark" />
        <result column="is_del" property="isDel" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, checkin_date, checkin_time, learn_duration, remark, is_del, created_at
    </sql>

</mapper>
