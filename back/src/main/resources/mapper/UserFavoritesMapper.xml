<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.UserFavoritesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.UserFavorites">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="course_id" property="courseId" />
        <result column="is_del" property="isDel" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, course_id, is_del, created_at
    </sql>

</mapper>
