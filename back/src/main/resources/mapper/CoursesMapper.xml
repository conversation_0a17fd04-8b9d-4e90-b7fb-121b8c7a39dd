<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.CoursesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.Courses">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="subtitle" property="subtitle" />
        <result column="description" property="description" />
        <result column="cover_image" property="coverImage" />
        <result column="teacher_id" property="teacherId" />
        <result column="category_id" property="categoryId" />
        <result column="price" property="price" />
        <result column="original_price" property="originalPrice" />
        <result column="level" property="level" />
        <result column="age_group" property="ageGroup" />
        <result column="duration" property="duration" />
        <result column="lesson_count" property="lessonCount" />
        <result column="student_count" property="studentCount" />
        <result column="rating" property="rating" />
        <result column="review_count" property="reviewCount" />
        <result column="is_live" property="isLive" />
        <result column="is_featured" property="isFeatured" />
        <result column="is_special_training" property="isSpecialTraining" />
        <result column="is_one_on_one" property="isOneOnOne" />
        <result column="status" property="status" />
        <result column="is_del" property="isDel" />
        <result column="contact_info_phone" property="contactInfoPhone" />
        <result column="contact_info_wechat" property="contactInfoWechat" />
        <result column="contact_info_remark" property="contactInfoRemark" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, subtitle, description, cover_image, teacher_id, category_id, price, original_price, level, age_group, duration, lesson_count, student_count, rating, review_count, is_live, is_featured, is_special_training, is_one_on_one, status, is_del, contact_info_phone, contact_info_wechat, contact_info_remark, created_at, updated_at
    </sql>

</mapper>
