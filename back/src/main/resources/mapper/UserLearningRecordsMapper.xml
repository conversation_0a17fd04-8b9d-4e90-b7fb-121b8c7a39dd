<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.UserLearningRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.UserLearningRecords">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="course_id" property="courseId" />
        <result column="lesson_id" property="lessonId" />
        <result column="progress" property="progress" />
        <result column="duration" property="duration" />
        <result column="progress_rate" property="progressRate" />
        <result column="is_completed" property="isCompleted" />
        <result column="last_learn_time" property="lastLearnTime" />
        <result column="learn_duration" property="learnDuration" />
        <result column="is_del" property="isDel" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, course_id, lesson_id, progress, duration, progress_rate, is_completed, last_learn_time, learn_duration, is_del, created_at, updated_at
    </sql>

</mapper>
