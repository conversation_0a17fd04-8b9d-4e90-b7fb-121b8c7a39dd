<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.UsersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.Users">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="nickname" property="nickname" />
        <result column="avatar" property="avatar" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="gender" property="gender" />
        <result column="age" property="age" />
        <result column="bio" property="bio" />
        <result column="grade" property="grade" />
        <result column="status" property="status" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="created_at" property="createdAt" />
        <result column="is_del" property="isDel" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, password, nickname, avatar, phone, email, gender, age, bio, grade, status, last_login_time, created_at, is_del, updated_at
    </sql>

</mapper>
