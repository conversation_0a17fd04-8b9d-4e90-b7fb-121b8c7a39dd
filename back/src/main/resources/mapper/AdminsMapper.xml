<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.AdminsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.Admins">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="avatar" property="avatar" />
        <result column="role" property="role" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="permissions" property="permissions" />
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="login_count" property="loginCount" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, username, password, real_name, avatar, role, phone, email, permissions, last_login_ip, last_login_time, login_count, remark, status, created_by, created_at, updated_at
    </sql>

</mapper>
