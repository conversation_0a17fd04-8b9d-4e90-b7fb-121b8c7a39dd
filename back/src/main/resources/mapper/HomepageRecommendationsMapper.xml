<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pox.com.dianfeng.mapper.HomepageRecommendationsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.HomepageRecommendations">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="subtitle" property="subtitle" />
        <result column="description" property="description" />
        <result column="cover_image" property="coverImage" />
        <result column="link_type" property="linkType" />
        <result column="link_target_id" property="linkTargetId" />
        <result column="link_url" property="linkUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="is_del" property="isDel" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, subtitle, description, cover_image, link_type, link_target_id, link_url, sort_order, status, is_del, created_at, updated_at
    </sql>

</mapper>
