<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鼎峰课堂 - 文件上传测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin: 10px;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-label {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .file-input-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .upload-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: left;
        }

        .file-info h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .file-info p {
            margin: 5px 0;
            color: #666;
        }

        .category-buttons {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }

        .category-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-btn.active {
            background: #667eea;
            color: white;
        }

        .upload-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .upload-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 鼎峰课堂</h1>
            <p>文件上传测试系统 - 支持头像、封面、视频、音频等多种文件类型</p>
        </div>

        <div class="content">
            <div class="upload-grid">
                <!-- 头像上传 -->
                <div class="upload-section">
                    <h3>👤 头像上传</h3>
                    <p>支持格式：JPG, PNG, GIF (最大10MB)</p>
                    <div class="file-input-wrapper">
                        <input type="file" id="avatarFile" class="file-input" accept="image/*">
                        <label for="avatarFile" class="file-input-label">选择头像文件</label>
                    </div>
                    <br>
                    <button class="upload-btn" onclick="uploadFile('avatar')">上传头像</button>
                    <div class="progress-bar" id="avatarProgress">
                        <div class="progress"></div>
                    </div>
                    <div class="result" id="avatarResult"></div>
                </div>

                <!-- 封面上传 -->
                <div class="upload-section">
                    <h3>🖼️ 封面上传</h3>
                    <p>支持格式：JPG, PNG, GIF (最大10MB)</p>
                    <div class="file-input-wrapper">
                        <input type="file" id="coverFile" class="file-input" accept="image/*">
                        <label for="coverFile" class="file-input-label">选择封面文件</label>
                    </div>
                    <br>
                    <button class="upload-btn" onclick="uploadFile('cover')">上传封面</button>
                    <div class="progress-bar" id="coverProgress">
                        <div class="progress"></div>
                    </div>
                    <div class="result" id="coverResult"></div>
                </div>

                <!-- 视频上传 -->
                <div class="upload-section">
                    <h3>🎬 视频上传</h3>
                    <p>支持格式：MP4, AVI, MOV (最大500MB)</p>
                    <div class="file-input-wrapper">
                        <input type="file" id="videoFile" class="file-input" accept="video/*">
                        <label for="videoFile" class="file-input-label">选择视频文件</label>
                    </div>
                    <br>
                    <button class="upload-btn" onclick="uploadFile('video')">上传视频</button>
                    <div class="progress-bar" id="videoProgress">
                        <div class="progress"></div>
                    </div>
                    <div class="result" id="videoResult"></div>
                </div>

                <!-- 音频上传 -->
                <div class="upload-section">
                    <h3>🎵 音频上传</h3>
                    <p>支持格式：MP3, WAV, FLAC (最大50MB)</p>
                    <div class="file-input-wrapper">
                        <input type="file" id="audioFile" class="file-input" accept="audio/*">
                        <label for="audioFile" class="file-input-label">选择音频文件</label>
                    </div>
                    <br>
                    <button class="upload-btn" onclick="uploadFile('audio')">上传音频</button>
                    <div class="progress-bar" id="audioProgress">
                        <div class="progress"></div>
                    </div>
                    <div class="result" id="audioResult"></div>
                </div>
            </div>

            <!-- 通用上传 -->
            <div class="upload-section" style="margin-top: 40px;">
                <h3>📁 通用文件上传</h3>
                <div class="category-buttons">
                    <button class="category-btn active" data-category="document">文档</button>
                    <button class="category-btn" data-category="image">图片</button>
                    <button class="category-btn" data-category="other">其他</button>
                </div>
                <div class="file-input-wrapper">
                    <input type="file" id="generalFile" class="file-input">
                    <label for="generalFile" class="file-input-label">选择文件</label>
                </div>
                <br>
                <button class="upload-btn" onclick="uploadGeneralFile()">上传文件</button>
                <div class="progress-bar" id="generalProgress">
                    <div class="progress"></div>
                </div>
                <div class="result" id="generalResult"></div>
            </div>
        </div>
    </div>

    <script>
        let selectedCategory = 'document';

        // 分类按钮点击事件
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                selectedCategory = this.dataset.category;
            });
        });

        // 上传文件函数
        async function uploadFile(category) {
            const fileInput = document.getElementById(category + 'File');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult(category, '请选择文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                showProgress(category, true);
                const response = await fetch(`/api/file/upload/${category}`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showFileInfo(category, result.data);
                    showResult(category, '上传成功！', 'success');
                } else {
                    showResult(category, result.message || '上传失败', 'error');
                }
            } catch (error) {
                showResult(category, '上传失败: ' + error.message, 'error');
            } finally {
                showProgress(category, false);
            }
        }

        // 通用文件上传
        async function uploadGeneralFile() {
            const fileInput = document.getElementById('generalFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('general', '请选择文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('category', selectedCategory);

            try {
                showProgress('general', true);
                const response = await fetch('/api/file/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showFileInfo('general', result.data);
                    showResult('general', '上传成功！', 'success');
                } else {
                    showResult('general', result.message || '上传失败', 'error');
                }
            } catch (error) {
                showResult('general', '上传失败: ' + error.message, 'error');
            } finally {
                showProgress('general', false);
            }
        }

        // 显示进度条
        function showProgress(category, show) {
            const progressBar = document.getElementById(category + 'Progress');
            if (show) {
                progressBar.style.display = 'block';
                progressBar.querySelector('.progress').style.width = '100%';
            } else {
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    progressBar.querySelector('.progress').style.width = '0%';
                }, 500);
            }
        }

        // 显示结果
        function showResult(category, message, type) {
            const resultDiv = document.getElementById(category + 'Result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
            
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }

        // 显示文件信息
        function showFileInfo(category, fileData) {
            const resultDiv = document.getElementById(category + 'Result');
            
            const fileInfo = `
                <div class="file-info">
                    <h4>📄 文件信息</h4>
                    <p><strong>文件名:</strong> ${fileData.fileName}</p>
                    <p><strong>原始名称:</strong> ${fileData.originalFileName}</p>
                    <p><strong>文件大小:</strong> ${formatFileSize(fileData.fileSize)}</p>
                    <p><strong>文件类型:</strong> ${fileData.contentType}</p>
                    <p><strong>存储桶:</strong> ${fileData.bucketName}</p>
                    <p><strong>分类:</strong> ${fileData.category}</p>
                    <p><strong>访问URL:</strong> <a href="${fileData.fileUrl}" target="_blank">${fileData.fileUrl}</a></p>
                </div>
            `;
            
            resultDiv.innerHTML = fileInfo;
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载完成后获取上传配置信息
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('/api/file/info');
                const result = await response.json();
                if (result.code === 200) {
                    console.log('上传配置信息:', result.data);
                }
            } catch (error) {
                console.error('获取上传配置失败:', error);
            }
        });
    </script>
</body>
</html>
