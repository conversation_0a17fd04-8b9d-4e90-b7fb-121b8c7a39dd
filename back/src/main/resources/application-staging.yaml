# 预发布环境配置
spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************
    username: dianfeng_staging
    password: staging_password_here
    hikari:
      # 连接池配置
      maximum-pool-size: 20
      minimum-idle: 10
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
  # Redis配置
  data:
    redis:
      host: staging-redis.dianfeng.com
      port: 6379
      password: staging_redis_password
      database: 2
      # 连接超时时间（毫秒）
      timeout: 5000
      # 连接超时时间（毫秒）
      connect-timeout: 3000
      client-name: dianfeng-redis-client-staging
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 30
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: 3000ms
          # 连接池中的最大空闲连接
          max-idle: 15
          # 连接池中的最小空闲连接
          min-idle: 5
          # 空闲连接检测间隔时间（毫秒）
          time-between-eviction-runs: 30000ms
        # 关闭超时时间
        shutdown-timeout: 200ms

# 服务器配置
server:
  port: 8082

# 日志配置
logging:
  level:
    root: info
    "[pox.com.dianfeng]": info
  file:
    name: logs/dianfeng-class-staging.log

# OSS 对象存储配置 - 预发布环境
oss:
  base-url: https://dianfeng-class-staging.oss-cn-chengdu.aliyuncs.com/
  endpoint: https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com
  preview-url: https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com
  region: cn-chengdu
  access-key: LTAI5tP9cnnNBvQzusQRuMuh
  secret-key: ******************************
  bucket-name: dianfeng-class-staging
  check-bucket: false
  path-style-access: true
  expiring-buckets:
    temp-uploads: 7
    cache-files: 30

# 阿里云OSS PostObject签名配置
aliyun:
  oss:
    post-object:
      expire-seconds: 1800
      max-file-size: 524288000
      allowed-content-types:
        - "image/jpeg"
        - "image/jpg"
        - "image/png"
        - "image/gif"
        - "image/webp"
        - "image/bmp"
        - "video/mp4"
        - "video/avi"
        - "video/mov"
        - "video/wmv"
        - "video/flv"
        - "video/mkv"
        - "video/webm"
        - "audio/mp3"
        - "audio/wav"
        - "audio/aac"
        - "audio/flac"
        - "audio/ogg"
        - "audio/m4a"
        - "application/pdf"
        - "application/msword"
        - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
