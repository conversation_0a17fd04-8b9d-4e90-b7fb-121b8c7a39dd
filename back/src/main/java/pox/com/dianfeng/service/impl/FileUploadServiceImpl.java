package pox.com.dianfeng.service.impl;

import com.alltobs.oss.service.OssTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import pox.com.dianfeng.entity.dto.FileUploadResult;
import pox.com.dianfeng.service.IFileUploadService;
import pox.com.dianfeng.util.FileUploadUtil;
import software.amazon.awssdk.services.s3.model.S3Object;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件上传服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl implements IFileUploadService {
    
    private final OssTemplate ossTemplate;
    
    @Value("${oss.bucket-name:dianfeng-class}")
    private String defaultBucketName;
    
    @Value("${oss.preview-url:${oss.endpoint}}")
    private String previewBaseUrl;
    
    @Override
    public FileUploadResult uploadFile(MultipartFile file, String category) {
        return uploadFile(file, category, defaultBucketName);
    }
    
    @Override
    public FileUploadResult uploadFile(MultipartFile file, String category, String bucketName) {
        try {
            // 验证文件
            validateFile(file, category);
            
            // 生成文件名
            String fileName = FileUploadUtil.generateDatePathFileName(file.getOriginalFilename(), category);
            
            // 上传文件
            ossTemplate.putObject(bucketName, fileName, file.getInputStream());
            
            // 构建返回结果
            return buildUploadResult(file, fileName, bucketName, category);
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public FileUploadResult uploadFile(InputStream inputStream, String fileName, String contentType, String category) {
        try {
            // 生成唯一文件名
            String uniqueFileName = FileUploadUtil.generateDatePathFileName(fileName, category);
            
            // 上传文件
            ossTemplate.putObject(defaultBucketName, uniqueFileName, inputStream);
            
            // 构建返回结果
            return FileUploadResult.builder()
                    .fileName(uniqueFileName)
                    .originalFileName(fileName)
                    .contentType(contentType)
                    .bucketName(defaultBucketName)
                    .fileUrl(uniqueFileName) // 只存储相对路径，不包含base URL
                    .previewUrl(buildFileUrl(uniqueFileName)) // 预览URL包含完整路径
                    .uploadTime(System.currentTimeMillis())
                    .category(category)
                    .build();
                    
        } catch (Exception e) {
            log.error("文件流上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件流上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<FileUploadResult> uploadFiles(List<MultipartFile> files, String category) {
        List<FileUploadResult> results = new ArrayList<>();
        
        for (MultipartFile file : files) {
            try {
                FileUploadResult result = uploadFile(file, category);
                results.add(result);
            } catch (Exception e) {
                log.error("批量上传文件失败: {}", file.getOriginalFilename(), e);
                // 继续上传其他文件
            }
        }
        
        return results;
    }
    
    @Override
    public boolean deleteFile(String fileName, String bucketName) {
        try {
            ossTemplate.removeObject(bucketName, fileName);
            log.info("文件删除成功: {}/{}", bucketName, fileName);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: {}/{}, 错误: {}", bucketName, fileName, e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean deleteFile(String fileName) {
        return deleteFile(fileName, defaultBucketName);
    }
    
    @Override
    public String getPreviewUrl(String fileName, String bucketName, int expireMinutes) {
        try {
            return ossTemplate.getObjectURL(bucketName, fileName, expireMinutes);
        } catch (Exception e) {
            log.error("获取预览URL失败: {}/{}, 错误: {}", bucketName, fileName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public String getPreviewUrl(String fileName, int expireMinutes) {
        return getPreviewUrl(fileName, defaultBucketName, expireMinutes);
    }
    
    @Override
    public String generatePreSignedUploadUrl(String fileName, String bucketName, int expireMinutes) {
        try {
            return ossTemplate.generatePreSignedUrlForPut(bucketName, fileName, expireMinutes);
        } catch (Exception e) {
            log.error("生成预签名URL失败: {}/{}, 错误: {}", bucketName, fileName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public List<String> listFiles(String bucketName, String prefix) {
        try {
            return ossTemplate.getAllObjectsByPrefix(bucketName, prefix)
                    .stream()
                    .map(S3Object::key)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取文件列表失败: {}/{}, 错误: {}", bucketName, prefix, e.getMessage());
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean copyFile(String sourceFileName, String sourceBucket, String targetFileName, String targetBucket) {
        try {
            ossTemplate.copyObject(sourceBucket, sourceFileName, targetBucket, targetFileName);
            log.info("文件复制成功: {}/{} -> {}/{}", sourceBucket, sourceFileName, targetBucket, targetFileName);
            return true;
        } catch (Exception e) {
            log.error("文件复制失败: {}/{} -> {}/{}, 错误: {}", 
                    sourceBucket, sourceFileName, targetBucket, targetFileName, e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean setFileTags(String fileName, String bucketName, Map<String, String> tags) {
        try {
            ossTemplate.setObjectTags(bucketName, fileName, tags);
            log.info("文件标签设置成功: {}/{}", bucketName, fileName);
            return true;
        } catch (Exception e) {
            log.error("文件标签设置失败: {}/{}, 错误: {}", bucketName, fileName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file, String category) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        // 验证文件类型
        if (!FileUploadUtil.isValidFileType(originalFilename, category)) {
            throw new IllegalArgumentException("不支持的文件类型: " + FileUploadUtil.getFileExtension(originalFilename));
        }
        
        // 验证文件大小
        if (!FileUploadUtil.isValidFileSize(file.getSize(), category)) {
            throw new IllegalArgumentException("文件大小超出限制: " + FileUploadUtil.formatFileSize(file.getSize()));
        }
    }
    
    /**
     * 构建上传结果
     */
    private FileUploadResult buildUploadResult(MultipartFile file, String fileName, String bucketName, String category) {
        return FileUploadResult.builder()
                .fileName(fileName)
                .originalFileName(file.getOriginalFilename())
                .fileSize(file.getSize())
                .contentType(file.getContentType())
                .bucketName(bucketName)
                .fileUrl(fileName) // 只存储相对路径，不包含base URL
                .previewUrl(buildFileUrl(fileName)) // 预览URL包含完整路径
                .uploadTime(System.currentTimeMillis())
                .category(category)
                .build();
    }

    /**
     * 构建文件访问URL
     */
    private String buildFileUrl(String fileName) {
        return previewBaseUrl + "/" + defaultBucketName + "/" + fileName;
    }
}
