package pox.com.dianfeng.service;


import pox.com.dianfeng.entity.dto.CaptchaResponseDTO;
import pox.com.dianfeng.entity.dto.LoginRequestDTO;
import pox.com.dianfeng.entity.dto.LoginResponseDTO;

/**
 * 管理员登录服务接口
 */
public interface IAdminLoginService {

    /**
     * 生成验证码
     * 
     * @return 验证码信息
     */
    CaptchaResponseDTO generateCaptcha();

    /**
     * 管理员登录
     * 
     * @param loginRequest 登录请求
     * @param loginIp      登录IP
     * @return 登录响应
     */
    LoginResponseDTO login(LoginRequestDTO loginRequest, String loginIp);
}