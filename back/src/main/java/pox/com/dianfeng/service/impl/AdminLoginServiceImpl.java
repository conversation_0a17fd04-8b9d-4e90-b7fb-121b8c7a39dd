package pox.com.dianfeng.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pox.com.dianfeng.entity.Admins;
import pox.com.dianfeng.entity.dto.CaptchaResponseDTO;
import pox.com.dianfeng.entity.dto.LoginRequestDTO;
import pox.com.dianfeng.entity.dto.LoginResponseDTO;
import pox.com.dianfeng.service.IAdminLoginService;
import pox.com.dianfeng.service.IAdminsService;
import pox.com.dianfeng.util.CaptchaUtil;

import java.time.LocalDateTime;

/**
 * 管理员登录服务实现类
 */
@Service
public class AdminLoginServiceImpl implements IAdminLoginService {

    @Autowired
    private IAdminsService adminsService;

    @Autowired
    private CaptchaUtil captchaUtil;

    /**
     * 生成验证码
     */
    @Override
    public CaptchaResponseDTO generateCaptcha() {
        CaptchaUtil.CaptchaVO captchaVO = captchaUtil.generateCaptcha();
        return CaptchaResponseDTO.builder()
                .captchaId(captchaVO.getCaptchaId())
                .captchaImage(captchaVO.getBase64Image())
                .build();
    }

    /**
     * 管理员登录
     */
    @Override
    public LoginResponseDTO login(LoginRequestDTO loginRequest, String loginIp) {
        // 1. 验证验证码
        boolean captchaValid = captchaUtil.validateCaptcha(
                loginRequest.getCaptchaId(),
                loginRequest.getCaptchaCode());

        if (!captchaValid) {
            throw new RuntimeException("验证码错误或已过期");
        }

        // 2. 查询用户
        QueryWrapper<Admins> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", loginRequest.getUsername());
        Admins admin = adminsService.getOne(queryWrapper);

        // 3. 验证用户存在
        if (admin == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 4. 验证用户状态
        if (!admin.getStatus()) {
            throw new RuntimeException("账号已被禁用，请联系管理员");
        }

        // 5. 验证密码
        String encryptedPassword = SaSecureUtil.md5(loginRequest.getPassword());
        if (!encryptedPassword.equals(admin.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 6. 更新登录信息
        admin.setLastLoginIp(loginIp);
        admin.setLastLoginTime(LocalDateTime.now());
        admin.setLoginCount(admin.getLoginCount() + 1);
        adminsService.updateById(admin);

        // 7. 执行登录
        StpUtil.login(admin.getId());

        // 8. 返回登录信息
        return LoginResponseDTO.builder()
                .userId(admin.getId())
                .username(admin.getUsername())
                .realName(admin.getRealName())
                .avatar(admin.getAvatar())
                .role(admin.getRole())
                .token(StpUtil.getTokenValue())
                .build();
    }
}