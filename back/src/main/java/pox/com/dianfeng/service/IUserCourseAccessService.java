package pox.com.dianfeng.service;

import com.baomidou.mybatisplus.extension.service.IService;
import pox.com.dianfeng.entity.UserCourseAccess;
import pox.com.dianfeng.dto.CourseAccessRequestDTO;
import pox.com.dianfeng.dto.CourseAccessResponseDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户课程权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
public interface IUserCourseAccessService extends IService<UserCourseAccess> {

    /**
     * 检查用户是否有访问权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID（可为null）
     * @param lessonId 课时ID（可为null）
     * @return 权限检查结果
     */
    CourseAccessResponseDTO checkAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId);

    /**
     * 授予用户课程权限（购买方式）
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID（课程权限时为null）
     * @param lessonId 课时ID（课程/章节权限时为null）
     * @param pricePaid 实际支付金额
     * @param originalPrice 原价
     * @param paymentMethod 支付方式
     * @param orderId 订单ID
     * @param isBuyout 是否买断
     * @param expireTime 过期时间（永久权限时为null）
     * @return 权限记录
     */
    UserCourseAccess grantPurchaseAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                       BigDecimal pricePaid, BigDecimal originalPrice, String paymentMethod,
                                       String orderId, Boolean isBuyout, LocalDateTime expireTime);

    /**
     * 授予用户免费权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID
     * @param lessonId 课时ID
     * @param expireTime 过期时间
     * @return 权限记录
     */
    UserCourseAccess grantFreeAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                   LocalDateTime expireTime);

    /**
     * 积分兑换权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID
     * @param lessonId 课时ID
     * @param pointsUsed 使用的积分
     * @param originalPrice 原价
     * @param expireTime 过期时间
     * @return 权限记录
     */
    UserCourseAccess grantPointsAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                     Integer pointsUsed, BigDecimal originalPrice, LocalDateTime expireTime);

    /**
     * 优惠券兑换权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID
     * @param lessonId 课时ID
     * @param couponId 优惠券ID
     * @param pricePaid 实际支付金额
     * @param originalPrice 原价
     * @param expireTime 过期时间
     * @return 权限记录
     */
    UserCourseAccess grantCouponAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                     Integer couponId, BigDecimal pricePaid, BigDecimal originalPrice,
                                     LocalDateTime expireTime);

    /**
     * 管理员赠送权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID
     * @param lessonId 课时ID
     * @param adminId 管理员ID
     * @param remark 备注
     * @param expireTime 过期时间
     * @return 权限记录
     */
    UserCourseAccess grantAdminGift(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                  Integer adminId, String remark, LocalDateTime expireTime);

    /**
     * 批量授予权限
     * 
     * @param request 批量授权请求
     * @return 授权结果
     */
    List<UserCourseAccess> batchGrantAccess(CourseAccessRequestDTO request);

    /**
     * 退款处理
     * 
     * @param accessId 权限记录ID
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 是否成功
     */
    Boolean processRefund(Integer accessId, BigDecimal refundAmount, String refundReason);

    /**
     * 激活权限
     * 
     * @param accessId 权限记录ID
     * @return 是否成功
     */
    Boolean activateAccess(Integer accessId);

    /**
     * 停用权限
     * 
     * @param accessId 权限记录ID
     * @param reason 停用原因
     * @return 是否成功
     */
    Boolean deactivateAccess(Integer accessId, String reason);

    /**
     * 获取用户的所有有效权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<UserCourseAccess> getUserValidAccess(Integer userId);

    /**
     * 获取用户某个课程的权限详情
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 权限列表
     */
    List<UserCourseAccess> getUserCourseAccess(Integer userId, Integer courseId);

    /**
     * 检查权限是否即将到期（7天内到期）
     * 
     * @param userId 用户ID
     * @return 即将到期的权限列表
     */
    List<UserCourseAccess> getExpiringAccess(Integer userId);

    /**
     * 获取用户统计信息
     *
     * @param userId 用户ID
     * @return 统计数据
     */
    CourseAccessResponseDTO.AccessStatistics getUserAccessStatistics(Integer userId);

    /**
     * 获取全局统计信息
     *
     * @return 全局统计数据
     */
    CourseAccessResponseDTO.AccessStatistics getGlobalAccessStatistics();
} 