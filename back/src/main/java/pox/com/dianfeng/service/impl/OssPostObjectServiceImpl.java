package pox.com.dianfeng.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import pox.com.dianfeng.config.AliyunOssPostObjectConfig;
import pox.com.dianfeng.dto.OssPostObjectResponse;
import pox.com.dianfeng.service.OssPostObjectService;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * OSS PostObject签名服务实现
 * 
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OssPostObjectServiceImpl implements OssPostObjectService {
    
    private final AliyunOssPostObjectConfig postObjectConfig;
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Value("${oss.bucket-name}")
    private String bucketName;

    @Value("${oss.endpoint}")
    private String ossEndpoint;

    @Value("${oss.access-key}")
    private String accessKeyId;

    @Value("${oss.secret-key}")
    private String accessKeySecret;
    
    @Override
    public OssPostObjectResponse generatePostObjectSignature(String category, String fileName) {
        // 生成路径前缀
        String pathPrefix = generatePathPrefix(category);
        
        // 如果没有提供文件名，生成一个随机文件名
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = generateRandomFileName(category);
        }
        
        // 根据分类确定允许的文件类型和大小
        String[] allowedTypes = getAllowedContentTypesByCategory(category);
        Long maxSize = getMaxFileSizeByCategory(category);
        
        return generatePostObjectSignature(pathPrefix, fileName, null, maxSize);
    }
    
    @Override
    public OssPostObjectResponse generatePostObjectSignature(String pathPrefix, String fileName, 
                                                             String contentType, Long maxFileSize) {
        try {
            // 计算过期时间
            Instant expiration = Instant.now().plusSeconds(postObjectConfig.getExpireSeconds());
            String expirationStr = expiration.atOffset(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ISO_INSTANT);
            
            // 构建完整的对象键
            String objectKey = pathPrefix + fileName;
            
            // 构建Policy
            Map<String, Object> policy = buildPolicy(objectKey, contentType, maxFileSize, expirationStr);
            
            // 将Policy转换为JSON并进行Base64编码
            String policyJson = objectMapper.writeValueAsString(policy);
            String policyBase64 = Base64.getEncoder().encodeToString(policyJson.getBytes(StandardCharsets.UTF_8));
            
            // 生成签名
            String signature = generateSignature(policyBase64);
            
            // 构建上传URL
            String uploadUrl = ossEndpoint;
            
            // 构建文件访问URL
            String fileUrl = ossEndpoint + "/" + objectKey;
            
            // 构建表单字段
            Map<String, String> formFields = new HashMap<>();
            formFields.put("key", objectKey);
            formFields.put("policy", policyBase64);
            formFields.put("OSSAccessKeyId", accessKeyId);
            formFields.put("signature", signature);
            formFields.put("success_action_status", "200");
            
            log.info("生成PostObject签名成功，对象键: {}, 过期时间: {}", objectKey, expirationStr);
            
            return OssPostObjectResponse.builder()
                    .uploadUrl(uploadUrl)
                    .bucketName(bucketName)
                    .objectKey(objectKey)
                    .accessKeyId(accessKeyId)
                    .policy(policyBase64)
                    .signature(signature)
                    .expiration(expirationStr)
                    .fileUrl(fileUrl)
                    .maxFileSize(maxFileSize)
                    .allowedContentTypes(getAllowedContentTypesByCategory(extractCategoryFromPath(pathPrefix)))
                    .formFields(formFields)
                    .uploadTips("请使用POST方法上传文件到uploadUrl，并包含所有formFields作为表单数据")
                    .build();
                    
        } catch (Exception e) {
            log.error("生成PostObject签名失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成PostObject签名失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建Policy
     */
    private Map<String, Object> buildPolicy(String objectKey, String contentType, Long maxFileSize, String expiration) {
        Map<String, Object> policy = new HashMap<>();
        policy.put("expiration", expiration);
        
        List<Object> conditions = new ArrayList<>();
        
        // 限制存储桶
        conditions.add(Map.of("bucket", bucketName));
        
        // 限制对象键
        conditions.add(List.of("starts-with", "$key", objectKey.substring(0, objectKey.lastIndexOf('/') + 1)));
        
        // 限制文件大小
        if (maxFileSize != null && maxFileSize > 0) {
            conditions.add(List.of("content-length-range", 1, maxFileSize));
        }
        
        // 限制内容类型（如果指定）
        if (contentType != null && !contentType.trim().isEmpty()) {
            conditions.add(Map.of("content-type", contentType));
        } else {
            // 允许的内容类型
            String[] allowedTypes = getAllowedContentTypesByCategory(extractCategoryFromPath(objectKey));
            if (allowedTypes.length > 0) {
                for (String type : allowedTypes) {
                    conditions.add(List.of("starts-with", "$content-type", type.split("/")[0] + "/"));
                }
            }
        }
        
        // 设置成功状态码
        conditions.add(Map.of("success_action_status", "200"));
        
        policy.put("conditions", conditions);
        
        return policy;
    }
    
    /**
     * 生成签名
     */
    private String generateSignature(String policyBase64) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKey = new SecretKeySpec(accessKeySecret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(secretKey);
        byte[] signatureBytes = mac.doFinal(policyBase64.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signatureBytes);
    }
    
    /**
     * 生成路径前缀
     */
    private String generatePathPrefix(String category) {
        String now = Instant.now().atOffset(ZoneOffset.of("+8")).format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return category + "/" + now + "/";
    }
    
    /**
     * 生成随机文件名
     */
    private String generateRandomFileName(String category) {
        String extension = getDefaultExtensionByCategory(category);
        return UUID.randomUUID().toString() + "." + extension;
    }
    
    /**
     * 根据分类获取默认扩展名
     */
    private String getDefaultExtensionByCategory(String category) {
        switch (category.toLowerCase()) {
            case "image": return "jpg";
            case "video": return "mp4";
            case "audio": return "mp3";
            case "document": return "pdf";
            default: return "bin";
        }
    }
    
    /**
     * 根据分类获取允许的内容类型
     */
    private String[] getAllowedContentTypesByCategory(String category) {
        switch (category.toLowerCase()) {
            case "image":
                return new String[]{"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/bmp"};
            case "video":
                return new String[]{"video/mp4", "video/avi", "video/mov", "video/wmv", "video/flv", "video/mkv", "video/webm"};
            case "audio":
                return new String[]{"audio/mp3", "audio/wav", "audio/aac", "audio/flac", "audio/ogg", "audio/m4a"};
            case "document":
                return new String[]{"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"};
            default:
                return new String[]{};
        }
    }
    
    /**
     * 根据分类获取最大文件大小
     */
    private Long getMaxFileSizeByCategory(String category) {
        switch (category.toLowerCase()) {
            case "image": return 10 * 1024 * 1024L;      // 10MB
            case "video": return 500 * 1024 * 1024L;     // 500MB
            case "audio": return 50 * 1024 * 1024L;      // 50MB
            case "document": return 20 * 1024 * 1024L;   // 20MB
            default: return postObjectConfig.getMaxFileSize();
        }
    }
    
    /**
     * 从路径中提取分类
     */
    private String extractCategoryFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return "document";
        }
        String[] parts = path.split("/");
        return parts.length > 0 ? parts[0] : "document";
    }
}
