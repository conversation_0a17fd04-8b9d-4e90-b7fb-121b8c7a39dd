package pox.com.dianfeng.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import pox.com.dianfeng.entity.CourseChapters;
import pox.com.dianfeng.entity.CourseLessons;
import pox.com.dianfeng.entity.Courses;

import pox.com.dianfeng.entity.Teachers;
import pox.com.dianfeng.entity.dto.ChapterWithLessonsDTO;
import pox.com.dianfeng.entity.dto.CourseWithTeacherDTO;
import pox.com.dianfeng.mapper.CoursesMapper;

import pox.com.dianfeng.service.ICourseChaptersService;
import pox.com.dianfeng.service.ICourseLessonsService;
import pox.com.dianfeng.service.ICoursesService;
import pox.com.dianfeng.service.ITagConfigsService;
import pox.com.dianfeng.service.ITeachersService;
import pox.com.dianfeng.service.OssUrlService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class CoursesServiceImpl extends ServiceImpl<CoursesMapper, Courses> implements ICoursesService {

    @Autowired
    private ITeachersService teachersService;



    @Autowired
    private ITagConfigsService tagConfigsService;

    @Autowired
    private OssUrlService ossUrlService;

    @Autowired
    private ICourseChaptersService courseChaptersService;

    @Autowired
    private ICourseLessonsService courseLessonsService;

    @Override
    public IPage<CourseWithTeacherDTO> pageWithTeacher(Page<Courses> page, Courses entity) {
        // 先查询课程分页数据
        IPage<Courses> coursePage = this.page(page);

        // 获取所有课程
        List<Courses> courses = coursePage.getRecords();
        if (courses.isEmpty()) {
            return page.convert(course -> null);
        }

        // 设置课程的完整URL
        ossUrlService.setCourseFullUrls(courses);

        // 提取讲师ID和分类ID
        List<Integer> teacherIds = courses.stream()
                .map(Courses::getTeacherId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询讲师信息
        Map<Integer, Teachers> teacherMap = teacherIds.isEmpty() ?
                Map.of() :
                teachersService.listByIds(teacherIds).stream()
                        .collect(Collectors.toMap(Teachers::getId, teacher -> teacher));

        // 转换为DTO
        List<CourseWithTeacherDTO> dtoList = courses.stream()
                .map(course -> {
                    Teachers teacher = teacherMap.get(course.getTeacherId());
                    String categoryName = getCategoryLabel(course.getCategoryId());

                    return CourseWithTeacherDTO.builder()
                            .course(course)
                            .teacher(teacher)
                            .teacherName(teacher != null ? teacher.getName() : null)
                            .teacherAvatar(teacher != null ? teacher.getAvatar() : null)
                            .categoryName(categoryName)
                            .build();
                })
                .collect(Collectors.toList());

        // 构建返回结果
        Page<CourseWithTeacherDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), coursePage.getTotal());
        resultPage.setRecords(dtoList);

        return resultPage;
    }

    @Override
    public CourseWithTeacherDTO getWithTeacherById(Integer id) {
        Courses course = this.getById(id);
        if (course == null) {
            return null;
        }

        // 设置课程的完整URL
        ossUrlService.setCourseFullUrls(course);

        Teachers teacher = course.getTeacherId() != null ?
                teachersService.getById(course.getTeacherId()) : null;
        String categoryName = getCategoryLabel(course.getCategoryId());

        // 设置讲师的完整URL
        if (teacher != null) {
            ossUrlService.setTeacherFullUrls(teacher);
        }

        // 加载章节和课时信息
        List<ChapterWithLessonsDTO> chapters = loadChaptersWithLessons(id);

        return CourseWithTeacherDTO.builder()
                .course(course)
                .teacher(teacher)
                .teacherName(teacher != null ? teacher.getName() : null)
                .teacherAvatar(teacher != null ? teacher.getAvatar() : null)
                .categoryName(categoryName)
                .levelLabel(getLevelLabel(course.getLevel()))
                .ageGroupLabel(getAgeGroupLabel(course.getAgeGroup()))
                .chapters(chapters)
                .build();
    }

    /**
     * 加载课程的章节和课时信息
     */
    private List<ChapterWithLessonsDTO> loadChaptersWithLessons(Integer courseId) {
        // 查询课程的所有章节
        List<CourseChapters> chapters = courseChaptersService.lambdaQuery()
                .eq(CourseChapters::getCourseId, courseId)
                .eq(CourseChapters::getIsDel, false)
                .orderByAsc(CourseChapters::getSortOrder)
                .list();

        if (chapters.isEmpty()) {
            return List.of();
        }

        // 获取所有章节ID
        List<Integer> chapterIds = chapters.stream()
                .map(CourseChapters::getId)
                .collect(Collectors.toList());

        // 批量查询所有课时
        List<CourseLessons> allLessons = courseLessonsService.lambdaQuery()
                .in(CourseLessons::getChapterId, chapterIds)
                .eq(CourseLessons::getIsDel, false)
                .orderByAsc(CourseLessons::getSortOrder)
                .list();

        // 为课时设置完整的视频URL
        allLessons.forEach(lesson -> {
            if (lesson.getVideoUrl() != null && !lesson.getVideoUrl().isEmpty()) {
                // 设置课时的完整URL（如果OSS服务支持）
                ossUrlService.setLessonFullUrls(lesson);
            }
        });
        // 按章节ID分组课时
        Map<Integer, List<CourseLessons>> lessonsByChapter = allLessons.stream()
                .collect(Collectors.groupingBy(CourseLessons::getChapterId));

        // 构建章节DTO列表
        return chapters.stream()
                .map(chapter -> {
                    ChapterWithLessonsDTO dto = ChapterWithLessonsDTO.fromEntity(chapter);
                    dto.setLessons(lessonsByChapter.getOrDefault(chapter.getId(), List.of()));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取难度级别标签
     */
    private String getLevelLabel(Integer level) {
        return tagConfigsService.getTagLabel("level", level);
    }

    /**
     * 获取年龄段标签
     */
    private String getAgeGroupLabel(Integer ageGroup) {
        return tagConfigsService.getTagLabel("age_group", ageGroup);
    }

    /**
     * 获取分类标签
     */
    private String getCategoryLabel(Integer categoryId) {
        return tagConfigsService.getTagLabel("course_category", categoryId);
    }

}
