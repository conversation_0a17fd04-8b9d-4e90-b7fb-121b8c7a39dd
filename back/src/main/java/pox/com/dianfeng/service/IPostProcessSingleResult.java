package pox.com.dianfeng.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;

public interface IPostProcessSingleResult<T> {
    T postProcessSingleResult(T entity);

    /**
     * 分页结果后置处理
     * 子类可以重写此方法进行自定义处理
     */
    default void postProcessPageResult(IPage<T> result) {
        if (result != null && result.getRecords() != null) {
            postProcessListResult(result.getRecords());
        }
    }

    /**
     * 列表结果后置处理
     * 子类可以重写此方法进行自定义处理
     */
    default void postProcessListResult(List<T> list) {
        if (list != null) {
            list.forEach(item -> {

                postProcessSingleResult(item);
            });
        }
    }
}
