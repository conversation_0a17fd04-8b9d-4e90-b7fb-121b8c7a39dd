package pox.com.dianfeng.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import pox.com.dianfeng.entity.LiveCourses;
import pox.com.dianfeng.entity.Teachers;
import pox.com.dianfeng.entity.dto.LiveCourseWithTeacherDTO;
import pox.com.dianfeng.mapper.LiveCoursesMapper;
import pox.com.dianfeng.service.ITagConfigsService;
import pox.com.dianfeng.service.ILiveCoursesService;
import pox.com.dianfeng.service.ITeachersService;
import pox.com.dianfeng.service.OssUrlService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 直播通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class LiveCoursesServiceImpl extends ServiceImpl<LiveCoursesMapper, LiveCourses> implements ILiveCoursesService {

    @Autowired
    private ITeachersService teachersService;

    @Autowired
    private ITagConfigsService tagConfigsService;

    @Autowired
    private OssUrlService ossUrlService;

    @Override
    public IPage<LiveCourseWithTeacherDTO> pageWithTeacher(Page<LiveCourses> page, LiveCourses entity) {
        // 先查询直播课程分页数据
        IPage<LiveCourses> livePage = this.page(page);

        // 获取所有直播课程
        List<LiveCourses> liveCourses = livePage.getRecords();
        if (liveCourses.isEmpty()) {
            return page.convert(live -> null);
        }

        // 设置直播课程的完整URL
        ossUrlService.setLiveCourseFullUrls(liveCourses);

        // 提取讲师ID和分类ID
        List<Integer> teacherIds = liveCourses.stream()
                .map(LiveCourses::getTeacherId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询讲师信息
        Map<Integer, Teachers> teacherMap = teacherIds.isEmpty() ?
                Map.of() :
                teachersService.listByIds(teacherIds).stream()
                        .collect(Collectors.toMap(Teachers::getId, teacher -> teacher));

        // 转换为DTO
        List<LiveCourseWithTeacherDTO> dtoList = liveCourses.stream()
                .map(liveCourse -> {
                    Teachers teacher = teacherMap.get(liveCourse.getTeacherId());
                    String categoryName = getCategoryLabel(liveCourse.getCategoryId());

                    return LiveCourseWithTeacherDTO.builder()
                            .liveCourse(liveCourse)
                            .teacher(teacher)
                            .teacherName(teacher != null ? teacher.getName() : null)
                            .teacherAvatar(teacher != null ? teacher.getAvatar() : null)
                            .categoryName(categoryName)
                            .build();
                })
                .collect(Collectors.toList());

        // 构建返回结果
        Page<LiveCourseWithTeacherDTO> resultPage = new Page<>(page.getCurrent(), page.getSize(), livePage.getTotal());
        resultPage.setRecords(dtoList);

        return resultPage;
    }

    @Override
    public LiveCourseWithTeacherDTO getWithTeacherById(Integer id) {
        LiveCourses liveCourse = this.getById(id);
        if (liveCourse == null) {
            return null;
        }

        Teachers teacher = liveCourse.getTeacherId() != null ?
                teachersService.getById(liveCourse.getTeacherId()) : null;
        String categoryName = getCategoryLabel(liveCourse.getCategoryId());

        return LiveCourseWithTeacherDTO.builder()
                .liveCourse(liveCourse)
                .teacher(teacher)
                .teacherName(teacher != null ? teacher.getName() : null)
                .teacherAvatar(teacher != null ? teacher.getAvatar() : null)
                .categoryName(categoryName)
                .build();
    }

    /**
     * 获取分类标签
     */
    private String getCategoryLabel(Integer categoryId) {
        return tagConfigsService.getTagLabel("course_category", categoryId);
    }

}
