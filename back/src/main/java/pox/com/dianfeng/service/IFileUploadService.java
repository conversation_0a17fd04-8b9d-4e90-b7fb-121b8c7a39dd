package pox.com.dianfeng.service;

import org.springframework.web.multipart.MultipartFile;
import pox.com.dianfeng.entity.dto.FileUploadResult;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 文件上传服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IFileUploadService {
    
    /**
     * 上传单个文件
     * 
     * @param file 文件
     * @param category 文件分类（avatar/cover/video/audio/document等）
     * @return 上传结果
     */
    FileUploadResult uploadFile(MultipartFile file, String category);
    
    /**
     * 上传单个文件到指定桶
     * 
     * @param file 文件
     * @param category 文件分类
     * @param bucketName 存储桶名称
     * @return 上传结果
     */
    FileUploadResult uploadFile(MultipartFile file, String category, String bucketName);
    
    /**
     * 上传文件流
     * 
     * @param inputStream 文件流
     * @param fileName 文件名
     * @param contentType 文件类型
     * @param category 文件分类
     * @return 上传结果
     */
    FileUploadResult uploadFile(InputStream inputStream, String fileName, String contentType, String category);
    
    /**
     * 批量上传文件
     * 
     * @param files 文件列表
     * @param category 文件分类
     * @return 上传结果列表
     */
    List<FileUploadResult> uploadFiles(List<MultipartFile> files, String category);
    
    /**
     * 删除文件
     * 
     * @param fileName 文件名
     * @param bucketName 存储桶名称
     * @return 是否删除成功
     */
    boolean deleteFile(String fileName, String bucketName);
    
    /**
     * 删除文件（使用默认桶）
     * 
     * @param fileName 文件名
     * @return 是否删除成功
     */
    boolean deleteFile(String fileName);
    
    /**
     * 获取文件预览URL
     * 
     * @param fileName 文件名
     * @param bucketName 存储桶名称
     * @param expireMinutes 过期时间（分钟）
     * @return 预览URL
     */
    String getPreviewUrl(String fileName, String bucketName, int expireMinutes);
    
    /**
     * 获取文件预览URL（使用默认桶）
     * 
     * @param fileName 文件名
     * @param expireMinutes 过期时间（分钟）
     * @return 预览URL
     */
    String getPreviewUrl(String fileName, int expireMinutes);
    
    /**
     * 生成预签名上传URL
     * 
     * @param fileName 文件名
     * @param bucketName 存储桶名称
     * @param expireMinutes 过期时间（分钟）
     * @return 预签名URL
     */
    String generatePreSignedUploadUrl(String fileName, String bucketName, int expireMinutes);
    
    /**
     * 获取指定桶下的所有文件
     * 
     * @param bucketName 存储桶名称
     * @param prefix 文件前缀
     * @return 文件名列表
     */
    List<String> listFiles(String bucketName, String prefix);
    
    /**
     * 复制文件
     * 
     * @param sourceFileName 源文件名
     * @param sourceBucket 源存储桶
     * @param targetFileName 目标文件名
     * @param targetBucket 目标存储桶
     * @return 是否复制成功
     */
    boolean copyFile(String sourceFileName, String sourceBucket, String targetFileName, String targetBucket);
    
    /**
     * 设置文件标签
     * 
     * @param fileName 文件名
     * @param bucketName 存储桶名称
     * @param tags 标签
     * @return 是否设置成功
     */
    boolean setFileTags(String fileName, String bucketName, Map<String, String> tags);
}
