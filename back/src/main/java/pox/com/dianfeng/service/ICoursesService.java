package pox.com.dianfeng.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import pox.com.dianfeng.entity.Courses;
import pox.com.dianfeng.entity.dto.CourseWithTeacherDTO;

/**
 * <p>
 * 课程信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface ICoursesService extends IService<Courses> {

    /**
     * 分页查询课程及关联的讲师和分类信息
     *
     * @param page 分页参数
     * @param entity 查询条件
     * @return 课程及关联信息的分页结果
     */
    IPage<CourseWithTeacherDTO> pageWithTeacher(Page<Courses> page, Courses entity);

    /**
     * 根据ID查询课程及关联的讲师和分类信息
     *
     * @param id 课程ID
     * @return 课程及关联信息
     */
    CourseWithTeacherDTO getWithTeacherById(Integer id);

}
