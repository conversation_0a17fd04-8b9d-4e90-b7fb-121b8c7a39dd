package pox.com.dianfeng.service;

import pox.com.dianfeng.dto.OssPostObjectResponse;

/**
 * OSS PostObject签名服务接口
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface OssPostObjectService {

    /**
     * 生成PostObject签名和Policy
     *
     * @param category 文件分类（image/video/audio/document）
     * @param fileName 文件名（可选，如果不提供则生成随机文件名）
     * @return PostObject签名响应
     */
    OssPostObjectResponse generatePostObjectSignature(String category, String fileName);

    /**
     * 生成PostObject签名和Policy（自定义路径前缀）
     *
     * @param pathPrefix 路径前缀
     * @param fileName 文件名
     * @param contentType 文件类型
     * @param maxFileSize 最大文件大小
     * @return PostObject签名响应
     */
    OssPostObjectResponse generatePostObjectSignature(String pathPrefix, String fileName,
                                                     String contentType, Long maxFileSize);
}
