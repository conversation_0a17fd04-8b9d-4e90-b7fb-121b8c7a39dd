package pox.com.dianfeng.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import pox.com.dianfeng.entity.LiveCourses;
import pox.com.dianfeng.entity.dto.LiveCourseWithTeacherDTO;

/**
 * <p>
 * 直播通知表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface ILiveCoursesService extends IService<LiveCourses> {

    /**
     * 分页查询直播课程及关联的讲师和分类信息
     *
     * @param page 分页参数
     * @param entity 查询条件
     * @return 直播课程及关联信息的分页结果
     */
    IPage<LiveCourseWithTeacherDTO> pageWithTeacher(Page<LiveCourses> page, LiveCourses entity);

    /**
     * 根据ID查询直播课程及关联的讲师和分类信息
     *
     * @param id 直播课程ID
     * @return 直播课程及关联信息
     */
    LiveCourseWithTeacherDTO getWithTeacherById(Integer id);

}
