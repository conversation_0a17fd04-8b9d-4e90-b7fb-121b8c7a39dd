package pox.com.dianfeng.service.impl;

import com.aliyuncs.IAcsClient;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import pox.com.dianfeng.config.AliyunStsConfig;
import pox.com.dianfeng.entity.dto.StsTokenResponse;
import pox.com.dianfeng.service.IStsService;
import pox.com.dianfeng.util.FileUploadUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * STS临时访问凭证服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StsServiceImpl implements IStsService {
    
    private final IAcsClient stsClient;
    private final AliyunStsConfig stsConfig;
    
    @Value("${oss.bucket-name:dianfeng-class}")
    private String bucketName;
    
    @Value("${oss.endpoint}")
    private String ossEndpoint;
    
    @Value("${oss.region:cn-chengdu}")
    private String ossRegion;
    
    @Override
    public StsTokenResponse getStsToken(String category) {
        String pathPrefix = category + "/" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")) + "/";
        return getStsToken(category, pathPrefix);
    }
    
    @Override
    public StsTokenResponse getStsToken(String category, String pathPrefix) {
        Long maxFileSize = getMaxFileSizeByCategory(category);
        return getStsToken(category, pathPrefix, maxFileSize, stsConfig.getDurationSeconds());
    }
    
    @Override
    public StsTokenResponse getStsToken(String category, String pathPrefix, Long maxFileSize, Long durationSeconds) {
        try {
            // 检查是否配置了真实的角色ARN
            if (stsConfig.getRoleArn() == null || stsConfig.getRoleArn().contains("REPLACE_WITH_REAL_ARN")) {
                log.warn("未配置真实的RAM角色ARN，返回模拟数据用于开发测试");
                return createMockStsToken(pathPrefix, maxFileSize, durationSeconds, category);
            }

            // 创建STS请求
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setRoleArn(stsConfig.getRoleArn());
            request.setRoleSessionName(stsConfig.getRoleSessionName());
            request.setDurationSeconds(durationSeconds);

            // 设置权限策略，限制只能上传到指定路径
            String policy = buildPolicy(pathPrefix, maxFileSize);
            request.setPolicy(policy);

            log.info("正在获取STS临时凭证，角色ARN: {}, 会话名称: {}", stsConfig.getRoleArn(), stsConfig.getRoleSessionName());

            // 调用STS服务
            AssumeRoleResponse response = stsClient.getAcsResponse(request);
            AssumeRoleResponse.Credentials credentials = response.getCredentials();

            log.info("STS临时凭证获取成功，过期时间: {}", credentials.getExpiration());

            // 构建响应
            return StsTokenResponse.builder()
                    .accessKeyId(credentials.getAccessKeyId())
                    .accessKeySecret(credentials.getAccessKeySecret())
                    .securityToken(credentials.getSecurityToken())
                    .expiration(credentials.getExpiration())
                    .bucketName(bucketName)
                    .endpoint(ossEndpoint)
                    .region(ossRegion)
                    .pathPrefix(pathPrefix)
                    .maxFileSize(maxFileSize)
                    .allowedFileTypes(getAllowedFileTypesByCategory(category))
                    .durationSeconds(durationSeconds)
                    .build();

        } catch (Exception e) {
            log.error("获取STS临时凭证失败: {}", e.getMessage(), e);

            // 如果是权限问题，提供详细的错误信息
            if (e.getMessage().contains("NoPermission") || e.getMessage().contains("AccessDenied")) {
                throw new RuntimeException("STS权限不足，请检查RAM角色配置和权限策略: " + e.getMessage());
            } else if (e.getMessage().contains("InvalidParameter.RoleArn")) {
                throw new RuntimeException("RAM角色ARN格式错误，请检查配置: " + e.getMessage());
            } else {
                throw new RuntimeException("获取STS临时凭证失败: " + e.getMessage());
            }
        }
    }

    /**
     * 创建模拟STS凭证（用于开发测试）
     */
    private StsTokenResponse createMockStsToken(String pathPrefix, Long maxFileSize, Long durationSeconds, String category) {
        String mockAccessKeyId = "STS.MOCK" + System.currentTimeMillis();
        String mockAccessKeySecret = "MOCK_SECRET_" + System.currentTimeMillis();
        String mockSecurityToken = "MOCK_TOKEN_" + System.currentTimeMillis();
        String mockExpiration = java.time.Instant.now().plusSeconds(durationSeconds).toString();

        return StsTokenResponse.builder()
                .accessKeyId(mockAccessKeyId)
                .accessKeySecret(mockAccessKeySecret)
                .securityToken(mockSecurityToken)
                .expiration(mockExpiration)
                .bucketName(bucketName)
                .endpoint(ossEndpoint)
                .region(ossRegion)
                .pathPrefix(pathPrefix)
                .maxFileSize(maxFileSize)
                .allowedFileTypes(getAllowedFileTypesByCategory(category))
                .durationSeconds(durationSeconds)
                .build();
    }
    
    /**
     * 构建STS权限策略
     */
    private String buildPolicy(String pathPrefix, Long maxFileSize) {
        return String.format("""
            {
                "Version": "1",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Action": [
                            "oss:PutObject",
                            "oss:PutObjectAcl"
                        ],
                        "Resource": "acs:oss:*:*:%s/%s*",
                        "Condition": {
                            "NumericLessThanEquals": {
                                "oss:content-length": %d
                            }
                        }
                    }
                ]
            }
            """, bucketName, pathPrefix, maxFileSize);
    }
    
    /**
     * 根据文件分类获取最大文件大小
     */
    private Long getMaxFileSizeByCategory(String category) {
        return switch (category.toLowerCase()) {
            case "image" -> 10L * 1024 * 1024; // 10MB
            case "video" -> 500L * 1024 * 1024; // 500MB
            case "audio" -> 50L * 1024 * 1024; // 50MB
            case "document" -> 20L * 1024 * 1024; // 20MB
            default -> 10L * 1024 * 1024; // 默认10MB
        };
    }
    
    /**
     * 根据文件分类获取允许的文件类型
     */
    private String[] getAllowedFileTypesByCategory(String category) {
        return switch (category.toLowerCase()) {
            case "image" -> new String[]{"jpg", "jpeg", "png", "gif", "webp", "bmp"};
            case "video" -> new String[]{"mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"};
            case "audio" -> new String[]{"mp3", "wav", "aac", "flac", "ogg", "m4a"};
            case "document" -> new String[]{"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};
            default -> new String[]{"jpg", "jpeg", "png", "gif"};
        };
    }
}
