package pox.com.dianfeng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import pox.com.dianfeng.entity.TagConfigs;
import pox.com.dianfeng.mapper.TagConfigsMapper;
import pox.com.dianfeng.service.ITagConfigsService;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class TagConfigsServiceImpl extends ServiceImpl<TagConfigsMapper, TagConfigs> implements ITagConfigsService {

    // 标签缓存
    private final Map<String, Map<Integer, String>> tagLabelCache = new ConcurrentHashMap<>();


    @Override
    public List<TagConfigs> getByCategory(String category) {
        QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category)
                   .orderByAsc("sort_order", "id");
        return list(queryWrapper);
    }

    @Override
    public List<TagConfigs> getAvailableByCategory(String category) {
        QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category)
                   .eq("status", 1)
                   .orderByAsc("sort_order", "id");
        return list(queryWrapper);
    }

    @Override
    public boolean checkValueExists(String category, Integer value, Integer excludeId) {
        QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category)
                   .eq("value", value);

        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }

        return count(queryWrapper) > 0;
    }

    @Override
    public String getTagLabel(String category, Integer value) {
        if (value == null) return "未知";

        // 从缓存中获取
        Map<Integer, String> categoryCache = tagLabelCache.get(category);
        if (categoryCache != null) {
            return categoryCache.getOrDefault(value, "未知");
        }

        // 缓存未命中，查询数据库并缓存
        List<TagConfigs> configs = getAvailableByCategory(category);
        Map<Integer, String> labelMap = configs.stream()
                .collect(Collectors.toMap(
                        TagConfigs::getValue,
                        TagConfigs::getLabel,
                        (existing, replacement) -> existing // 处理重复key
                ));

        // 缓存结果
        tagLabelCache.put(category, labelMap);

        return labelMap.getOrDefault(value, "未知");
    }
} 