package pox.com.dianfeng.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pox.com.dianfeng.entity.CourseLessons;
import pox.com.dianfeng.entity.Courses;
import pox.com.dianfeng.entity.LiveCourses;
import pox.com.dianfeng.entity.Teachers;
import pox.com.dianfeng.util.OssUrlUtil;

import java.util.List;

/**
 * OSS URL处理服务
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Service
public class OssUrlService {

    @Autowired
    private OssUrlUtil ossUrlUtil;

    /**
     * 为课程对象设置完整URL
     * 
     * @param course 课程对象
     */
    public void setCourseFullUrls(Courses course) {
        if (course == null) {
            return;
        }
        
        // 设置封面图片完整URL
        course.setCoverImageFullUrl(ossUrlUtil.buildFullUrl(course.getCoverImage()));
    }

    /**
     * 为课程列表设置完整URL
     * 
     * @param courses 课程列表
     */
    public void setCourseFullUrls(List<Courses> courses) {
        if (courses == null || courses.isEmpty()) {
            return;
        }
        
        courses.forEach(this::setCourseFullUrls);
    }

    /**
     * 为课时对象设置完整URL
     * 
     * @param lesson 课时对象
     */
    public void setLessonFullUrls(CourseLessons lesson) {
        if (lesson == null) {
            return;
        }
        
        // 设置视频完整URL
        lesson.setVideoUrlFullUrl(ossUrlUtil.buildFullUrl(lesson.getVideoUrl()));
    }

    /**
     * 为课时列表设置完整URL
     * 
     * @param lessons 课时列表
     */
    public void setLessonFullUrls(List<CourseLessons> lessons) {
        if (lessons == null || lessons.isEmpty()) {
            return;
        }
        
        lessons.forEach(this::setLessonFullUrls);
    }

    /**
     * 为直播课程对象设置完整URL
     * 
     * @param liveCourse 直播课程对象
     */
    public void setLiveCourseFullUrls(LiveCourses liveCourse) {
        if (liveCourse == null) {
            return;
        }
        
        // 设置封面图片完整URL
        liveCourse.setCoverImageFullUrl(ossUrlUtil.buildFullUrl(liveCourse.getCoverImage()));
    }

    /**
     * 为直播课程列表设置完整URL
     * 
     * @param liveCourses 直播课程列表
     */
    public void setLiveCourseFullUrls(List<LiveCourses> liveCourses) {
        if (liveCourses == null || liveCourses.isEmpty()) {
            return;
        }
        
        liveCourses.forEach(this::setLiveCourseFullUrls);
    }

    /**
     * 为讲师对象设置完整URL
     * 
     * @param teacher 讲师对象
     */
    public void setTeacherFullUrls(Teachers teacher) {
        if (teacher == null) {
            return;
        }
        
        // 设置头像完整URL
        teacher.setAvatarFullUrl(ossUrlUtil.buildFullUrl(teacher.getAvatar()));
    }

    /**
     * 为讲师列表设置完整URL
     * 
     * @param teachers 讲师列表
     */
    public void setTeacherFullUrls(List<Teachers> teachers) {
        if (teachers == null || teachers.isEmpty()) {
            return;
        }
        
        teachers.forEach(this::setTeacherFullUrls);
    }

    /**
     * 从完整URL提取相对路径（用于保存时）
     * 
     * @param fullUrl 完整URL
     * @return 相对路径
     */
    public String extractRelativePath(String fullUrl) {
        return ossUrlUtil.extractRelativePath(fullUrl);
    }

    /**
     * 构建完整URL
     * 
     * @param relativePath 相对路径
     * @return 完整URL
     */
    public String buildFullUrl(String relativePath) {
        return ossUrlUtil.buildFullUrl(relativePath);
    }
}
