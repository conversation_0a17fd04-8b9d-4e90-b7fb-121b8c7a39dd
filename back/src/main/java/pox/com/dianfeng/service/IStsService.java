package pox.com.dianfeng.service;

import pox.com.dianfeng.entity.dto.StsTokenResponse;

/**
 * STS临时访问凭证服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IStsService {
    
    /**
     * 获取STS临时访问凭证
     * 用于前端直传OSS
     *
     * @param category 文件分类（image/video/audio/document）
     * @return STS临时访问凭证
     */
    StsTokenResponse getStsToken(String category);
    
    /**
     * 获取STS临时访问凭证（指定路径前缀）
     *
     * @param category 文件分类
     * @param pathPrefix 路径前缀
     * @return STS临时访问凭证
     */
    StsTokenResponse getStsToken(String category, String pathPrefix);
    
    /**
     * 获取STS临时访问凭证（完整参数）
     *
     * @param category 文件分类
     * @param pathPrefix 路径前缀
     * @param maxFileSize 最大文件大小（字节）
     * @param durationSeconds 凭证有效期（秒）
     * @return STS临时访问凭证
     */
    StsTokenResponse getStsToken(String category, String pathPrefix, Long maxFileSize, Long durationSeconds);
}
