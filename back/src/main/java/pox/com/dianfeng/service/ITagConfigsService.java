package pox.com.dianfeng.service;

import com.baomidou.mybatisplus.extension.service.IService;
import pox.com.dianfeng.entity.TagConfigs;

import java.util.List;

/**
 * <p>
 * 标签配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface ITagConfigsService extends IService<TagConfigs> {
    
    /**
     * 根据分类获取标签配置列表
     * 
     * @param category 标签分类
     * @return 标签配置列表
     */
    List<TagConfigs> getByCategory(String category);
    
    /**
     * 获取可用的标签配置列表
     * 
     * @param category 标签分类
     * @return 可用的标签配置列表
     */
    List<TagConfigs> getAvailableByCategory(String category);
    
    /**
     * 检查标签值是否存在
     *
     * @param category 标签分类
     * @param value 标签值
     * @param excludeId 排除的ID（用于更新时排除自己）
     * @return 是否存在
     */
    boolean checkValueExists(String category, Integer value, Integer excludeId);

    /**
     * 根据分类和值获取标签名称（带缓存）
     *
     * @param category 标签分类
     * @param value 标签值
     * @return 标签名称，未找到返回"未知"
     */
    String getTagLabel(String category, Integer value);
} 