package pox.com.dianfeng.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pox.com.dianfeng.entity.UserCourseAccess;
import pox.com.dianfeng.dto.CourseAccessRequestDTO;
import pox.com.dianfeng.dto.CourseAccessResponseDTO;
import pox.com.dianfeng.mapper.UserCourseAccessMapper;
import pox.com.dianfeng.service.IUserCourseAccessService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 用户课程权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Slf4j
@Service
public class UserCourseAccessServiceImpl extends ServiceImpl<UserCourseAccessMapper, UserCourseAccess> 
        implements IUserCourseAccessService {

    @Autowired
    private UserCourseAccessMapper userCourseAccessMapper;

    @Override
    public CourseAccessResponseDTO checkAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId) {
        log.info("检查用户权限: userId={}, courseId={}, chapterId={}, lessonId={}", 
                userId, courseId, chapterId, lessonId);
        
        // 通过Mapper查询权限
        UserCourseAccess access = userCourseAccessMapper.checkUserAccess(userId, courseId, chapterId, lessonId);
        
        if (access == null) {
            return CourseAccessResponseDTO.noAccess("用户没有访问该内容的权限");
        }
        
        // 检查权限是否已过期
        if (access.getExpireTime() != null && access.getExpireTime().isBefore(LocalDateTime.now())) {
            return CourseAccessResponseDTO.noAccess("权限已过期");
        }
        
        // 检查权限是否激活
        if (!access.getIsActive()) {
            return CourseAccessResponseDTO.noAccess("权限未激活");
        }
        
        // 检查权限状态
        if (!Integer.valueOf(UserCourseAccess.Status.VALID).equals(access.getStatus())) {
            String reason = Integer.valueOf(UserCourseAccess.Status.REFUNDED).equals(access.getStatus()) ? "权限已退款" : "权限已失效";
            return CourseAccessResponseDTO.noAccess(reason);
        }
        
        return CourseAccessResponseDTO.hasAccess(access);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserCourseAccess grantPurchaseAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                              BigDecimal pricePaid, BigDecimal originalPrice, String paymentMethod,
                                              String orderId, Boolean isBuyout, LocalDateTime expireTime) {
        
        log.info("授予购买权限: userId={}, courseId={}, isBuyout={}", userId, courseId, isBuyout);
        
        UserCourseAccess access = new UserCourseAccess();
        access.setUserId(userId);
        access.setCourseId(courseId);
        access.setChapterId(chapterId);
        access.setLessonId(lessonId);
        access.setAccessType(determineAccessType(chapterId, lessonId));
        access.setAcquireMethod(UserCourseAccess.AcquireMethod.PURCHASE);
        access.setIsBuyout(isBuyout != null ? isBuyout : false);
        access.setPricePaid(pricePaid);
        access.setOriginalPrice(originalPrice);
        access.setPaymentMethod(paymentMethod);
        access.setOrderId(orderId);
        access.setExpireTime(expireTime);
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        
        // 保存权限记录
        save(access);
        
        log.info("购买权限授予成功: accessId={}", access.getId());
        return access;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserCourseAccess grantFreeAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                          LocalDateTime expireTime) {
        
        log.info("授予免费权限: userId={}, courseId={}", userId, courseId);
        
        UserCourseAccess access = new UserCourseAccess();
        access.setUserId(userId);
        access.setCourseId(courseId);
        access.setChapterId(chapterId);
        access.setLessonId(lessonId);
        access.setAccessType(determineAccessType(chapterId, lessonId));
        access.setAcquireMethod(UserCourseAccess.AcquireMethod.FREE);
        access.setIsBuyout(false);
        access.setPricePaid(BigDecimal.ZERO);
        access.setOriginalPrice(BigDecimal.ZERO);
        access.setPaymentMethod(UserCourseAccess.PaymentMethod.GIFT);
        access.setExpireTime(expireTime);
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        
        save(access);
        
        log.info("免费权限授予成功: accessId={}", access.getId());
        return access;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserCourseAccess grantPointsAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                            Integer pointsUsed, BigDecimal originalPrice, LocalDateTime expireTime) {
        
        log.info("授予积分兑换权限: userId={}, courseId={}, pointsUsed={}", userId, courseId, pointsUsed);
        
        UserCourseAccess access = new UserCourseAccess();
        access.setUserId(userId);
        access.setCourseId(courseId);
        access.setChapterId(chapterId);
        access.setLessonId(lessonId);
        access.setAccessType(determineAccessType(chapterId, lessonId));
        access.setAcquireMethod(UserCourseAccess.AcquireMethod.POINTS);
        access.setIsBuyout(false);
        access.setPricePaid(BigDecimal.ZERO);
        access.setOriginalPrice(originalPrice);
        access.setPaymentMethod(UserCourseAccess.PaymentMethod.POINTS);
        access.setPointsUsed(pointsUsed);
        access.setExpireTime(expireTime);
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        
        save(access);
        
        log.info("积分兑换权限授予成功: accessId={}", access.getId());
        return access;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserCourseAccess grantCouponAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                            Integer couponId, BigDecimal pricePaid, BigDecimal originalPrice,
                                            LocalDateTime expireTime) {
        
        log.info("授予优惠券兑换权限: userId={}, courseId={}, couponId={}", userId, courseId, couponId);
        
        UserCourseAccess access = new UserCourseAccess();
        access.setUserId(userId);
        access.setCourseId(courseId);
        access.setChapterId(chapterId);
        access.setLessonId(lessonId);
        access.setAccessType(determineAccessType(chapterId, lessonId));
        access.setAcquireMethod(UserCourseAccess.AcquireMethod.COUPON);
        access.setIsBuyout(false);
        access.setPricePaid(pricePaid);
        access.setOriginalPrice(originalPrice);
        access.setPaymentMethod(UserCourseAccess.PaymentMethod.COUPON);
        access.setCouponId(couponId);
        access.setExpireTime(expireTime);
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        
        save(access);
        
        log.info("优惠券兑换权限授予成功: accessId={}", access.getId());
        return access;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserCourseAccess grantAdminGift(Integer userId, Integer courseId, Integer chapterId, Integer lessonId,
                                         Integer adminId, String remark, LocalDateTime expireTime) {
        
        log.info("管理员赠送权限: userId={}, courseId={}, adminId={}", userId, courseId, adminId);
        
        UserCourseAccess access = new UserCourseAccess();
        access.setUserId(userId);
        access.setCourseId(courseId);
        access.setChapterId(chapterId);
        access.setLessonId(lessonId);
        access.setAccessType(determineAccessType(chapterId, lessonId));
        access.setAcquireMethod(UserCourseAccess.AcquireMethod.GIFT);
        access.setIsBuyout(false);
        access.setPricePaid(BigDecimal.ZERO);
        access.setOriginalPrice(BigDecimal.ZERO);
        access.setPaymentMethod(UserCourseAccess.PaymentMethod.GIFT);
        access.setAdminId(adminId);
        access.setRemark(remark);
        access.setExpireTime(expireTime);
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        
        save(access);
        
        log.info("管理员赠送权限成功: accessId={}", access.getId());
        return access;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserCourseAccess> batchGrantAccess(CourseAccessRequestDTO request) {
        log.info("批量授予权限: userIds={}, courseId={}", request.getUserIds(), request.getCourseId());
        
        List<UserCourseAccess> accessList = new ArrayList<>();
        
        for (Integer userId : request.getUserIds()) {
            UserCourseAccess access = new UserCourseAccess();
            access.setUserId(userId);
            access.setCourseId(request.getCourseId());
            access.setChapterId(request.getChapterId());
            access.setLessonId(request.getLessonId());
            access.setAccessType(request.getAccessType());
            access.setAcquireMethod(request.getAcquireMethod());
            access.setIsBuyout(request.getIsBuyout() != null ? request.getIsBuyout() : false);
            access.setPricePaid(request.getPricePaid() != null ? request.getPricePaid() : BigDecimal.ZERO);
            access.setOriginalPrice(request.getOriginalPrice() != null ? request.getOriginalPrice() : BigDecimal.ZERO);
            access.setPaymentMethod(request.getPaymentMethod());
            access.setOrderId(request.getOrderId());
            access.setCouponId(request.getCouponId());
            access.setPointsUsed(request.getPointsUsed());
            access.setExpireTime(request.getExpireTime());
            access.setAdminId(request.getAdminId());
            access.setRemark(request.getRemark());
            access.setIsActive(request.getIsActive() != null ? request.getIsActive() : true);
            access.setStatus(UserCourseAccess.Status.VALID);
            
            accessList.add(access);
        }
        
        // 批量保存
        saveBatch(accessList);
        
        log.info("批量授予权限成功: count={}", accessList.size());
        return accessList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processRefund(Integer accessId, BigDecimal refundAmount, String refundReason) {
        log.info("处理退款: accessId={}, refundAmount={}", accessId, refundAmount);
        
        UserCourseAccess access = getById(accessId);
        if (access == null) {
            log.warn("权限记录不存在: accessId={}", accessId);
            return false;
        }
        
        access.setStatus(UserCourseAccess.Status.REFUNDED);
        access.setRefundTime(LocalDateTime.now());
        access.setRefundAmount(refundAmount);
        access.setRefundReason(refundReason);
        
        boolean result = updateById(access);
        
        log.info("退款处理结果: accessId={}, result={}", accessId, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean activateAccess(Integer accessId) {
        log.info("激活权限: accessId={}", accessId);
        
        UserCourseAccess access = getById(accessId);
        if (access == null) {
            log.warn("权限记录不存在: accessId={}", accessId);
            return false;
        }
        
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        
        boolean result = updateById(access);
        
        log.info("权限激活结果: accessId={}, result={}", accessId, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deactivateAccess(Integer accessId, String reason) {
        log.info("停用权限: accessId={}, reason={}", accessId, reason);
        
        UserCourseAccess access = getById(accessId);
        if (access == null) {
            log.warn("权限记录不存在: accessId={}", accessId);
            return false;
        }
        
        access.setIsActive(false);
        access.setStatus(UserCourseAccess.Status.INVALID);
        access.setRemark(access.getRemark() + " [停用原因: " + reason + "]");
        
        boolean result = updateById(access);
        
        log.info("权限停用结果: accessId={}, result={}", accessId, result);
        return result;
    }

    @Override
    public List<UserCourseAccess> getUserValidAccess(Integer userId) {
        return userCourseAccessMapper.getUserValidAccess(userId);
    }

    @Override
    public List<UserCourseAccess> getUserCourseAccess(Integer userId, Integer courseId) {
        return userCourseAccessMapper.getUserCourseAccess(userId, courseId);
    }

    @Override
    public List<UserCourseAccess> getExpiringAccess(Integer userId) {
        QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_del", 0)
                .eq("is_active", 1)
                .eq("status", UserCourseAccess.Status.VALID)
                .isNotNull("expire_time")
                .le("expire_time", LocalDateTime.now().plusDays(7))
                .gt("expire_time", LocalDateTime.now())
                .orderByAsc("expire_time");
        
        return list(queryWrapper);
    }

    @Override
    public CourseAccessResponseDTO.AccessStatistics getUserAccessStatistics(Integer userId) {
        QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("is_del", 0);
        
        List<UserCourseAccess> allAccess = list(queryWrapper);
        
        long totalAccess = allAccess.size();
        
        long validAccess = allAccess.stream()
                .filter(access -> access.getIsActive() && Integer.valueOf(UserCourseAccess.Status.VALID).equals(access.getStatus()) &&
                        (access.getExpireTime() == null || access.getExpireTime().isAfter(LocalDateTime.now())))
                .count();
        
        long expiredAccess = allAccess.stream()
                .filter(access -> access.getExpireTime() != null && access.getExpireTime().isBefore(LocalDateTime.now()))
                .count();
        
        long buyoutAccess = allAccess.stream()
                .filter(access -> access.getIsBuyout())
                .count();
        
        long expiringAccess = allAccess.stream()
                .filter(access -> access.getExpireTime() != null &&
                        access.getExpireTime().isAfter(LocalDateTime.now()) &&
                        access.getExpireTime().isBefore(LocalDateTime.now().plusDays(7)))
                .count();
        
        long freeAccess = allAccess.stream()
                .filter(access -> access.getAcquireMethod().equals(UserCourseAccess.AcquireMethod.FREE))
                .count();
        
        long purchasedAccess = allAccess.stream()
                .filter(access -> access.getAcquireMethod().equals(UserCourseAccess.AcquireMethod.PURCHASE))
                .count();
        
        long totalCourses = allAccess.stream()
                .filter(access -> access.getAccessType().equals(UserCourseAccess.AccessType.COURSE))
                .map(UserCourseAccess::getCourseId)
                .distinct()
                .count();
        
        long totalChapters = allAccess.stream()
                .filter(access -> access.getAccessType().equals(UserCourseAccess.AccessType.CHAPTER))
                .count();
        
        long totalLessons = allAccess.stream()
                .filter(access -> access.getAccessType().equals(UserCourseAccess.AccessType.LESSON))
                .count();
        
        return CourseAccessResponseDTO.AccessStatistics.builder()
                .totalAccess(totalAccess)
                .validAccess(validAccess)
                .expiredAccess(expiredAccess)
                .buyoutAccess(buyoutAccess)
                .expiringAccess(expiringAccess)
                .freeAccess(freeAccess)
                .purchasedAccess(purchasedAccess)
                .totalCourses(totalCourses)
                .totalChapters(totalChapters)
                .totalLessons(totalLessons)
                .build();
    }

    @Override
    public CourseAccessResponseDTO.AccessStatistics getGlobalAccessStatistics() {
        // 获取所有权限记录（未删除的）
        QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);

        List<UserCourseAccess> allAccess = list(queryWrapper);

        long totalAccess = allAccess.size();

        // 统计有效权限
        long validAccess = allAccess.stream()
                .filter(access -> access.getIsActive() && Integer.valueOf(UserCourseAccess.Status.VALID).equals(access.getStatus()) &&
                        (access.getExpireTime() == null || access.getExpireTime().isAfter(LocalDateTime.now())))
                .count();

        // 统计过期权限
        long expiredAccess = allAccess.stream()
                .filter(access -> access.getExpireTime() != null && access.getExpireTime().isBefore(LocalDateTime.now()))
                .count();

        // 统计买断权限
        long buyoutAccess = allAccess.stream()
                .filter(access -> Boolean.TRUE.equals(access.getIsBuyout()))
                .count();

        // 统计即将过期权限（7天内）
        LocalDateTime sevenDaysLater = LocalDateTime.now().plusDays(7);
        long expiringAccess = allAccess.stream()
                .filter(access -> access.getExpireTime() != null &&
                        access.getExpireTime().isAfter(LocalDateTime.now()) &&
                        access.getExpireTime().isBefore(sevenDaysLater) &&
                        access.getIsActive() && Integer.valueOf(UserCourseAccess.Status.VALID).equals(access.getStatus()))
                .count();

        // 统计免费权限
        long freeAccess = allAccess.stream()
                .filter(access -> Integer.valueOf(UserCourseAccess.AcquireMethod.FREE).equals(access.getAcquireMethod()))
                .count();

        // 统计购买权限
        long purchasedAccess = allAccess.stream()
                .filter(access -> Integer.valueOf(UserCourseAccess.AcquireMethod.PURCHASE).equals(access.getAcquireMethod()))
                .count();

        // 统计涉及的课程数量
        long totalCourses = allAccess.stream()
                .map(UserCourseAccess::getCourseId)
                .distinct()
                .count();

        // 统计涉及的章节数量
        long totalChapters = allAccess.stream()
                .filter(access -> access.getChapterId() != null)
                .map(UserCourseAccess::getChapterId)
                .distinct()
                .count();

        // 统计涉及的课时数量
        long totalLessons = allAccess.stream()
                .filter(access -> access.getLessonId() != null)
                .map(UserCourseAccess::getLessonId)
                .distinct()
                .count();

        return CourseAccessResponseDTO.AccessStatistics.builder()
                .totalAccess(totalAccess)
                .validAccess(validAccess)
                .expiredAccess(expiredAccess)
                .buyoutAccess(buyoutAccess)
                .expiringAccess(expiringAccess)
                .freeAccess(freeAccess)
                .purchasedAccess(purchasedAccess)
                .totalCourses(totalCourses)
                .totalChapters(totalChapters)
                .totalLessons(totalLessons)
                .build();
    }

    /**
     * 根据章节ID和课时ID确定权限类型
     */
    private Integer determineAccessType(Integer chapterId, Integer lessonId) {
        if (lessonId != null) {
            return UserCourseAccess.AccessType.LESSON;
        } else if (chapterId != null) {
            return UserCourseAccess.AccessType.CHAPTER;
        } else {
            return UserCourseAccess.AccessType.COURSE;
        }
    }
} 