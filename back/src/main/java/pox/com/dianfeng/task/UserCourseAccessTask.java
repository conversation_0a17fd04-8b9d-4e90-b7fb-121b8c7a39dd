package pox.com.dianfeng.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pox.com.dianfeng.entity.UserCourseAccess;
import pox.com.dianfeng.service.IUserCourseAccessService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户课程权限定时任务
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@Slf4j
@Component
public class UserCourseAccessTask {

    @Autowired
    private IUserCourseAccessService userCourseAccessService;

    /**
     * 处理过期权限
     * 每小时执行一次，将已过期的权限状态更新为失效
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void processExpiredAccess() {
        log.info("开始处理过期权限...");
        
        try {
            // 查询已过期但状态仍为有效的权限
            QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 0)
                    .eq("is_active", 1)
                    .eq("status", UserCourseAccess.Status.VALID)
                    .isNotNull("expire_time")
                    .le("expire_time", LocalDateTime.now());
            
            List<UserCourseAccess> expiredAccessList = userCourseAccessService.list(queryWrapper);
            
            if (expiredAccessList.isEmpty()) {
                log.info("没有需要处理的过期权限");
                return;
            }
            
            // 批量更新过期权限状态
            for (UserCourseAccess access : expiredAccessList) {
                access.setStatus(UserCourseAccess.Status.INVALID);
                access.setIsActive(false);
                access.setRemark(access.getRemark() + " [系统自动处理: 权限已过期]");
            }
            
            boolean result = userCourseAccessService.updateBatchById(expiredAccessList);
            
            if (result) {
                log.info("成功处理过期权限，数量: {}", expiredAccessList.size());
            } else {
                log.error("处理过期权限失败");
            }
            
        } catch (Exception e) {
            log.error("处理过期权限时发生异常", e);
        }
    }

    /**
     * 权限过期提醒
     * 每天上午9点执行，检查即将过期的权限（7天内）
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void checkExpiringAccess() {
        log.info("开始检查即将过期的权限...");
        
        try {
            // 查询7天内即将过期的权限
            QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 0)
                    .eq("is_active", 1)
                    .eq("status", UserCourseAccess.Status.VALID)
                    .isNotNull("expire_time")
                    .gt("expire_time", LocalDateTime.now())
                    .le("expire_time", LocalDateTime.now().plusDays(7));
            
            List<UserCourseAccess> expiringAccessList = userCourseAccessService.list(queryWrapper);
            
            if (expiringAccessList.isEmpty()) {
                log.info("没有即将过期的权限");
                return;
            }
            
            log.info("发现即将过期的权限，数量: {}", expiringAccessList.size());
            
            // 这里可以添加发送通知的逻辑
            // 例如：发送邮件、短信、站内消息等
            for (UserCourseAccess access : expiringAccessList) {
                log.info("用户 {} 的课程 {} 权限将于 {} 过期", 
                        access.getUserId(), access.getCourseId(), access.getExpireTime());
                
                // TODO: 发送过期提醒通知
                // notificationService.sendExpiryNotification(access);
            }
            
        } catch (Exception e) {
            log.error("检查即将过期权限时发生异常", e);
        }
    }

    /**
     * 权限统计报告
     * 每天凌晨1点执行，生成权限统计报告
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateAccessStatistics() {
        log.info("开始生成权限统计报告...");
        
        try {
            // 统计总权限数
            long totalAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>().eq("is_del", 0));
            
            // 统计有效权限数
            long validAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("is_active", 1)
                            .eq("status", UserCourseAccess.Status.VALID)
                            .and(wrapper -> wrapper.isNull("expire_time")
                                    .or().gt("expire_time", LocalDateTime.now())));
            
            // 统计过期权限数
            long expiredAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .isNotNull("expire_time")
                            .le("expire_time", LocalDateTime.now()));
            
            // 统计买断权限数
            long buyoutAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("is_buyout", 1));
            
            // 统计各种获取方式的权限数
            long purchasedAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("acquire_method", UserCourseAccess.AcquireMethod.PURCHASE));
            
            long freeAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("acquire_method", UserCourseAccess.AcquireMethod.FREE));
            
            long pointsAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("acquire_method", UserCourseAccess.AcquireMethod.POINTS));
            
            long couponAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("acquire_method", UserCourseAccess.AcquireMethod.COUPON));
            
            long giftAccess = userCourseAccessService.count(
                    new QueryWrapper<UserCourseAccess>()
                            .eq("is_del", 0)
                            .eq("acquire_method", UserCourseAccess.AcquireMethod.GIFT));
            
            // 输出统计报告
            log.info("=== 权限统计报告 ===");
            log.info("总权限数: {}", totalAccess);
            log.info("有效权限数: {}", validAccess);
            log.info("过期权限数: {}", expiredAccess);
            log.info("买断权限数: {}", buyoutAccess);
            log.info("购买权限数: {}", purchasedAccess);
            log.info("免费权限数: {}", freeAccess);
            log.info("积分兑换权限数: {}", pointsAccess);
            log.info("优惠券兑换权限数: {}", couponAccess);
            log.info("赠送权限数: {}", giftAccess);
            log.info("=== 报告结束 ===");
            
            // TODO: 可以将统计数据保存到数据库或发送给管理员
            
        } catch (Exception e) {
            log.error("生成权限统计报告时发生异常", e);
        }
    }

    /**
     * 清理软删除的权限记录
     * 每周日凌晨2点执行，清理超过30天的软删除记录
     */
    @Scheduled(cron = "0 0 2 ? * SUN")
    public void cleanupDeletedAccess() {
        log.info("开始清理软删除的权限记录...");
        
        try {
            // 查询30天前被软删除的记录
            QueryWrapper<UserCourseAccess> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("is_del", 1)
                    .le("updated_at", LocalDateTime.now().minusDays(30));
            
            List<UserCourseAccess> deletedAccessList = userCourseAccessService.list(queryWrapper);
            
            if (deletedAccessList.isEmpty()) {
                log.info("没有需要清理的软删除记录");
                return;
            }
            
            // 物理删除这些记录
            boolean result = userCourseAccessService.removeBatchByIds(
                    deletedAccessList.stream()
                            .map(UserCourseAccess::getId)
                            .collect(java.util.stream.Collectors.toList()));
            
            if (result) {
                log.info("成功清理软删除记录，数量: {}", deletedAccessList.size());
            } else {
                log.error("清理软删除记录失败");
            }
            
        } catch (Exception e) {
            log.error("清理软删除记录时发生异常", e);
        }
    }

    /**
     * 权限数据一致性检查
     * 每月1号凌晨3点执行，检查权限数据的一致性
     */
    @Scheduled(cron = "0 0 3 1 * ?")
    public void checkDataConsistency() {
        log.info("开始进行权限数据一致性检查...");
        
        try {
            // 检查是否有重复的权限记录
            // 这里可以添加具体的一致性检查逻辑
            
            log.info("权限数据一致性检查完成");
            
        } catch (Exception e) {
            log.error("权限数据一致性检查时发生异常", e);
        }
    }
}
