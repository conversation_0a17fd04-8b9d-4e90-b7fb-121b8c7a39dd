package pox.com.dianfeng.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pox.com.dianfeng.entity.LiveCourses;
import pox.com.dianfeng.service.ILiveCoursesService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 直播课程状态定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Component
public class LiveCourseStatusTask {

    @Autowired
    private ILiveCoursesService liveCoursesService;

    // 直播状态常量
    private static final Integer STATUS_NOT_STARTED = 0;  // 未开始
    private static final Integer STATUS_LIVING = 1;       // 直播中
    private static final Integer STATUS_ENDED = 2;        // 已结束

    /**
     * 每分钟执行一次，检查并更新直播状态
     * cron表达式：秒 分 时 日 月 周
     * 0 * * * * ? 表示每分钟的第0秒执行
     */
    @Scheduled(cron = "0 * * * * ?")
    public void updateLiveCourseStatus() {
        try {
            log.debug("开始执行直播状态更新任务...");
            
            LocalDateTime now = LocalDateTime.now();
            
            // 1. 更新应该开始的直播（状态从 0-未开始 改为 1-直播中）
            updateToLiving(now);
            
            // 2. 更新应该结束的直播（状态从 1-直播中 改为 2-已结束）
            updateToEnded(now);
            
            log.debug("直播状态更新任务执行完成");
            
        } catch (Exception e) {
            log.error("执行直播状态更新任务时发生错误", e);
        }
    }

    /**
     * 更新应该开始的直播状态
     * 
     * @param now 当前时间
     */
    private void updateToLiving(LocalDateTime now) {
        try {
            // 查询应该开始但还未开始的直播
            QueryWrapper<LiveCourses> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", STATUS_NOT_STARTED)  // 未开始
                       .le("start_time", now)  // 开始时间 <= 当前时间
                       .gt("end_time", now)    // 结束时间 > 当前时间
                       .eq("is_del", 0);       // 未删除
            
            List<LiveCourses> coursesToStart = liveCoursesService.list(queryWrapper);
            
            if (!coursesToStart.isEmpty()) {
                // 批量更新状态为直播中
                UpdateWrapper<LiveCourses> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("status", STATUS_LIVING)  // 直播中
                           .set("updated_at", now)
                           .eq("status", STATUS_NOT_STARTED)  // 未开始
                           .le("start_time", now)
                           .gt("end_time", now)
                           .eq("is_del", 0);
                
                boolean updated = liveCoursesService.update(updateWrapper);
                
                if (updated) {
                    log.info("成功将 {} 个直播课程状态更新为【直播中】", coursesToStart.size());
                    
                    // 记录具体更新的课程
                    for (LiveCourses course : coursesToStart) {
                        log.info("直播课程 [{}] (ID: {}) 已自动开始", course.getTitle(), course.getId());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("更新直播开始状态时发生错误", e);
        }
    }

    /**
     * 更新应该结束的直播状态
     * 
     * @param now 当前时间
     */
    private void updateToEnded(LocalDateTime now) {
        try {
            // 查询应该结束但还在直播中的课程
            QueryWrapper<LiveCourses> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", STATUS_LIVING)  // 直播中
                       .le("end_time", now)    // 结束时间 <= 当前时间
                       .eq("is_del", 0);       // 未删除
            
            List<LiveCourses> coursesToEnd = liveCoursesService.list(queryWrapper);
            
            if (!coursesToEnd.isEmpty()) {
                // 批量更新状态为已结束
                UpdateWrapper<LiveCourses> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("status", STATUS_ENDED)  // 已结束
                           .set("updated_at", now)
                           .eq("status", STATUS_LIVING)  // 直播中
                           .le("end_time", now)
                           .eq("is_del", 0);
                
                boolean updated = liveCoursesService.update(updateWrapper);
                
                if (updated) {
                    log.info("成功将 {} 个直播课程状态更新为【已结束】", coursesToEnd.size());
                    
                    // 记录具体更新的课程
                    for (LiveCourses course : coursesToEnd) {
                        log.info("直播课程 [{}] (ID: {}) 已自动结束", course.getTitle(), course.getId());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("更新直播结束状态时发生错误", e);
        }
    }

    /**
     * 每天凌晨2点执行一次，清理过期的直播数据（可选）
     * 这个方法可以用来清理一些过期的临时数据或进行数据统计
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyCleanup() {
        try {
            log.info("开始执行直播数据日常清理任务...");
            
            LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
            
            // 这里可以添加一些清理逻辑，比如：
            // 1. 清理过期的临时文件
            // 2. 统计直播数据
            // 3. 发送统计报告等
            
            log.info("直播数据日常清理任务执行完成");
            
        } catch (Exception e) {
            log.error("执行直播数据日常清理任务时发生错误", e);
        }
    }

    /**
     * 手动执行状态更新任务（用于测试和紧急情况）
     * 可以通过接口调用此方法
     */
    public void manualUpdateStatus() {
        log.info("手动执行直播状态更新任务");
        updateLiveCourseStatus();
    }

    /**
     * 获取当前直播状态统计信息
     */
    public String getStatusStatistics() {
        try {
            // 统计各状态的直播课程数量
            int notStartedCount = Math.toIntExact(liveCoursesService.count(
                new QueryWrapper<LiveCourses>().eq("status", STATUS_NOT_STARTED).eq("is_del", 0)
            ));
            
            int livingCount = Math.toIntExact(liveCoursesService.count(
                new QueryWrapper<LiveCourses>().eq("status", STATUS_LIVING).eq("is_del", 0)
            ));
            
            int endedCount = Math.toIntExact(liveCoursesService.count(
                new QueryWrapper<LiveCourses>().eq("status", STATUS_ENDED).eq("is_del", 0)
            ));
            
            return String.format("直播状态统计 - 未开始: %d, 直播中: %d, 已结束: %d", 
                notStartedCount, livingCount, endedCount);
                
        } catch (Exception e) {
            log.error("获取状态统计时发生错误", e);
            return "获取统计信息失败: " + e.getMessage();
        }
    }
}
