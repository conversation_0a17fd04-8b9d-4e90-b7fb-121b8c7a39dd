package pox.com.dianfeng.util;

import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.awt.FontFormatException;
import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 验证码工具类
 */
@Component
public class CaptchaUtil {

    private static final String CAPTCHA_KEY_PREFIX = "captcha:";

    // 验证码有效期（5分钟）
    private static final long CAPTCHA_EXPIRE_TIME = 5;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 生成验证码
     *
     * @return CaptchaVO 验证码值对象
     */
    public CaptchaVO generateCaptcha() {
        // 生成验证码ID
        String captchaId = UUID.randomUUID().toString();

        try {
            // 生成验证码
            SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
            // 设置字体
            specCaptcha.setFont(Captcha.FONT_1);
            // 设置类型
            specCaptcha.setCharType(Captcha.TYPE_DEFAULT);

            // 获取验证码的值
            String captchaValue = specCaptcha.text().toLowerCase();

            // 将验证码存储到Redis中，设置过期时间（带重试机制）
            String redisKey = CAPTCHA_KEY_PREFIX + captchaId;
            saveToRedisWithRetry(redisKey, captchaValue, CAPTCHA_EXPIRE_TIME);

            // 返回验证码信息
            return new CaptchaVO(captchaId, specCaptcha.toBase64());
        } catch (FontFormatException | IOException e) {
            throw new RuntimeException("生成验证码失败", e);
        }
    }

    /**
     * 验证验证码
     *
     * @param captchaId   验证码ID
     * @param captchaCode 用户输入的验证码
     * @return 是否正确
     */
    public boolean validateCaptcha(String captchaId, String captchaCode) {
        if (captchaId == null || captchaCode == null) {
            return false;
        }

        try {
            // 从Redis中获取验证码（带重试机制）
            String redisKey = CAPTCHA_KEY_PREFIX + captchaId;
            String correctCode = getFromRedisWithRetry(redisKey);

            // 验证后，删除Redis中的验证码（一次性使用）
            deleteFromRedisWithRetry(redisKey);

            // 验证码已过期或不存在
            if (correctCode == null) {
                return false;
            }

            // 验证码不区分大小写
            return correctCode.equalsIgnoreCase(captchaCode);
        } catch (Exception e) {
            // Redis 异常时，记录日志但不影响验证流程
            System.err.println("Redis 操作异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 带重试机制的 Redis 保存操作
     */
    private void saveToRedisWithRetry(String key, String value, long expireTime) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                redisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.MINUTES);
                return; // 成功则返回
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw new RuntimeException("Redis 保存失败，已重试 " + maxRetries + " 次", e);
                }

                // 等待一段时间后重试
                try {
                    Thread.sleep(100 * retryCount); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
    }

    /**
     * 带重试机制的 Redis 获取操作
     */
    private String getFromRedisWithRetry(String key) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                return redisTemplate.opsForValue().get(key);
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw new RuntimeException("Redis 获取失败，已重试 " + maxRetries + " 次", e);
                }

                // 等待一段时间后重试
                try {
                    Thread.sleep(100 * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
        return null;
    }

    /**
     * 带重试机制的 Redis 删除操作
     */
    private void deleteFromRedisWithRetry(String key) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                redisTemplate.delete(key);
                return; // 成功则返回
            } catch (Exception e) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    // 删除失败不抛异常，只记录日志
                    System.err.println("Redis 删除失败，已重试 " + maxRetries + " 次: " + e.getMessage());
                    return;
                }

                // 等待一段时间后重试
                try {
                    Thread.sleep(100 * retryCount);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
    }

    /**
     * 验证码信息值对象
     */
    public static class CaptchaVO {
        private String captchaId;
        private String base64Image;

        public CaptchaVO(String captchaId, String base64Image) {
            this.captchaId = captchaId;
            this.base64Image = base64Image;
        }

        public String getCaptchaId() {
            return captchaId;
        }

        public String getBase64Image() {
            return base64Image;
        }
    }
}