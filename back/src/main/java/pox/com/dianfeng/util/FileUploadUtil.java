package pox.com.dianfeng.util;

import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传工具类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class FileUploadUtil {
    
    /**
     * 支持的图片格式
     */
    private static final List<String> IMAGE_EXTENSIONS = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"
    );
    
    /**
     * 支持的视频格式
     */
    private static final List<String> VIDEO_EXTENSIONS = Arrays.asList(
            "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "m4v"
    );
    
    /**
     * 支持的音频格式
     */
    private static final List<String> AUDIO_EXTENSIONS = Arrays.asList(
            "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a"
    );
    
    /**
     * 支持的文档格式
     */
    private static final List<String> DOCUMENT_EXTENSIONS = Arrays.asList(
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"
    );
    
    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }
    
    /**
     * 生成唯一文件名
     */
    public static String generateUniqueFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return StringUtils.hasText(extension) ? uuid + "." + extension : uuid;
    }
    
    /**
     * 生成带日期路径的文件名
     */
    public static String generateDatePathFileName(String originalFileName, String category) {
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String uniqueFileName = generateUniqueFileName(originalFileName);
        return category + "/" + datePath + "/" + uniqueFileName;
    }
    
    /**
     * 验证文件类型
     */
    public static boolean isValidFileType(String fileName, String category) {
        String extension = getFileExtension(fileName);
        if (!StringUtils.hasText(extension)) {
            return false;
        }
        
        switch (category.toLowerCase()) {
            case "avatar":
            case "cover":
            case "image":
                return IMAGE_EXTENSIONS.contains(extension);
            case "video":
                return VIDEO_EXTENSIONS.contains(extension);
            case "audio":
                return AUDIO_EXTENSIONS.contains(extension);
            case "document":
                return DOCUMENT_EXTENSIONS.contains(extension);
            default:
                return true; // 其他类型默认允许
        }
    }
    
    /**
     * 获取文件分类
     */
    public static String getFileCategory(String fileName) {
        String extension = getFileExtension(fileName);
        
        if (IMAGE_EXTENSIONS.contains(extension)) {
            return "image";
        } else if (VIDEO_EXTENSIONS.contains(extension)) {
            return "video";
        } else if (AUDIO_EXTENSIONS.contains(extension)) {
            return "audio";
        } else if (DOCUMENT_EXTENSIONS.contains(extension)) {
            return "document";
        } else {
            return "other";
        }
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 验证文件大小
     */
    public static boolean isValidFileSize(long fileSize, String category) {
        // 根据文件类型设置不同的大小限制
        switch (category.toLowerCase()) {
            case "avatar":
            case "cover":
            case "image":
                return fileSize <= 10 * 1024 * 1024; // 10MB
            case "video":
                return fileSize <= 500 * 1024 * 1024; // 500MB
            case "audio":
                return fileSize <= 50 * 1024 * 1024; // 50MB
            case "document":
                return fileSize <= 20 * 1024 * 1024; // 20MB
            default:
                return fileSize <= 100 * 1024 * 1024; // 100MB
        }
    }
}
