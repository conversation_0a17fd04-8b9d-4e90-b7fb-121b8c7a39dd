package pox.com.dianfeng.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pox.com.dianfeng.dto.CourseAccessResponseDTO;
import pox.com.dianfeng.entity.UserCourseAccess;
import pox.com.dianfeng.service.IUserCourseAccessService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程权限验证工具类
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@Slf4j
@Component
public class CourseAccessUtil {

    @Autowired
    private IUserCourseAccessService userCourseAccessService;

    /**
     * 检查用户是否有课程访问权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否有权限
     */
    public boolean hasAccessToCourse(Integer userId, Integer courseId) {
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(userId, courseId, null, null);
        return response.getHasAccess();
    }

    /**
     * 检查用户是否有章节访问权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID
     * @return 是否有权限
     */
    public boolean hasAccessToChapter(Integer userId, Integer courseId, Integer chapterId) {
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(userId, courseId, chapterId, null);
        return response.getHasAccess();
    }

    /**
     * 检查用户是否有课时访问权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID
     * @param lessonId 课时ID
     * @return 是否有权限
     */
    public boolean hasAccessToLesson(Integer userId, Integer courseId, Integer chapterId, Integer lessonId) {
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(userId, courseId, chapterId, lessonId);
        return response.getHasAccess();
    }

    /**
     * 获取用户权限详情
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID（可选）
     * @param lessonId 课时ID（可选）
     * @return 权限详情
     */
    public CourseAccessResponseDTO getAccessDetail(Integer userId, Integer courseId, Integer chapterId, Integer lessonId) {
        return userCourseAccessService.checkAccess(userId, courseId, chapterId, lessonId);
    }

    /**
     * 检查用户是否有买断权限
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否有买断权限
     */
    public boolean hasBuyoutAccess(Integer userId, Integer courseId) {
        List<UserCourseAccess> accessList = userCourseAccessService.getUserCourseAccess(userId, courseId);
        return accessList.stream()
                .anyMatch(access -> access.getIsBuyout() && 
                         access.getIsActive() && 
                         access.getStatus().equals(UserCourseAccess.Status.VALID) &&
                         (access.getExpireTime() == null || access.getExpireTime().isAfter(LocalDateTime.now())));
    }

    /**
     * 获取用户在课程中的最高权限级别
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 权限级别：1-课程，2-章节，3-课时，0-无权限
     */
    public Integer getHighestAccessLevel(Integer userId, Integer courseId) {
        List<UserCourseAccess> accessList = userCourseAccessService.getUserValidAccess(userId);
        
        return accessList.stream()
                .filter(access -> access.getCourseId().equals(courseId))
                .filter(access -> access.getIsActive() && 
                               access.getStatus().equals(UserCourseAccess.Status.VALID) &&
                               (access.getExpireTime() == null || access.getExpireTime().isAfter(LocalDateTime.now())))
                .mapToInt(UserCourseAccess::getAccessType)
                .min()  // 数字越小权限级别越高
                .orElse(0);  // 无权限
    }

    /**
     * 检查权限是否即将过期
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param days 天数阈值
     * @return 是否即将过期
     */
    public boolean isAccessExpiringSoon(Integer userId, Integer courseId, int days) {
        List<UserCourseAccess> accessList = userCourseAccessService.getUserCourseAccess(userId, courseId);
        LocalDateTime threshold = LocalDateTime.now().plusDays(days);
        
        return accessList.stream()
                .anyMatch(access -> access.getExpireTime() != null &&
                                  access.getExpireTime().isBefore(threshold) &&
                                  access.getExpireTime().isAfter(LocalDateTime.now()) &&
                                  access.getIsActive() &&
                                  access.getStatus().equals(UserCourseAccess.Status.VALID));
    }

    /**
     * 获取用户的权限摘要信息
     * 
     * @param userId 用户ID
     * @return 权限摘要
     */
    public AccessSummary getUserAccessSummary(Integer userId) {
        List<UserCourseAccess> allAccess = userCourseAccessService.getUserValidAccess(userId);
        
        long totalCourses = allAccess.stream()
                .filter(access -> access.getAccessType().equals(UserCourseAccess.AccessType.COURSE))
                .count();
        
        long buyoutCourses = allAccess.stream()
                .filter(access -> access.getIsBuyout())
                .count();
        
        long expiringCount = allAccess.stream()
                .filter(access -> access.getExpireTime() != null &&
                                access.getExpireTime().isBefore(LocalDateTime.now().plusDays(7)) &&
                                access.getExpireTime().isAfter(LocalDateTime.now()))
                .count();
        
        return AccessSummary.builder()
                .totalCourses(totalCourses)
                .buyoutCourses(buyoutCourses)
                .expiringCount(expiringCount)
                .build();
    }

    /**
     * 权限摘要信息
     */
    @lombok.Data
    @lombok.Builder
    public static class AccessSummary {
        private Long totalCourses;      // 总课程数
        private Long buyoutCourses;     // 买断课程数
        private Long expiringCount;     // 即将过期数量
    }

    /**
     * 验证权限并抛出异常（用于需要强制验证的场景）
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param chapterId 章节ID（可选）
     * @param lessonId 课时ID（可选）
     * @throws IllegalAccessException 无权限时抛出异常
     */
    public void validateAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId) 
            throws IllegalAccessException {
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(userId, courseId, chapterId, lessonId);
        if (!response.getHasAccess()) {
            throw new IllegalAccessException("用户无权限访问该内容: " + response.getReason());
        }
    }

    /**
     * 验证课程权限
     */
    public void validateCourseAccess(Integer userId, Integer courseId) throws IllegalAccessException {
        validateAccess(userId, courseId, null, null);
    }

    /**
     * 验证章节权限
     */
    public void validateChapterAccess(Integer userId, Integer courseId, Integer chapterId) throws IllegalAccessException {
        validateAccess(userId, courseId, chapterId, null);
    }

    /**
     * 验证课时权限
     */
    public void validateLessonAccess(Integer userId, Integer courseId, Integer chapterId, Integer lessonId) 
            throws IllegalAccessException {
        validateAccess(userId, courseId, chapterId, lessonId);
    }

    /**
     * 获取权限类型描述
     * 
     * @param accessType 权限类型
     * @return 描述
     */
    public static String getAccessTypeDescription(Integer accessType) {
        switch (accessType) {
            case 1: return "课程权限";
            case 2: return "章节权限";
            case 3: return "课时权限";
            default: return "未知权限";
        }
    }

    /**
     * 获取获取方式描述
     * 
     * @param acquireMethod 获取方式
     * @return 描述
     */
    public static String getAcquireMethodDescription(Integer acquireMethod) {
        switch (acquireMethod) {
            case 1: return "购买";
            case 2: return "免费";
            case 3: return "积分兑换";
            case 4: return "优惠券兑换";
            case 5: return "管理员赠送";
            case 6: return "推广活动";
            default: return "未知方式";
        }
    }

    /**
     * 获取权限状态描述
     * 
     * @param status 状态
     * @return 描述
     */
    public static String getStatusDescription(Integer status) {
        switch (status) {
            case 0: return "已失效";
            case 1: return "有效";
            case 2: return "已退款";
            default: return "未知状态";
        }
    }
}
