package pox.com.dianfeng.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * OSS URL处理工具类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Component
public class OssUrlUtil {

    @Value("${oss.base-url:https://dianfeng-class.oss-cn-chengdu.aliyuncs.com}")
    private String baseUrl;

    @Value("${oss.default-bucket-name:dianfeng-class}")
    private String defaultBucketName;

    /**
     * 将相对路径转换为完整的OSS访问URL
     * 
     * @param relativePath 相对路径
     * @return 完整的OSS访问URL
     */
    public String buildFullUrl(String relativePath) {
        if (!StringUtils.hasText(relativePath)) {
            return null;
        }
        
        // 如果已经是完整URL，直接返回
        if (relativePath.startsWith("http://") || relativePath.startsWith("https://")) {
            return relativePath;
        }
        
        // 拼接完整URL
        return baseUrl + "/" + relativePath;
    }

    /**
     * 从完整URL中提取相对路径
     * 
     * @param fullUrl 完整URL
     * @return 相对路径
     */
    public String extractRelativePath(String fullUrl) {
        if (!StringUtils.hasText(fullUrl)) {
            return null;
        }
        
        // 如果不是完整URL，直接返回
        if (!fullUrl.startsWith("http://") && !fullUrl.startsWith("https://")) {
            return fullUrl;
        }
        
        try {
            // 提取路径部分
            String path = fullUrl.substring(fullUrl.indexOf("://") + 3);
            int slashIndex = path.indexOf("/");
            if (slashIndex > 0) {
                return path.substring(slashIndex + 1);
            }
        } catch (Exception e) {
            // 解析失败，返回原值
            return fullUrl;
        }
        
        return fullUrl;
    }

    /**
     * 批量处理URL拼接
     * 
     * @param relativePaths 相对路径数组
     * @return 完整URL数组
     */
    public String[] buildFullUrls(String... relativePaths) {
        if (relativePaths == null) {
            return null;
        }
        
        String[] fullUrls = new String[relativePaths.length];
        for (int i = 0; i < relativePaths.length; i++) {
            fullUrls[i] = buildFullUrl(relativePaths[i]);
        }
        
        return fullUrls;
    }

    /**
     * 检查是否为有效的OSS URL
     * 
     * @param url URL地址
     * @return 是否有效
     */
    public boolean isValidOssUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }
        
        return url.contains(defaultBucketName) || 
               url.startsWith(baseUrl) ||
               (!url.startsWith("http://") && !url.startsWith("https://"));
    }

    /**
     * 获取预览基础URL
     * 
     * @return 预览基础URL
     */
    public String getPreviewBaseUrl() {
        return baseUrl;
    }

    /**
     * 获取默认存储桶名称
     * 
     * @return 默认存储桶名称
     */
    public String getDefaultBucketName() {
        return defaultBucketName;
    }
}
