package pox.com.dianfeng.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Redis 健康检查组件
 * 定期检查 Redis 连接状态，及时发现连接问题
 */
@Slf4j
@Component
public class RedisHealthChecker {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String HEALTH_CHECK_KEY = "health:check";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 每30秒执行一次健康检查
     */
    @Scheduled(fixedRate = 30000)
    public void checkRedisHealth() {
        try {
            String timestamp = LocalDateTime.now().format(FORMATTER);
            
            // 执行简单的 Redis 操作来检查连接
            redisTemplate.opsForValue().set(HEALTH_CHECK_KEY, timestamp, 60, java.util.concurrent.TimeUnit.SECONDS);
            String result = redisTemplate.opsForValue().get(HEALTH_CHECK_KEY);
            
            if (timestamp.equals(result)) {
                // log.debug("Redis 连接正常 - {}", timestamp);
            } else {
                log.warn("Redis 连接异常 - 写入和读取数据不一致");
            }
            
        } catch (Exception e) {
            log.error("Redis 连接检查失败: {}", e.getMessage());
            
            // 可以在这里添加告警逻辑，比如发送邮件或短信通知
            handleRedisConnectionFailure(e);
        }
    }

    /**
     * 处理 Redis 连接失败
     */
    private void handleRedisConnectionFailure(Exception e) {
        // 记录详细错误信息
        log.error("Redis 连接失败详情:", e);
        
        // 这里可以添加更多的处理逻辑：
        // 1. 发送告警通知
        // 2. 尝试重新连接
        // 3. 切换到备用缓存策略
        // 4. 记录到监控系统
        
        // 示例：简单的重连尝试
        try {
            Thread.sleep(1000); // 等待1秒后重试
            redisTemplate.opsForValue().set("reconnect:test", "test");
            log.info("Redis 重连成功");
        } catch (Exception retryException) {
            log.error("Redis 重连失败: {}", retryException.getMessage());
        }
    }

    /**
     * 获取 Redis 连接信息
     */
    public String getRedisConnectionInfo() {
        try {
            // 获取 Redis 信息
            return redisTemplate.getConnectionFactory().getConnection().ping();
        } catch (Exception e) {
            return "Redis 连接异常: " + e.getMessage();
        }
    }
}
