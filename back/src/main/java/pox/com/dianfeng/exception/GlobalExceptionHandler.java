package pox.com.dianfeng.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import pox.com.dianfeng.common.R;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统异常，提供友好的错误响应
 * 
 * <AUTHOR>
 * @since 2025-06-06
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理Sa-Token未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public ResponseEntity<R<String>> handleNotLoginException(NotLoginException e) {
        log.warn("用户未登录或token已失效: {}", e.getMessage());

        e.printStackTrace();
        String message = "用户未登录或登录已过期，请重新登录";

        // 根据异常类型提供更具体的错误信息
        switch (e.getType()) {
            case NotLoginException.NOT_TOKEN:
                message = "未提供登录凭证，请先登录";
                break;
            case NotLoginException.INVALID_TOKEN:
                message = "登录凭证无效，请重新登录";
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                message = "登录已过期，请重新登录";
                break;
            case NotLoginException.BE_REPLACED:
                message = "账号在其他地方登录，请重新登录";
                break;
            case NotLoginException.KICK_OUT:
                message = "账号已被强制下线，请重新登录";
                break;
            default:
                message = "登录状态异常，请重新登录";
                break;
        }

        R<String> result = R.error(401, message);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(result);
    }

    /**
     * 处理Sa-Token权限不足异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public ResponseEntity<R<String>> handleNotPermissionException(NotPermissionException e) {
        log.warn("权限不足: {}", e.getMessage());

        R<String> result = R.error(403, "权限不足，无法访问该资源");
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
    }

    /**
     * 处理Sa-Token角色不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    public ResponseEntity<R<String>> handleNotRoleException(NotRoleException e) {
        log.warn("角色权限不足: {}", e.getMessage());

        R<String> result = R.error(403, "角色权限不足，无法访问该资源");
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(result);
    }

    /**
     * 处理JSON反序列化异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<R<String>> handleHttpMessageNotReadable(HttpMessageNotReadableException e) {
        log.error("JSON反序列化异常: {}", e.getMessage(), e);
        
        String message = "请求参数格式错误";
        
        // 检查是否是未识别字段异常
        if (e.getCause() instanceof UnrecognizedPropertyException) {
            UnrecognizedPropertyException upe = (UnrecognizedPropertyException) e.getCause();
            message = String.format("未识别的字段: %s", upe.getPropertyName());
        }
        
        R<String> result = R.error(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<R<String>> handleMethodArgumentNotValid(MethodArgumentNotValidException e) {
        log.error("参数验证异常: {}", e.getMessage(), e);
        
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        R<String> result = R.error("参数验证失败: " + message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<R<String>> handleBindException(BindException e) {
        log.error("参数绑定异常: {}", e.getMessage(), e);
        
        String message = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        R<String> result = R.error("参数绑定失败: " + message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }



    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<R<String>> handleIllegalArgument(IllegalArgumentException e) {
        log.error("非法参数异常: {}", e.getMessage(), e);
        
        R<String> result = R.error("参数错误: " + e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<R<String>> handleNullPointer(NullPointerException e) {
        log.error("空指针异常: {}", e.getMessage(), e);
        
        R<String> result = R.error("系统内部错误，请联系管理员");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<R<String>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        R<String> result = R.error("系统异常: " + e.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<R<String>> handleException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        
        R<String> result = R.error("系统异常，请稍后重试");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
}
