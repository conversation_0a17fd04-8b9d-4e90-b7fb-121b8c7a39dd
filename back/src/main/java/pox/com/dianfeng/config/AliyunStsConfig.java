package pox.com.dianfeng.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云STS配置类
 * 用于生成临时访问凭证，实现前端直传OSS
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.sts")
public class AliyunStsConfig {
    
    /**
     * 阿里云访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * 阿里云访问密钥Secret
     */
    private String accessKeySecret;
    
    /**
     * 地域ID，例如：cn-chengdu
     */
    private String regionId = "cn-chengdu";
    
    /**
     * STS服务端点
     */
    private String endpoint = "sts.cn-chengdu.aliyuncs.com";
    
    /**
     * 角色ARN，用于STS授权
     * 格式：acs:ram::账号ID:role/角色名称
     */
    private String roleArn;
    
    /**
     * 会话名称，用于标识STS会话
     */
    private String roleSessionName = "dianfeng-oss-upload";
    
    /**
     * 临时凭证有效期（秒），最小900秒，最大3600秒
     */
    private Long durationSeconds = 3600L;
    
    /**
     * 创建阿里云STS客户端
     */
    @Bean
    public IAcsClient stsClient() {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        return new DefaultAcsClient(profile);
    }
}
