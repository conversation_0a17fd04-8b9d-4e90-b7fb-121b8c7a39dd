package pox.com.dianfeng.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token的路由拦截器
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 登录认证 - 拦截所有管理后台接口
            SaRouter.match("/**")
                    // 排除登录接口、验证码接口和Swagger文档接口
                    .notMatch("/admins/login", "/admins/captcha")
                    .notMatch("/swagger-ui/**", "/swagger-resources/**", "/v2/api-docs/**", "/webjars/**")
                    .notMatch("/doc.html", "/favicon.ico")
                    // 排除文件上传相关接口（用于测试）
                    .notMatch("/file/**", "/upload-demo.html")
                    // 排除STS临时凭证接口（用于OSS直传）
                    .notMatch("/sts/**")
                    // 排除OSS PostObject签名接口（用于OSS直传）
                    .notMatch("/oss/**")
                    // 检查是否登录
                    .check(r -> StpUtil.checkLogin());
        })).addPathPatterns("/**");
    }
}