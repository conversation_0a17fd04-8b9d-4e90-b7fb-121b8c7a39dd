package pox.com.dianfeng.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 阿里云OSS PostObject配置类
 * 用于配置PostObject签名相关参数
 * 
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@Component
@ConfigurationProperties(prefix = "aliyun.oss.post-object")
public class AliyunOssPostObjectConfig {
    
    /**
     * 签名有效期（秒）
     * 默认30分钟
     */
    private Long expireSeconds = 1800L;
    
    /**
     * 允许的最大文件大小（字节）
     * 默认5000MB
     */
    private Long maxFileSize = 5242880000L;
    
    /**
     * 允许的文件类型列表
     */
    private List<String> allowedContentTypes;
    
    /**
     * 获取文件大小限制（MB）
     */
    public double getMaxFileSizeMB() {
        return maxFileSize / 1024.0 / 1024.0;
    }
    
    /**
     * 检查文件类型是否允许
     */
    public boolean isContentTypeAllowed(String contentType) {
        if (allowedContentTypes == null || allowedContentTypes.isEmpty()) {
            return true;
        }
        return allowedContentTypes.contains(contentType);
    }
}
