package pox.com.dianfeng.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import pox.com.dianfeng.entity.UserCourseAccess;

import java.util.List;

/**
 * <p>
 * 用户课程权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Mapper
public interface UserCourseAccessMapper extends BaseMapper<UserCourseAccess> {

    /**
     * 检查用户是否有课程访问权限
     * 优先级：买断 > 课程权限 > 章节权限 > 课时权限
     */
    @Select({
        "<script>",
        "SELECT * FROM user_course_access",
        "WHERE user_id = #{userId}",
        "AND course_id = #{courseId}",
        "AND is_del = 0",
        "AND is_active = 1",
        "AND status = 1",
        "AND (expire_time IS NULL OR expire_time > NOW())",
        "AND (",
            // 买断权限（优先级最高）
            "is_buyout = 1",
            // 或者课程权限
            "OR (access_type = 1 AND chapter_id IS NULL AND lesson_id IS NULL)",
            // 或者指定章节权限
            "<if test='chapterId != null'>",
                "OR (access_type = 2 AND chapter_id = #{chapterId} AND lesson_id IS NULL)",
            "</if>",
            // 或者指定课时权限
            "<if test='lessonId != null'>",
                "OR (access_type = 3 AND lesson_id = #{lessonId})",
            "</if>",
        ")",
        "ORDER BY is_buyout DESC, access_type ASC, created_at DESC",
        "LIMIT 1",
        "</script>"
    })
    UserCourseAccess checkUserAccess(@Param("userId") Integer userId, 
                                   @Param("courseId") Integer courseId,
                                   @Param("chapterId") Integer chapterId,
                                   @Param("lessonId") Integer lessonId);

    /**
     * 获取用户的所有有效权限
     */
    @Select({
        "SELECT * FROM user_course_access",
        "WHERE user_id = #{userId}",
        "AND is_del = 0",
        "AND is_active = 1", 
        "AND status = 1",
        "AND (expire_time IS NULL OR expire_time > NOW())",
        "ORDER BY course_id, is_buyout DESC, access_type ASC"
    })
    List<UserCourseAccess> getUserValidAccess(@Param("userId") Integer userId);

    /**
     * 获取用户某个课程的所有权限
     */
    @Select({
        "SELECT * FROM user_course_access",
        "WHERE user_id = #{userId}",
        "AND course_id = #{courseId}",
        "AND is_del = 0",
        "ORDER BY is_buyout DESC, access_type ASC, created_at DESC"
    })
    List<UserCourseAccess> getUserCourseAccess(@Param("userId") Integer userId, 
                                             @Param("courseId") Integer courseId);

} 