package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("users")
@ApiModel(value = "Users对象", description = "用户信息表")
public class Users extends Model<Users> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID，主键
     */
    @ApiModelProperty("用户ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名，登录账号
     */
    @TableField("username")
    @ApiModelProperty("用户名，登录账号")
    private String username;

    /**
     * 密码，加密存储
     */
    @TableField("password")
    @ApiModelProperty("密码，加密存储")
    private String password;

    /**
     * 用户头像URL（相对路径）
     */
    @TableField(exist = false)
    @ApiModelProperty("搜索关键字")
    private String searchKeyWord;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    @ApiModelProperty("用户昵称")
    private String nickname;

    /**
     * 用户头像URL（相对路径）
     */
    @TableField("avatar")
    @ApiModelProperty("用户头像URL（相对路径）")
    private String avatar;

    /**
     * 用户头像完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("用户头像完整URL")
    private String avatarFullUrl;

    /**
     * 手机号码
     */
    @TableField("phone")
    @ApiModelProperty("手机号码")
    private String phone;

    /**
     * 电子邮箱
     */
    @TableField("email")
    @ApiModelProperty("电子邮箱")
    private String email;

    /**
     * 性别：0-未知，1-男，2-女
     */
    @TableField("gender")
    @ApiModelProperty("性别：0-未知，1-男，2-女")
    private int gender;

    /**
     * 年龄
     */
    @TableField("age")
    @ApiModelProperty("年龄")
    private Integer age;

    /**
     * 个人简介
     */
    @TableField("bio")
    @ApiModelProperty("个人简介")
    private String bio;

    /**
     * 年级
     */
    @TableField("grade")
    @ApiModelProperty("年级")
    private Integer grade;

    /**
     * 学校名称
     */
    @TableField("school")
    @ApiModelProperty("学校名称")
    private String school;

    /**
     * 用户所在地
     */
    @TableField("location")
    @ApiModelProperty("用户所在地")
    private String location;

    /**
     * 账号状态：0-禁用，1-正常
     */
    @TableField("status")
    @ApiModelProperty("账号状态：0-禁用，1-正常")
    private Boolean status;

    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
