package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户学习记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("user_learning_records")
@ApiModel(value = "UserLearningRecords对象", description = "用户学习记录表")
public class UserLearningRecords extends Model<UserLearningRecords> {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID，主键
     */
    @ApiModelProperty("记录ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("用户ID")
    private Integer userId;

    /**
     * 课程ID
     */
    @TableField("course_id")
    @ApiModelProperty("课程ID")
    private Integer courseId;

    /**
     * 课时ID
     */
    @TableField("lesson_id")
    @ApiModelProperty("课时ID")
    private Integer lessonId;

    /**
     * 学习进度（秒）
     */
    @TableField("progress")
    @ApiModelProperty("学习进度（秒）")
    private Integer progress;

    /**
     * 课时总时长（秒）
     */
    @TableField("duration")
    @ApiModelProperty("课时总时长（秒）")
    private Integer duration;

    /**
     * 完成比例，0-100%
     */
    @TableField("progress_rate")
    @ApiModelProperty("完成比例，0-100%")
    private BigDecimal progressRate;

    /**
     * 是否完成：0-未完成，1-已完成
     */
    @TableField("is_completed")
    @ApiModelProperty("是否完成：0-未完成，1-已完成")
    private Boolean isCompleted;

    /**
     * 最后学习时间
     */
    @ApiModelProperty("最后学习时间")
    @TableField("last_learn_time")
    private LocalDateTime lastLearnTime;

    /**
     * 学习时长（秒）
     */
    @ApiModelProperty("学习时长（秒）")
    @TableField("learn_duration")
    private Integer learnDuration;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
