package pox.com.dianfeng.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pox.com.dianfeng.entity.UserCourseAccess;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程访问权限响应DTO
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "课程访问权限响应DTO")
public class CourseAccessResponseDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("是否有访问权限")
    private Boolean hasAccess;
    
    @ApiModelProperty("权限类型：1-课程，2-章节，3-课时")
    private Integer accessType;
    
    @ApiModelProperty("获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动")
    private Integer acquireMethod;
    
    @ApiModelProperty("是否买断权限")
    private Boolean isBuyout;
    
    @ApiModelProperty("是否永久有效")
    private Boolean isPermanent;
    
    @ApiModelProperty("过期时间")
    private LocalDateTime expireTime;
    
    @ApiModelProperty("剩余有效天数")
    private Long remainingDays;
    
    @ApiModelProperty("权限描述")
    private String accessDescription;
    
    @ApiModelProperty("权限详情")
    private UserCourseAccess accessDetail;
    
    @ApiModelProperty("限制原因（无权限时）")
    private String restrictReason;
    
    @ApiModelProperty("相关权限列表")
    private List<UserCourseAccess> relatedAccess;
    
    /**
     * 访问统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "用户权限统计信息")
    public static class AccessStatistics implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty("总权限数量")
        private Long totalAccess;
        
        @ApiModelProperty("有效权限数量")
        private Long validAccess;
        
        @ApiModelProperty("已过期权限数量")
        private Long expiredAccess;
        
        @ApiModelProperty("买断权限数量")
        private Long buyoutAccess;
        
        @ApiModelProperty("即将过期权限数量（7天内）")
        private Long expiringAccess;
        
        @ApiModelProperty("免费权限数量")
        private Long freeAccess;
        
        @ApiModelProperty("购买权限数量")
        private Long purchasedAccess;
        
        @ApiModelProperty("总课程数量")
        private Long totalCourses;
        
        @ApiModelProperty("总章节数量")
        private Long totalChapters;
        
        @ApiModelProperty("总课时数量")
        private Long totalLessons;
    }
    
    /**
     * 权限级别枚举
     */
    public enum AccessLevel {
        NONE("无权限"),
        LESSON("课时权限"),
        CHAPTER("章节权限"),
        COURSE("课程权限"),
        BUYOUT("买断权限");
        
        private final String description;
        
        AccessLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建无权限响应
     */
    public static CourseAccessResponseDTO noAccess(String reason) {
        return CourseAccessResponseDTO.builder()
                .hasAccess(false)
                .restrictReason(reason)
                .accessDescription("无访问权限")
                .build();
    }
    
    /**
     * 创建有权限响应
     */
    public static CourseAccessResponseDTO hasAccess(UserCourseAccess access) {
        CourseAccessResponseDTOBuilder builder = CourseAccessResponseDTO.builder()
                .hasAccess(true)
                .accessType(access.getAccessType())
                .acquireMethod(access.getAcquireMethod())
                .isBuyout(access.getIsBuyout())
                .expireTime(access.getExpireTime())
                .accessDetail(access);
        
        // 判断是否永久有效
        boolean isPermanent = access.getExpireTime() == null;
        builder.isPermanent(isPermanent);
        
        // 计算剩余天数
        if (!isPermanent) {
            long remainingDays = java.time.Duration.between(LocalDateTime.now(), access.getExpireTime()).toDays();
            builder.remainingDays(Math.max(0, remainingDays));
        }
        
        // 生成权限描述
        String description = generateAccessDescription(access);
        builder.accessDescription(description);
        
        return builder.build();
    }
    
    /**
     * 生成权限描述
     */
    private static String generateAccessDescription(UserCourseAccess access) {
        StringBuilder desc = new StringBuilder();
        
        // 权限级别
        if (access.getIsBuyout()) {
            desc.append("买断权限");
        } else {
            switch (access.getAccessType()) {
                case 1:
                    desc.append("课程权限");
                    break;
                case 2:
                    desc.append("章节权限");
                    break;
                case 3:
                    desc.append("课时权限");
                    break;
                default:
                    desc.append("未知权限");
            }
        }
        
        // 获取方式
        switch (access.getAcquireMethod()) {
            case 1:
                desc.append("（购买获得）");
                break;
            case 2:
                desc.append("（免费获得）");
                break;
            case 3:
                desc.append("（积分兑换）");
                break;
            case 4:
                desc.append("（优惠券兑换）");
                break;
            case 5:
                desc.append("（管理员赠送）");
                break;
            case 6:
                desc.append("（推广活动）");
                break;
        }
        
        // 有效期
        if (access.getExpireTime() == null) {
            desc.append(" - 永久有效");
        } else {
            desc.append(" - 有效期至 ").append(access.getExpireTime().toString());
        }
        
        return desc.toString();
    }
} 