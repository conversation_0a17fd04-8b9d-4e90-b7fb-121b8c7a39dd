package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 讲师信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("teachers")
@ApiModel(value = "Teachers对象", description = "讲师信息表")
public class Teachers extends Model<Teachers> {

    private static final long serialVersionUID = 1L;

    /**
     * 讲师ID，主键
     */
    @ApiModelProperty("讲师ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 讲师姓名
     */
    @TableField("name")
    @ApiModelProperty("讲师姓名")
    private String name;

    /**
     * 讲师头像URL（相对路径）
     */
    @TableField("avatar")
    @ApiModelProperty("讲师头像URL（相对路径）")
    private String avatar;

    /**
     * 讲师头像完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("讲师头像完整URL")
    private String avatarFullUrl;

    /**
     * 讲师职称/头衔
     */
    @TableField("title")
    @ApiModelProperty("讲师职称/头衔")
    private String title;

    /**
     * 讲师简介
     */
    @ApiModelProperty("讲师简介")
    @TableField("introduction")
    private String introduction;

    /**
     * 教学经验
     */
    @ApiModelProperty("教学经验")
    @TableField("experience")
    private String experience;

    /**
     * 讲师所在地
     */
    @TableField("location")
    @ApiModelProperty("讲师所在地")
    private String location;

    /**
     * 专业领域
     */
    @TableField("expertise")
    @ApiModelProperty("专业领域")
    private String expertise;

    /**
     * 讲师评分
     */
    @TableField("rating")
    @ApiModelProperty("讲师评分")
    private BigDecimal rating;

    /**
     * 学生总数
     */
    @ApiModelProperty("学生总数")
    @TableField("student_count")
    private Integer studentCount;

    /**
     * 课程总数
     */
    @ApiModelProperty("课程总数")
    @TableField("course_count")
    private Integer courseCount;

    /**
     * 状态：0-禁用，1-正常
     */
    @TableField("status")
    @ApiModelProperty("状态：0-禁用，1-正常")
    private int status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
