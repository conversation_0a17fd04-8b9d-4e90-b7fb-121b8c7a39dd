package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 首页推荐表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Getter
@Setter
@ToString
@TableName("homepage_recommendations")
@ApiModel(value = "HomepageRecommendations对象", description = "首页推荐表")
public class HomepageRecommendations extends Model<HomepageRecommendations> {

    private static final long serialVersionUID = 1L;

    /**
     * 推荐ID，主键
     */
    @ApiModelProperty("推荐ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 推荐标题
     */
    @TableField("title")
    @ApiModelProperty("推荐标题")
    private String title;

    /**
     * 推荐副标题
     */
    @TableField("subtitle")
    @ApiModelProperty("推荐副标题")
    private String subtitle;

    /**
     * 推荐描述
     */
    @TableField("description")
    @ApiModelProperty("推荐描述")
    private String description;

    /**
     * 封面图片URL（相对路径）
     */
    @TableField("cover_image")
    @ApiModelProperty("封面图片URL（相对路径）")
    private String coverImage;

    /**
     * 封面图片完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("封面图片完整URL")
    private String coverImageFullUrl;

    /**
     * 链接类型：1-课程，2-直播，3-外部链接
     */
    @TableField("link_type")
    @ApiModelProperty("链接类型：1-课程，2-直播，3-外部链接")
    private Integer linkType;

    /**
     * 关联目标ID（课程ID或直播ID）
     */
    @TableField("link_target_id")
    @ApiModelProperty("关联目标ID（课程ID或直播ID）")
    private Integer linkTargetId;

    /**
     * 跳转链接URL
     */
    @TableField("link_url")
    @ApiModelProperty("跳转链接URL")
    private String linkUrl;

    /**
     * 排序值，越小越靠前
     */
    @TableField("sort_order")
    @ApiModelProperty("排序值，越小越靠前")
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    @ApiModelProperty("状态：0-禁用，1-启用")
    private Boolean status;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 关联课程标题（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("关联课程标题")
    private String courseTitle;

    /**
     * 关联直播标题（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("关联直播标题")
    private String liveTitle;

    /**
     * 链接类型标签（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("链接类型标签")
    private String linkTypeLabel;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
