package pox.com.dianfeng.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import pox.com.dianfeng.entity.Courses;
import pox.com.dianfeng.entity.Teachers;

import java.io.Serializable;
import java.util.List;

/**
 * 课程与讲师关联查询DTO
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseWithTeacherDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 课程信息
     */
    private Courses course;
    
    /**
     * 讲师信息
     */
    private Teachers teacher;
    

    
    /**
     * 讲师姓名（冗余字段，便于前端使用）
     */
    private String teacherName;
    
    /**
     * 讲师头像（冗余字段，便于前端使用）
     */
    private String teacherAvatar;
    
    /**
     * 分类名称（冗余字段，便于前端使用）
     */
    private String categoryName;
    
    /**
     * 难度级别标签（冗余字段，便于前端使用）
     */
    private String levelLabel;
    
    /**
     * 年龄段标签（冗余字段，便于前端使用）
     */
    private String ageGroupLabel;

    /**
     * 课程章节列表（包含课时信息）
     */
    private List<ChapterWithLessonsDTO> chapters;
}
