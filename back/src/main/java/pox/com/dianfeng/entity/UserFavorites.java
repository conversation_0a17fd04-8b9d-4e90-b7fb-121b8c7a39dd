package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户收藏表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("user_favorites")
@ApiModel(value = "UserFavorites对象", description = "用户收藏表")
public class UserFavorites extends Model<UserFavorites> {

    private static final long serialVersionUID = 1L;

    /**
     * 收藏ID，主键
     */
    @ApiModelProperty("收藏ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("用户ID")
    private Integer userId;

    /**
     * 课程ID
     */
    @TableField("course_id")
    @ApiModelProperty("课程ID")
    private Integer courseId;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
