package pox.com.dianfeng.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程访问权限请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "课程访问权限请求DTO")
public class CourseAccessRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("用户ID列表（批量授权时使用）")
    private List<Integer> userIds;
    
    @ApiModelProperty("用户ID（单个授权时使用）")
    private Integer userId;
    
    @ApiModelProperty("课程ID")
    private Integer courseId;
    
    @ApiModelProperty("章节ID（课程权限时为null）")
    private Integer chapterId;
    
    @ApiModelProperty("课时ID（课程/章节权限时为null）")
    private Integer lessonId;
    
    @ApiModelProperty("权限类型：1-课程，2-章节，3-课时")
    private Integer accessType;
    
    @ApiModelProperty("获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动")
    private Integer acquireMethod;
    
    @ApiModelProperty("是否买断：0-否，1-是")
    private Boolean isBuyout;
    
    @ApiModelProperty("实际支付金额")
    private BigDecimal pricePaid;
    
    @ApiModelProperty("原价")
    private BigDecimal originalPrice;
    
    @ApiModelProperty("支付方式")
    private String paymentMethod;
    
    @ApiModelProperty("订单ID")
    private String orderId;
    
    @ApiModelProperty("优惠券ID")
    private Integer couponId;
    
    @ApiModelProperty("使用的积分数量")
    private Integer pointsUsed;
    
    @ApiModelProperty("过期时间")
    private LocalDateTime expireTime;
    
    @ApiModelProperty("操作管理员ID")
    private Integer adminId;
    
    @ApiModelProperty("备注信息")
    private String remark;
    
    @ApiModelProperty("是否激活")
    private Boolean isActive;
} 