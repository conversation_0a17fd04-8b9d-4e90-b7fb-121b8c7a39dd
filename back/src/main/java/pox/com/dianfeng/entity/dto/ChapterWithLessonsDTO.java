package pox.com.dianfeng.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pox.com.dianfeng.entity.CourseChapters;
import pox.com.dianfeng.entity.CourseLessons;

import java.io.Serializable;
import java.util.List;

/**
 * 章节与课时关联查询DTO
 * 
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterWithLessonsDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 章节ID，主键
     */
    private Integer id;
    
    /**
     * 课程ID
     */
    private Integer courseId;
    
    /**
     * 章节标题
     */
    private String title;
    
    /**
     * 章节描述
     */
    private String description;
    
    /**
     * 排序值，越小越靠前
     */
    private Integer sortOrder;
    
    /**
     * 章节下的课时列表
     */
    private List<CourseLessons> lessons;
    
    /**
     * 从CourseChapters实体创建DTO
     */
    public static ChapterWithLessonsDTO fromEntity(CourseChapters chapter) {
        return ChapterWithLessonsDTO.builder()
                .id(chapter.getId())
                .courseId(chapter.getCourseId())
                .title(chapter.getTitle())
                .description(chapter.getDescription())
                .sortOrder(chapter.getSortOrder())
                .build();
    }
}
