package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("courses")
@ApiModel(value = "Courses对象", description = "课程信息表")
public class Courses extends Model<Courses> {

    private static final long serialVersionUID = 1L;

    /**
     * 课程ID，主键
     */
    @ApiModelProperty("课程ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 课程标题
     */
    @TableField("title")
    @ApiModelProperty("课程标题")
    private String title;

    /**
     * 课程副标题
     */
    @TableField("subtitle")
    @ApiModelProperty("课程副标题")
    private String subtitle;

    /**
     * 课程描述
     */
    @ApiModelProperty("课程描述")
    @TableField("description")
    private String description;

    /**
     * 课程封面图片URL（相对路径）
     */
    @TableField("cover_image")
    @ApiModelProperty("课程封面图片URL（相对路径）")
    private String coverImage;

    /**
     * 课程封面图片完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("课程封面图片完整URL")
    private String coverImageFullUrl;

    /**
     * 讲师ID
     */
    @ApiModelProperty("讲师ID")
    @TableField("teacher_id")
    private Integer teacherId;

    /**
     * 分类ID
     */
    @ApiModelProperty("分类ID")
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 课程价格
     */
    @TableField("price")
    @ApiModelProperty("课程价格")
    private BigDecimal price;

    /**
     * 课程原价
     */
    @ApiModelProperty("课程原价")
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 课程难度：1-入门，2-初级，3-中级，4-高级
     */
    @TableField("level")
    @ApiModelProperty("课程难度：1-入门，2-初级，3-中级，4-高级")
    private Integer level;

    /**
     * 适合年龄段：1-青少年，2-大学生，3-成人，0-不限
     */
    @TableField("age_group")
    @ApiModelProperty("适合年龄段：1-青少年，2-大学生，3-成人，0-不限")
    private Integer ageGroup;

    /**
     * 课程总时长（分钟）
     */
    @TableField("duration")
    @ApiModelProperty("课程总时长（分钟）")
    private Integer duration;

    /**
     * 课时总数
     */
    @ApiModelProperty("课时总数")
    @TableField("lesson_count")
    private Integer lessonCount;

    /**
     * 学习人数
     */
    @ApiModelProperty("学习人数")
    @TableField("student_count")
    private Integer studentCount;

    /**
     * 课程评分
     */
    @TableField("rating")
    @ApiModelProperty("课程评分")
    private BigDecimal rating;

    /**
     * 评价数量
     */
    @ApiModelProperty("评价数量")
    @TableField("review_count")
    private Integer reviewCount;

    /**
     * 是否直播课：0-录播，1-直播
     */
    @TableField("is_live")
    @ApiModelProperty("是否直播课：0-录播，1-直播")
    private Boolean isLive;

    /**
     * 是否推荐课程：0-否，1-是
     */
    @TableField("is_featured")
    @ApiModelProperty("是否推荐课程：0-否，1-是")
    private Boolean isFeatured;

    /**
     * 是否特训营：0-否，1-是
     */
    @ApiModelProperty("是否特训营：0-否，1-是")
    @TableField("is_special_training")
    private Boolean isSpecialTraining;

    /**
     * 是否一对一：0-否，1-是
     */
    @TableField("is_one_on_one")
    @ApiModelProperty("是否一对一：0-否，1-是")
    private Boolean isOneOnOne;

    /**
     * 状态：0-下架，1-上架
     */
    @TableField("status")
    @ApiModelProperty("状态：0-下架，1-上架")
    private Boolean status;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 联系信息-手机号
     */
    @ApiModelProperty("联系信息-手机号")
    @TableField("contact_info_phone")
    private String contactInfoPhone;

    /**
     * 联系信息-微信
     */
    @ApiModelProperty("联系信息-微信")
    @TableField("contact_info_wechat")
    private String contactInfoWechat;

    /**
     * 联系信息-备注
     */
    @ApiModelProperty("联系信息-备注")
    @TableField("contact_info_remark")
    private String contactInfoRemark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
