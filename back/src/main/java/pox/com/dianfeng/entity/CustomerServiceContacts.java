package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客服联系信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("customer_service_contacts")
@ApiModel(value = "CustomerServiceContacts对象", description = "客服联系信息表")
public class CustomerServiceContacts extends Model<CustomerServiceContacts> {

    private static final long serialVersionUID = 1L;

    /**
     * 客服ID，主键
     */
    @ApiModelProperty("客服ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 客服姓名
     */
    @TableField("name")
    @ApiModelProperty("客服姓名")
    private String name;

    /**
     * 客服头像URL（相对路径）
     */
    @TableField("avatar")
    @ApiModelProperty("客服头像URL（相对路径）")
    private String avatar;

    /**
     * 客服头像完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("客服头像完整URL")
    private String avatarFullUrl;

    /**
     * 客服手机号
     */
    @TableField("phone")
    @ApiModelProperty("客服手机号")
    private String phone;

    /**
     * 客服微信
     */
    @TableField("wechat")
    @ApiModelProperty("客服微信")
    private String wechat;

    /**
     * 客服邮箱
     */
    @TableField("email")
    @ApiModelProperty("客服邮箱")
    private String email;

    /**
     * 客服QQ
     */
    @TableField("qq")
    @ApiModelProperty("客服QQ")
    private String qq;

    /**
     * 备注信息
     */
    @TableField("remark")
    @ApiModelProperty("备注信息")
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 状态：0-禁用，1-正常
     */
    @TableField("status")
    @ApiModelProperty("状态：0-禁用，1-正常")
    private Boolean status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;



    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
