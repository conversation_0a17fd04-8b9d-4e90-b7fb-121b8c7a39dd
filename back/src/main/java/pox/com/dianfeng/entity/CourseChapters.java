package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程章节表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("course_chapters")
@ApiModel(value = "CourseChapters对象", description = "课程章节表")
public class CourseChapters extends Model<CourseChapters> {

    private static final long serialVersionUID = 1L;

    /**
     * 章节ID，主键
     */
    @ApiModelProperty("章节ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 课程ID
     */
    @TableField("course_id")
    @ApiModelProperty("课程ID")
    private Integer courseId;

    /**
     * 章节标题
     */
    @TableField("title")
    @ApiModelProperty("章节标题")
    private String title;

    /**
     * 章节描述
     */
    @ApiModelProperty("章节描述")
    @TableField("description")
    private String description;

    /**
     * 排序值，越小越靠前
     */
    @TableField("sort_order")
    @ApiModelProperty("排序值，越小越靠前")
    private Integer sortOrder;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
