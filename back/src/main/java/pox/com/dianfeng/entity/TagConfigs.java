package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@ToString
@TableName("tag_configs")
@ApiModel(value = "TagConfigs对象", description = "标签配置表")
public class TagConfigs extends Model<TagConfigs> {

    private static final long serialVersionUID = 1L;

    /**
     * 标签配置ID，主键
     */
    @ApiModelProperty("标签配置ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 标签分类：level-难度级别, age_group-年龄段
     */
    @TableField("category")
    @ApiModelProperty("标签分类：level-难度级别, age_group-年龄段")
    private String category;

    /**
     * 标签显示名称
     */
    @TableField("label")
    @ApiModelProperty("标签显示名称")
    private String label;

    /**
     * 标签值 整数型
     */
    @TableField("value")
    @ApiModelProperty("标签值")
    private Integer value;

    /**
     * 排序值，越小越靠前
     */
    @TableField("sort_order")
    @ApiModelProperty("排序值，越小越靠前")
    private Integer sortOrder;

    /**
     * 是否系统内置：0-否，1-是
     */
    @TableField("is_system")
    @ApiModelProperty("是否系统内置：0-否，1-是")
    private Boolean isSystem;

    /**
     * 状态：0-禁用，1-正常
     */
    @TableField("status")
    @ApiModelProperty("状态：0-禁用，1-正常")
    private Boolean status;

    /**
     * 备注信息
     */
    @TableField("remark")
    @ApiModelProperty("备注信息")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
} 