package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 直播通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("live_courses")
@ApiModel(value = "LiveCourses对象", description = "直播通知表")
public class LiveCourses extends Model<LiveCourses> {

    private static final long serialVersionUID = 1L;

    /**
     * 直播ID，主键
     */
    @ApiModelProperty("直播ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 直播标题
     */
    @TableField("title")
    @ApiModelProperty("直播标题")
    private String title;

    /**
     * 直播描述
     */
    @ApiModelProperty("直播描述")
    @TableField("description")
    private String description;

    /**
     * 直播封面图片URL（相对路径）
     */
    @TableField("cover_image")
    @ApiModelProperty("直播封面图片URL（相对路径）")
    private String coverImage;

    /**
     * 直播封面图片完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("直播封面图片完整URL")
    private String coverImageFullUrl;

    /**
     * 讲师ID
     */
    @ApiModelProperty("讲师ID")
    @TableField("teacher_id")
    private Integer teacherId;

    /**
     * 分类ID
     */
    @ApiModelProperty("分类ID")
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 直播价格
     */
    @ApiModelProperty("直播价格")
    @TableField("price")
    private BigDecimal price;

    /**
     * 直播间ID
     */
    @ApiModelProperty("直播间ID")
    @TableField("room_id")
    private String roomId;

    /**
     * 直播间密码
     */
    @ApiModelProperty("直播间密码")
    @TableField("room_password")
    private String roomPassword;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 直播地址
     */
    @TableField("live_url")
    @ApiModelProperty("直播地址")
    private String liveUrl;


    /**
     * 回放视频完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("回放视频完整URL")
    private String replayVideoFullUrl;

    /**
     * 讲师姓名标签（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("讲师姓名标签")
    private String teacherLabel;

    /**
     * 分类名称标签（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("分类名称标签")
    private String categoryLabel;

    /**
     * 状态：0-未开始，1-直播中，2-已结束
     */
    @TableField("status")
    @ApiModelProperty("状态：0-未开始，1-直播中，2-已结束")
    private Integer status;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
