package pox.com.dianfeng.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * STS临时访问凭证响应
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "STS临时访问凭证响应")
public class StsTokenResponse {
    
    @ApiModelProperty(value = "临时访问密钥ID", required = true)
    private String accessKeyId;
    
    @ApiModelProperty(value = "临时访问密钥Secret", required = true)
    private String accessKeySecret;
    
    @ApiModelProperty(value = "安全令牌", required = true)
    private String securityToken;
    
    @ApiModelProperty(value = "凭证过期时间（ISO 8601格式）", required = true)
    private String expiration;
    
    @ApiModelProperty(value = "OSS存储桶名称", required = true)
    private String bucketName;
    
    @ApiModelProperty(value = "OSS端点地址", required = true)
    private String endpoint;
    
    @ApiModelProperty(value = "OSS地域", required = true)
    private String region;
    
    @ApiModelProperty(value = "允许上传的路径前缀", required = true)
    private String pathPrefix;
    
    @ApiModelProperty(value = "最大文件大小（字节）", required = true)
    private Long maxFileSize;
    
    @ApiModelProperty(value = "允许的文件类型", required = true)
    private String[] allowedFileTypes;
    
    @ApiModelProperty(value = "凭证有效期（秒）", required = true)
    private Long durationSeconds;
}
