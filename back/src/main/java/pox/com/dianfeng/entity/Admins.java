package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 管理员账户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("admins")
public class Admins extends Model<Admins> {

    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID，主键
     */
    @ApiModelProperty("管理员ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 管理员账号
     */
    @TableField("username")
    @ApiModelProperty("管理员账号")
    private String username;

    /**
     * 密码，加密存储
     */
    @TableField("password")
    @ApiModelProperty("密码，加密存储")
    private String password;

    /**
     * 管理员真实姓名
     */
    @TableField("real_name")
    @ApiModelProperty("管理员真实姓名")
    private String realName;

    /**
     * 头像URL
     */
    @TableField("avatar")
    @ApiModelProperty("头像URL")
    private String avatar;

    /**
     * 角色：1-超级管理员，2-课程管理员，3-运营管理员
     */
    @TableField("role")
    @ApiModelProperty("角色：1-超级管理员，2-课程管理员，3-运营管理员")
    private int role;

    /**
     * 手机号码
     */
    @TableField("phone")
    @ApiModelProperty("手机号码")
    private String phone;

    /**
     * 电子邮箱
     */
    @TableField("email")
    @ApiModelProperty("电子邮箱")
    private String email;

    /**
     * 权限列表，JSON格式存储
     */
    @TableField("permissions")
    @ApiModelProperty("权限列表，JSON格式存储")
    private String permissions;

    /**
     * 最后登录IP
     */
    @ApiModelProperty("最后登录IP")
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 最后登录时间
     */
    @ApiModelProperty("最后登录时间")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 登录次数
     */
    @ApiModelProperty("登录次数")
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 备注信息
     */
    @TableField("remark")
    @ApiModelProperty("备注信息")
    private String remark;

    /**
     * 状态：0-禁用，1-正常
     */
    @TableField("status")
    @ApiModelProperty("状态：0-禁用，1-正常")
    private Boolean status;

    /**
     * 创建者ID
     */
    @TableField("created_by")
    @ApiModelProperty("创建者ID")
    private Integer createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
