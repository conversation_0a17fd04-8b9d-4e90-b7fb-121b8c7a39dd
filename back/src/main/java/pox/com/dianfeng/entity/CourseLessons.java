package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课时信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("course_lessons")
@ApiModel(value = "CourseLessons对象", description = "课时信息表")
public class CourseLessons extends Model<CourseLessons> {

    private static final long serialVersionUID = 1L;

    /**
     * 课时ID，主键
     */
    @ApiModelProperty("课时ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 课程ID
     */
    @TableField("course_id")
    @ApiModelProperty("课程ID")
    private Integer courseId;

    /**
     * 章节ID
     */
    @ApiModelProperty("章节ID")
    @TableField("chapter_id")
    private Integer chapterId;

    /**
     * 课时标题
     */
    @TableField("title")
    @ApiModelProperty("课时标题")
    private String title;

    /**
     * 课时描述
     */
    @ApiModelProperty("课时描述")
    @TableField("description")
    private String description;

    /**
     * 视频URL（相对路径）
     */
    @TableField("video_url")
    @ApiModelProperty("视频URL（相对路径）")
    private String videoUrl;

    /**
     * 视频完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("视频完整URL")
    private String videoUrlFullUrl;

    /**
     * 视频时长（秒）
     */
    @TableField("duration")
    @ApiModelProperty("视频时长（秒）")
    private Integer duration;

    /**
     * 是否免费：0-否，1-是
     */
    @TableField("is_free")
    @ApiModelProperty("是否免费：0-否，1-是")
    private Boolean isFree;

    /**
     * 排序值，越小越靠前
     */
    @TableField("sort_order")
    @ApiModelProperty("排序值，越小越靠前")
    private Integer sortOrder;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
