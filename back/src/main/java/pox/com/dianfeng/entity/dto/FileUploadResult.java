package pox.com.dianfeng.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传结果DTO
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadResult {
    
    /**
     * 文件名称
     */
    private String fileName;
    
    /**
     * 文件原始名称
     */
    private String originalFileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型
     */
    private String contentType;
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 文件访问URL
     */
    private String fileUrl;
    
    /**
     * 文件预览URL（带过期时间）
     */
    private String previewUrl;
    
    /**
     * 上传时间戳
     */
    private Long uploadTime;
    
    /**
     * 文件分类（avatar/cover/video/audio/document等）
     */
    private String category;
}
