package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户课程权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Getter
@Setter
@ToString
@TableName("user_course_access")
@ApiModel(value = "UserCourseAccess对象", description = "用户课程权限表")
public class UserCourseAccess extends Model<UserCourseAccess> {

    private static final long serialVersionUID = 1L;

    /**
     * 权限记录ID，主键
     */
    @ApiModelProperty("权限记录ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("用户ID")
    private Integer userId;

    /**
     * 课程ID
     */
    @TableField("course_id")
    @ApiModelProperty("课程ID")
    private Integer courseId;

    /**
     * 章节ID，null表示整个课程权限
     */
    @TableField("chapter_id")
    @ApiModelProperty("章节ID，null表示整个课程权限")
    private Integer chapterId;

    /**
     * 课时ID，null表示章节权限
     */
    @TableField("lesson_id")
    @ApiModelProperty("课时ID，null表示章节权限")
    private Integer lessonId;

    /**
     * 权限类型：1-课程，2-章节，3-课时
     */
    @TableField("access_type")
    @ApiModelProperty("权限类型：1-课程，2-章节，3-课时")
    private Integer accessType;

    /**
     * 获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动
     */
    @TableField("acquire_method")
    @ApiModelProperty("获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动")
    private Integer acquireMethod;

    /**
     * 是否买断：0-否，1-是（买断权限优先级最高）
     */
    @TableField("is_buyout")
    @ApiModelProperty("是否买断：0-否，1-是（买断权限优先级最高）")
    private Boolean isBuyout;

    /**
     * 实际支付金额
     */
    @TableField("price_paid")
    @ApiModelProperty("实际支付金额")
    private BigDecimal pricePaid;

    /**
     * 原价
     */
    @TableField("original_price")
    @ApiModelProperty("原价")
    private BigDecimal originalPrice;

    /**
     * 支付方式：wechat-微信，alipay-支付宝，points-积分，coupon-优惠券，gift-赠送
     */
    @TableField("payment_method")
    @ApiModelProperty("支付方式：wechat-微信，alipay-支付宝，points-积分，coupon-优惠券，gift-赠送")
    private String paymentMethod;

    /**
     * 订单ID（如果是购买获得）
     */
    @TableField("order_id")
    @ApiModelProperty("订单ID（如果是购买获得）")
    private String orderId;

    /**
     * 使用的优惠券ID
     */
    @TableField("coupon_id")
    @ApiModelProperty("使用的优惠券ID")
    private Integer couponId;

    /**
     * 使用的积分数量
     */
    @TableField("points_used")
    @ApiModelProperty("使用的积分数量")
    private Integer pointsUsed;

    /**
     * 过期时间，null表示永久有效
     */
    @TableField("expire_time")
    @ApiModelProperty("过期时间，null表示永久有效")
    private LocalDateTime expireTime;

    /**
     * 是否激活：0-未激活，1-已激活
     */
    @TableField("is_active")
    @ApiModelProperty("是否激活：0-未激活，1-已激活")
    private Boolean isActive;

    /**
     * 状态：0-已失效，1-有效，2-已退款
     */
    @TableField("status")
    @ApiModelProperty("状态：0-已失效，1-有效，2-已退款")
    private Integer status;

    /**
     * 备注信息
     */
    @TableField("remark")
    @ApiModelProperty("备注信息")
    private String remark;

    /**
     * 操作管理员ID（赠送时记录）
     */
    @TableField("admin_id")
    @ApiModelProperty("操作管理员ID（赠送时记录）")
    private Integer adminId;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    @ApiModelProperty("退款时间")
    private LocalDateTime refundTime;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    @ApiModelProperty("退款原因")
    private String refundReason;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 用户名称（不存储到数据库，用于前端显示）
     */
    @TableField(exist = false)
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 课程名称（不存储到数据库，用于前端显示）
     */
    @TableField(exist = false)
    @ApiModelProperty("课程名称")
    private String courseName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    // 常量定义
    public static class AccessType {
        public static final int COURSE = 1;    // 课程权限
        public static final int CHAPTER = 2;   // 章节权限
        public static final int LESSON = 3;    // 课时权限
    }

    public static class AcquireMethod {
        public static final int PURCHASE = 1;     // 购买
        public static final int FREE = 2;         // 免费
        public static final int POINTS = 3;       // 积分兑换
        public static final int COUPON = 4;       // 优惠券兑换
        public static final int GIFT = 5;         // 管理员赠送
        public static final int PROMOTION = 6;    // 推广活动
    }

    public static class Status {
        public static final int INVALID = 0;   // 已失效
        public static final int VALID = 1;     // 有效
        public static final int REFUNDED = 2;  // 已退款
    }

    public static class PaymentMethod {
        public static final String WECHAT = "wechat";      // 微信支付
        public static final String ALIPAY = "alipay";      // 支付宝
        public static final String POINTS = "points";      // 积分支付
        public static final String COUPON = "coupon";      // 优惠券
        public static final String GIFT = "gift";          // 赠送
    }
} 