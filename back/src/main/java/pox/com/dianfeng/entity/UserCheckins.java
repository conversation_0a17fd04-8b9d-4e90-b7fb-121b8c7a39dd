package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户打卡记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("user_checkins")
@ApiModel(value = "UserCheckins对象", description = "用户打卡记录表")
public class UserCheckins extends Model<UserCheckins> {

    private static final long serialVersionUID = 1L;

    /**
     * 打卡ID，主键
     */
    @ApiModelProperty("打卡ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("用户ID")
    private Integer userId;

    /**
     * 打卡日期
     */
    @ApiModelProperty("打卡日期")
    @TableField("checkin_date")
    private LocalDate checkinDate;

    /**
     * 打卡时间
     */
    @ApiModelProperty("打卡时间")
    @TableField("checkin_time")
    private LocalDateTime checkinTime;

    /**
     * 学习时长（分钟）
     */
    @ApiModelProperty("学习时长（分钟）")
    @TableField("learn_duration")
    private Integer learnDuration;

    /**
     * 打卡备注
     */
    @TableField("remark")
    @ApiModelProperty("打卡备注")
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
