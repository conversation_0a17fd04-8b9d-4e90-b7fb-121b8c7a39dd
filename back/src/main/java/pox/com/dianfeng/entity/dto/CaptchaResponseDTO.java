package pox.com.dianfeng.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "验证码响应DTO")
public class CaptchaResponseDTO {

    @ApiModelProperty(value = "验证码ID", required = true)
    private String captchaId;

    @ApiModelProperty(value = "验证码图片(Base64)", required = true)
    private String captchaImage;
} 