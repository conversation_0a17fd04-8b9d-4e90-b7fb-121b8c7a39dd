package pox.com.dianfeng.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@ToString
@TableName("course_reviews")
@ApiModel(value = "CourseReviews对象", description = "课程评价表")
public class CourseReviews extends Model<CourseReviews> {

    private static final long serialVersionUID = 1L;

    /**
     * 评价ID，主键
     */
    @ApiModelProperty("评价ID，主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 课程ID
     */
    @TableField("course_id")
    @ApiModelProperty("课程ID")
    private Integer courseId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @ApiModelProperty("用户ID")
    private Integer userId;

    /**
     * 评分，1-5分
     */
    @TableField("rating")
    @ApiModelProperty("评分，1-5分")
    private Boolean rating;

    /**
     * 评价内容
     */
    @TableField("content")
    @ApiModelProperty("评价内容")
    private String content;

    /**
     * 状态：0-隐藏，1-显示
     */
    @TableField("status")
    @ApiModelProperty("状态：0-隐藏，1-显示")
    private Boolean status;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("is_del")
    @ApiModelProperty("是否删除：0-否，1-是")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
