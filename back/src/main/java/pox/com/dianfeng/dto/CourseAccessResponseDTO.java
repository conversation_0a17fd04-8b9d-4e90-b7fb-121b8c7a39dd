package pox.com.dianfeng.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import pox.com.dianfeng.entity.UserCourseAccess;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程权限响应DTO
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ApiModel(value = "CourseAccessResponseDTO", description = "课程权限响应DTO")
public class CourseAccessResponseDTO {

    @ApiModelProperty("是否有权限")
    private Boolean hasAccess;

    @ApiModelProperty("权限级别：1-课程权限，2-章节权限，3-课时权限")
    private Integer accessLevel;

    @ApiModelProperty("是否为买断权限")
    private Boolean isBuyout;

    @ApiModelProperty("权限来源：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动")
    private Integer acquireMethod;

    @ApiModelProperty("过期时间，null表示永久有效")
    private LocalDateTime expireTime;

    @ApiModelProperty("是否即将过期（7天内）")
    private Boolean nearExpiry;

    @ApiModelProperty("权限详情")
    private UserCourseAccess accessDetail;

    @ApiModelProperty("用户权限统计信息")
    private AccessStatistics statistics;

    @ApiModelProperty("无权限访问时的原因")
    private String reason;

    /**
     * 创建有权限的响应
     */
    public static CourseAccessResponseDTO hasAccess(UserCourseAccess access) {
        CourseAccessResponseDTO response = new CourseAccessResponseDTO();
        response.setHasAccess(true);
        response.setAccessLevel(access.getAccessType());
        response.setIsBuyout(access.getIsBuyout());
        response.setAcquireMethod(access.getAcquireMethod());
        response.setExpireTime(access.getExpireTime());
        response.setNearExpiry(access.getExpireTime() != null &&
                access.getExpireTime().isBefore(LocalDateTime.now().plusDays(7)));
        response.setAccessDetail(access);
        return response;
    }

    /**
     * 创建无权限的响应
     */
    public static CourseAccessResponseDTO noAccess(String reason) {
        CourseAccessResponseDTO response = new CourseAccessResponseDTO();
        response.setHasAccess(false);
        response.setReason(reason);
        return response;
    }

    /**
     * 用户权限统计信息
     */
    @Data
    @Builder
    @ApiModel(value = "AccessStatistics", description = "用户权限统计信息")
    public static class AccessStatistics {

        @ApiModelProperty("总权限数")
        private Long totalAccess;

        @ApiModelProperty("有效权限数")
        private Long validAccess;

        @ApiModelProperty("已过期权限数")
        private Long expiredAccess;

        @ApiModelProperty("买断权限数")
        private Long buyoutAccess;

        @ApiModelProperty("即将过期权限数")
        private Long expiringAccess;

        @ApiModelProperty("免费权限数")
        private Long freeAccess;

        @ApiModelProperty("购买权限数")
        private Long purchasedAccess;

        @ApiModelProperty("总课程数")
        private Long totalCourses;

        @ApiModelProperty("总章节数")
        private Long totalChapters;

        @ApiModelProperty("总课时数")
        private Long totalLessons;
    }

    /**
     * 权限详情列表（用于获取用户所有权限时使用）
     */
    @ApiModelProperty("权限详情列表")
    private List<AccessDetail> accessList;

    @Data
    @ApiModel(value = "AccessDetail", description = "权限详情")
    public static class AccessDetail {

        @ApiModelProperty("权限ID")
        private Integer id;

        @ApiModelProperty("课程ID")
        private Integer courseId;

        @ApiModelProperty("课程名称")
        private String courseName;

        @ApiModelProperty("章节ID")
        private Integer chapterId;

        @ApiModelProperty("章节名称")
        private String chapterName;

        @ApiModelProperty("课时ID")
        private Integer lessonId;

        @ApiModelProperty("课时名称")
        private String lessonName;

        @ApiModelProperty("权限类型：1-课程，2-章节，3-课时")
        private Integer accessType;

        @ApiModelProperty("获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动")
        private Integer acquireMethod;

        @ApiModelProperty("是否买断")
        private Boolean isBuyout;

        @ApiModelProperty("支付金额")
        private BigDecimal pricePaid;

        @ApiModelProperty("原价")
        private BigDecimal originalPrice;

        @ApiModelProperty("过期时间")
        private LocalDateTime expireTime;

        @ApiModelProperty("创建时间")
        private LocalDateTime createdAt;

        @ApiModelProperty("状态：0-已失效，1-有效，2-已退款")
        private Integer status;
    }
} 