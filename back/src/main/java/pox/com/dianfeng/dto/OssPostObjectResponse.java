package pox.com.dianfeng.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * OSS PostObject签名响应DTO
 * 
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssPostObjectResponse {
    
    /**
     * OSS上传端点URL
     */
    private String uploadUrl;
    
    /**
     * 存储桶名称
     */
    private String bucketName;
    
    /**
     * 文件对象键（完整路径）
     */
    private String objectKey;
    
    /**
     * 访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * Base64编码的Policy
     */
    private String policy;
    
    /**
     * 签名
     */
    private String signature;
    
    /**
     * 签名过期时间（ISO 8601格式）
     */
    private String expiration;
    
    /**
     * 文件访问URL（上传成功后的访问地址）
     */
    private String fileUrl;
    
    /**
     * 允许的最大文件大小（字节）
     */
    private Long maxFileSize;
    
    /**
     * 允许的文件类型
     */
    private String[] allowedContentTypes;
    
    /**
     * 额外的表单字段
     */
    private Map<String, String> formFields;
    
    /**
     * 上传提示信息
     */
    private String uploadTips;
}
