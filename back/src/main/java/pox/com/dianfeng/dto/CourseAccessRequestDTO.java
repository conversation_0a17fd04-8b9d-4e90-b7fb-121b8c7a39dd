package pox.com.dianfeng.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 课程权限请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@ApiModel(value = "CourseAccessRequestDTO", description = "课程权限请求DTO")
public class CourseAccessRequestDTO {

    @ApiModelProperty("用户ID")
    private Integer userId;

    @ApiModelProperty("课程ID")
    private Integer courseId;

    @ApiModelProperty("章节ID，null表示整个课程权限")
    private Integer chapterId;

    @ApiModelProperty("课时ID，null表示章节权限")
    private Integer lessonId;

    @ApiModelProperty("权限类型：1-课程，2-章节，3-课时")
    private Integer accessType;

    @ApiModelProperty("获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动")
    private Integer acquireMethod;

    @ApiModelProperty("是否买断：0-否，1-是（买断权限优先级最高）")
    private Boolean isBuyout;

    @ApiModelProperty("实际支付金额")
    private BigDecimal pricePaid;

    @ApiModelProperty("原价")
    private BigDecimal originalPrice;

    @ApiModelProperty("支付方式：wechat-微信，alipay-支付宝，points-积分，coupon-优惠券，gift-赠送")
    private String paymentMethod;

    @ApiModelProperty("订单ID（如果是购买获得）")
    private String orderId;

    @ApiModelProperty("使用的优惠券ID")
    private Integer couponId;

    @ApiModelProperty("使用的积分数量")
    private Integer pointsUsed;

    @ApiModelProperty("过期时间，null表示永久有效")
    private LocalDateTime expireTime;

    @ApiModelProperty("备注信息")
    private String remark;

    @ApiModelProperty("操作管理员ID（赠送时记录）")
    private Integer adminId;

    // 批量授权时使用
    @ApiModelProperty("批量用户ID列表")
    private List<Integer> userIds;

    @ApiModelProperty("批量课程ID列表")
    private List<Integer> courseIds;

    @ApiModelProperty("批量章节ID列表")
    private List<Integer> chapterIds;

    @ApiModelProperty("批量课时ID列表")
    private List<Integer> lessonIds;

    @ApiModelProperty("是否激活：0-未激活，1-已激活")
    private Boolean isActive;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款原因")
    private String refundReason;
}