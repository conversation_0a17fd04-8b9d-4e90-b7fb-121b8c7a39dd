package pox.com.dianfeng.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import pox.com.dianfeng.entity.Courses;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程列表展示DTO，只包含列表展示需要的核心信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@ToString
@ApiModel(value = "CourseListDto对象", description = "课程列表展示DTO")
public class CourseListDto {

    /**
     * 课程ID
     */
    @ApiModelProperty("课程ID")
    private Integer id;

    /**
     * 课程标题
     */
    @ApiModelProperty("课程标题")
    private String title;

    /**
     * 课程副标题
     */
    @ApiModelProperty("课程副标题")
    private String subtitle;

    /**
     * 封面图片路径
     */
    @ApiModelProperty("封面图片路径")
    private String coverImage;

    /**
     * 讲师ID
     */
    @ApiModelProperty("讲师ID")
    private Integer teacherId;

    /**
     * 分类ID
     */
    @ApiModelProperty("分类ID")
    private Integer categoryId;

    /**
     * 课程价格
     */
    @ApiModelProperty("课程价格")
    private BigDecimal price;

    /**
     * 原价
     */
    @ApiModelProperty("原价")
    private BigDecimal originalPrice;

    /**
     * 难度级别：1-入门，2-初级，3-中级，4-高级
     */
    @ApiModelProperty("难度级别")
    private Integer level;

    /**
     * 适合年龄段：0-不限，1-青少年，2-大学生，3-成人
     */
    @ApiModelProperty("适合年龄段")
    private Integer ageGroup;

    /**
     * 课程时长（分钟）
     */
    @ApiModelProperty("课程时长（分钟）")
    private Integer duration;

    /**
     * 课时数量
     */
    @ApiModelProperty("课时数量")
    private Integer lessonCount;

    /**
     * 学员数量
     */
    @ApiModelProperty("学员数量")
    private Integer studentCount;

    /**
     * 评分（1-5星）
     */
    @ApiModelProperty("评分")
    private BigDecimal rating;

    /**
     * 评价数量
     */
    @ApiModelProperty("评价数量")
    private Integer reviewCount;

    /**
     * 是否为直播课程
     */
    @ApiModelProperty("是否为直播课程")
    private Boolean isLive;

    /**
     * 是否推荐课程
     */
    @ApiModelProperty("是否推荐课程")
    private Boolean isFeatured;

    /**
     * 是否特训营
     */
    @ApiModelProperty("是否特训营")
    private Boolean isSpecialTraining;

    /**
     * 是否一对一
     */
    @ApiModelProperty("是否一对一")
    private Boolean isOneOnOne;

    /**
     * 课程状态：0-下架，1-上架
     */
    @ApiModelProperty("课程状态")
    private Boolean status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

    /**
     * 讲师姓名
     */
    @ApiModelProperty("讲师姓名")
    private String teacherName;

    /**
     * 讲师头像
     */
    @ApiModelProperty("讲师头像")
    private String teacherAvatar;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String categoryName;

    /**
     * 难度级别标签
     */
    @ApiModelProperty("难度级别标签")
    private String levelLabel;

    /**
     * 年龄段标签
     */
    @ApiModelProperty("年龄段标签")
    private String ageGroupLabel;

    /**
     * 完整的封面图片URL（拼接OSS域名）
     */
    @ApiModelProperty("完整的封面图片URL")
    private String fullCoverImageUrl;

    /**
     * 构造函数
     */
    public CourseListDto() {
    }

    /**
     * 从Courses实体复制基础信息（只复制列表展示需要的字段）
     */
    public static CourseListDto fromCourse(Courses course) {
        CourseListDto dto = new CourseListDto();

        // 复制核心字段
        dto.setId(course.getId());
        dto.setTitle(course.getTitle());
        dto.setSubtitle(course.getSubtitle());
        dto.setCoverImage(course.getCoverImage());
        dto.setTeacherId(course.getTeacherId());
        dto.setCategoryId(course.getCategoryId());
        dto.setPrice(course.getPrice());
        dto.setOriginalPrice(course.getOriginalPrice());
        dto.setLevel(course.getLevel());
        dto.setAgeGroup(course.getAgeGroup());
        dto.setDuration(course.getDuration());
        dto.setLessonCount(course.getLessonCount());
        dto.setStudentCount(course.getStudentCount());
        dto.setRating(course.getRating());
        dto.setReviewCount(course.getReviewCount());
        dto.setIsLive(course.getIsLive());
        dto.setIsFeatured(course.getIsFeatured());
        dto.setIsSpecialTraining(course.getIsSpecialTraining());
        dto.setIsOneOnOne(course.getIsOneOnOne());
        dto.setStatus(course.getStatus());
        dto.setCreatedAt(course.getCreatedAt());
        dto.setUpdatedAt(course.getUpdatedAt());

        return dto;
    }

    /**
     * 生成完整的封面图片URL
     */
    public void generateFullCoverImageUrl() {
        if (this.getCoverImage() != null && !this.getCoverImage().isEmpty()) {
            // 如果已经是完整URL，直接使用
            if (this.getCoverImage().startsWith("http")) {
                this.fullCoverImageUrl = this.getCoverImage();
            } else {
                // 拼接OSS域名
                this.fullCoverImageUrl = "https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/" + this.getCoverImage();
            }
        } else {
            // 使用默认封面
            // this.fullCoverImageUrl = "https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/default/course-cover.jpg";
        }
    }
} 