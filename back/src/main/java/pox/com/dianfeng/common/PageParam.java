package pox.com.dianfeng.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "分页查询参数")
public class PageParam {
    /**
     * 页码，从1开始
     */
    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    /**
     * 每页显示条数，默认10
     */
    @ApiModelProperty(value = "每页显示条数，默认10", example = "10")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段", example = "id")
    private String orderField;

    /**
     * 排序方式：asc/desc
     */
    @ApiModelProperty(value = "排序方式：asc/desc", example = "desc")
    private String orderType = "desc";

    /**
     * 获取页码
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * 设置页码
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取每页显示条数
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * 设置每页显示条数
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}