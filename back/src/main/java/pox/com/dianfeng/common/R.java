package pox.com.dianfeng.common;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 通用响应类
 *
 * <AUTHOR>
 * @param <T> 数据类型
 */
@Data
@Accessors(chain = true)
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 返回消息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 成功
     */
    public static <T> R<T> ok() {
        return restResult(null, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
    }

    /**
     * 成功，携带数据
     */
    public static <T> R<T> ok(T data) {
        return restResult(data, ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMsg());
    }

    /**
     * 成功，自定义消息
     */
    public static <T> R<T> ok(String msg) {
        return restResult(null, ResultCode.SUCCESS.getCode(), msg);
    }

    /**
     * 成功，携带数据和自定义消息
     */
    public static <T> R<T> ok(T data, String msg) {
        return restResult(data, ResultCode.SUCCESS.getCode(), msg);
    }

    /**
     * 失败
     */
    public static <T> R<T> error() {
        return restResult(null, ResultCode.ERROR.getCode(), ResultCode.ERROR.getMsg());
    }

    /**
     * 失败，自定义消息
     */
    public static <T> R<T> error(String msg) {
        return restResult(null, ResultCode.ERROR.getCode(), msg);
    }

    /**
     * 失败，自定义状态码和消息
     */
    public static <T> R<T> error(int code, String msg) {
        return restResult(null, code, msg);
    }

    /**
     * 构造响应结果
     */
    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> r = new R<>();
        r.setCode(code);
        r.setMsg(msg);
        r.setData(data);
        return r;
    }
}