package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.PageParam;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.service.IPostProcessSingleResult;

import java.util.List;

/**
 * 基础Controller
 *
 * @param <S> Service类型
 * @param <T> 实体类型
 * <AUTHOR>
 */
public abstract class BaseController<S extends IService<T>, T> implements IPostProcessSingleResult<T> {

    @Autowired
    protected S service;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询")
    @GetMapping("/page")
    public R<IPage<T>> page(PageParam pageParam, T entity) {
        // 创建分页对象
        Page<T> page = new Page<>(pageParam.getPageNum(), pageParam.getPageSize());

        // 构建查询条件
        QueryWrapper<T> queryWrapper = getQueryWrapper(entity);

        // 处理排序
        handleOrder(pageParam, queryWrapper);

        // 执行分页查询
        IPage<T> result = service.page(page, queryWrapper);

        // 后置处理
        postProcessPageResult(result);

        return R.ok(result);
    }

    /**
     * 查询所有
     */
    @ApiOperation("查询所有")
    @GetMapping("/list")
    public R<List<T>> list(T entity) {
        // 构建查询条件
        QueryWrapper<T> queryWrapper = getQueryWrapper(entity);

        // 执行查询
        List<T> list = service.list(queryWrapper);

        // 后置处理
        postProcessListResult(list);

        return R.ok(list);
    }

    /**
     * 根据ID查询
     */
    @ApiOperation("根据ID查询")
    @GetMapping("/{id}")
    public R<T> getById(@PathVariable Integer id) {
        // 执行查询
        T entity = service.getById(id);

        // 后置处理
        if (entity != null) {
            postProcessSingleResult(entity);
        }

        return entity != null ? R.ok(entity) : R.error("数据不存在");
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public R<T> save(@RequestBody T entity) {
        // 执行新增
        boolean success = service.save(entity);

        return success ? R.ok(entity) : R.error("新增失败");
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public R<Boolean> update(@RequestBody T entity) {
        // 执行修改
        boolean success = service.updateById(entity);

        return success ? R.ok() : R.error("修改失败");
    }

    /**
     * 删除（逻辑删除）
     */
    @ApiOperation("删除")
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Integer id) {
        // 执行删除
        boolean success = service.removeById(id);

        return success ? R.ok() : R.error("删除失败");
    }

    /**
     * 批量删除
     */
    @ApiOperation("批量删除")
    @DeleteMapping("/batch")
    public R<Boolean> removeBatch(@RequestBody List<Integer> ids) {
        // 执行批量删除（逻辑删除由MyBatis-Plus配置完成）
        boolean success = service.removeByIds(ids);

        return success ? R.ok() : R.error("批量删除失败");
    }

    /**
     * 构建查询条件
     */
    protected QueryWrapper<T> getQueryWrapper(T entity) {
        return new QueryWrapper<>();
    }

    /**
     * 处理排序
     */
    protected void handleOrder(PageParam pageParam, QueryWrapper<T> queryWrapper) {
        // 如果排序字段不为空
        if (pageParam.getOrderField() != null && !pageParam.getOrderField().isEmpty()) {
            // 升序
            if ("asc".equalsIgnoreCase(pageParam.getOrderType())) {
                queryWrapper.orderByAsc(pageParam.getOrderField());
            }
            // 降序
            else {
                queryWrapper.orderByDesc(pageParam.getOrderField());
            }
        }
    }



    public T postProcessSingleResult(T entity) {
        return entity;
    }

}