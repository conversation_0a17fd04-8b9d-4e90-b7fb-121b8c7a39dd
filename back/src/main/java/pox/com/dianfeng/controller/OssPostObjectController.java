package pox.com.dianfeng.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.dto.OssPostObjectResponse;
import pox.com.dianfeng.service.OssPostObjectService;

/**
 * OSS PostObject签名控制器
 * 
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@RestController
@RequestMapping("/oss")
@RequiredArgsConstructor
@Tag(name = "OSS PostObject", description = "OSS PostObject签名接口")
public class OssPostObjectController {
    
    private final OssPostObjectService ossPostObjectService;
    
    /**
     * 获取PostObject签名
     * 
     * @param category 文件分类（image/video/audio/document）
     * @param fileName 文件名（可选）
     * @return PostObject签名响应
     */
    @GetMapping("/signature")
    @Operation(summary = "获取PostObject签名", description = "生成OSS PostObject上传所需的签名和Policy")
    public R<OssPostObjectResponse> getPostObjectSignature(
            @Parameter(description = "文件分类", example = "image") 
            @RequestParam String category,
            @Parameter(description = "文件名（可选）", example = "avatar.jpg") 
            @RequestParam(required = false) String fileName) {
        
        log.info("获取PostObject签名，文件分类: {}, 文件名: {}", category, fileName);
        
        try {
            // 验证分类参数
            if (!isValidCategory(category)) {
                return R.error("不支持的文件分类: " + category + "，支持的分类: image, video, audio, document");
            }
            
            OssPostObjectResponse response = ossPostObjectService.generatePostObjectSignature(category, fileName);
            
            log.info("PostObject签名生成成功，对象键: {}", response.getObjectKey());
            
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("获取PostObject签名失败: {}", e.getMessage(), e);
            return R.error("获取PostObject签名失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取自定义PostObject签名
     * 
     * @param pathPrefix 路径前缀
     * @param fileName 文件名
     * @param contentType 文件类型（可选）
     * @param maxFileSize 最大文件大小（可选）
     * @return PostObject签名响应
     */
    @GetMapping("/signature/custom")
    @Operation(summary = "获取自定义PostObject签名", description = "生成自定义路径的OSS PostObject上传签名")
    public R<OssPostObjectResponse> getCustomPostObjectSignature(
            @Parameter(description = "路径前缀", example = "custom/uploads/") 
            @RequestParam String pathPrefix,
            @Parameter(description = "文件名", example = "document.pdf") 
            @RequestParam String fileName,
            @Parameter(description = "文件类型", example = "application/pdf") 
            @RequestParam(required = false) String contentType,
            @Parameter(description = "最大文件大小（字节）", example = "10485760") 
            @RequestParam(required = false) Long maxFileSize) {
        
        log.info("获取自定义PostObject签名，路径前缀: {}, 文件名: {}, 文件类型: {}, 最大大小: {}", 
                pathPrefix, fileName, contentType, maxFileSize);
        
        try {
            // 确保路径前缀以/结尾
            if (!pathPrefix.endsWith("/")) {
                pathPrefix += "/";
            }
            
            OssPostObjectResponse response = ossPostObjectService.generatePostObjectSignature(
                    pathPrefix, fileName, contentType, maxFileSize);
            
            log.info("自定义PostObject签名生成成功，对象键: {}", response.getObjectKey());
            
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("获取自定义PostObject签名失败: {}", e.getMessage(), e);
            return R.error("获取自定义PostObject签名失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证文件分类是否有效
     */
    private boolean isValidCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return false;
        }
        String lowerCategory = category.toLowerCase();
        return "image".equals(lowerCategory) || 
               "video".equals(lowerCategory) || 
               "audio".equals(lowerCategory) || 
               "document".equals(lowerCategory);
    }
}
