package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.TagConfigs;
import pox.com.dianfeng.service.ITagConfigsService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Api(tags = "标签配置管理")
@RestController
@RequestMapping("/tag-configs")
public class TagConfigsController extends BaseController<ITagConfigsService, TagConfigs> {

    @Autowired
    private ITagConfigsService tagConfigsService;

    /**
     * 根据分类获取标签配置列表
     */
    @ApiOperation("根据分类获取标签配置列表")
    @GetMapping("/getByCategory/{category}")
    public R<List<TagConfigs>> getByCategory(@PathVariable String category) {
        List<TagConfigs> list = tagConfigsService.getByCategory(category);
        return R.ok(list);
    }

    /**
     * 获取可用的标签配置列表
     */
    @ApiOperation("获取可用的标签配置列表")
    @GetMapping("/getAvailableByCategory/{category}")
    public R<List<TagConfigs>> getAvailableByCategory(@PathVariable String category) {
        List<TagConfigs> list = tagConfigsService.getAvailableByCategory(category);
        return R.ok(list);
    }

    /**
     * 检查标签值是否存在
     */
    @ApiOperation("检查标签值是否存在")
    @GetMapping("/checkValueExists")
    public R<Boolean> checkValueExists(String category, Integer value, Integer excludeId) {
        boolean exists = tagConfigsService.checkValueExists(category, value, excludeId);
        return R.ok(exists);
    }

    /**
     * 获取标签类型统计
     */
    @ApiOperation("获取标签类型统计")
    @GetMapping("/categories")
    public R<Map<String, Long>> getCategories() {
        List<TagConfigs> allTags = tagConfigsService.list();
        Map<String, Long> categoryStats = allTags.stream()
                .collect(Collectors.groupingBy(TagConfigs::getCategory, Collectors.counting()));
        return R.ok(categoryStats);
    }

    /**
     * 获取所有标签类型列表（包含中文名称）
     */
    @ApiOperation("获取所有标签类型列表")
    @GetMapping("/category-list")
    public R<List<Map<String, Object>>> getCategoryList() {
        List<TagConfigs> allTags = tagConfigsService.list();

        // 获取所有不同的类型
        Map<String, Long> categoryStats = allTags.stream()
                .collect(Collectors.groupingBy(TagConfigs::getCategory, Collectors.counting()));

        // 构建类型列表，包含中文名称
        List<Map<String, Object>> categoryList = new ArrayList<>();

        // 添加"全部"选项
        Map<String, Object> allCategory = new HashMap<>();
        allCategory.put("label", "全部");
        allCategory.put("value", "");
        allCategory.put("count", categoryStats.values().stream().mapToLong(Long::longValue).sum());
        categoryList.add(allCategory);

        // 定义类型映射
        Map<String, String> categoryLabels = new HashMap<>();
        categoryLabels.put("level", "课程难度");
        categoryLabels.put("age_group", "适合年龄");
        categoryLabels.put("course_category", "课程分类");
        categoryLabels.put("teacher_specialty", "讲师专长");
        categoryLabels.put("course_tag", "课程标签");

        // 添加其他类型
        for (Map.Entry<String, Long> entry : categoryStats.entrySet()) {
            String category = entry.getKey();
            Long count = entry.getValue();

            Map<String, Object> categoryItem = new HashMap<>();
            categoryItem.put("label", categoryLabels.getOrDefault(category, category));
            categoryItem.put("value", category);
            categoryItem.put("count", count);
            categoryList.add(categoryItem);
        }

        return R.ok(categoryList);
    }

    /**
     * 新增标签配置（重写以添加验证）
     */
    @ApiOperation("新增标签配置")
    @PostMapping
    @Override
    public R<TagConfigs> save(@RequestBody TagConfigs entity) {
        // 检查同分类下标签值是否已存在
        if (tagConfigsService.checkValueExists(entity.getCategory(), entity.getValue(), null)) {
            return R.error("该分类下已存在相同的标签值");
        }

        // 如果没有设置排序值，自动设置为最大值+1
        if (entity.getSortOrder() == null) {
            QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("category", entity.getCategory())
                       .orderByDesc("sort_order")
                       .last("LIMIT 1");
            TagConfigs lastTag = tagConfigsService.getOne(queryWrapper);
            entity.setSortOrder(lastTag != null ? lastTag.getSortOrder() + 1 : 1);
        }

        // 设置value 为当前tag 最大value +1
        QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", entity.getCategory())
                   .orderByDesc("value")
                   .last("LIMIT 1");
        TagConfigs lastTag = tagConfigsService.getOne(queryWrapper);
        entity.setValue(lastTag != null ? lastTag.getValue() + 1 : 1);

        // 执行新增
        boolean success = service.save(entity);
        return success ? R.ok(entity) : R.error("新增失败");
    }

    /**
     * 修改标签配置（重写以添加验证）
     */
    @ApiOperation("修改标签配置")
    @PutMapping
    @Override
    public R<Boolean> update(@RequestBody TagConfigs entity) {
        // 检查同分类下标签值是否已存在（排除自己）
        if (tagConfigsService.checkValueExists(entity.getCategory(), entity.getValue(), entity.getId())) {
            return R.error("该分类下已存在相同的标签值");
        }
        
        // 执行修改
        boolean success = service.updateById(entity);
        return success ? R.ok() : R.error("修改失败");
    }

    /**
     * 删除前检查是否为系统内置
     */
    @ApiOperation("删除标签配置")
    @DeleteMapping("/{id}")
    @Override
    public R<Boolean> remove(@PathVariable Integer id) {
        TagConfigs tagConfig = service.getById(id);
        if (tagConfig == null) {
            return R.error("标签配置不存在");
        }
        
        if (tagConfig.getIsSystem()) {
            return R.error("系统内置标签不允许删除");
        }
        
        // 执行删除
        boolean success = service.removeById(id);
        return success ? R.ok() : R.error("删除失败");
    }

    /**
     * 批量删除前检查是否为系统内置
     */
    @ApiOperation("批量删除标签配置")
    @DeleteMapping("/batch")
    @Override
    public R<Boolean> removeBatch(@RequestBody List<Integer> ids) {
        // 检查是否存在系统内置标签
        QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids).eq("is_system", 1);
        long systemCount = service.count(queryWrapper);
        
        if (systemCount > 0) {
            return R.error("包含系统内置标签，不允许删除");
        }
        
        // 执行批量删除
        boolean success = service.removeByIds(ids);
        return success ? R.ok() : R.error("批量删除失败");
    }

    /**
     * 构建查询条件
     */
    @Override
    protected QueryWrapper<TagConfigs> getQueryWrapper(TagConfigs entity) {
        QueryWrapper<TagConfigs> queryWrapper = new QueryWrapper<>();

        if (entity != null) {
            // 分类筛选
            if (entity.getCategory() != null && !entity.getCategory().isEmpty()) {
                queryWrapper.eq("category", entity.getCategory());
            }

            // 标签名称模糊查询
            if (entity.getLabel() != null && !entity.getLabel().isEmpty()) {
                queryWrapper.like("label", entity.getLabel());
            }

            // 状态筛选
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }

            // 是否系统内置筛选
            if (entity.getIsSystem() != null) {
                queryWrapper.eq("is_system", entity.getIsSystem());
            }
        }

        // 默认按排序值和ID排序
        queryWrapper.orderByAsc("sort_order", "id");

        return queryWrapper;
    }
} 