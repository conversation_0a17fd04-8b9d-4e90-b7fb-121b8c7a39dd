package pox.com.dianfeng.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.dto.FileUploadResult;
import pox.com.dianfeng.service.IFileUploadService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件上传控制器
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/file")
@RequiredArgsConstructor
@Api(tags = "文件上传管理")
public class FileUploadController {
    
    private final IFileUploadService fileUploadService;
    
    @PostMapping("/upload")
    @ApiOperation("上传单个文件")
    public R<FileUploadResult> uploadFile(
            @ApiParam("文件") @RequestParam("file") MultipartFile file,
            @ApiParam("文件分类(avatar/cover/video/audio/document)") @RequestParam("category") String category) {
        
        try {
            FileUploadResult result = fileUploadService.uploadFile(file, category);
            return R.ok(result);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return R.error("文件上传失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/upload/batch")
    @ApiOperation("批量上传文件")
    public R<List<FileUploadResult>> uploadFiles(
            @ApiParam("文件列表") @RequestParam("files") List<MultipartFile> files,
            @ApiParam("文件分类") @RequestParam("category") String category) {
        
        try {
            List<FileUploadResult> results = fileUploadService.uploadFiles(files, category);
            return R.ok(results);
        } catch (Exception e) {
            log.error("批量文件上传失败: {}", e.getMessage(), e);
            return R.error("批量文件上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload/avatar")
    @ApiOperation("上传用户头像")
    public R<FileUploadResult> uploadAvatar(
            @ApiParam("头像文件") @RequestParam("file") MultipartFile file) {

        try {
            FileUploadResult result = fileUploadService.uploadFile(file, "avatar");
            return R.ok(result);
        } catch (Exception e) {
            log.error("头像上传失败: {}", e.getMessage(), e);
            return R.error("头像上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload/cover")
    @ApiOperation("上传课程封面")
    public R<FileUploadResult> uploadCover(
            @ApiParam("封面文件") @RequestParam("file") MultipartFile file) {

        try {
            FileUploadResult result = fileUploadService.uploadFile(file, "cover");
            return R.ok(result);
        } catch (Exception e) {
            log.error("封面上传失败: {}", e.getMessage(), e);
            return R.error("封面上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload/video")
    @ApiOperation("上传视频文件")
    public R<FileUploadResult> uploadVideo(
            @ApiParam("视频文件") @RequestParam("file") MultipartFile file) {

        try {
            FileUploadResult result = fileUploadService.uploadFile(file, "video");
            return R.ok(result);
        } catch (Exception e) {
            log.error("视频上传失败: {}", e.getMessage(), e);
            return R.error("视频上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/upload/audio")
    @ApiOperation("上传音频文件")
    public R<FileUploadResult> uploadAudio(
            @ApiParam("音频文件") @RequestParam("file") MultipartFile file) {

        try {
            FileUploadResult result = fileUploadService.uploadFile(file, "audio");
            return R.ok(result);
        } catch (Exception e) {
            log.error("音频上传失败: {}", e.getMessage(), e);
            return R.error("音频上传失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/delete")
    @ApiOperation("删除文件")
    public R<String> deleteFile(
            @ApiParam("文件名") @RequestParam("fileName") String fileName,
            @ApiParam("存储桶名称(可选)") @RequestParam(value = "bucketName", required = false) String bucketName) {
        
        try {
            boolean success = bucketName != null ? 
                    fileUploadService.deleteFile(fileName, bucketName) : 
                    fileUploadService.deleteFile(fileName);
            
            if (success) {
                return R.ok("文件删除成功");
            } else {
                return R.error("文件删除失败");
            }
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return R.error("文件删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/preview-url")
    @ApiOperation("获取文件预览URL")
    public R<String> getPreviewUrl(
            @ApiParam("文件名") @RequestParam("fileName") String fileName,
            @ApiParam("过期时间(分钟)") @RequestParam(value = "expireMinutes", defaultValue = "60") int expireMinutes,
            @ApiParam("存储桶名称(可选)") @RequestParam(value = "bucketName", required = false) String bucketName) {

        try {
            String previewUrl = bucketName != null ?
                    fileUploadService.getPreviewUrl(fileName, bucketName, expireMinutes) :
                    fileUploadService.getPreviewUrl(fileName, expireMinutes);

            if (previewUrl != null) {
                return R.ok(previewUrl);
            } else {
                return R.error("获取预览URL失败");
            }
        } catch (Exception e) {
            log.error("获取预览URL失败: {}", e.getMessage(), e);
            return R.error("获取预览URL失败: " + e.getMessage());
        }
    }

    @GetMapping("/presigned-upload-url")
    @ApiOperation("生成预签名上传URL")
    public R<String> generatePreSignedUploadUrl(
            @ApiParam("文件名") @RequestParam("fileName") String fileName,
            @ApiParam("存储桶名称") @RequestParam("bucketName") String bucketName,
            @ApiParam("过期时间(分钟)") @RequestParam(value = "expireMinutes", defaultValue = "15") int expireMinutes) {

        try {
            String uploadUrl = fileUploadService.generatePreSignedUploadUrl(fileName, bucketName, expireMinutes);
            if (uploadUrl != null) {
                return R.ok(uploadUrl);
            } else {
                return R.error("生成预签名URL失败");
            }
        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", e.getMessage(), e);
            return R.error("生成预签名URL失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/list")
    @ApiOperation("获取文件列表")
    public R<List<String>> listFiles(
            @ApiParam("存储桶名称") @RequestParam("bucketName") String bucketName,
            @ApiParam("文件前缀(可选)") @RequestParam(value = "prefix", defaultValue = "") String prefix) {
        
        try {
            List<String> files = fileUploadService.listFiles(bucketName, prefix);
            return R.ok(files);
        } catch (Exception e) {
            log.error("获取文件列表失败: {}", e.getMessage(), e);
            return R.error("获取文件列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/copy")
    @ApiOperation("复制文件")
    public R<String> copyFile(
            @ApiParam("源文件名") @RequestParam("sourceFileName") String sourceFileName,
            @ApiParam("源存储桶") @RequestParam("sourceBucket") String sourceBucket,
            @ApiParam("目标文件名") @RequestParam("targetFileName") String targetFileName,
            @ApiParam("目标存储桶") @RequestParam("targetBucket") String targetBucket) {

        try {
            boolean success = fileUploadService.copyFile(sourceFileName, sourceBucket, targetFileName, targetBucket);
            if (success) {
                return R.ok("文件复制成功");
            } else {
                return R.error("文件复制失败");
            }
        } catch (Exception e) {
            log.error("文件复制失败: {}", e.getMessage(), e);
            return R.error("文件复制失败: " + e.getMessage());
        }
    }

    @PostMapping("/tags")
    @ApiOperation("设置文件标签")
    public R<String> setFileTags(
            @ApiParam("文件名") @RequestParam("fileName") String fileName,
            @ApiParam("存储桶名称") @RequestParam("bucketName") String bucketName,
            @ApiParam("标签") @RequestBody Map<String, String> tags) {

        try {
            boolean success = fileUploadService.setFileTags(fileName, bucketName, tags);
            if (success) {
                return R.ok("文件标签设置成功");
            } else {
                return R.error("文件标签设置失败");
            }
        } catch (Exception e) {
            log.error("文件标签设置失败: {}", e.getMessage(), e);
            return R.error("文件标签设置失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/info")
    @ApiOperation("获取文件上传配置信息")
    public R<Map<String, Object>> getUploadInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("maxImageSize", "10MB");
        info.put("maxVideoSize", "500MB");
        info.put("maxAudioSize", "50MB");
        info.put("maxDocumentSize", "20MB");
        info.put("supportedImageTypes", "jpg,jpeg,png,gif,bmp,webp,svg");
        info.put("supportedVideoTypes", "mp4,avi,mov,wmv,flv,webm,mkv,m4v");
        info.put("supportedAudioTypes", "mp3,wav,flac,aac,ogg,wma,m4a");
        info.put("supportedDocumentTypes", "pdf,doc,docx,xls,xlsx,ppt,pptx,txt");
        
        return R.ok(info);
    }
}
