package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.controller.BaseController;
import pox.com.dianfeng.entity.Courses;
import pox.com.dianfeng.entity.HomepageRecommendations;
import pox.com.dianfeng.entity.LiveCourses;
import pox.com.dianfeng.service.ICoursesService;
import pox.com.dianfeng.service.IHomepageRecommendationsService;
import pox.com.dianfeng.service.ILiveCoursesService;
import pox.com.dianfeng.service.OssUrlService;

import java.util.List;

/**
 * <p>
 * 首页推荐表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Api(tags = "首页推荐管理")
@RestController
@RequestMapping("/homepage-recommendations")
public class HomepageRecommendationsController extends BaseController<IHomepageRecommendationsService, HomepageRecommendations> {

    @Autowired
    private OssUrlService ossUrlService;

    @Autowired
    private ICoursesService coursesService;

    @Autowired
    private ILiveCoursesService liveCoursesService;

    /**
     * 获取启用的首页推荐列表（前台使用）
     */
    @ApiOperation("获取启用的首页推荐列表")
    @GetMapping("/enabled")
    public R<List<HomepageRecommendations>> getEnabledRecommendations() {
        QueryWrapper<HomepageRecommendations> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .eq("is_del", 0)
                .orderByAsc("sort_order", "id");

        List<HomepageRecommendations> list = service.list(queryWrapper);

        // 后置处理
        postProcessListResult(list);

        return R.ok(list);
    }

    /**
     * 切换推荐状态
     */
    @ApiOperation("切换推荐状态")
    @PutMapping("/toggle-status/{id}")
    public R<Boolean> toggleStatus(@PathVariable Integer id) {
        HomepageRecommendations recommendation = service.getById(id);
        if (recommendation == null) {
            return R.error("推荐不存在");
        }

        recommendation.setStatus(!recommendation.getStatus());
        boolean success = service.updateById(recommendation);

        return success ? R.ok(true, "状态更新成功") : R.error("状态更新失败");
    }

    /**
     * 批量删除推荐
     */
    @ApiOperation("批量删除推荐")
    @DeleteMapping("/batch-delete")
    public R<Boolean> batchDelete(@RequestBody List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return R.error("请选择要删除的推荐");
        }

        boolean success = service.removeByIds(ids);
        return success ? R.ok(true, "批量删除成功") : R.error("批量删除失败");
    }

    /**
     * 重写单个实体后置处理，设置完整URL和关联信息
     */
    @Override
    public HomepageRecommendations postProcessSingleResult(HomepageRecommendations entity) {
        if (entity == null) {
            return entity;
        }

        // 设置封面图片完整URL
        if (entity.getCoverImage() != null) {
            entity.setCoverImageFullUrl(ossUrlService.buildFullUrl(entity.getCoverImage()));
        }

        // 设置链接类型标签
        if (entity.getLinkType() != null) {
            switch (entity.getLinkType()) {
                case 1:
                    entity.setLinkTypeLabel("课程");
                    // 获取关联课程信息
                    if (entity.getLinkTargetId() != null) {
                        Courses course = coursesService.getById(entity.getLinkTargetId());
                        if (course != null) {
                            entity.setCourseTitle(course.getTitle());
                        }
                    }
                    break;
                case 2:
                    entity.setLinkTypeLabel("直播");
                    // 获取关联直播信息
                    if (entity.getLinkTargetId() != null) {
                        LiveCourses liveCourse = liveCoursesService.getById(entity.getLinkTargetId());
                        if (liveCourse != null) {
                            entity.setLiveTitle(liveCourse.getTitle());
                        }
                    }
                    break;
                case 3:
                    entity.setLinkTypeLabel("外部链接");
                    break;
                default:
                    entity.setLinkTypeLabel("未知");
                    break;
            }
        }

        return entity;
    }
}
