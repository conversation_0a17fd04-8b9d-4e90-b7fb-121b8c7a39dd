package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.UserCheckins;
import pox.com.dianfeng.service.IUserCheckinsService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户打卡记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "用户打卡管理")
@RestController
@RequestMapping("/user-checkins")
public class UserCheckinsController extends BaseController<IUserCheckinsService, UserCheckins> {

    /**
     * 获取用户打卡记录
     */
    @ApiOperation("获取用户打卡记录")
    @GetMapping("/getByUserId/{userId}")
    public R<List<UserCheckins>> getByUserId(@PathVariable Integer userId) {
        QueryWrapper<UserCheckins> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_del", 0)
                .orderByDesc("checkin_date");

        List<UserCheckins> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取用户当天打卡记录
     */
    @ApiOperation("获取用户当天打卡记录")
    @GetMapping("/getTodayCheckin/{userId}")
    public R<UserCheckins> getTodayCheckin(@PathVariable Integer userId) {
        QueryWrapper<UserCheckins> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("checkin_date", LocalDate.now())
                .eq("is_del", 0);

        UserCheckins checkin = service.getOne(queryWrapper);

        return checkin != null ? R.ok(checkin) : R.error("今日未打卡");
    }

    /**
     * 用户打卡
     */
    @ApiOperation("用户打卡")
    @PostMapping("/checkin")
    public R<Boolean> checkin(@RequestBody UserCheckins checkin) {
        // 检查是否已打卡
        QueryWrapper<UserCheckins> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", checkin.getUserId())
                .eq("checkin_date", LocalDate.now())
                .eq("is_del", 0);

        UserCheckins existCheckin = service.getOne(queryWrapper);

        if (existCheckin != null) {
            return R.error("今日已打卡");
        }

        // 设置打卡日期和时间
        checkin.setCheckinDate(LocalDate.now());
        checkin.setCheckinTime(LocalDateTime.now());

        boolean success = service.save(checkin);

        return success ? R.ok(true, "打卡成功") : R.error("打卡失败");
    }

    /**
     * 获取用户连续打卡天数
     */
    @ApiOperation("获取用户连续打卡天数")
    @GetMapping("/getConsecutiveDays/{userId}")
    public R<Integer> getConsecutiveDays(@PathVariable Integer userId) {
        // 获取用户所有打卡记录，按日期倒序排序
        QueryWrapper<UserCheckins> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_del", 0)
                .orderByDesc("checkin_date");

        List<UserCheckins> checkins = service.list(queryWrapper);

        if (checkins.isEmpty()) {
            return R.ok(0);
        }

        // 检查今天是否打卡
        LocalDate today = LocalDate.now();
        if (!checkins.get(0).getCheckinDate().equals(today)) {
            return R.ok(0); // 如果今天没打卡，连续天数为0
        }

        // 计算连续打卡天数
        int consecutiveDays = 1; // 今天已打卡，从1开始计数
        LocalDate expectedDate = today.minusDays(1); // 从昨天开始检查

        for (int i = 1; i < checkins.size(); i++) {
            if (checkins.get(i).getCheckinDate().equals(expectedDate)) {
                consecutiveDays++;
                expectedDate = expectedDate.minusDays(1);
            } else {
                break; // 出现断层，结束计算
            }
        }

        return R.ok(consecutiveDays);
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<UserCheckins> getQueryWrapper(UserCheckins entity) {
        QueryWrapper<UserCheckins> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果打卡实体不为空
        if (entity != null) {
            // 用户ID查询
            if (entity.getUserId() != null) {
                queryWrapper.eq("user_id", entity.getUserId());
            }

            // 打卡日期查询
            if (entity.getCheckinDate() != null) {
                queryWrapper.eq("checkin_date", entity.getCheckinDate());
            }
        }

        // 默认按打卡日期倒序排列
        queryWrapper.orderByDesc("checkin_date");

        return queryWrapper;
    }
}
