package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.PageParam;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.Users;
import pox.com.dianfeng.service.IUsersService;
import pox.com.dianfeng.service.OssUrlService;

import java.util.List;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/users")
public class UserController extends BaseController<IUsersService, Users> {

    @Autowired
    private OssUrlService ossUrlService;

    /**
     * 根据用户名查询用户
     */
    @ApiOperation("根据用户名查询用户")
    @GetMapping("/getByUsername")
    public R<Users> getByUsername(String username) {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);

        Users user = service.getOne(queryWrapper);

        // 后置处理
        if (user != null) {
            postProcessSingleResult(user);
        }

        return user != null ? R.ok(user) : R.error("用户不存在");
    }

    /**
     * 根据手机号查询用户
     */
    @ApiOperation("根据手机号查询用户")
    @GetMapping("/getByPhone")
    public R<Users> getByPhone(String phone) {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone", phone);

        Users user = service.getOne(queryWrapper);

        // 后置处理
        if (user != null) {
            postProcessSingleResult(user);
        }

        return user != null ? R.ok(user) : R.error("用户不存在");
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<Users> getQueryWrapper(Users entity) {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果用户实体不为空
        if (entity != null) {
            // 用户名模糊查询
            if (entity.getUsername() != null && !entity.getUsername().isEmpty()) {
                queryWrapper.like("username", entity.getUsername());
            }

            // 昵称模糊查询
            if (entity.getNickname() != null && !entity.getNickname().isEmpty()) {
                queryWrapper.like("nickname", entity.getNickname());
            }

            // 手机号精确查询
            if (entity.getPhone() != null && !entity.getPhone().isEmpty()) {
                queryWrapper.eq("phone", entity.getPhone());
            }

            // 邮箱精确查询
            if (entity.getEmail() != null && !entity.getEmail().isEmpty()) {
                queryWrapper.eq("email", entity.getEmail());
            }

            //// 性别查询
            //if (entity.getGender() != null) {
            //    queryWrapper.eq("gender", entity.getGender());
            //}

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        return queryWrapper;
    }

    /**
     * 重写单个实体后置处理，设置头像完整URL
     */
    @Override
    public Users postProcessSingleResult(Users user) {
        if (user != null) {
            user.setAvatarFullUrl(ossUrlService.buildFullUrl(user.getAvatar()));
        }
        return user;
    }
}