package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.Admins;

import pox.com.dianfeng.entity.dto.CaptchaResponseDTO;
import pox.com.dianfeng.entity.dto.LoginRequestDTO;
import pox.com.dianfeng.entity.dto.LoginResponseDTO;
import pox.com.dianfeng.service.IAdminLoginService;
import pox.com.dianfeng.service.IAdminsService;
import cn.dev33.satoken.stp.StpUtil;

/**
 * <p>
 * 管理员账户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "管理员管理")
@RestController
@RequestMapping("/admins")
public class AdminsController extends BaseController<IAdminsService, Admins> {

    @Autowired
    private IAdminLoginService adminLoginService;

    /**
     * 获取验证码
     */
    @ApiOperation("获取验证码")
    @GetMapping("/captcha")
    public R<CaptchaResponseDTO> getCaptcha() {
        CaptchaResponseDTO captcha = adminLoginService.generateCaptcha();
        return R.ok(captcha);
    }

    /**
     * 管理员登录
     */
    @ApiOperation("管理员登录")
    @PostMapping("/login")
    public R<LoginResponseDTO> login(@RequestBody LoginRequestDTO loginRequest, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取登录IP
            String loginIp = getClientIp(request);

            // 执行登录
            LoginResponseDTO loginResponse = adminLoginService.login(loginRequest, loginIp);

            // 手动设置Cookie（确保在线上环境生效）
            Cookie tokenCookie = new Cookie("satoken", loginResponse.getToken());
            tokenCookie.setPath("/");
            tokenCookie.setMaxAge(-1); // 会话级别cookie
            tokenCookie.setHttpOnly(false); // 允许JavaScript访问
            
            // 如果是HTTPS环境，设置secure属性
            String scheme = request.getScheme();
            if ("https".equals(scheme)) {
                tokenCookie.setSecure(true);
            }
            
            response.addCookie(tokenCookie);

            return R.ok(loginResponse);
        } catch (Exception e) {
            return R.error(e.getMessage());
        }
    }

    /**
     * 管理员退出登录
     */
    @ApiOperation("管理员退出登录")
    @PostMapping("/logout")
    public R<String> logout(HttpServletResponse response) {
        try {
            // 执行Sa-Token退出登录
            StpUtil.logout();
            
            // 清除cookie
            Cookie tokenCookie = new Cookie("satoken", "");
            tokenCookie.setPath("/");
            tokenCookie.setMaxAge(0); // 立即过期
            response.addCookie(tokenCookie);
            
            return R.ok("退出登录成功");
        } catch (Exception e) {
            return R.error("退出登录失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户名查询管理员
     */
    @ApiOperation("根据用户名查询管理员")
    @GetMapping("/getByUsername")
    public R<Admins> getByUsername(String username) {
        QueryWrapper<Admins> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);

        Admins admin = service.getOne(queryWrapper);

        return admin != null ? R.ok(admin) : R.error("管理员不存在");
    }

    /**
     * 更新管理员最后登录信息
     */
    @ApiOperation("更新管理员最后登录信息")
    @PostMapping("/updateLoginInfo")
    public R<Boolean> updateLoginInfo(Integer adminId, String loginIp) {
        Admins admin = service.getById(adminId);
        if (admin == null) {
            return R.error("管理员不存在");
        }

        admin.setLastLoginIp(loginIp);
        admin.setLoginCount(admin.getLoginCount() + 1);

        boolean success = service.updateById(admin);

        return success ? R.ok() : R.error("更新登录信息失败");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");

        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<Admins> getQueryWrapper(Admins entity) {
        QueryWrapper<Admins> queryWrapper = new QueryWrapper<>();

        // 如果管理员实体不为空
        if (entity != null) {
            // 用户名模糊查询
            if (entity.getUsername() != null && !entity.getUsername().isEmpty()) {
                queryWrapper.like("username", entity.getUsername());
            }

            // 真实姓名模糊查询
            if (entity.getRealName() != null && !entity.getRealName().isEmpty()) {
                queryWrapper.like("real_name", entity.getRealName());
            }

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        return queryWrapper;
    }
}
