package pox.com.dianfeng.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.dto.StsTokenResponse;
import pox.com.dianfeng.service.IStsService;

/**
 * STS临时访问凭证控制器
 * 用于前端直传OSS
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/sts")
@RequiredArgsConstructor
@Api(tags = "STS临时访问凭证管理")
@CrossOrigin(origins = "*", maxAge = 3600)
public class StsController {
    
    private final IStsService stsService;
    
    @GetMapping("/token")
    @ApiOperation(value = "获取STS临时访问凭证", notes = "用于前端直传OSS，避免大文件通过服务器中转")
    public R<StsTokenResponse> getStsToken(
            @ApiParam(value = "文件分类", required = true, allowableValues = "image,video,audio,document")
            @RequestParam String category) {
        
        try {
            log.info("获取STS临时凭证，文件分类: {}", category);
            
            // 验证文件分类
            if (!isValidCategory(category)) {
                return R.error("不支持的文件分类: " + category);
            }

            StsTokenResponse stsToken = stsService.getStsToken(category);

            log.info("STS临时凭证获取成功，过期时间: {}", stsToken.getExpiration());
            return R.ok(stsToken);
            
        } catch (Exception e) {
            log.error("获取STS临时凭证失败: {}", e.getMessage(), e);
            return R.error("获取STS临时凭证失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/token/custom")
    @ApiOperation(value = "获取自定义STS临时访问凭证", notes = "支持自定义路径前缀和文件大小限制")
    public R<StsTokenResponse> getCustomStsToken(
            @ApiParam(value = "文件分类", required = true)
            @RequestParam String category,
            @ApiParam(value = "路径前缀", required = false)
            @RequestParam(required = false) String pathPrefix,
            @ApiParam(value = "最大文件大小（字节）", required = false)
            @RequestParam(required = false) Long maxFileSize,
            @ApiParam(value = "凭证有效期（秒）", required = false)
            @RequestParam(required = false) Long durationSeconds) {
        
        try {
            log.info("获取自定义STS临时凭证，分类: {}, 路径前缀: {}, 最大大小: {}, 有效期: {}", 
                    category, pathPrefix, maxFileSize, durationSeconds);
            
            // 验证文件分类
            if (!isValidCategory(category)) {
                return R.error("不支持的文件分类: " + category);
            }
            
            StsTokenResponse stsToken;
            if (pathPrefix != null && maxFileSize != null && durationSeconds != null) {
                stsToken = stsService.getStsToken(category, pathPrefix, maxFileSize, durationSeconds);
            } else if (pathPrefix != null) {
                stsToken = stsService.getStsToken(category, pathPrefix);
            } else {
                stsToken = stsService.getStsToken(category);
            }
            
            log.info("自定义STS临时凭证获取成功");
            return R.ok(stsToken);
            
        } catch (Exception e) {
            log.error("获取自定义STS临时凭证失败: {}", e.getMessage(), e);
            return R.error("获取自定义STS临时凭证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证文件分类是否有效
     */
    private boolean isValidCategory(String category) {
        return category != null && 
               ("image".equalsIgnoreCase(category) || 
                "video".equalsIgnoreCase(category) || 
                "audio".equalsIgnoreCase(category) || 
                "document".equalsIgnoreCase(category));
    }
}
