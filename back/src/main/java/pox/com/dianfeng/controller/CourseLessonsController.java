package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.CourseLessons;
import pox.com.dianfeng.service.ICourseLessonsService;

import java.util.List;

/**
 * <p>
 * 课时信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "课时管理")
@RestController
@RequestMapping("/course-lessons")
public class CourseLessonsController extends BaseController<ICourseLessonsService, CourseLessons> {

    /**
     * 根据章节ID获取课时列表
     */
    @ApiOperation("根据章节ID获取课时列表")
    @GetMapping("/getByChapterId/{chapterId}")
    public R<List<CourseLessons>> getByChapterId(@PathVariable Integer chapterId) {
        QueryWrapper<CourseLessons> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("chapter_id", chapterId)
                .eq("is_del", 0)
                .orderByAsc("sort_order");

        List<CourseLessons> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 根据课程ID获取所有课时
     */
    @ApiOperation("根据课程ID获取所有课时")
    @GetMapping("/getByCourseId/{courseId}")
    public R<List<CourseLessons>> getByCourseId(@PathVariable Integer courseId) {
        QueryWrapper<CourseLessons> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId)
                .eq("is_del", 0)
                .orderByAsc("sort_order");

        List<CourseLessons> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取免费课时列表
     */
    @ApiOperation("获取免费课时列表")
    @GetMapping("/getFreeLessons/{courseId}")
    public R<List<CourseLessons>> getFreeLessons(@PathVariable Integer courseId) {
        QueryWrapper<CourseLessons> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId)
                .eq("is_free", 1)
                .eq("is_del", 0)
                .orderByAsc("sort_order");

        List<CourseLessons> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<CourseLessons> getQueryWrapper(CourseLessons entity) {
        QueryWrapper<CourseLessons> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果课时实体不为空
        if (entity != null) {
            // 课程ID查询
            if (entity.getCourseId() != null) {
                queryWrapper.eq("course_id", entity.getCourseId());
            }

            // 章节ID查询
            if (entity.getChapterId() != null) {
                queryWrapper.eq("chapter_id", entity.getChapterId());
            }

            // 课时标题模糊查询
            if (entity.getTitle() != null && !entity.getTitle().isEmpty()) {
                queryWrapper.like("title", entity.getTitle());
            }

            // 是否免费查询
            if (entity.getIsFree() != null) {
                queryWrapper.eq("is_free", entity.getIsFree());
            }
        }

        // 默认按排序值正序排列
        queryWrapper.orderByAsc("sort_order");

        return queryWrapper;
    }
}
