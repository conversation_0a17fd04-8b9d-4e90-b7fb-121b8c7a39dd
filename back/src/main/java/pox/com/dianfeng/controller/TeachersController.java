package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.PageParam;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.Teachers;
import pox.com.dianfeng.service.ITeachersService;
import pox.com.dianfeng.service.OssUrlService;

import java.util.List;

/**
 * <p>
 * 讲师信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "讲师管理")
@RestController
@RequestMapping("/teachers")
public class TeachersController extends BaseController<ITeachersService, Teachers> {

    @Autowired
    private OssUrlService ossUrlService;

    /**
     * 获取推荐讲师列表
     */
    @ApiOperation("获取推荐讲师列表")
    @GetMapping("/recommended")
    public R<List<Teachers>> getRecommendedTeachers() {
        QueryWrapper<Teachers> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .eq("status", 1)
                .orderByDesc("rating", "student_count")
                .last("limit 6"); // 获取前6位讲师

        List<Teachers> list = service.list(queryWrapper);

        // 后置处理
        postProcessListResult(list);

        return R.ok(list);
    }

    /**
     * 根据评分获取讲师列表
     */
    @ApiOperation("根据评分获取讲师列表")
    @GetMapping("/getByRating")
    public R<IPage<Teachers>> getByRating(Integer pageNum, Integer pageSize) {
        Page<Teachers> page = new Page<>(pageNum != null ? pageNum : 1, pageSize != null ? pageSize : 10);

        QueryWrapper<Teachers> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .eq("status", 1)
                .orderByDesc("rating");

        IPage<Teachers> pageResult = service.page(page, queryWrapper);

        // 后置处理
        postProcessPageResult(pageResult);

        return R.ok(pageResult);
    }

    /**
     * 根据学生数量获取讲师列表
     */
    @ApiOperation("根据学生数量获取讲师列表")
    @GetMapping("/getByStudentCount")
    public R<IPage<Teachers>> getByStudentCount(Integer pageNum, Integer pageSize) {
        Page<Teachers> page = new Page<>(pageNum != null ? pageNum : 1, pageSize != null ? pageSize : 10);

        QueryWrapper<Teachers> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .eq("status", 1)
                .orderByDesc("student_count");

        IPage<Teachers> pageResult = service.page(page, queryWrapper);

        // 后置处理
        postProcessPageResult(pageResult);

        return R.ok(pageResult);
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<Teachers> getQueryWrapper(Teachers entity) {
        QueryWrapper<Teachers> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果讲师实体不为空
        if (entity != null) {
            // 讲师姓名模糊查询
            if (entity.getName() != null && !entity.getName().isEmpty()) {
                queryWrapper.like("name", entity.getName());
            }

            // 讲师职称模糊查询
            if (entity.getTitle() != null && !entity.getTitle().isEmpty()) {
                queryWrapper.like("title", entity.getTitle());
            }

            // 专业领域模糊查询
            if (entity.getExpertise() != null && !entity.getExpertise().isEmpty()) {
                queryWrapper.like("expertise", entity.getExpertise());
            }

        }

        // 默认按评分和学生数量倒序排列
        queryWrapper.orderByDesc("rating", "student_count");

        return queryWrapper;
    }

    /**
     * 重写单个实体后置处理，设置头像完整URL
     */
    @Override
    public Teachers postProcessSingleResult(Teachers teacher) {
        if (teacher != null) {
            ossUrlService.setTeacherFullUrls(teacher);
        }
        return teacher;
        
    }
}
