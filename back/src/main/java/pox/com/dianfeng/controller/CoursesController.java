package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import pox.com.dianfeng.common.PageParam;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.dto.CourseListDto;

import pox.com.dianfeng.entity.Courses;

import pox.com.dianfeng.entity.Teachers;
import pox.com.dianfeng.entity.dto.CourseWithTeacherDTO;

import pox.com.dianfeng.service.ICoursesService;
import pox.com.dianfeng.service.ITagConfigsService;
import pox.com.dianfeng.service.ITeachersService;
import pox.com.dianfeng.service.ICourseChaptersService;
import pox.com.dianfeng.service.ICourseLessonsService;
import pox.com.dianfeng.entity.CourseChapters;
import pox.com.dianfeng.entity.CourseLessons;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import java.util.stream.Collectors;

/**
 * <p>
 * 课程信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "课程CRUD管理")
@RestController
@RequestMapping("/courses-manage")
public class CoursesController extends BaseController<ICoursesService, Courses> {

    @Autowired
    private ITeachersService teachersService;



    @Autowired
    private ITagConfigsService tagConfigsService;

    @Autowired
    private ICourseChaptersService courseChaptersService;

    @Autowired
    private ICourseLessonsService courseLessonsService;

    /**
     * 根据课程状态获取课程列表
     */
    @ApiOperation("根据课程状态获取课程列表")
    @GetMapping("/getByStatus/{status}")
    public R<List<Courses>> getByStatus(@PathVariable Integer status) {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<Courses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 根据价格范围获取课程
     */
    @ApiOperation("根据价格范围获取课程")
    @GetMapping("/getByPriceRange")
    public R<List<Courses>> getByPriceRange(Double minPrice, Double maxPrice) {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1) // 上架状态
                .eq("is_del", 0);

        if (minPrice != null) {
            queryWrapper.ge("price", minPrice);
        }

        if (maxPrice != null) {
            queryWrapper.le("price", maxPrice);
        }

        queryWrapper.orderByAsc("price");

        List<Courses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 根据难度级别获取课程
     */
    @ApiOperation("根据难度级别获取课程")
    @GetMapping("/getByLevel/{level}")
    public R<List<Courses>> getByLevel(@PathVariable Integer level) {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("level", level)
                .eq("status", 1)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<Courses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 根据年龄段获取课程
     */
    @ApiOperation("根据年龄段获取课程")
    @GetMapping("/getByAgeGroup/{ageGroup}")
    public R<List<Courses>> getByAgeGroup(@PathVariable Integer ageGroup) {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("age_group", ageGroup)
                .eq("status", 1)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<Courses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取特训营课程
     */
    @ApiOperation("获取特训营课程")
    @GetMapping("/getSpecialTrainingCourses")
    public R<List<Courses>> getSpecialTrainingCourses() {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_special_training", 1)
                .eq("status", 1)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<Courses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取一对一课程
     */
    @ApiOperation("获取一对一课程")
    @GetMapping("/getOneOnOneCourses")
    public R<List<Courses>> getOneOnOneCourses() {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_one_on_one", 1)
                .eq("status", 1)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<Courses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 分页查询课程及关联的讲师和分类信息
     */
    @ApiOperation("分页查询课程及关联信息")
    @GetMapping("/page/with-teacher")
    public R<IPage<CourseWithTeacherDTO>> pageWithTeacher(PageParam pageParam, Courses entity) {
        // 创建分页对象
        Page<Courses> page = new Page<>(pageParam.getPageNum(), pageParam.getPageSize());

        // 执行关联查询
        IPage<CourseWithTeacherDTO> result = service.pageWithTeacher(page, entity);

        return R.ok(result);
    }

    /**
     * 根据ID查询课程及关联的讲师和分类信息
     */
    @ApiOperation("根据ID查询课程及关联信息")
    @GetMapping("/with-teacher/{id}")
    public R<CourseWithTeacherDTO> getWithTeacherById(@PathVariable Integer id) {
        CourseWithTeacherDTO result = service.getWithTeacherById(id);
        return result != null ? R.ok(result) : R.error("课程不存在");
    }

    /**
     * 获取课程详情（包含关联信息）
     */
    @ApiOperation("根据ID获取课程详情（包含关联信息）")
    @GetMapping("/detail/{id}")
    public R<CourseWithTeacherDTO> getCourseDetail(@PathVariable Integer id) {
        CourseWithTeacherDTO result = service.getWithTeacherById(id);
        if (result == null) {
            return R.error("课程不存在");
        }
        return R.ok(result);
    }

    /**
     * 获取课程树形结构（章节+课时）
     */
    @ApiOperation("获取课程树形结构")
    @GetMapping("/{courseId}/tree")
    public R<List<Map<String, Object>>> getCourseTree(@PathVariable Integer courseId) {
        try {
            // 获取课程章节
            QueryWrapper<CourseChapters> chapterWrapper = new QueryWrapper<>();
            chapterWrapper.eq("course_id", courseId)
                    .eq("is_del", 0)
                    .orderByAsc("sort_order", "id");
            List<CourseChapters> chapters = courseChaptersService.list(chapterWrapper);

            // 构建树形结构
            List<Map<String, Object>> treeData = chapters.stream().map(chapter -> {
                Map<String, Object> chapterNode = new java.util.HashMap<>();
                chapterNode.put("id", chapter.getId());
                chapterNode.put("title", chapter.getTitle());
                chapterNode.put("type", "chapter");
                chapterNode.put("sortOrder", chapter.getSortOrder());

                // 获取该章节下的课时
                QueryWrapper<CourseLessons> lessonWrapper = new QueryWrapper<>();
                lessonWrapper.eq("chapter_id", chapter.getId())
                        .eq("is_del", 0)
                        .orderByAsc("sort_order", "id");
                List<CourseLessons> lessons = courseLessonsService.list(lessonWrapper);

                List<Map<String, Object>> lessonNodes = lessons.stream().map(lesson -> {
                    Map<String, Object> lessonNode = new java.util.HashMap<>();
                    lessonNode.put("id", lesson.getId());
                    lessonNode.put("title", lesson.getTitle());
                    lessonNode.put("type", "lesson");
                    lessonNode.put("chapterId", lesson.getChapterId());
                    lessonNode.put("sortOrder", lesson.getSortOrder());
                    lessonNode.put("duration", lesson.getDuration());
                    return lessonNode;
                }).collect(Collectors.toList());

                chapterNode.put("lessons", lessonNodes);
                return chapterNode;
            }).collect(Collectors.toList());

            return R.ok(treeData);
        } catch (Exception e) {
            return R.error("获取课程结构失败：" + e.getMessage());
        }
    }

    /**
     * 增强的分页查询，支持多种筛选条件和连表查询
     */
    @ApiOperation("增强的分页查询")
    @GetMapping("/page/enhanced")
    public R<IPage<CourseListDto>> pageEnhanced(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer teacherId,
            @RequestParam(required = false) String categoryIds,
            @RequestParam(required = false) String ageGroupIds,
            @RequestParam(required = false) String levelIds,
            @RequestParam(required = false) String statusIds,
            @RequestParam(required = false) Boolean isFree,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice,
            @RequestParam(required = false) Boolean isLive,
            @RequestParam(required = false) Boolean isFeatured,
            @RequestParam(required = false) Boolean isSpecialTraining,
            @RequestParam(required = false) Boolean isOneOnOne) {
        
        // 创建分页对象
        Page<Courses> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        QueryWrapper<Courses> queryWrapper = buildEnhancedQueryWrapper(
                title, teacherId, categoryIds, ageGroupIds, levelIds, statusIds,
                isFree, minPrice, maxPrice, isLive, isFeatured, isSpecialTraining, isOneOnOne);

        // 执行分页查询
        IPage<Courses> coursePage = service.page(page, queryWrapper);

        // 转换为DTO并填充关联信息
        IPage<CourseListDto> result = convertToCourseDtoPage(coursePage);

        return R.ok(result);
    }

    /**
     * 构建增强的查询条件
     */
    private QueryWrapper<Courses> buildEnhancedQueryWrapper(
            String title, Integer teacherId, String categoryIds, String ageGroupIds,
            String levelIds, String statusIds, Boolean isFree, Double minPrice,
            Double maxPrice, Boolean isLive, Boolean isFeatured,
            Boolean isSpecialTraining, Boolean isOneOnOne) {
        
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 课程标题模糊查询
        if (title != null && !title.isEmpty()) {
            queryWrapper.like("title", title);
        }

        // 讲师ID查询
        if (teacherId != null) {
            queryWrapper.eq("teacher_id", teacherId);
        }

        // 多选分类筛选
        if (categoryIds != null && !categoryIds.isEmpty()) {
            String[] categoryArray = categoryIds.split(",");
            if (categoryArray.length > 0) {
                queryWrapper.in("category_id", Arrays.asList(categoryArray));
            }
        }

        // 多选年龄段筛选
        if (ageGroupIds != null && !ageGroupIds.isEmpty()) {
            String[] ageGroupArray = ageGroupIds.split(",");
            if (ageGroupArray.length > 0) {
                queryWrapper.in("age_group", Arrays.asList(ageGroupArray));
            }
        }

        // 多选难度级别筛选
        if (levelIds != null && !levelIds.isEmpty()) {
            String[] levelArray = levelIds.split(",");
            if (levelArray.length > 0) {
                queryWrapper.in("level", Arrays.asList(levelArray));
            }
        }

        // 多选状态筛选
        if (statusIds != null && !statusIds.isEmpty()) {
            String[] statusArray = statusIds.split(",");
            if (statusArray.length > 0) {
                queryWrapper.in("status", Arrays.asList(statusArray));
            }
        }

        // 免费课程筛选
        if (isFree != null && isFree) {
            queryWrapper.and(wrapper -> wrapper.eq("price", 0).or().isNull("price"));
        }

        // 价格范围筛选
        if (minPrice != null && minPrice >= 0) {
            queryWrapper.ge("price", minPrice);
        }

        if (maxPrice != null && maxPrice >= 0) {
            queryWrapper.le("price", maxPrice);
        }

        // 课程类型筛选
        if (isLive != null) {
            queryWrapper.eq("is_live", isLive);
        }

        if (isFeatured != null) {
            queryWrapper.eq("is_featured", isFeatured);
        }

        if (isSpecialTraining != null) {
            queryWrapper.eq("is_special_training", isSpecialTraining);
        }

        if (isOneOnOne != null) {
            queryWrapper.eq("is_one_on_one", isOneOnOne);
        }

        // 默认排序：创建时间倒序
        queryWrapper.orderByDesc("created_at");

        return queryWrapper;
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<Courses> getQueryWrapper(Courses entity) {
        QueryWrapper<Courses> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果课程实体不为空
        if (entity != null) {
            // 课程标题模糊查询
            if (entity.getTitle() != null && !entity.getTitle().isEmpty()) {
                queryWrapper.like("title", entity.getTitle());
            }

            // 讲师ID查询
            if (entity.getTeacherId() != null) {
                queryWrapper.eq("teacher_id", entity.getTeacherId());
            }

            // 分类ID查询
            if (entity.getCategoryId() != null) {
                queryWrapper.eq("category_id", entity.getCategoryId());
            }

            // 课程难度查询
            if (entity.getLevel() != null) {
                queryWrapper.eq("level", entity.getLevel());
            }

            // 年龄段查询
            if (entity.getAgeGroup() != null) {
                queryWrapper.eq("age_group", entity.getAgeGroup());
            }

            // 是否直播
            if (entity.getIsLive() != null) {
                queryWrapper.eq("is_live", entity.getIsLive());
            }

            // 是否推荐
            if (entity.getIsFeatured() != null) {
                queryWrapper.eq("is_featured", entity.getIsFeatured());
            }

            // 是否特训营
            if (entity.getIsSpecialTraining() != null) {
                queryWrapper.eq("is_special_training", entity.getIsSpecialTraining());
            }

            // 是否一对一
            if (entity.getIsOneOnOne() != null) {
                queryWrapper.eq("is_one_on_one", entity.getIsOneOnOne());
            }

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        return queryWrapper;
    }

    /**
     * 转换分页结果为CourseListDto
     */
    private IPage<CourseListDto> convertToCourseDtoPage(IPage<Courses> coursePage) {
        Page<CourseListDto> dtoPage = new Page<>(coursePage.getCurrent(), coursePage.getSize(), coursePage.getTotal());

        List<CourseListDto> courseDtoList = coursePage.getRecords().stream()
                .map(this::convertToCourseDto)
                .collect(Collectors.toList());

        dtoPage.setRecords(courseDtoList);
        return dtoPage;
    }

    /**
     * 转换单个课程为CourseListDto
     */
    private CourseListDto convertToCourseDto(Courses course) {
        CourseListDto dto = CourseListDto.fromCourse(course);

        // 获取讲师信息
        if (course.getTeacherId() != null) {
            Teachers teacher = teachersService.getById(course.getTeacherId());
            if (teacher != null) {
                dto.setTeacherName(teacher.getName());
                dto.setTeacherAvatar(teacher.getAvatar());
            }
        }

        // 获取分类信息
        if (course.getCategoryId() != null) {
            String categoryName = getCategoryLabel(course.getCategoryId());
            dto.setCategoryName(categoryName);
        }

        // 获取难度级别标签
        if (course.getLevel() != null) {
            dto.setLevelLabel(getLevelLabel(course.getLevel()));
        }

        // 获取年龄段标签
        if (course.getAgeGroup() != null) {
            dto.setAgeGroupLabel(getAgeGroupLabel(course.getAgeGroup()));
        }

        // 生成完整的封面图片URL
        dto.generateFullCoverImageUrl();

        return dto;
    }

    /**
     * 获取难度级别标签
     */
    private String getLevelLabel(Integer level) {
        return tagConfigsService.getTagLabel("level", level);
    }

    /**
     * 获取年龄段标签
     */
    private String getAgeGroupLabel(Integer ageGroup) {
        return tagConfigsService.getTagLabel("age_group", ageGroup);
    }

    /**
     * 获取分类标签
     */
    private String getCategoryLabel(Integer categoryId) {
        return tagConfigsService.getTagLabel("course_category", categoryId);
    }
}
