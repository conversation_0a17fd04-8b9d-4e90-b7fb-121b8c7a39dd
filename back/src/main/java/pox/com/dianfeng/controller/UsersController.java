package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.Users;
import pox.com.dianfeng.service.IUsersService;
import pox.com.dianfeng.service.OssUrlService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "用户CRUD管理")
@RestController
@RequestMapping("/users-manage")
public class UsersController extends BaseController<IUsersService, Users> {

    @Autowired
    private OssUrlService ossUrlService;

    /**
     * 根据用户状态获取用户列表
     */
    @ApiOperation("根据用户状态获取用户列表")
    @GetMapping("/getByStatus/{status}")
    public R<List<Users>> getByStatus(@PathVariable Integer status) {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<Users> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 启用用户
     */
    @ApiOperation("启用用户")
    @PutMapping("/enable/{id}")
    public R<Boolean> enableUser(@PathVariable Integer id) {
        Users user = service.getById(id);
        if (user == null || Integer.valueOf(1).equals(user.getIsDel())) {
            return R.error("用户不存在");
        }

        user.setStatus(true);
        user.setUpdatedAt(LocalDateTime.now());

        boolean success = service.updateById(user);

        return success ? R.ok() : R.error("启用用户失败");
    }

    /**
     * 禁用用户
     */
    @ApiOperation("禁用用户")
    @PutMapping("/disable/{id}")
    public R<Boolean> disableUser(@PathVariable Integer id) {
        Users user = service.getById(id);
        if (user == null || Integer.valueOf(1).equals(user.getIsDel())) {
            return R.error("用户不存在");
        }

        user.setStatus(false);
        user.setUpdatedAt(LocalDateTime.now());

        boolean success = service.updateById(user);

        return success ? R.ok() : R.error("禁用用户失败");
    }

    /**
     * 批量删除用户（逻辑删除）
     */
    @ApiOperation("批量删除用户")
    @DeleteMapping("/batchDelete")
    public R<Boolean> batchDeleteUsers(@RequestBody List<Integer> ids) {
        // 逻辑删除
        boolean success = service.lambdaUpdate()
                .set(Users::getIsDel, 1)
                .set(Users::getUpdatedAt, LocalDateTime.now())
                .in(Users::getId, ids)
                .update();

        return success ? R.ok() : R.error("批量删除用户失败");
    }

    /**
     * 搜索用户
     */
    @ApiOperation("搜索用户")
    @GetMapping("/search")
    public R<List<Users>> searchUsers(String keyword) {
        if (keyword == null || keyword.isEmpty()) {
            return R.error("搜索关键词不能为空");
        }

        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0)
                .and(w -> w.like("username", keyword)
                        .or().like("nickname", keyword)
                        .or().like("phone", keyword)
                        .or().like("email", keyword));

        List<Users> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<Users> getQueryWrapper(Users entity) {
        QueryWrapper<Users> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果用户实体不为空
        if (entity != null) {
            // 用户名模糊查询
            if (entity.getUsername() != null && !entity.getUsername().isEmpty()) {
                queryWrapper.like("username", entity.getUsername());
            }

            // 昵称模糊查询
            if (entity.getNickname() != null && !entity.getNickname().isEmpty()) {
                queryWrapper.like("nickname", entity.getNickname());
            }
            if (entity.getSearchKeyWord() != null && !entity.getSearchKeyWord().isEmpty()) {
                queryWrapper.like("username", entity.getSearchKeyWord())
                        .or().like("nickname", entity.getSearchKeyWord());
            }

            // 手机号精确查询
            if (entity.getPhone() != null && !entity.getPhone().isEmpty()) {
                queryWrapper.eq("phone", entity.getPhone());
            }

            // 邮箱精确查询
            if (entity.getEmail() != null && !entity.getEmail().isEmpty()) {
                queryWrapper.eq("email", entity.getEmail());
            }

            //// 性别查询
            // if (entity.getGender() != null) {
            // queryWrapper.eq("gender", entity.getGender());
            // }

            // 学校查询
            if (entity.getSchool() != null && !entity.getSchool().isEmpty()) {
                queryWrapper.like("school", entity.getSchool());
            }

            // 用户所在地查询
            if (entity.getLocation() != null && !entity.getLocation().isEmpty()) {
                queryWrapper.like("location", entity.getLocation());
            }

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        return queryWrapper;
    }

    /**
     * 重写单个实体后置处理，设置头像完整URL
     */
    @Override
    public Users postProcessSingleResult(Users user) {
        if (user != null) {
            user.setAvatarFullUrl(ossUrlService.buildFullUrl(user.getAvatar()));
        }
        return user;
    }
}
