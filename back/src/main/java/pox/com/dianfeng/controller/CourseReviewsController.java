package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.CourseReviews;
import pox.com.dianfeng.service.ICourseReviewsService;

import java.util.List;

/**
 * <p>
 * 课程评价表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "课程评价管理")
@RestController
@RequestMapping("/course-reviews")
public class CourseReviewsController extends BaseController<ICourseReviewsService, CourseReviews> {

    /**
     * 根据课程ID获取评价列表
     */
    @ApiOperation("根据课程ID获取评价列表")
    @GetMapping("/getByCourseId/{courseId}")
    public R<List<CourseReviews>> getByCourseId(@PathVariable Integer courseId) {
        QueryWrapper<CourseReviews> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId)
                .eq("status", 1)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<CourseReviews> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 根据用户ID获取评价列表
     */
    @ApiOperation("根据用户ID获取评价列表")
    @GetMapping("/getByUserId/{userId}")
    public R<List<CourseReviews>> getByUserId(@PathVariable Integer userId) {
        QueryWrapper<CourseReviews> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<CourseReviews> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 根据评分获取评价列表
     */
    @ApiOperation("根据评分获取评价列表")
    @GetMapping("/getByRating/{courseId}/{rating}")
    public R<List<CourseReviews>> getByRating(@PathVariable Integer courseId, @PathVariable Integer rating) {
        QueryWrapper<CourseReviews> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId)
                .eq("rating", rating)
                .eq("status", 1)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<CourseReviews> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<CourseReviews> getQueryWrapper(CourseReviews entity) {
        QueryWrapper<CourseReviews> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果评价实体不为空
        if (entity != null) {
            // 课程ID查询
            if (entity.getCourseId() != null) {
                queryWrapper.eq("course_id", entity.getCourseId());
            }

            // 用户ID查询
            if (entity.getUserId() != null) {
                queryWrapper.eq("user_id", entity.getUserId());
            }

            // 评分查询
            if (entity.getRating() != null) {
                queryWrapper.eq("rating", entity.getRating());
            }

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        // 默认按创建时间倒序排列
        queryWrapper.orderByDesc("created_at");

        return queryWrapper;
    }
}
