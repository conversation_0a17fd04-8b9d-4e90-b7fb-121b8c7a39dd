package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.UserLearningRecords;
import pox.com.dianfeng.service.IUserLearningRecordsService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户学习记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "用户学习记录管理")
@RestController
@RequestMapping("/user-learning-records")
public class UserLearningRecordsController extends BaseController<IUserLearningRecordsService, UserLearningRecords> {

    /**
     * 获取用户课程学习记录
     */
    @ApiOperation("获取用户课程学习记录")
    @GetMapping("/getByUserAndCourse")
    public R<List<UserLearningRecords>> getByUserAndCourse(Integer userId, Integer courseId) {
        QueryWrapper<UserLearningRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("course_id", courseId)
                .eq("is_del", 0)
                .orderByAsc("lesson_id");

        List<UserLearningRecords> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取用户课时学习记录
     */
    @ApiOperation("获取用户课时学习记录")
    @GetMapping("/getByUserAndLesson")
    public R<UserLearningRecords> getByUserAndLesson(Integer userId, Integer lessonId) {
        QueryWrapper<UserLearningRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("lesson_id", lessonId)
                .eq("is_del", 0);

        UserLearningRecords record = service.getOne(queryWrapper);

        return record != null ? R.ok(record) : R.error("学习记录不存在");
    }

    /**
     * 更新学习进度
     */
    @ApiOperation("更新学习进度")
    @PostMapping("/updateProgress")
    public R<Boolean> updateProgress(@RequestBody UserLearningRecords record) {
        // 查询是否存在记录
        QueryWrapper<UserLearningRecords> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", record.getUserId())
                .eq("lesson_id", record.getLessonId())
                .eq("is_del", 0);

        UserLearningRecords existRecord = service.getOne(queryWrapper);

        if (existRecord != null) {
            // 更新记录
            existRecord.setProgress(record.getProgress());
            existRecord.setLastLearnTime(LocalDateTime.now());

            // 计算进度比例
            if (existRecord.getDuration() > 0) {
                BigDecimal progressRate = BigDecimal
                        .valueOf((double) existRecord.getProgress() / existRecord.getDuration() * 100);
                existRecord.setProgressRate(
                        progressRate.compareTo(BigDecimal.valueOf(100)) > 0 ? BigDecimal.valueOf(100) : progressRate);
            }

            // 判断是否完成
            if (existRecord.getProgress() >= existRecord.getDuration()) {
                existRecord.setIsCompleted(true);
            }

            return service.updateById(existRecord) ? R.ok() : R.error("更新学习进度失败");
        } else {
            // 新增记录
            record.setLastLearnTime(LocalDateTime.now());

            // 计算进度比例
            if (record.getDuration() > 0) {
                BigDecimal progressRate = BigDecimal
                        .valueOf((double) record.getProgress() / record.getDuration() * 100);
                record.setProgressRate(
                        progressRate.compareTo(BigDecimal.valueOf(100)) > 0 ? BigDecimal.valueOf(100) : progressRate);
            }

            // 判断是否完成
            if (record.getProgress() >= record.getDuration()) {
                record.setIsCompleted(true);
            }

            return service.save(record) ? R.ok() : R.error("保存学习进度失败");
        }
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<UserLearningRecords> getQueryWrapper(UserLearningRecords entity) {
        QueryWrapper<UserLearningRecords> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果学习记录实体不为空
        if (entity != null) {
            // 用户ID查询
            if (entity.getUserId() != null) {
                queryWrapper.eq("user_id", entity.getUserId());
            }

            // 课程ID查询
            if (entity.getCourseId() != null) {
                queryWrapper.eq("course_id", entity.getCourseId());
            }

            // 课时ID查询
            if (entity.getLessonId() != null) {
                queryWrapper.eq("lesson_id", entity.getLessonId());
            }

            // 是否完成查询
            if (entity.getIsCompleted() != null) {
                queryWrapper.eq("is_completed", entity.getIsCompleted());
            }
        }

        return queryWrapper;
    }
}
