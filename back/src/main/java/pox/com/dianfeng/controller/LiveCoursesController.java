package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.PageParam;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.LiveCourses;
import pox.com.dianfeng.entity.Teachers;
import pox.com.dianfeng.entity.dto.LiveCourseWithTeacherDTO;
import pox.com.dianfeng.service.ILiveCoursesService;
import pox.com.dianfeng.service.ITeachersService;
import pox.com.dianfeng.service.ITagConfigsService;
import pox.com.dianfeng.service.OssUrlService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 直播通知表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "直播课程管理")
@RestController
@RequestMapping("/live-courses")
public class LiveCoursesController extends BaseController<ILiveCoursesService, LiveCourses> {

    @Autowired
    private ITeachersService teachersService;

    @Autowired
    private ITagConfigsService tagConfigsService;

    @Autowired
    private OssUrlService ossUrlService;

    // 直播状态常量
    private static final Integer STATUS_NOT_STARTED = 0;  // 未开始
    private static final Integer STATUS_LIVING = 1;       // 直播中
    private static final Integer STATUS_ENDED = 2;        // 已结束

    /**
     * 获取正在直播的课程
     */
    @ApiOperation("获取正在直播的课程")
    @GetMapping("/getLivingCourses")
    public R<List<LiveCourses>> getLivingCourses() {
        QueryWrapper<LiveCourses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", STATUS_LIVING) // 直播中
                .eq("is_del", 0)
                .orderByAsc("start_time");

        List<LiveCourses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取即将开始的直播课程
     */
    @ApiOperation("获取即将开始的直播课程")
    @GetMapping("/getUpcomingCourses")
    public R<List<LiveCourses>> getUpcomingCourses() {
        LocalDateTime now = LocalDateTime.now();

        QueryWrapper<LiveCourses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", STATUS_NOT_STARTED) // 未开始
                .gt("start_time", now) // 开始时间大于当前时间
                .eq("is_del", 0)
                .orderByAsc("start_time");

        List<LiveCourses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 获取已结束的直播课程
     */
    @ApiOperation("获取已结束的直播课程")
    @GetMapping("/getEndedCourses")
    public R<List<LiveCourses>> getEndedCourses() {
        QueryWrapper<LiveCourses> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", STATUS_ENDED) // 已结束
                .eq("is_del", 0)
                .orderByDesc("end_time");

        List<LiveCourses> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 分页查询直播课程及关联的讲师和分类信息
     */
    @ApiOperation("分页查询直播课程及关联信息")
    @GetMapping("/page/with-teacher")
    public R<IPage<LiveCourseWithTeacherDTO>> pageWithTeacher(PageParam pageParam, LiveCourses entity) {
        // 创建分页对象
        Page<LiveCourses> page = new Page<>(pageParam.getPageNum(), pageParam.getPageSize());

        // 执行关联查询
        IPage<LiveCourseWithTeacherDTO> result = service.pageWithTeacher(page, entity);

        return R.ok(result);
    }

    /**
     * 根据ID查询直播课程及关联的讲师和分类信息
     */
    @ApiOperation("根据ID查询直播课程及关联信息")
    @GetMapping("/with-teacher/{id}")
    public R<LiveCourseWithTeacherDTO> getWithTeacherById(@PathVariable Integer id) {
        LiveCourseWithTeacherDTO result = service.getWithTeacherById(id);
        return result != null ? R.ok(result) : R.error("直播课程不存在");
    }

    /**
     * 根据ID获取直播课程详情
     */
    @ApiOperation("根据ID获取直播课程详情")
    @GetMapping("/detail/{id}")
    public R<LiveCourses> getDetail(@PathVariable Integer id) {
        LiveCourses liveCourse = service.getById(id);

        if (liveCourse == null || Boolean.TRUE.equals(liveCourse.getIsDel())) {
            return R.error("直播课程不存在");
        }

        return R.ok(liveCourse);
    }

    /**
     * 手动开始直播
     */
    @ApiOperation("手动开始直播")
    @PutMapping("/start/{id}")
    public R<String> startLive(@PathVariable Integer id) {
        try {
            LiveCourses liveCourse = service.getById(id);
            if (liveCourse == null || Boolean.TRUE.equals(liveCourse.getIsDel())) {
                return R.error("直播课程不存在");
            }

            if (liveCourse.getStatus().equals(STATUS_LIVING)) {
                return R.error("直播已经在进行中");
            }

            if (liveCourse.getStatus().equals(STATUS_ENDED)) {
                return R.error("直播已结束，无法重新开始");
            }

            // 更新状态为直播中
            liveCourse.setStatus(STATUS_LIVING);
            boolean success = service.updateById(liveCourse);

            if (success) {
                return R.ok("直播已开始");
            } else {
                return R.error("开始直播失败");
            }
        } catch (Exception e) {
            return R.error("开始直播失败：" + e.getMessage());
        }
    }

    /**
     * 手动结束直播
     */
    @ApiOperation("手动结束直播")
    @PutMapping("/end/{id}")
    public R<String> endLive(@PathVariable Integer id) {
        try {
            LiveCourses liveCourse = service.getById(id);
            if (liveCourse == null || Boolean.TRUE.equals(liveCourse.getIsDel())) {
                return R.error("直播课程不存在");
            }

            if (liveCourse.getStatus().equals(STATUS_ENDED)) {
                return R.error("直播已经结束");
            }

            if (liveCourse.getStatus().equals(STATUS_NOT_STARTED)) {
                return R.error("直播尚未开始，无法结束");
            }

            // 更新状态为已结束
            liveCourse.setStatus(STATUS_ENDED);
            boolean success = service.updateById(liveCourse);

            if (success) {
                return R.ok("直播已结束");
            } else {
                return R.error("结束直播失败");
            }
        } catch (Exception e) {
            return R.error("结束直播失败：" + e.getMessage());
        }
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<LiveCourses> getQueryWrapper(LiveCourses entity) {
        QueryWrapper<LiveCourses> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果直播课程实体不为空
        if (entity != null) {
            // 标题模糊查询
            if (entity.getTitle() != null && !entity.getTitle().isEmpty()) {
                queryWrapper.like("title", entity.getTitle());
            }

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        // 默认按开始时间升序排列
        queryWrapper.orderByAsc("start_time");

        return queryWrapper;
    }

    /**
     * 后置处理单个结果，设置讲师、分类标签和完整URL
     */
    @Override
    public LiveCourses postProcessSingleResult(LiveCourses entity) {
        if (entity == null) {
            return entity;
        }

        // 设置完整URL
        ossUrlService.setLiveCourseFullUrls(entity);

        // 设置讲师标签
        if (entity.getTeacherId() != null) {
            Teachers teacher = teachersService.getById(entity.getTeacherId());
            if (teacher != null) {
                entity.setTeacherLabel(teacher.getName());
                // 同时设置讲师头像完整URL
                ossUrlService.setTeacherFullUrls(teacher);
            }
        }

        // 设置分类标签
        if (entity.getCategoryId() != null) {
            String categoryLabel = tagConfigsService.getTagLabel("course_category", entity.getCategoryId());
            entity.setCategoryLabel(categoryLabel);
        }

        return entity;
    }
}
