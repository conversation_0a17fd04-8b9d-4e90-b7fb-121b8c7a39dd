package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.UserFavorites;
import pox.com.dianfeng.service.IUserFavoritesService;

import java.util.List;

/**
 * <p>
 * 用户收藏表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "用户收藏管理")
@RestController
@RequestMapping("/user-favorites")
public class UserFavoritesController extends BaseController<IUserFavoritesService, UserFavorites> {

    /**
     * 根据用户ID获取收藏列表
     */
    @ApiOperation("根据用户ID获取收藏列表")
    @GetMapping("/getByUserId/{userId}")
    public R<List<UserFavorites>> getByUserId(@PathVariable Integer userId) {
        QueryWrapper<UserFavorites> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("is_del", 0)
                .orderByDesc("created_at");

        List<UserFavorites> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 检查用户是否收藏了课程
     */
    @ApiOperation("检查用户是否收藏了课程")
    @GetMapping("/checkFavorite")
    public R<Boolean> checkFavorite(Integer userId, Integer courseId) {
        QueryWrapper<UserFavorites> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("course_id", courseId)
                .eq("is_del", 0);

        UserFavorites favorite = service.getOne(queryWrapper);

        return R.ok(favorite != null);
    }

    /**
     * 添加收藏
     */
    @ApiOperation("添加收藏")
    @PostMapping("/add")
    public R<Boolean> addFavorite(@RequestBody UserFavorites favorite) {
        // 检查是否已收藏
        QueryWrapper<UserFavorites> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", favorite.getUserId())
                .eq("course_id", favorite.getCourseId())
                .eq("is_del", 0);

        UserFavorites existFavorite = service.getOne(queryWrapper);

        if (existFavorite != null) {
            return R.ok(true, "已经收藏过了");
        }

        boolean success = service.save(favorite);

        return success ? R.ok(true, "收藏成功") : R.error("收藏失败");
    }

    /**
     * 取消收藏
     */
    @ApiOperation("取消收藏")
    @DeleteMapping("/cancel")
    public R<Boolean> cancelFavorite(Integer userId, Integer courseId) {
        QueryWrapper<UserFavorites> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("course_id", courseId);

        boolean success = service.remove(queryWrapper);

        return success ? R.ok(true, "取消收藏成功") : R.error("取消收藏失败");
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<UserFavorites> getQueryWrapper(UserFavorites entity) {
        QueryWrapper<UserFavorites> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果收藏实体不为空
        if (entity != null) {
            // 用户ID查询
            if (entity.getUserId() != null) {
                queryWrapper.eq("user_id", entity.getUserId());
            }

            // 课程ID查询
            if (entity.getCourseId() != null) {
                queryWrapper.eq("course_id", entity.getCourseId());
            }
        }

        // 默认按创建时间倒序排列
        queryWrapper.orderByDesc("created_at");

        return queryWrapper;
    }
}
