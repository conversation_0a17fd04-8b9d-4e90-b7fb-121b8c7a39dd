package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.CourseChapters;
import pox.com.dianfeng.service.ICourseChaptersService;

import java.util.List;

/**
 * <p>
 * 课程章节表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "课程章节管理")
@RestController
@RequestMapping("/course-chapters")
public class CourseChaptersController extends BaseController<ICourseChaptersService, CourseChapters> {

    /**
     * 根据课程ID获取章节列表
     */
    @ApiOperation("根据课程ID获取章节列表")
    @GetMapping("/getByCourseId/{courseId}")
    public R<List<CourseChapters>> getByCourseId(@PathVariable Integer courseId) {
        QueryWrapper<CourseChapters> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("course_id", courseId)
                .eq("is_del", 0)
                .orderByAsc("sort_order");

        List<CourseChapters> list = service.list(queryWrapper);

        return R.ok(list);
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<CourseChapters> getQueryWrapper(CourseChapters entity) {
        QueryWrapper<CourseChapters> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果章节实体不为空
        if (entity != null) {
            // 课程ID查询
            if (entity.getCourseId() != null) {
                queryWrapper.eq("course_id", entity.getCourseId());
            }

            // 章节标题模糊查询
            if (entity.getTitle() != null && !entity.getTitle().isEmpty()) {
                queryWrapper.like("title", entity.getTitle());
            }
        }

        // 默认按排序值正序排列
        queryWrapper.orderByAsc("sort_order");

        return queryWrapper;
    }
}
