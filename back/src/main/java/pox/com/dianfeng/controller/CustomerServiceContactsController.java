package pox.com.dianfeng.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import pox.com.dianfeng.common.PageParam;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.CustomerServiceContacts;
import pox.com.dianfeng.service.ICustomerServiceContactsService;
import pox.com.dianfeng.service.OssUrlService;

import java.util.List;

/**
 * <p>
 * 客服联系信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Api(tags = "客服联系信息管理")
@RestController
@RequestMapping("/customer-service-contacts")
public class CustomerServiceContactsController
        extends BaseController<ICustomerServiceContactsService, CustomerServiceContacts> {

    @Autowired
    private OssUrlService ossUrlService;

    /**
     * 分页查询客服信息（支持搜索）
     */
    @ApiOperation("分页查询客服信息")
    @GetMapping("/page1")
    public R<IPage<CustomerServiceContacts>> page1(PageParam pageParam,
                                                 @RequestParam(required = false) String name,
                                                 @RequestParam(required = false) String contact,
                                                 @RequestParam(required = false) Boolean status) {
        // 创建分页对象
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CustomerServiceContacts> page = 
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageParam.getPageNum(), pageParam.getPageSize());

        // 构建查询条件
        QueryWrapper<CustomerServiceContacts> queryWrapper = new QueryWrapper<>();
        
        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 客服姓名模糊查询
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }

        // 联系方式模糊查询（OR查询：phone OR wechat OR email OR qq）
        if (StringUtils.hasText(contact)) {
            queryWrapper.and(wrapper -> wrapper
                .like("phone", contact)
                .or()
                .like("wechat", contact)
                .or()
                .like("email", contact)
                .or()
                .like("qq", contact)
            );
        }

        // 状态查询
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        // 处理排序
        if (pageParam.getOrderField() != null && !pageParam.getOrderField().isEmpty()) {
            if ("asc".equalsIgnoreCase(pageParam.getOrderType())) {
                queryWrapper.orderByAsc(pageParam.getOrderField());
            } else {
                queryWrapper.orderByDesc(pageParam.getOrderField());
            }
        } else {
            // 默认按创建时间降序
            queryWrapper.orderByDesc("created_at");
        }

        // 执行分页查询
        IPage<CustomerServiceContacts> result = service.page(page, queryWrapper);

        // 后置处理
        postProcessPageResult(result);

        return R.ok(result);
    }

    /**
     * 获取所有可用客服联系信息
     */
    @ApiOperation("获取所有可用客服联系信息")
    @GetMapping("/getAllAvailable")
    public R<List<CustomerServiceContacts>> getAllAvailable() {
        QueryWrapper<CustomerServiceContacts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .eq("is_del", 0)
                .orderByAsc("id");

        List<CustomerServiceContacts> list = service.list(queryWrapper);

        // 后置处理
        postProcessListResult(list);

        return R.ok(list);
    }

    /**
     * 获取随机一个客服联系信息
     */
    @ApiOperation("获取随机一个客服联系信息")
    @GetMapping("/getRandomOne")
    public R<CustomerServiceContacts> getRandomOne() {
        QueryWrapper<CustomerServiceContacts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                .eq("is_del", 0)
                .last("ORDER BY RAND() LIMIT 1");

        CustomerServiceContacts contact = service.getOne(queryWrapper);

        // 后置处理
        if (contact != null) {
            postProcessSingleResult(contact);
        }

        return contact != null ? R.ok(contact) : R.error("暂无可用客服");
    }

    /**
     * 启用客服
     */
    @ApiOperation("启用客服")
    @PutMapping("/enable/{id}")
    public R<Boolean> enableCustomerService(@PathVariable Integer id) {
        try {
            CustomerServiceContacts contact = service.getById(id);
            if (contact == null) {
                return R.error("客服不存在");
            }

            contact.setStatus(true);
            boolean success = service.updateById(contact);

            return success ? R.ok() : R.error("启用失败");
        } catch (Exception e) {
            return R.error("启用失败：" + e.getMessage());
        }
    }

    /**
     * 禁用客服
     */
    @ApiOperation("禁用客服")
    @PutMapping("/disable/{id}")
    public R<Boolean> disableCustomerService(@PathVariable Integer id) {
        try {
            CustomerServiceContacts contact = service.getById(id);
            if (contact == null) {
                return R.error("客服不存在");
            }

            contact.setStatus(false);
            boolean success = service.updateById(contact);

            return success ? R.ok() : R.error("禁用失败");
        } catch (Exception e) {
            return R.error("禁用失败：" + e.getMessage());
        }
    }

    /**
     * 重写查询条件构建方法
     */
    @Override
    protected QueryWrapper<CustomerServiceContacts> getQueryWrapper(CustomerServiceContacts entity) {
        QueryWrapper<CustomerServiceContacts> queryWrapper = new QueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq("is_del", 0);

        // 如果客服联系信息实体不为空
        if (entity != null) {
            // 客服姓名模糊查询
            if (entity.getName() != null && !entity.getName().isEmpty()) {
                queryWrapper.like("name", entity.getName());
            }

            // 手机号模糊查询
            if (entity.getPhone() != null && !entity.getPhone().isEmpty()) {
                queryWrapper.like("phone", entity.getPhone());
            }

            // 微信号模糊查询
            if (entity.getWechat() != null && !entity.getWechat().isEmpty()) {
                queryWrapper.like("wechat", entity.getWechat());
            }

            // 状态查询
            if (entity.getStatus() != null) {
                queryWrapper.eq("status", entity.getStatus());
            }
        }

        return queryWrapper;
    }

    /**
     * 重写分页结果后置处理，设置头像完整URL
     */
    @Override
    public void postProcessPageResult(IPage<CustomerServiceContacts> page) {
        if (page != null && page.getRecords() != null) {
            for (CustomerServiceContacts entity : page.getRecords()) {
                postProcessSingleResult(entity);
            }
        }
    }

    /**
     * 重写列表结果后置处理，设置头像完整URL
     */
    @Override
    public void postProcessListResult(java.util.List<CustomerServiceContacts> list) {
        if (list != null) {
            for (CustomerServiceContacts entity : list) {
                postProcessSingleResult(entity);
            }
        }
    }

    /**
     * 重写单个实体后置处理，设置头像完整URL
     */
    @Override
    public CustomerServiceContacts postProcessSingleResult(CustomerServiceContacts entity) {
        if (entity != null && entity.getAvatar() != null) {
            entity.setAvatarFullUrl(ossUrlService.buildFullUrl(entity.getAvatar()));
        }
        return entity;
    }
}
