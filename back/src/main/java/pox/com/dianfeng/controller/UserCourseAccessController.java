package pox.com.dianfeng.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pox.com.dianfeng.common.R;
import pox.com.dianfeng.entity.UserCourseAccess;
import pox.com.dianfeng.entity.Users;
import pox.com.dianfeng.entity.Courses;
import pox.com.dianfeng.dto.CourseAccessRequestDTO;
import pox.com.dianfeng.dto.CourseAccessResponseDTO;
import pox.com.dianfeng.service.IUserCourseAccessService;
import pox.com.dianfeng.service.IUsersService;
import pox.com.dianfeng.service.ICoursesService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户课程权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Api(tags = "用户课程权限管理")
@RestController
@RequestMapping("/user-course-access")
public class UserCourseAccessController extends BaseController<IUserCourseAccessService, UserCourseAccess> {

    @Autowired
    private IUserCourseAccessService userCourseAccessService;

    @Autowired
    private IUsersService usersService;

    @Autowired
    private ICoursesService coursesService;

    /**
     * 检查用户访问权限
     */
    @ApiOperation("检查用户访问权限")
    @GetMapping("/check")
    public R<CourseAccessResponseDTO> checkAccess(
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("课程ID") @RequestParam Integer courseId,
            @ApiParam("章节ID（可选）") @RequestParam(required = false) Integer chapterId,
            @ApiParam("课时ID（可选）") @RequestParam(required = false) Integer lessonId) {
        
        CourseAccessResponseDTO result = userCourseAccessService.checkAccess(userId, courseId, chapterId, lessonId);
        return R.ok(result);
    }

    /**
     * 授予购买权限
     */
    @ApiOperation("授予购买权限")
    @PostMapping("/grant/purchase")
    public R<UserCourseAccess> grantPurchaseAccess(@RequestBody CourseAccessRequestDTO request) {
        UserCourseAccess result = userCourseAccessService.grantPurchaseAccess(
                request.getUserId(),
                request.getCourseId(),
                request.getChapterId(),
                request.getLessonId(),
                request.getPricePaid(),
                request.getOriginalPrice(),
                request.getPaymentMethod(),
                request.getOrderId(),
                request.getIsBuyout(),
                request.getExpireTime()
        );
        return R.ok(result);
    }

    /**
     * 授予免费权限
     */
    @ApiOperation("授予免费权限")
    @PostMapping("/grant/free")
    public R<UserCourseAccess> grantFreeAccess(@RequestBody CourseAccessRequestDTO request) {
        UserCourseAccess result = userCourseAccessService.grantFreeAccess(
                request.getUserId(),
                request.getCourseId(),
                request.getChapterId(),
                request.getLessonId(),
                request.getExpireTime()
        );
        return R.ok(result);
    }

    /**
     * 积分兑换权限
     */
    @ApiOperation("积分兑换权限")
    @PostMapping("/grant/points")
    public R<UserCourseAccess> grantPointsAccess(@RequestBody CourseAccessRequestDTO request) {
        UserCourseAccess result = userCourseAccessService.grantPointsAccess(
                request.getUserId(),
                request.getCourseId(),
                request.getChapterId(),
                request.getLessonId(),
                request.getPointsUsed(),
                request.getOriginalPrice(),
                request.getExpireTime()
        );
        return R.ok(result);
    }

    /**
     * 优惠券兑换权限
     */
    @ApiOperation("优惠券兑换权限")
    @PostMapping("/grant/coupon")
    public R<UserCourseAccess> grantCouponAccess(@RequestBody CourseAccessRequestDTO request) {
        UserCourseAccess result = userCourseAccessService.grantCouponAccess(
                request.getUserId(),
                request.getCourseId(),
                request.getChapterId(),
                request.getLessonId(),
                request.getCouponId(),
                request.getPricePaid(),
                request.getOriginalPrice(),
                request.getExpireTime()
        );
        return R.ok(result);
    }

    /**
     * 管理员赠送权限
     */
    @ApiOperation("管理员赠送权限")
    @PostMapping("/grant/admin-gift")
    public R<UserCourseAccess> grantAdminGift(@RequestBody CourseAccessRequestDTO request) {
        UserCourseAccess result = userCourseAccessService.grantAdminGift(
                request.getUserId(),
                request.getCourseId(),
                request.getChapterId(),
                request.getLessonId(),
                request.getAdminId(),
                request.getRemark(),
                request.getExpireTime()
        );
        return R.ok(result);
    }

    /**
     * 批量授予权限
     */
    @ApiOperation("批量授予权限")
    @PostMapping("/grant/batch")
    public R<List<UserCourseAccess>> batchGrantAccess(@RequestBody CourseAccessRequestDTO request) {
        List<UserCourseAccess> result = userCourseAccessService.batchGrantAccess(request);
        return R.ok(result);
    }

    /**
     * 处理退款
     */
    @ApiOperation("处理退款")
    @PostMapping("/refund/{accessId}")
    public R<Boolean> processRefund(
            @PathVariable Integer accessId,
            @ApiParam("退款金额") @RequestParam BigDecimal refundAmount,
            @ApiParam("退款原因") @RequestParam String refundReason) {
        
        Boolean result = userCourseAccessService.processRefund(accessId, refundAmount, refundReason);
        return result ? R.ok(result) : R.error("退款处理失败");
    }

    /**
     * 激活权限
     */
    @ApiOperation("激活权限")
    @PostMapping("/activate/{accessId}")
    public R<Boolean> activateAccess(@PathVariable Integer accessId) {
        Boolean result = userCourseAccessService.activateAccess(accessId);
        return result ? R.ok(result) : R.error("权限激活失败");
    }

    /**
     * 停用权限
     */
    @ApiOperation("停用权限")
    @PostMapping("/deactivate/{accessId}")
    public R<Boolean> deactivateAccess(
            @PathVariable Integer accessId,
            @ApiParam("停用原因") @RequestParam String reason) {
        
        Boolean result = userCourseAccessService.deactivateAccess(accessId, reason);
        return result ? R.ok(result) : R.error("权限停用失败");
    }

    /**
     * 获取用户的所有有效权限
     */
    @ApiOperation("获取用户的所有有效权限")
    @GetMapping("/user/{userId}/valid")
    public R<List<UserCourseAccess>> getUserValidAccess(@PathVariable Integer userId) {
        List<UserCourseAccess> result = userCourseAccessService.getUserValidAccess(userId);
        return R.ok(result);
    }

    /**
     * 获取用户某个课程的权限详情
     */
    @ApiOperation("获取用户某个课程的权限详情")
    @GetMapping("/user/{userId}/course/{courseId}")
    public R<List<UserCourseAccess>> getUserCourseAccess(
            @PathVariable Integer userId,
            @PathVariable Integer courseId) {
        
        List<UserCourseAccess> result = userCourseAccessService.getUserCourseAccess(userId, courseId);
        return R.ok(result);
    }

    /**
     * 获取即将到期的权限
     */
    @ApiOperation("获取即将到期的权限（7天内）")
    @GetMapping("/user/{userId}/expiring")
    public R<List<UserCourseAccess>> getExpiringAccess(@PathVariable Integer userId) {
        List<UserCourseAccess> result = userCourseAccessService.getExpiringAccess(userId);
        return R.ok(result);
    }

    /**
     * 获取用户权限统计信息
     */
    @ApiOperation("获取用户权限统计信息")
    @GetMapping("/user/{userId}/statistics")
    public R<CourseAccessResponseDTO.AccessStatistics> getUserAccessStatistics(@PathVariable Integer userId) {
        CourseAccessResponseDTO.AccessStatistics result = userCourseAccessService.getUserAccessStatistics(userId);
        return R.ok(result);
    }

    /**
     * 获取全局权限统计信息
     */
    @ApiOperation("获取全局权限统计信息")
    @GetMapping("/statistics/global")
    public R<CourseAccessResponseDTO.AccessStatistics> getGlobalAccessStatistics() {
        CourseAccessResponseDTO.AccessStatistics result = userCourseAccessService.getGlobalAccessStatistics();
        return R.ok(result);
    }

    /**
     * 快速购买课程（整个课程）
     */
    @ApiOperation("快速购买课程")
    @PostMapping("/quick-purchase")
    public R<UserCourseAccess> quickPurchaseCourse(
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("课程ID") @RequestParam Integer courseId,
            @ApiParam("实际支付金额") @RequestParam BigDecimal pricePaid,
            @ApiParam("原价") @RequestParam BigDecimal originalPrice,
            @ApiParam("支付方式") @RequestParam String paymentMethod,
            @ApiParam("订单ID") @RequestParam String orderId,
            @ApiParam("是否买断") @RequestParam(defaultValue = "false") Boolean isBuyout) {
        
        UserCourseAccess result = userCourseAccessService.grantPurchaseAccess(
                userId, courseId, null, null,
                pricePaid, originalPrice, paymentMethod, orderId, 
                isBuyout, null  // 永久有效
        );
        return R.ok(result);
    }

    /**
     * 快速购买章节
     */
    @ApiOperation("快速购买章节")
    @PostMapping("/quick-purchase-chapter")
    public R<UserCourseAccess> quickPurchaseChapter(
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("课程ID") @RequestParam Integer courseId,
            @ApiParam("章节ID") @RequestParam Integer chapterId,
            @ApiParam("实际支付金额") @RequestParam BigDecimal pricePaid,
            @ApiParam("原价") @RequestParam BigDecimal originalPrice,
            @ApiParam("支付方式") @RequestParam String paymentMethod,
            @ApiParam("订单ID") @RequestParam String orderId,
            @ApiParam("是否买断") @RequestParam(defaultValue = "false") Boolean isBuyout,
            @ApiParam("过期时间（可选）") @RequestParam(required = false) LocalDateTime expireTime) {
        
        UserCourseAccess result = userCourseAccessService.grantPurchaseAccess(
                userId, courseId, chapterId, null,
                pricePaid, originalPrice, paymentMethod, orderId, 
                isBuyout, expireTime
        );
        return R.ok(result);
    }

    /**
     * 快速购买课时
     */
    @ApiOperation("快速购买课时")
    @PostMapping("/quick-purchase-lesson")
    public R<UserCourseAccess> quickPurchaseLesson(
            @ApiParam("用户ID") @RequestParam Integer userId,
            @ApiParam("课程ID") @RequestParam Integer courseId,
            @ApiParam("章节ID") @RequestParam Integer chapterId,
            @ApiParam("课时ID") @RequestParam Integer lessonId,
            @ApiParam("实际支付金额") @RequestParam BigDecimal pricePaid,
            @ApiParam("原价") @RequestParam BigDecimal originalPrice,
            @ApiParam("支付方式") @RequestParam String paymentMethod,
            @ApiParam("订单ID") @RequestParam String orderId,
            @ApiParam("过期时间（可选）") @RequestParam(required = false) LocalDateTime expireTime) {
        
        UserCourseAccess result = userCourseAccessService.grantPurchaseAccess(
                userId, courseId, chapterId, lessonId,
                pricePaid, originalPrice, paymentMethod, orderId, 
                false,  // 课时购买不支持买断
                expireTime
        );
        return R.ok(result);
    }

    /**
     * 后置处理单个结果，填充用户名称和课程名称
     */
    @Override
    public UserCourseAccess postProcessSingleResult(UserCourseAccess entity) {
        if (entity == null) {
            return entity;
        }

        // 填充用户名称
        if (entity.getUserId() != null) {
            Users user = usersService.getById(entity.getUserId());
            if (user != null) {
                // 优先使用昵称，如果没有昵称则使用用户名
                String userName = user.getNickname() != null && !user.getNickname().trim().isEmpty()
                    ? user.getNickname()
                    : user.getUsername();
                entity.setUserName(userName);
            }
        }

        // 填充课程名称
        if (entity.getCourseId() != null) {
            Courses course = coursesService.getById(entity.getCourseId());
            if (course != null) {
                entity.setCourseName(course.getTitle());
            }
        }

        return entity;
    }
}