package pox.com.dianfeng;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

/**
 * MyBatis-Plus代码生成器
 * 
 * <AUTHOR>
 * @since 2025/5/28 16:45
 */
public class CodeGenerator {
    public static void main(String[] args) {
        // 项目路径
        String projectPath = System.getProperty("user.dir");

        // 数据库配置
        String url = "********************************************************************************************************************************************************";
        String username = "root";
        String password = "rootmysql";

        // 代码生成路径配置
        String javaOutputDir = projectPath + "/back/src/main/java";
        String mapperOutputDir = projectPath + "/back/src/main/resources/mapper";

        // 表名列表 - 从init.sql获取
        String[] tables = {
                "users", "teachers", "categories", "courses",
                "course_chapters", "course_lessons", "live_courses",
                "course_reviews", "user_learning_records", "user_checkins",
                "user_favorites", "admins", "customer_service_contacts"
        };

        // 生成代码
        FastAutoGenerator.create(url, username, password)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author("pox") // 设置作者
                            .commentDate("yyyy-MM-dd")
                            .outputDir(javaOutputDir) // 指定输出目录
                            .disableOpenDir() // 禁止打开输出目录
                            .enableSwagger(); // 开启swagger模式
                })
                // 数据源配置
                .dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                    if (typeCode == Types.TINYINT) {
                        // TINYINT转为Integer
                        return DbColumnType.INTEGER;
                    }
                            return typeRegistry.getColumnType(metaInfo);
                }))
                // 包配置
                .packageConfig(builder -> {
                    Map<OutputFile, String> pathInfo = new HashMap<>();
                    pathInfo.put(OutputFile.xml, mapperOutputDir);

                    builder.parent("pox.com.dianfeng") // 设置父包名
                            .entity("entity") // 实体类包名
                            .service("service") // 服务类包名
                            .serviceImpl("service.impl") // 服务实现类包名
                            .mapper("mapper") // Mapper接口包名
                            .controller("controller") // 控制器包名
                            .pathInfo(pathInfo); // 设置路径信息
                })
                // 策略配置
                .strategyConfig(builder -> {
                    // 设置需要生成的表名
                    builder.addInclude(tables)
                            // Entity策略配置
                            .entityBuilder()
                            .enableLombok() // 开启Lombok
                            .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                            .naming(NamingStrategy.underline_to_camel) // 表名转驼峰命名
                            .columnNaming(NamingStrategy.underline_to_camel) // 字段名转驼峰命名
                            .logicDeleteColumnName("is_del") // 逻辑删除字段
                            .enableActiveRecord() // 开启ActiveRecord模式
                            .formatFileName("%s") // 格式化文件名

                            // Controller策略配置
                            .controllerBuilder()
                            .enableRestStyle() // 开启RestController
                            .formatFileName("%sController") // 格式化文件名

                            // Service策略配置
                            .serviceBuilder()
                            .formatServiceFileName("I%sService") // 格式化Service接口文件名
                            .formatServiceImplFileName("%sServiceImpl") // 格式化Service实现类文件名

                            // Mapper策略配置
                            .mapperBuilder()
                            .enableMapperAnnotation() // 开启Mapper注解
                            .formatMapperFileName("%sMapper") // 格式化Mapper接口文件名
                            .formatXmlFileName("%sMapper") // 格式化XML文件名
                            .enableBaseResultMap() // 启用BaseResultMap生成
                            .enableBaseColumnList(); // 启用BaseColumnList生成
                })
                // 使用默认的Velocity引擎模板，不需要显式指定
                .execute();

        System.out.println("=== 代码生成完成 ===");
    }
}
