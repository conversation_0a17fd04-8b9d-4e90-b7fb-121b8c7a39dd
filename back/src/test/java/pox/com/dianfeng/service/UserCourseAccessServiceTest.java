package pox.com.dianfeng.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import pox.com.dianfeng.dto.CourseAccessRequestDTO;
import pox.com.dianfeng.dto.CourseAccessResponseDTO;
import pox.com.dianfeng.entity.UserCourseAccess;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户课程权限服务测试类
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@SpringBootTest
@Transactional
public class UserCourseAccessServiceTest {

    @Autowired
    private IUserCourseAccessService userCourseAccessService;

    private static final Integer TEST_USER_ID = 999;
    private static final Integer TEST_COURSE_ID = 999;
    private static final Integer TEST_CHAPTER_ID = 999;
    private static final Integer TEST_LESSON_ID = 999;

    @Test
    public void testGrantPurchaseAccess() {
        // 测试授予购买权限
        UserCourseAccess access = userCourseAccessService.grantPurchaseAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                new BigDecimal("99.00"), new BigDecimal("199.00"),
                "wechat", "TEST_ORDER_001", true, null
        );

        assertNotNull(access);
        assertNotNull(access.getId());
        assertEquals(TEST_USER_ID, access.getUserId());
        assertEquals(TEST_COURSE_ID, access.getCourseId());
        assertEquals(UserCourseAccess.AccessType.COURSE, access.getAccessType());
        assertEquals(UserCourseAccess.AcquireMethod.PURCHASE, access.getAcquireMethod());
        assertTrue(access.getIsBuyout());
        assertEquals(new BigDecimal("99.00"), access.getPricePaid());
        assertEquals(UserCourseAccess.Status.VALID, access.getStatus());
        assertTrue(access.getIsActive());
    }

    @Test
    public void testGrantFreeAccess() {
        // 测试授予免费权限
        LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
        UserCourseAccess access = userCourseAccessService.grantFreeAccess(
                TEST_USER_ID, TEST_COURSE_ID, TEST_CHAPTER_ID, null, expireTime
        );

        assertNotNull(access);
        assertEquals(UserCourseAccess.AccessType.CHAPTER, access.getAccessType());
        assertEquals(UserCourseAccess.AcquireMethod.FREE, access.getAcquireMethod());
        assertFalse(access.getIsBuyout());
        assertEquals(BigDecimal.ZERO, access.getPricePaid());
        assertEquals(expireTime, access.getExpireTime());
    }

    @Test
    public void testGrantPointsAccess() {
        // 测试积分兑换权限
        UserCourseAccess access = userCourseAccessService.grantPointsAccess(
                TEST_USER_ID, TEST_COURSE_ID, TEST_CHAPTER_ID, TEST_LESSON_ID,
                1000, new BigDecimal("50.00"), LocalDateTime.now().plusDays(60)
        );

        assertNotNull(access);
        assertEquals(UserCourseAccess.AccessType.LESSON, access.getAccessType());
        assertEquals(UserCourseAccess.AcquireMethod.POINTS, access.getAcquireMethod());
        assertEquals(Integer.valueOf(1000), access.getPointsUsed());
        assertEquals(UserCourseAccess.PaymentMethod.POINTS, access.getPaymentMethod());
    }

    @Test
    public void testCheckAccess() {
        // 先授予权限
        userCourseAccessService.grantPurchaseAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                new BigDecimal("99.00"), new BigDecimal("199.00"),
                "wechat", "TEST_ORDER_002", true, null
        );

        // 测试权限检查
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null
        );

        assertNotNull(response);
        assertTrue(response.getHasAccess());
        assertEquals(UserCourseAccess.AccessType.COURSE, response.getAccessLevel());
        assertTrue(response.getIsBuyout());
        assertEquals(UserCourseAccess.AcquireMethod.PURCHASE, response.getAcquireMethod());
        assertNotNull(response.getAccessDetail());
    }

    @Test
    public void testCheckAccessWithoutPermission() {
        // 测试无权限的情况
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(
                TEST_USER_ID + 1, TEST_COURSE_ID, null, null
        );

        assertNotNull(response);
        assertFalse(response.getHasAccess());
        assertNotNull(response.getReason());
    }

    @Test
    public void testBuyoutPriority() {
        // 先授予章节权限
        userCourseAccessService.grantFreeAccess(
                TEST_USER_ID, TEST_COURSE_ID, TEST_CHAPTER_ID, null,
                LocalDateTime.now().plusDays(30)
        );

        // 再授予买断的课程权限
        userCourseAccessService.grantPurchaseAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                new BigDecimal("199.00"), new BigDecimal("299.00"),
                "alipay", "TEST_ORDER_003", true, null
        );

        // 检查权限，应该返回买断权限
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(
                TEST_USER_ID, TEST_COURSE_ID, TEST_CHAPTER_ID, null
        );

        assertTrue(response.getHasAccess());
        assertTrue(response.getIsBuyout());
        assertEquals(UserCourseAccess.AccessType.COURSE, response.getAccessLevel());
    }

    @Test
    public void testBatchGrantAccess() {
        // 测试批量授权
        CourseAccessRequestDTO request = new CourseAccessRequestDTO();
        request.setUserIds(Arrays.asList(TEST_USER_ID, TEST_USER_ID + 1, TEST_USER_ID + 2));
        request.setCourseId(TEST_COURSE_ID);
        request.setAccessType(UserCourseAccess.AccessType.COURSE);
        request.setAcquireMethod(UserCourseAccess.AcquireMethod.GIFT);
        request.setPaymentMethod(UserCourseAccess.PaymentMethod.GIFT);
        request.setAdminId(1);
        request.setRemark("批量赠送测试");

        List<UserCourseAccess> accessList = userCourseAccessService.batchGrantAccess(request);

        assertNotNull(accessList);
        assertEquals(3, accessList.size());

        for (UserCourseAccess access : accessList) {
            assertEquals(TEST_COURSE_ID, access.getCourseId());
            assertEquals(UserCourseAccess.AcquireMethod.GIFT, access.getAcquireMethod());
            assertEquals(Integer.valueOf(1), access.getAdminId());
            assertTrue(access.getRemark().contains("批量赠送测试"));
        }
    }

    @Test
    public void testProcessRefund() {
        // 先授予权限
        UserCourseAccess access = userCourseAccessService.grantPurchaseAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                new BigDecimal("99.00"), new BigDecimal("199.00"),
                "wechat", "TEST_ORDER_004", false, null
        );

        // 处理退款
        Boolean result = userCourseAccessService.processRefund(
                access.getId(), new BigDecimal("99.00"), "用户申请退款"
        );

        assertTrue(result);

        // 验证退款后状态
        UserCourseAccess refundedAccess = userCourseAccessService.getById(access.getId());
        assertEquals(UserCourseAccess.Status.REFUNDED, refundedAccess.getStatus());
        assertEquals(new BigDecimal("99.00"), refundedAccess.getRefundAmount());
        assertEquals("用户申请退款", refundedAccess.getRefundReason());
        assertNotNull(refundedAccess.getRefundTime());
    }

    @Test
    public void testActivateAndDeactivateAccess() {
        // 先授予权限
        UserCourseAccess access = userCourseAccessService.grantPurchaseAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                new BigDecimal("99.00"), new BigDecimal("199.00"),
                "wechat", "TEST_ORDER_005", false, null
        );

        // 停用权限
        Boolean deactivateResult = userCourseAccessService.deactivateAccess(
                access.getId(), "测试停用"
        );
        assertTrue(deactivateResult);

        UserCourseAccess deactivatedAccess = userCourseAccessService.getById(access.getId());
        assertFalse(deactivatedAccess.getIsActive());
        assertEquals(UserCourseAccess.Status.INVALID, deactivatedAccess.getStatus());

        // 重新激活权限
        Boolean activateResult = userCourseAccessService.activateAccess(access.getId());
        assertTrue(activateResult);

        UserCourseAccess activatedAccess = userCourseAccessService.getById(access.getId());
        assertTrue(activatedAccess.getIsActive());
        assertEquals(UserCourseAccess.Status.VALID, activatedAccess.getStatus());
    }

    @Test
    public void testGetUserAccessStatistics() {
        // 授予多种类型的权限
        userCourseAccessService.grantPurchaseAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                new BigDecimal("99.00"), new BigDecimal("199.00"),
                "wechat", "TEST_ORDER_006", true, null
        );

        userCourseAccessService.grantFreeAccess(
                TEST_USER_ID, TEST_COURSE_ID + 1, null, null,
                LocalDateTime.now().plusDays(30)
        );

        userCourseAccessService.grantPointsAccess(
                TEST_USER_ID, TEST_COURSE_ID + 2, TEST_CHAPTER_ID, null,
                500, new BigDecimal("25.00"), LocalDateTime.now().plusDays(15)
        );

        // 获取统计信息
        CourseAccessResponseDTO.AccessStatistics statistics = 
                userCourseAccessService.getUserAccessStatistics(TEST_USER_ID);

        assertNotNull(statistics);
        assertTrue(statistics.getTotalAccess() >= 3);
        assertTrue(statistics.getValidAccess() >= 3);
        assertTrue(statistics.getBuyoutAccess() >= 1);
        assertTrue(statistics.getFreeAccess() >= 1);
        assertTrue(statistics.getPurchasedAccess() >= 1);
    }

    @Test
    public void testExpiredAccess() {
        // 授予已过期的权限
        UserCourseAccess access = userCourseAccessService.grantFreeAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                LocalDateTime.now().minusDays(1)  // 昨天过期
        );

        // 检查权限，应该无权限
        CourseAccessResponseDTO response = userCourseAccessService.checkAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null
        );

        assertFalse(response.getHasAccess());
        assertTrue(response.getReason().contains("过期"));
    }

    @Test
    public void testGetExpiringAccess() {
        // 授予即将过期的权限
        userCourseAccessService.grantFreeAccess(
                TEST_USER_ID, TEST_COURSE_ID, null, null,
                LocalDateTime.now().plusDays(3)  // 3天后过期
        );

        // 获取即将过期的权限
        List<UserCourseAccess> expiringAccess = 
                userCourseAccessService.getExpiringAccess(TEST_USER_ID);

        assertNotNull(expiringAccess);
        assertTrue(expiringAccess.size() >= 1);
        
        UserCourseAccess expiring = expiringAccess.get(0);
        assertNotNull(expiring.getExpireTime());
        assertTrue(expiring.getExpireTime().isAfter(LocalDateTime.now()));
        assertTrue(expiring.getExpireTime().isBefore(LocalDateTime.now().plusDays(7)));
    }
}
