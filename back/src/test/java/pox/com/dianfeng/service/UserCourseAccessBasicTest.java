package pox.com.dianfeng.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import pox.com.dianfeng.entity.UserCourseAccess;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户课程权限基础测试类
 * 
 * <AUTHOR>
 * @since 2025-06-04
 */
@SpringBootTest
public class UserCourseAccessBasicTest {

    @Test
    public void testUserCourseAccessEntity() {
        // 测试实体类基本功能
        UserCourseAccess access = new UserCourseAccess();
        
        // 设置基本属性
        access.setUserId(1);
        access.setCourseId(1);
        access.setAccessType(UserCourseAccess.AccessType.COURSE);
        access.setAcquireMethod(UserCourseAccess.AcquireMethod.PURCHASE);
        access.setIsBuyout(true);
        access.setPricePaid(new BigDecimal("99.00"));
        access.setOriginalPrice(new BigDecimal("199.00"));
        access.setPaymentMethod(UserCourseAccess.PaymentMethod.WECHAT);
        access.setOrderId("TEST_ORDER_001");
        access.setIsActive(true);
        access.setStatus(UserCourseAccess.Status.VALID);
        access.setCreatedAt(LocalDateTime.now());
        access.setUpdatedAt(LocalDateTime.now());
        
        // 验证属性设置
        assertEquals(Integer.valueOf(1), access.getUserId());
        assertEquals(Integer.valueOf(1), access.getCourseId());
        assertEquals(Integer.valueOf(UserCourseAccess.AccessType.COURSE), access.getAccessType());
        assertEquals(Integer.valueOf(UserCourseAccess.AcquireMethod.PURCHASE), access.getAcquireMethod());
        assertTrue(access.getIsBuyout());
        assertEquals(new BigDecimal("99.00"), access.getPricePaid());
        assertEquals(new BigDecimal("199.00"), access.getOriginalPrice());
        assertEquals(UserCourseAccess.PaymentMethod.WECHAT, access.getPaymentMethod());
        assertEquals("TEST_ORDER_001", access.getOrderId());
        assertTrue(access.getIsActive());
        assertEquals(Integer.valueOf(UserCourseAccess.Status.VALID), access.getStatus());
        assertNotNull(access.getCreatedAt());
        assertNotNull(access.getUpdatedAt());
    }

    @Test
    public void testAccessTypeConstants() {
        // 测试权限类型常量
        assertEquals(1, UserCourseAccess.AccessType.COURSE);
        assertEquals(2, UserCourseAccess.AccessType.CHAPTER);
        assertEquals(3, UserCourseAccess.AccessType.LESSON);
    }

    @Test
    public void testAcquireMethodConstants() {
        // 测试获取方式常量
        assertEquals(1, UserCourseAccess.AcquireMethod.PURCHASE);
        assertEquals(2, UserCourseAccess.AcquireMethod.FREE);
        assertEquals(3, UserCourseAccess.AcquireMethod.POINTS);
        assertEquals(4, UserCourseAccess.AcquireMethod.COUPON);
        assertEquals(5, UserCourseAccess.AcquireMethod.GIFT);
        assertEquals(6, UserCourseAccess.AcquireMethod.PROMOTION);
    }

    @Test
    public void testStatusConstants() {
        // 测试状态常量
        assertEquals(0, UserCourseAccess.Status.INVALID);
        assertEquals(1, UserCourseAccess.Status.VALID);
        assertEquals(2, UserCourseAccess.Status.REFUNDED);
    }

    @Test
    public void testPaymentMethodConstants() {
        // 测试支付方式常量
        assertEquals("wechat", UserCourseAccess.PaymentMethod.WECHAT);
        assertEquals("alipay", UserCourseAccess.PaymentMethod.ALIPAY);
        assertEquals("points", UserCourseAccess.PaymentMethod.POINTS);
        assertEquals("coupon", UserCourseAccess.PaymentMethod.COUPON);
        assertEquals("gift", UserCourseAccess.PaymentMethod.GIFT);
    }

    @Test
    public void testAccessTypeLogic() {
        // 测试权限类型逻辑
        UserCourseAccess courseAccess = new UserCourseAccess();
        courseAccess.setAccessType(UserCourseAccess.AccessType.COURSE);
        courseAccess.setChapterId(null);
        courseAccess.setLessonId(null);
        
        UserCourseAccess chapterAccess = new UserCourseAccess();
        chapterAccess.setAccessType(UserCourseAccess.AccessType.CHAPTER);
        chapterAccess.setChapterId(1);
        chapterAccess.setLessonId(null);
        
        UserCourseAccess lessonAccess = new UserCourseAccess();
        lessonAccess.setAccessType(UserCourseAccess.AccessType.LESSON);
        lessonAccess.setChapterId(1);
        lessonAccess.setLessonId(1);
        
        // 验证权限类型设置正确
        assertEquals(Integer.valueOf(UserCourseAccess.AccessType.COURSE), courseAccess.getAccessType());
        assertNull(courseAccess.getChapterId());
        assertNull(courseAccess.getLessonId());
        
        assertEquals(Integer.valueOf(UserCourseAccess.AccessType.CHAPTER), chapterAccess.getAccessType());
        assertEquals(Integer.valueOf(1), chapterAccess.getChapterId());
        assertNull(chapterAccess.getLessonId());
        
        assertEquals(Integer.valueOf(UserCourseAccess.AccessType.LESSON), lessonAccess.getAccessType());
        assertEquals(Integer.valueOf(1), lessonAccess.getChapterId());
        assertEquals(Integer.valueOf(1), lessonAccess.getLessonId());
    }

    @Test
    public void testBuyoutLogic() {
        // 测试买断逻辑
        UserCourseAccess buyoutAccess = new UserCourseAccess();
        buyoutAccess.setIsBuyout(true);
        buyoutAccess.setExpireTime(null); // 买断通常没有过期时间
        
        UserCourseAccess temporaryAccess = new UserCourseAccess();
        temporaryAccess.setIsBuyout(false);
        temporaryAccess.setExpireTime(LocalDateTime.now().plusDays(30));
        
        assertTrue(buyoutAccess.getIsBuyout());
        assertNull(buyoutAccess.getExpireTime());
        
        assertFalse(temporaryAccess.getIsBuyout());
        assertNotNull(temporaryAccess.getExpireTime());
    }

    @Test
    public void testStatusTransition() {
        // 测试状态转换
        UserCourseAccess access = new UserCourseAccess();
        
        // 初始状态：有效
        access.setStatus(UserCourseAccess.Status.VALID);
        access.setIsActive(true);
        assertEquals(Integer.valueOf(UserCourseAccess.Status.VALID), access.getStatus());
        assertTrue(access.getIsActive());
        
        // 状态转换：失效
        access.setStatus(UserCourseAccess.Status.INVALID);
        access.setIsActive(false);
        assertEquals(Integer.valueOf(UserCourseAccess.Status.INVALID), access.getStatus());
        assertFalse(access.getIsActive());
        
        // 状态转换：退款
        access.setStatus(UserCourseAccess.Status.REFUNDED);
        access.setRefundTime(LocalDateTime.now());
        access.setRefundAmount(new BigDecimal("99.00"));
        access.setRefundReason("用户申请退款");
        
        assertEquals(Integer.valueOf(UserCourseAccess.Status.REFUNDED), access.getStatus());
        assertNotNull(access.getRefundTime());
        assertEquals(new BigDecimal("99.00"), access.getRefundAmount());
        assertEquals("用户申请退款", access.getRefundReason());
    }

    @Test
    public void testExpirationLogic() {
        // 测试过期逻辑
        LocalDateTime now = LocalDateTime.now();
        
        // 未过期的权限
        UserCourseAccess validAccess = new UserCourseAccess();
        validAccess.setExpireTime(now.plusDays(7));
        assertTrue(validAccess.getExpireTime().isAfter(now));
        
        // 已过期的权限
        UserCourseAccess expiredAccess = new UserCourseAccess();
        expiredAccess.setExpireTime(now.minusDays(1));
        assertTrue(expiredAccess.getExpireTime().isBefore(now));
        
        // 永久权限
        UserCourseAccess permanentAccess = new UserCourseAccess();
        permanentAccess.setExpireTime(null);
        assertNull(permanentAccess.getExpireTime());
    }

    @Test
    public void testPriceLogic() {
        // 测试价格逻辑
        UserCourseAccess paidAccess = new UserCourseAccess();
        paidAccess.setPricePaid(new BigDecimal("99.00"));
        paidAccess.setOriginalPrice(new BigDecimal("199.00"));
        
        // 计算折扣
        BigDecimal discount = paidAccess.getOriginalPrice().subtract(paidAccess.getPricePaid());
        assertEquals(new BigDecimal("100.00"), discount);
        
        // 免费权限
        UserCourseAccess freeAccess = new UserCourseAccess();
        freeAccess.setPricePaid(BigDecimal.ZERO);
        freeAccess.setOriginalPrice(BigDecimal.ZERO);
        
        assertEquals(BigDecimal.ZERO, freeAccess.getPricePaid());
        assertEquals(BigDecimal.ZERO, freeAccess.getOriginalPrice());
    }

    @Test
    public void testPointsLogic() {
        // 测试积分逻辑
        UserCourseAccess pointsAccess = new UserCourseAccess();
        pointsAccess.setAcquireMethod(UserCourseAccess.AcquireMethod.POINTS);
        pointsAccess.setPaymentMethod(UserCourseAccess.PaymentMethod.POINTS);
        pointsAccess.setPointsUsed(1000);
        pointsAccess.setPricePaid(BigDecimal.ZERO);
        pointsAccess.setOriginalPrice(new BigDecimal("50.00"));
        
        assertEquals(Integer.valueOf(UserCourseAccess.AcquireMethod.POINTS), pointsAccess.getAcquireMethod());
        assertEquals(UserCourseAccess.PaymentMethod.POINTS, pointsAccess.getPaymentMethod());
        assertEquals(Integer.valueOf(1000), pointsAccess.getPointsUsed());
        assertEquals(BigDecimal.ZERO, pointsAccess.getPricePaid());
        assertEquals(new BigDecimal("50.00"), pointsAccess.getOriginalPrice());
    }

    @Test
    public void testCouponLogic() {
        // 测试优惠券逻辑
        UserCourseAccess couponAccess = new UserCourseAccess();
        couponAccess.setAcquireMethod(UserCourseAccess.AcquireMethod.COUPON);
        couponAccess.setPaymentMethod(UserCourseAccess.PaymentMethod.COUPON);
        couponAccess.setCouponId(123);
        couponAccess.setPricePaid(new BigDecimal("49.00"));
        couponAccess.setOriginalPrice(new BigDecimal("99.00"));
        
        assertEquals(Integer.valueOf(UserCourseAccess.AcquireMethod.COUPON), couponAccess.getAcquireMethod());
        assertEquals(UserCourseAccess.PaymentMethod.COUPON, couponAccess.getPaymentMethod());
        assertEquals(Integer.valueOf(123), couponAccess.getCouponId());
        assertEquals(new BigDecimal("49.00"), couponAccess.getPricePaid());
        assertEquals(new BigDecimal("99.00"), couponAccess.getOriginalPrice());
    }
}
