#!/bin/bash

# 数据库配置
DB_HOST="*************"
DB_PORT="3306"
DB_NAME="dianfeng_class"
DB_USER="dianfeng_class"
DB_PASSWORD="tiMzeNWW3QKydYsr"

echo "正在连接数据库并执行tag_config.sql..."

# 检查是否有mysql命令
if command -v mysql &> /dev/null; then
    # 使用mysql命令执行SQL文件
    mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASSWORD} ${DB_NAME} < tag_config.sql
    echo "数据库初始化完成！"
else
    echo "未找到mysql命令行工具。"
    echo "您可以手动执行tag_config.sql文件中的SQL语句。"
    echo ""
    echo "数据库连接信息："
    echo "主机: ${DB_HOST}"
    echo "端口: ${DB_PORT}"
    echo "数据库: ${DB_NAME}"
    echo "用户名: ${DB_USER}"
    echo "密码: ${DB_PASSWORD}"
fi