-- 标签配置表
CREATE TABLE `tag_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '标签配置ID，主键',
  `category` varchar(50) NOT NULL COMMENT '标签分类：level-难度级别, age_group-年龄段',
  `label` varchar(100) NOT NULL COMMENT '标签显示名称',
  `value` int(50) NOT NULL COMMENT '标签值',
  `sort_order` int DEFAULT 0 COMMENT '排序值，越小越靠前',
  `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统内置：0-否，1-是',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='标签配置表';

-- 插入默认数据
INSERT INTO `tag_configs` (`category`, `label`, `value`, `sort_order`, `is_system`, `status`) VALUES
-- 难度级别
('level', '入门', '1', 1, 1, 1),
('level', '初级', '2', 2, 1, 1),
('level', '中级', '3', 3, 1, 1),
('level', '高级', '4', 4, 1, 1),
-- 年龄段
('age_group', '青少年', '1', 1, 1, 1),
('age_group', '大学生', '2', 2, 1, 1),
('age_group', '成人', '3', 3, 1, 1),
('age_group', '不限', '0', 4, 1, 1); 