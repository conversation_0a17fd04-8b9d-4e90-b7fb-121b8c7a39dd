# 标签配置功能部署指南

## 功能概述

本功能实现了标签配置的动态管理，替换了前端课程创建页面中硬编码的难度级别和年龄段选项。管理员可以通过后台管理界面灵活配置标签选项，无需修改代码即可调整课程分类标准。

## 核心特性

- ✅ **数据驱动**: 标签选项从数据库动态加载，不再硬编码
- ✅ **分类管理**: 支持不同类型的标签配置（难度级别、年龄段等）
- ✅ **完整CRUD**: 新增、编辑、删除、查询标签配置
- ✅ **系统保护**: 系统内置标签不允许删除，保证数据完整性
- ✅ **排序控制**: 支持自定义排序值控制显示顺序
- ✅ **状态管理**: 支持启用/禁用状态切换
- ✅ **重复检查**: 防止相同分类下标签值重复
- ✅ **前后端验证**: 完整的数据验证机制

## 部署步骤

### 1. 数据库初始化

#### 手动执行SQL（推荐）
1. 连接到MySQL数据库
   ```
   主机: 118.24.74.226
   端口: 3306
   数据库: dianfeng_class
   用户名: dianfeng_class
   密码: tiMzeNWW3QKydYsr
   ```

2. 执行 `tag_config.sql` 文件中的SQL语句：
   ```sql
   -- 创建标签配置表
   CREATE TABLE IF NOT EXISTS `tag_configs` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     `category` varchar(50) NOT NULL COMMENT '标签分类',
     `label` varchar(100) NOT NULL COMMENT '标签显示名称',
     `value` int NOT NULL COMMENT '标签值',
     `sort_order` int DEFAULT '0' COMMENT '排序值',
     `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统内置',
     `status` tinyint(1) DEFAULT '1' COMMENT '状态',
     `remark` varchar(255) DEFAULT NULL COMMENT '备注',
     `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_category_value` (`category`,`value`),
     KEY `idx_category` (`category`),
     KEY `idx_status` (`status`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签配置表';

   -- 插入默认数据
   INSERT INTO `tag_configs` (`category`, `label`, `value`, `sort_order`, `is_system`, `status`, `remark`) VALUES
   ('level', '入门', 1, 1, 1, 1, '适合零基础学员'),
   ('level', '初级', 2, 2, 1, 1, '适合有一定基础的学员'),
   ('level', '中级', 3, 3, 1, 1, '适合有较强基础的学员'),
   ('level', '高级', 4, 4, 1, 1, '适合高水平学员'),
   ('age_group', '青少年', 1, 1, 1, 1, '6-18岁'),
   ('age_group', '大学生', 2, 2, 1, 1, '18-25岁'),
   ('age_group', '成人', 3, 3, 1, 1, '25岁以上'),
   ('age_group', '不限', 0, 4, 1, 1, '适合所有年龄段');
   ```

#### 使用脚本执行（如果有MySQL客户端）
```bash
./init_tag_config.sh
```

### 2. 后端部署

确保以下文件已正确放置：

```
back/
├── src/main/java/pox/com/dianfeng/
│   ├── entity/TagConfigs.java                    # 实体类
│   ├── mapper/TagConfigsMapper.java              # Mapper接口
│   ├── service/ITagConfigsService.java           # Service接口
│   ├── service/impl/TagConfigsServiceImpl.java   # Service实现
│   └── controller/TagConfigsController.java      # Controller
└── src/main/resources/mapper/TagConfigsMapper.xml # Mapper XML
```

重启后端服务：
```bash
cd back
./mvnw spring-boot:run
```

### 3. 前端部署

确保以下文件已正确放置：

```
front/admin_front/
├── src/api/tagConfig.js                          # API调用
├── src/views/tag-config/index.vue                # 管理页面
├── src/views/course/create.vue                   # 课程创建页面（已修改）
└── src/router/index.js                           # 路由配置（已添加）
```

启动前端服务：
```bash
cd front/admin_front
npm run dev
```

## API接口文档

### 基础CRUD接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/tag-configs` | 获取标签配置列表（分页） |
| GET | `/api/tag-configs/{id}` | 根据ID获取标签配置详情 |
| POST | `/api/tag-configs` | 新增标签配置 |
| PUT | `/api/tag-configs/{id}` | 修改标签配置 |
| DELETE | `/api/tag-configs/{id}` | 删除标签配置 |

### 业务接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/tag-configs/category/{category}` | 根据分类获取标签配置 |
| GET | `/api/tag-configs/available/{category}` | 获取可用的标签配置 |
| GET | `/api/tag-configs/check-value/{category}/{value}` | 检查标签值是否存在 |

### 请求示例

#### 新增标签配置
```json
POST /api/tag-configs
{
  "category": "level",
  "label": "专家级",
  "value": 5,
  "sortOrder": 5,
  "status": 1,
  "remark": "适合专业人士"
}
```

#### 获取可用的难度级别
```json
GET /api/tag-configs/available/level

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "category": "level",
      "label": "入门",
      "value": 1,
      "sortOrder": 1
    },
    {
      "id": 2,
      "category": "level", 
      "label": "初级",
      "value": 2,
      "sortOrder": 2
    }
  ]
}
```

## 使用说明

### 1. 访问管理界面

启动前端服务后，访问：`http://localhost:3000/#/tag-config/list`

### 2. 管理标签配置

- **查看标签**: 在列表中查看所有标签配置，支持按分类筛选
- **新增标签**: 点击"新增"按钮添加新的标签配置
- **编辑标签**: 点击编辑按钮修改标签信息
- **删除标签**: 点击删除按钮删除标签（系统内置标签不可删除）
- **状态管理**: 切换标签的启用/禁用状态

### 3. 课程创建中的应用

在课程创建页面，难度级别和年龄段选项将自动从后端加载，不再是硬编码值。

## 数据库表结构

### tag_configs 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| category | varchar(50) | 标签分类（level/age_group等） |
| label | varchar(100) | 标签显示名称 |
| value | int | 标签值 |
| sort_order | int | 排序值 |
| is_system | tinyint(1) | 是否系统内置 |
| status | tinyint(1) | 状态（1启用/0禁用） |
| remark | varchar(255) | 备注 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |
| is_del | tinyint(1) | 逻辑删除标记 |

### 索引
- 主键：`PRIMARY KEY (id)`
- 唯一索引：`UNIQUE KEY uk_category_value (category, value)`
- 普通索引：`KEY idx_category (category)`, `KEY idx_status (status)`

## 扩展功能

### 添加新的标签分类

1. 在数据库中插入新分类的标签数据
2. 在前端页面中调用相应的API获取标签选项
3. 无需修改后端代码

### 示例：添加课程类型分类
```sql
INSERT INTO `tag_configs` (`category`, `label`, `value`, `sort_order`, `is_system`, `status`, `remark`) VALUES
('course_type', '录播课', 1, 1, 1, 1, '录制的视频课程'),
('course_type', '直播课', 2, 2, 1, 1, '实时直播课程'),
('course_type', '一对一', 3, 3, 1, 1, '一对一辅导');
```

然后在前端调用：
```javascript
const courseTypeOptions = await getAvailableTagConfigByCategory('course_type');
```

## 注意事项

1. **系统内置标签**: 带有 `is_system=1` 标记的标签不允许删除，确保基础功能正常
2. **唯一性约束**: 同一分类下的标签值必须唯一
3. **状态管理**: 只有启用状态的标签才会在前端显示
4. **排序值**: 数值越小越靠前显示
5. **逻辑删除**: 使用 `is_del` 字段标记删除，不会真正删除数据

## 故障排除

### 1. 标签选项不显示
- 检查数据库连接是否正常
- 确认标签配置的状态是否为启用（status=1）
- 检查后端API是否正常返回数据

### 2. 无法删除标签
- 检查标签是否为系统内置（is_system=1）
- 系统内置标签不允许删除

### 3. 标签值重复错误
- 确保同一分类下的标签值唯一
- 检查数据库唯一索引约束

## 更新日志

### v1.0.0 (当前版本)
- ✅ 完成基础标签配置功能
- ✅ 实现完整的CRUD操作
- ✅ 集成到课程创建页面
- ✅ 添加系统内置标签保护
- ✅ 实现前后端数据验证
- ✅ 添加管理界面路由配置 