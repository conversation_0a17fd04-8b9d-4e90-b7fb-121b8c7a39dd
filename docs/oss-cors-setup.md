# OSS CORS 配置指南

## 🚨 问题描述

前端上传文件时出现CORS错误：
```
Access to XMLHttpRequest at 'https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com/' 
from origin 'http://localhost:8088' has been blocked by CORS policy
```

## 🔍 原因分析

CORS（跨域资源共享）错误是因为：
1. 前端运行在 `http://localhost:8088`
2. OSS服务在 `https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com`
3. 浏览器的同源策略阻止了跨域请求
4. OSS Bucket没有配置允许前端域名的CORS规则

## 🛠️ 解决方案

### 方法1：阿里云控制台配置（推荐）

#### 步骤1：登录阿里云控制台
1. 访问 [阿里云OSS控制台](https://oss.console.aliyun.com/)
2. 使用您的阿里云账号登录

#### 步骤2：找到目标Bucket
1. 在Bucket列表中找到 `dianfeng-class`
2. 点击Bucket名称进入详情页

#### 步骤3：配置CORS规则
1. 在左侧菜单中找到 **"权限管理"**
2. 点击 **"跨域设置"**
3. 点击 **"设置"** 按钮
4. 点击 **"创建规则"**

#### 步骤4：添加CORS规则
配置以下规则：

**规则1：开发环境规则**
```
来源(Origin): http://localhost:8088,http://localhost:8090,http://127.0.0.1:8088,http://127.0.0.1:8090
允许Methods: GET,POST,PUT,DELETE,HEAD,OPTIONS
允许Headers: *
暴露Headers: ETag,x-oss-request-id,x-oss-version-id
缓存时间(秒): 3600
```

**规则2：通用规则（可选）**
```
来源(Origin): *
允许Methods: GET,POST,PUT,DELETE,HEAD
允许Headers: *
暴露Headers: ETag,x-oss-request-id
缓存时间(秒): 3600
```

### 方法2：使用阿里云CLI

如果您安装了阿里云CLI，可以运行：

```bash
chmod +x scripts/setup-oss-cors.sh
./scripts/setup-oss-cors.sh
```

### 方法3：使用OSS SDK配置

```javascript
// 仅供参考，通常在后端配置
const OSS = require('ali-oss');

const client = new OSS({
  region: 'oss-cn-chengdu',
  accessKeyId: 'your-access-key',
  accessKeySecret: 'your-secret-key',
  bucket: 'dianfeng-class'
});

await client.putBucketCORS('dianfeng-class', [
  {
    allowedOrigin: ['http://localhost:8088', 'http://localhost:8090'],
    allowedMethod: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS'],
    allowedHeader: ['*'],
    exposeHeader: ['ETag', 'x-oss-request-id'],
    maxAgeSeconds: 3600
  }
]);
```

## 📋 详细配置说明

### CORS规则字段说明

| 字段 | 说明 | 推荐值 |
|------|------|--------|
| **来源(Origin)** | 允许访问的域名 | `http://localhost:8088,http://localhost:8090` |
| **允许Methods** | 允许的HTTP方法 | `GET,POST,PUT,DELETE,HEAD,OPTIONS` |
| **允许Headers** | 允许的请求头 | `*` |
| **暴露Headers** | 暴露给前端的响应头 | `ETag,x-oss-request-id` |
| **缓存时间** | 预检请求缓存时间 | `3600`（1小时） |

### 开发vs生产环境

#### 开发环境
```
来源: http://localhost:8088,http://localhost:8090
```

#### 生产环境
```
来源: https://yourdomain.com,https://www.yourdomain.com
```

## 🧪 验证配置

### 1. 检查CORS规则
在OSS控制台确认规则已生效：
- 权限管理 → 跨域设置
- 查看已配置的规则列表

### 2. 测试上传功能
1. 刷新前端页面
2. 尝试上传文件
3. 检查浏览器控制台是否还有CORS错误

### 3. 使用curl测试
```bash
curl -X OPTIONS \
  -H "Origin: http://localhost:8088" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com/
```

## 🔧 故障排除

### 常见问题

#### 1. 配置后仍有CORS错误
- **检查Origin拼写**：确保域名完全匹配
- **等待生效**：配置可能需要几分钟生效
- **清除缓存**：清除浏览器缓存并刷新

#### 2. OPTIONS请求失败
- **添加OPTIONS方法**：确保允许的方法包含OPTIONS
- **检查预检请求**：查看浏览器网络面板的OPTIONS请求

#### 3. 特定头部被阻止
- **允许所有头部**：设置允许Headers为 `*`
- **检查自定义头部**：确保所有自定义头部都被允许

### 调试技巧

#### 1. 浏览器开发者工具
- 打开Network面板
- 查看OPTIONS预检请求
- 检查响应头中的CORS相关字段

#### 2. 检查响应头
成功的CORS响应应包含：
```
Access-Control-Allow-Origin: http://localhost:8088
Access-Control-Allow-Methods: GET,POST,PUT,DELETE,HEAD,OPTIONS
Access-Control-Allow-Headers: *
```

## 🚀 最佳实践

### 1. 安全考虑
- **生产环境**：不要使用 `*` 作为Origin，指定具体域名
- **最小权限**：只允许必要的HTTP方法和头部
- **定期审查**：定期检查和更新CORS规则

### 2. 性能优化
- **合理缓存时间**：设置适当的MaxAgeSeconds减少预检请求
- **精确匹配**：避免过于宽泛的规则

### 3. 维护建议
- **文档记录**：记录所有CORS规则的用途
- **环境隔离**：开发和生产环境使用不同的规则
- **监控告警**：监控CORS相关的错误

## 📝 配置模板

### 开发环境CORS配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<CORSConfiguration>
    <CORSRule>
        <AllowedOrigin>http://localhost:8088</AllowedOrigin>
        <AllowedOrigin>http://localhost:8090</AllowedOrigin>
        <AllowedMethod>GET</AllowedMethod>
        <AllowedMethod>POST</AllowedMethod>
        <AllowedMethod>PUT</AllowedMethod>
        <AllowedMethod>DELETE</AllowedMethod>
        <AllowedMethod>HEAD</AllowedMethod>
        <AllowedMethod>OPTIONS</AllowedMethod>
        <AllowedHeader>*</AllowedHeader>
        <ExposeHeader>ETag</ExposeHeader>
        <ExposeHeader>x-oss-request-id</ExposeHeader>
        <MaxAgeSeconds>3600</MaxAgeSeconds>
    </CORSRule>
</CORSConfiguration>
```

## ✅ 检查清单

配置完成后，请确认：

- [ ] 已在OSS控制台添加CORS规则
- [ ] Origin包含了前端运行的域名和端口
- [ ] 允许的方法包含POST和OPTIONS
- [ ] 允许的头部设置为 `*` 或包含所需头部
- [ ] 配置已生效（等待几分钟）
- [ ] 前端上传功能正常工作
- [ ] 浏览器控制台无CORS错误

完成这些配置后，您的文件上传功能应该可以正常工作了！🎉
