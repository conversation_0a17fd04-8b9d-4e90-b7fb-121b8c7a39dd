# 鼎峰课堂环境配置与构建指南

## 📋 概述

本文档介绍如何为鼎峰课堂项目配置不同环境并进行构建部署。项目支持四种环境：开发(dev)、测试(test)、预发布(staging)、生产(prod)。

## 🏗️ 项目结构

```
dianfeng_class/
├── front/                          # 前端项目
│   ├── admin_front/                # 管理后台
│   │   ├── .env.development        # 开发环境配置
│   │   ├── .env.test              # 测试环境配置
│   │   ├── .env.staging           # 预发布环境配置
│   │   ├── .env.production        # 生产环境配置
│   │   └── vue.config.js          # Vue配置文件
│   └── use_front/                 # 移动端
│       ├── .env.development       # 开发环境配置
│       ├── .env.test             # 测试环境配置
│       ├── .env.staging          # 预发布环境配置
│       ├── .env.production       # 生产环境配置
│       └── app.config.js         # Expo配置文件
├── back/                          # 后端项目
│   └── src/main/resources/
│       ├── application.yaml       # 通用配置
│       ├── application-dev.yaml   # 开发环境配置
│       ├── application-test.yaml  # 测试环境配置
│       ├── application-staging.yaml # 预发布环境配置
│       └── application-prod.yaml  # 生产环境配置
└── deploy/                        # 部署脚本
    ├── build-all.sh              # 全栈构建脚本
    ├── build-frontend.sh         # 前端构建脚本
    ├── build-backend.sh          # 后端构建脚本
    ├── docker-compose.dev.yml    # 开发环境Docker配置
    └── docker-compose.prod.yml   # 生产环境Docker配置
```

## 🔧 环境配置

### 前端环境变量

#### 管理后台 (admin_front)

| 变量名 | 开发环境 | 测试环境 | 预发布环境 | 生产环境 |
|--------|----------|----------|------------|----------|
| VUE_APP_API_BASE_URL | http://localhost:8082/api | http://test-api.dianfeng.com/api | http://staging-api.dianfeng.com/api | https://api.dianfeng.com/api |
| VUE_APP_DEBUG | true | true | false | false |
| VUE_APP_CONSOLE_LOG | true | true | false | false |

#### 移动端 (use_front)

| 变量名 | 开发环境 | 测试环境 | 预发布环境 | 生产环境 |
|--------|----------|----------|------------|----------|
| EXPO_PUBLIC_API_BASE_URL | http://localhost:8082/api | http://test-api.dianfeng.com/api | http://staging-api.dianfeng.com/api | https://api.dianfeng.com/api |
| EXPO_PUBLIC_DEBUG | true | true | false | false |

### 后端环境配置

#### 数据库配置

| 环境 | 数据库地址 | 数据库名 | 连接池大小 |
|------|------------|----------|------------|
| dev | localhost:3306 | dianfeng_class_dev | 10 |
| test | test-db.dianfeng.com:3306 | dianfeng_class_test | 15 |
| staging | staging-db.dianfeng.com:3306 | dianfeng_class_staging | 20 |
| prod | *************:3306 | dianfeng_class | 30 |

#### Redis配置

| 环境 | Redis地址 | 数据库 | 连接池大小 |
|------|-----------|--------|------------|
| dev | localhost:6379 | 0 | 20 |
| test | test-redis.dianfeng.com:6379 | 1 | 20 |
| staging | staging-redis.dianfeng.com:6379 | 2 | 30 |
| prod | *************:16379 | 0 | 50 |

## 🚀 构建命令

### 快速构建

```bash
# 构建所有组件 - 开发环境
./deploy/build-all.sh dev

# 构建所有组件 - 生产环境
./deploy/build-all.sh prod

# 只构建前端 - 测试环境
./deploy/build-all.sh test frontend

# 只构建后端 - 预发布环境
./deploy/build-all.sh staging backend
```

### 前端构建

#### 管理后台

```bash
cd front/admin_front

# 开发环境
pnpm run build:dev

# 测试环境
pnpm run build:test

# 预发布环境
pnpm run build:staging

# 生产环境
pnpm run build:prod
```

#### 移动端

```bash
cd front/use_front

# Web版本构建
pnpm run build:web:dev      # 开发环境
pnpm run build:web:test     # 测试环境
pnpm run build:web:staging  # 预发布环境
pnpm run build:web:prod     # 生产环境

# 移动应用构建
pnpm run build:android:prod  # Android生产版本
pnpm run build:ios:prod      # iOS生产版本
```

### 后端构建

```bash
cd back

# 使用Maven Profile构建
mvn clean package -Pdev      # 开发环境
mvn clean package -Ptest     # 测试环境
mvn clean package -Pstaging  # 预发布环境
mvn clean package -Pprod     # 生产环境

# 或使用构建脚本
./deploy/build-backend.sh dev
./deploy/build-backend.sh prod
```

## 📦 构建产物

### 前端产物

- **管理后台**: `front/admin_front/dist-{环境}/`
- **移动端**: `front/use_front/dist/`

### 后端产物

- **JAR文件**: `back/target/back-0.0.1-SNAPSHOT-{环境}.jar`
- **启动脚本**: `back/start-{环境}.sh`

## 🐳 Docker部署

### 开发环境

```bash
# 启动开发环境（包含MySQL、Redis）
docker-compose -f deploy/docker-compose.dev.yml up -d

# 查看日志
docker-compose -f deploy/docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f deploy/docker-compose.dev.yml down
```

### 生产环境

```bash
# 启动生产环境
docker-compose -f deploy/docker-compose.prod.yml up -d

# 启动包含监控的生产环境
docker-compose -f deploy/docker-compose.prod.yml --profile monitoring up -d

# 查看服务状态
docker-compose -f deploy/docker-compose.prod.yml ps

# 停止服务
docker-compose -f deploy/docker-compose.prod.yml down
```

## 🔍 环境验证

### 前端验证

1. **管理后台**: 访问对应端口，检查API调用是否正确
2. **移动端**: 检查Expo配置和API连接

### 后端验证

1. **健康检查**: `curl http://localhost:8082/api/health`
2. **数据库连接**: 检查应用日志中的数据库连接信息
3. **Redis连接**: 检查缓存功能是否正常

## ⚠️ 注意事项

### 安全配置

1. **生产环境**: 确保关闭调试模式和日志输出
2. **敏感信息**: 生产环境的密码和密钥应通过环境变量注入
3. **HTTPS**: 生产环境必须使用HTTPS

### 性能优化

1. **前端**: 生产环境启用代码压缩和Tree Shaking
2. **后端**: 生产环境使用G1垃圾收集器和合适的JVM参数
3. **数据库**: 根据环境调整连接池大小

### 监控配置

1. **日志**: 不同环境使用不同的日志级别
2. **监控**: 生产环境可启用Prometheus和Grafana监控
3. **告警**: 配置关键指标的告警机制

## 🛠️ 故障排除

### 常见问题

1. **构建失败**: 检查Node.js、Java、Maven版本
2. **环境变量**: 确认.env文件配置正确
3. **端口冲突**: 检查端口是否被占用
4. **权限问题**: 确保脚本有执行权限

### 日志查看

```bash
# 后端日志
tail -f back/logs/dianfeng-class-{环境}.log

# Docker日志
docker logs dianfeng-backend-{环境}

# 前端构建日志
查看构建脚本输出
```

## 📞 技术支持

如遇到问题，请检查：

1. 环境配置是否正确
2. 依赖是否安装完整
3. 网络连接是否正常
4. 服务器资源是否充足

---

*最后更新: 2025-01-07*
