# 鼎峰课堂 - 文件上传功能集成文档

## 📋 概述

本文档描述了为鼎峰课堂后端项目集成 `alltobs-oss` 对象存储功能的完整实现方案。该方案支持多种文件类型的上传、管理和访问，适用于视频网站的各种媒体文件需求。

## 🎯 功能特性

### 支持的文件类型
- **头像文件**: JPG, PNG, GIF (最大10MB)
- **封面图片**: JPG, PNG, GIF (最大10MB)  
- **视频文件**: MP4, AVI, MOV, WMV, FLV, WEBM, MKV, M4V (最大500MB)
- **音频文件**: MP3, WAV, FLAC, AAC, OGG, WMA, M4A (最大50MB)
- **文档文件**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT (最大20MB)

### 核心功能
- ✅ 单文件上传
- ✅ 批量文件上传
- ✅ 文件删除
- ✅ 文件复制
- ✅ 预览URL生成
- ✅ 预签名URL生成
- ✅ 文件标签管理
- ✅ 文件列表查询
- ✅ 自动文件分类
- ✅ 文件大小验证
- ✅ 文件类型验证

## 🏗️ 架构设计

### 项目结构
```
back/src/main/java/pox/com/dianfeng/
├── config/
│   └── OssConfig.java                    # OSS配置类
├── controller/
│   └── FileUploadController.java         # 文件上传控制器
├── entity/dto/
│   └── FileUploadResult.java            # 文件上传结果DTO
├── service/
│   ├── IFileUploadService.java          # 文件上传服务接口
│   └── impl/
│       └── FileUploadServiceImpl.java   # 文件上传服务实现
└── util/
    └── FileUploadUtil.java              # 文件上传工具类
```

### 配置文件
```yaml
# application.yaml
oss:
  endpoint: http://localhost:9000          # MinIO服务地址
  preview-url: http://localhost:9000      # 预览URL
  region: cn-north-1                      # 地区
  access-key: minioadmin                  # 访问密钥ID
  secret-key: minioadmin                  # 访问密钥
  bucket-name: dianfeng-class             # 默认存储桶
  expiring-buckets:                       # 过期存储桶配置
    temp-uploads: 7                       # 临时文件7天过期
    cache-files: 30                       # 缓存文件30天过期
```

## 🔧 技术实现

### 依赖配置
```xml
<!-- pom.xml -->
<dependency>
    <groupId>com.alltobs</groupId>
    <artifactId>alltobs-oss</artifactId>
    <version>1.0.5</version>
</dependency>
```

### 核心组件

#### 1. 配置类 (OssConfig.java)
- 使用 `@EnableAllbsOss` 注解启用自动配置
- 自动读取 application.yaml 中的配置
- 自动注入 `OssTemplate` 实例

#### 2. 工具类 (FileUploadUtil.java)
- 文件扩展名提取
- 唯一文件名生成
- 文件类型验证
- 文件大小验证
- 文件分类识别

#### 3. 服务层 (FileUploadService)
- 单文件上传
- 批量文件上传
- 文件管理操作
- URL生成
- 错误处理

#### 4. 控制器层 (FileUploadController)
- RESTful API 接口
- Swagger 文档注解
- 统一异常处理
- 参数验证

## 📡 API 接口

### 文件上传接口

#### 1. 通用文件上传
```http
POST /api/file/upload
Content-Type: multipart/form-data

Parameters:
- file: 文件 (required)
- category: 文件分类 (required)
```

#### 2. 专用上传接口
```http
POST /api/file/upload/avatar    # 头像上传
POST /api/file/upload/cover     # 封面上传
POST /api/file/upload/video     # 视频上传
POST /api/file/upload/audio     # 音频上传
```

#### 3. 批量上传
```http
POST /api/file/upload/batch
Content-Type: multipart/form-data

Parameters:
- files: 文件列表 (required)
- category: 文件分类 (required)
```

### 文件管理接口

#### 1. 删除文件
```http
DELETE /api/file/delete?fileName={fileName}&bucketName={bucketName}
```

#### 2. 获取预览URL
```http
GET /api/file/preview-url?fileName={fileName}&expireMinutes={minutes}
```

#### 3. 生成预签名上传URL
```http
GET /api/file/presigned-upload-url?fileName={fileName}&bucketName={bucket}&expireMinutes={minutes}
```

#### 4. 文件列表
```http
GET /api/file/list?bucketName={bucket}&prefix={prefix}
```

#### 5. 文件复制
```http
POST /api/file/copy
Parameters:
- sourceFileName: 源文件名
- sourceBucket: 源存储桶
- targetFileName: 目标文件名
- targetBucket: 目标存储桶
```

## 🎨 测试页面

项目提供了完整的测试页面 `upload-demo.html`，包含：

### 功能特性
- 🎨 现代化UI设计
- 📱 响应式布局
- 🔄 实时上传进度
- 📊 详细文件信息显示
- 🎯 分类上传支持
- ⚡ 异步上传处理

### 访问方式
```
http://localhost:8082/api/upload-demo.html
```

## 🚀 部署指南

### 1. 环境准备
```bash
# 使用 Docker 启动 MinIO
docker run -d \
  --name minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v /data/minio:/data \
  minio/minio server /data --console-address ":9001"
```

### 2. 配置修改
根据实际环境修改 `application.yaml` 中的 OSS 配置：
- `endpoint`: MinIO/OSS 服务地址
- `access-key`: 访问密钥ID
- `secret-key`: 访问密钥
- `bucket-name`: 存储桶名称

### 3. 启动应用
```bash
mvn spring-boot:run
```

### 4. 验证功能
- 访问 Swagger 文档: `http://localhost:8082/api/swagger-ui/`
- 访问测试页面: `http://localhost:8082/api/upload-demo.html`

## 🔒 安全考虑

### 文件验证
- 文件类型白名单验证
- 文件大小限制
- 文件名安全处理
- 恶意文件检测

### 访问控制
- 预签名URL过期时间控制
- 存储桶访问权限管理
- API接口权限验证
- 文件访问日志记录

## 📈 性能优化

### 上传优化
- 分片上传支持大文件
- 断点续传功能
- 并发上传控制
- 压缩优化

### 存储优化
- 文件生命周期管理
- 自动过期清理
- 存储成本优化
- CDN加速支持

## 🐛 故障排除

### 常见问题

#### 1. 连接失败
```
检查 MinIO 服务是否启动
验证 endpoint 配置是否正确
确认网络连接是否正常
```

#### 2. 权限错误
```
检查 access-key 和 secret-key 是否正确
验证存储桶是否存在
确认用户权限是否足够
```

#### 3. 文件上传失败
```
检查文件大小是否超限
验证文件类型是否支持
确认存储空间是否充足
```

## 📚 扩展功能

### 计划中的功能
- 🎬 视频转码支持
- 🖼️ 图片压缩处理
- 🔍 文件内容搜索
- 📊 上传统计分析
- 🔄 自动备份机制
- 🌐 多云存储支持

### 集成建议
- 与用户系统集成
- 与课程管理集成
- 与权限系统集成
- 与日志系统集成

## 📞 技术支持

如有问题，请联系开发团队或查看：
- [alltobs-oss GitHub](https://github.com/chenqi92/alltobs-oss)
- [项目文档](./README.md)
- [API文档](http://localhost:8082/api/swagger-ui/)

---

*文档版本: v1.0*  
*最后更新: 2025-01-15*
