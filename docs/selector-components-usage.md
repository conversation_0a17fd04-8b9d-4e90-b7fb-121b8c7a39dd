# 通用选择器组件使用文档

## 📋 组件概述

我们创建了两个通用的选择器组件，支持异步搜索和按名称筛选：

1. **UserSelector** - 用户选择器
2. **CourseSelector** - 课程选择器

## 🎯 组件特性

### 共同特性
- ✅ **异步搜索**：支持远程搜索，输入关键词实时查询
- ✅ **按名称搜索**：用户按用户名搜索，课程按课程标题搜索
- ✅ **单选/多选**：支持单选和多选模式
- ✅ **清空功能**：支持清空选择
- ✅ **加载状态**：显示搜索加载状态
- ✅ **空状态提示**：友好的空状态和搜索提示
- ✅ **初始值加载**：支持初始值的回显

## 🔧 UserSelector 用户选择器

### 基本用法

```vue
<template>
  <UserSelector
    v-model:value="selectedUserId"
    placeholder="请搜索并选择用户"
    @change="handleUserChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import UserSelector from '@/components/Selector/UserSelector.vue'

const selectedUserId = ref(null)

const handleUserChange = (value, users) => {
  console.log('选中的用户ID:', value)
  console.log('选中的用户信息:', users)
}
</script>
```

### 多选模式

```vue
<template>
  <UserSelector
    v-model:value="selectedUserIds"
    :multiple="true"
    placeholder="请搜索并选择多个用户"
    @change="handleUsersChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUserIds = ref([])

const handleUsersChange = (values, users) => {
  console.log('选中的用户ID列表:', values)
  console.log('选中的用户信息列表:', users)
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String/Number/Array | null | 选中的值 |
| placeholder | String | '请选择用户' | 占位符文本 |
| multiple | Boolean | false | 是否多选 |
| clearable | Boolean | true | 是否可清空 |
| showUserId | Boolean | true | 是否显示用户ID |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | value | 值变化时触发 |
| change | (value, users) | 选择变化时触发，返回值和用户信息 |

## 🔧 CourseSelector 课程选择器

### 基本用法

```vue
<template>
  <CourseSelector
    v-model:value="selectedCourseId"
    placeholder="请搜索并选择课程"
    :status="1"
    @change="handleCourseChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import CourseSelector from '@/components/Selector/CourseSelector.vue'

const selectedCourseId = ref(null)

const handleCourseChange = (value, courses) => {
  console.log('选中的课程ID:', value)
  console.log('选中的课程信息:', courses)
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String/Number/Array | null | 选中的值 |
| placeholder | String | '请选择课程' | 占位符文本 |
| multiple | Boolean | false | 是否多选 |
| clearable | Boolean | true | 是否可清空 |
| showCourseId | Boolean | true | 是否显示课程ID |
| status | String/Number | null | 课程状态筛选 |

### Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | value | 值变化时触发 |
| change | (value, courses) | 选择变化时触发，返回值和课程信息 |

## 📱 在权限管理中的应用

### 1. 权限列表搜索

在权限列表页面中，我们使用选择器组件替代了原来的输入框：

```vue
<!-- 原来的输入框 -->
<n-input v-model:value="searchForm.userId" placeholder="请输入用户ID" />

<!-- 现在的选择器 -->
<UserSelector v-model:value="searchForm.userId" placeholder="请搜索用户" />
```

### 2. 授予权限弹窗

在授予权限弹窗中，用户和课程选择更加直观：

```vue
<n-form-item label="选择用户" path="userId">
  <UserSelector
    v-model:value="formData.userId"
    placeholder="请搜索并选择用户"
    @change="handleUserChange"
  />
</n-form-item>

<n-form-item label="选择课程" path="courseId">
  <CourseSelector
    v-model:value="formData.courseId"
    placeholder="请搜索并选择课程"
    :status="1"
    @change="handleCourseChange"
  />
</n-form-item>
```

### 3. 批量授权

批量授权中使用课程选择器：

```vue
<n-form-item label="选择课程" path="courseId">
  <CourseSelector
    v-model:value="formData.courseId"
    placeholder="请搜索并选择课程"
    :status="1"
    @change="handleCourseChange"
  />
</n-form-item>
```

### 4. 权限统计查询

在权限统计页面中使用用户选择器：

```vue
<n-form-item-gi :span="12" label="选择用户" path="userId">
  <UserSelector
    v-model:value="queryForm.userId"
    placeholder="请搜索并选择用户"
    clearable
    @change="handleUserChange"
  />
</n-form-item-gi>
```

## 🎨 UI 改进

### 1. 搜索体验优化
- **实时搜索**：输入2个字符后开始搜索
- **加载状态**：显示搜索加载动画
- **空状态提示**：友好的提示信息

### 2. 显示格式优化
- **用户显示**：`用户名 (ID: 123)` 格式
- **课程显示**：`课程标题 (ID: 456)` 格式
- **可配置显示**：通过 `showUserId`/`showCourseId` 控制是否显示ID

### 3. 交互优化
- **清空功能**：一键清空选择
- **多选支持**：支持批量选择
- **回调信息**：返回完整的对象信息，不仅仅是ID

## 🔍 技术实现

### 1. 异步搜索机制

```javascript
const handleSearch = async (query) => {
  if (!query || query.length < 2) {
    options.value = []
    return
  }

  loading.value = true
  try {
    const { data } = await getUserList({
      page: 1,
      pageSize: 20,
      keyword: query,
      status: 1
    })
    
    options.value = data.records.map(user => ({
      label: `${user.username} (ID: ${user.id})`,
      value: user.id,
      user: user
    }))
  } catch (error) {
    message.error('搜索失败：' + error.message)
  } finally {
    loading.value = false
  }
}
```

### 2. 初始值加载

```javascript
const loadInitialUsers = async () => {
  if (!props.value) return
  
  const values = Array.isArray(props.value) ? props.value : [props.value]
  // 批量加载用户信息
  const users = await Promise.all(
    values.map(userId => getUserInfo(userId))
  )
  
  options.value = users.map(user => ({
    label: `${user.username} (ID: ${user.id})`,
    value: user.id,
    user: user
  }))
}
```

## 🚀 使用建议

### 1. 性能优化
- **搜索防抖**：避免频繁请求
- **结果缓存**：缓存搜索结果
- **分页加载**：限制每次加载数量

### 2. 用户体验
- **最小搜索长度**：设置最小搜索字符数
- **清晰提示**：提供明确的操作提示
- **错误处理**：友好的错误提示

### 3. 扩展性
- **自定义显示**：支持自定义显示格式
- **筛选条件**：支持额外的筛选条件
- **回调扩展**：提供丰富的回调信息

## 📞 技术支持

如有问题，请参考：
- 组件源码：`@/components/Selector/`
- 使用示例：权限管理相关页面
- API文档：用户和课程相关API
