# OSS PostObject 直传功能实现

## 概述

本文档描述了从STS临时凭证方式迁移到OSS PostObject签名方式的文件上传功能实现。PostObject方式相比STS方式具有以下优势：

- **更简单的权限管理**：无需配置复杂的RAM角色和STS服务
- **更好的安全性**：签名在服务端生成，密钥不暴露给前端
- **更高的性能**：减少了获取临时凭证的网络请求
- **更低的成本**：无需STS服务调用费用

## 架构设计

### 后端实现

#### 1. 配置类 (AliyunOssPostObjectConfig)
```java
@Data
@Component
@ConfigurationProperties(prefix = "oss.post-object")
public class AliyunOssPostObjectConfig {
    private Integer expireSeconds = 3600;  // 签名有效期（秒）
    private Long maxFileSize = 100 * 1024 * 1024L;  // 默认最大文件大小100MB
}
```

#### 2. 响应DTO (OssPostObjectResponse)
包含上传所需的所有信息：
- uploadUrl: OSS上传地址
- objectKey: 文件对象键
- policy: Base64编码的策略
- signature: 签名
- formFields: 表单字段
- fileUrl: 文件访问URL

#### 3. 服务接口 (OssPostObjectService)
提供两个主要方法：
- `generatePostObjectSignature(category, fileName)`: 根据分类生成签名
- `generatePostObjectSignature(pathPrefix, fileName, contentType, maxFileSize)`: 自定义参数生成签名

#### 4. 控制器 (OssPostObjectController)
提供REST API接口：
- `GET /oss/signature`: 获取PostObject签名
- `GET /oss/signature/custom`: 获取自定义PostObject签名

### 前端实现

#### 1. API封装 (oss.js)
```javascript
// 获取OSS PostObject签名
export function getOssSignature(category, fileName = null)

// 使用PostObject方式上传文件
export function uploadToOssWithPostObject(file, signature, onProgress)

// 便捷的文件上传函数（获取签名+上传）
export function uploadFile(file, category, onProgress)
```

#### 2. 组件更新
- **OssDirectUpload.vue**: 图片/文档上传组件
- **OssVideoUpload.vue**: 视频上传组件

## 使用方式

### 1. 后端配置

在 `application.yaml` 中配置：
```yaml
oss:
  post-object:
    expire-seconds: 3600      # 签名有效期
    max-file-size: 104857600  # 默认最大文件大小
```

### 2. 前端使用

#### 简单上传
```javascript
import { uploadFile } from '@/api/oss';

// 上传文件
const result = await uploadFile(file, 'image', (progress) => {
  console.log('上传进度:', progress + '%');
});
```

#### 自定义上传
```javascript
import { getOssSignature, uploadToOssWithPostObject } from '@/api/oss';

// 1. 获取签名
const signatureResponse = await getOssSignature('image', 'avatar.jpg');
const signature = signatureResponse.data;

// 2. 上传文件
const result = await uploadToOssWithPostObject(file, signature, onProgress);
```

## 文件分类和限制

| 分类 | 允许的文件类型 | 最大文件大小 | 路径前缀 |
|------|---------------|-------------|----------|
| image | jpg, png, gif, webp, bmp | 10MB | image/yyyy/MM/dd/ |
| video | mp4, avi, mov, wmv, flv, mkv, webm | 500MB | video/yyyy/MM/dd/ |
| audio | mp3, wav, aac, flac, ogg, m4a | 50MB | audio/yyyy/MM/dd/ |
| document | pdf, doc, docx | 20MB | document/yyyy/MM/dd/ |

## 安全特性

1. **签名验证**：所有上传请求都需要有效的签名
2. **时间限制**：签名有过期时间，默认1小时
3. **文件大小限制**：根据文件类型限制最大上传大小
4. **文件类型限制**：只允许指定类型的文件上传
5. **路径限制**：文件只能上传到指定的路径前缀下

## 测试页面

访问 `/test/oss-upload` 可以测试上传功能，包括：
- 图片上传测试
- 视频上传测试
- API签名测试
- 错误处理测试

## 迁移说明

从STS方式迁移到PostObject方式的主要变更：

1. **后端**：
   - 移除STS相关配置和代码
   - 添加PostObject签名生成逻辑
   - 更新API接口

2. **前端**：
   - 更新API调用方式
   - 修改上传逻辑
   - 移除STS Token相关代码

3. **配置**：
   - 移除RAM角色配置
   - 简化OSS配置
   - 更新权限设置

## 故障排除

### 常见问题

1. **签名错误**：检查AccessKey和SecretKey配置
2. **上传失败**：检查文件大小和类型限制
3. **权限错误**：确认OSS Bucket权限配置
4. **CORS错误**：检查OSS Bucket的CORS设置

### 调试方法

1. 查看后端日志中的签名生成信息
2. 使用浏览器开发者工具检查网络请求
3. 访问测试页面进行功能验证
4. 检查OSS控制台的访问日志

## 性能优化

1. **并发上传**：支持多文件同时上传
2. **断点续传**：大文件支持断点续传（需要额外实现）
3. **压缩优化**：图片自动压缩（可选）
4. **CDN加速**：配置CDN加速文件访问

## 总结

PostObject方式相比STS方式更加简单、安全、高效。通过本次迁移，我们实现了：

- ✅ 简化了权限配置
- ✅ 提高了安全性
- ✅ 优化了性能
- ✅ 降低了成本
- ✅ 改善了用户体验

建议在生产环境中使用PostObject方式进行文件上传。
