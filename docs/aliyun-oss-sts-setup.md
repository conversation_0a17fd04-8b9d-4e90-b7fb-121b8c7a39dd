# 阿里云OSS STS直传配置指南

## 概述

为了解决大文件上传时的 413 Payload Too Large 错误，我们实现了阿里云OSS的STS（Security Token Service）直传功能。这样前端可以直接上传文件到OSS，不经过后端服务器，避免文件大小限制。

## 🔧 配置步骤

### 1. 创建RAM角色

在阿里云控制台中创建一个用于OSS上传的RAM角色：

#### 1.1 进入RAM控制台
- 登录阿里云控制台
- 搜索并进入"访问控制 RAM"

#### 1.2 创建角色
1. 点击"角色" -> "创建角色"
2. 选择"阿里云服务"
3. 角色类型选择"普通服务角色"
4. 角色名称：`OSSUploadRole`
5. 备注：`用于前端直传OSS的角色`

#### 1.3 配置信任策略
```json
{
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Effect": "Allow",
      "Principal": {
        "RAM": [
          "acs:ram::你的账号ID:root"
        ]
      }
    }
  ],
  "Version": "1"
}
```

#### 1.4 添加权限策略
为角色添加OSS上传权限：

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "oss:PutObject",
        "oss:PutObjectAcl"
      ],
      "Resource": "acs:oss:*:*:dianfeng-class/*"
    }
  ]
}
```

### 2. 获取角色ARN

创建完成后，在角色详情页面可以看到角色ARN，格式如下：
```
acs:ram::1282081849547244:role/OSSUploadRole
```

### 3. 配置应用

在 `application.yaml` 中配置STS相关信息：

```yaml
# 阿里云STS配置 - 用于前端直传OSS
aliyun:
  sts:
    # 阿里云访问密钥ID（与OSS相同）
    access-key-id: LTAI5tP9cnnNBvQzusQRuMuh
    # 阿里云访问密钥Secret（与OSS相同）
    access-key-secret: ******************************
    # 地域ID
    region-id: cn-chengdu
    # STS服务端点
    endpoint: sts.cn-chengdu.aliyuncs.com
    # 角色ARN（替换为你实际的角色ARN）
    role-arn: acs:ram::1282081849547244:role/OSSUploadRole
    # 会话名称
    role-session-name: dianfeng-oss-upload
    # 临时凭证有效期（秒），最小900秒，最大3600秒
    duration-seconds: 3600
```

## 🚀 功能特点

### 1. 直传优势
- ✅ 无文件大小限制（不经过服务器）
- ✅ 上传速度更快（直连OSS）
- ✅ 减少服务器带宽压力
- ✅ 支持断点续传
- ✅ 实时上传进度显示

### 2. 安全特性
- ✅ 临时访问凭证（STS Token）
- ✅ 权限最小化原则
- ✅ 路径限制（只能上传到指定目录）
- ✅ 文件大小限制
- ✅ 文件类型验证

### 3. 用户体验
- ✅ 拖拽上传支持
- ✅ 实时进度显示
- ✅ 上传速度显示
- ✅ 文件预览功能
- ✅ 错误处理和重试

## 📡 API接口

### 获取STS临时凭证
```http
GET /api/sts/token?category=video

Response:
{
  "code": 200,
  "data": {
    "accessKeyId": "STS.xxx",
    "accessKeySecret": "xxx",
    "securityToken": "xxx",
    "expiration": "2025-01-15T10:00:00Z",
    "bucketName": "dianfeng-class",
    "endpoint": "https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com",
    "region": "cn-chengdu",
    "pathPrefix": "video/2025/01/15/",
    "maxFileSize": 524288000,
    "allowedFileTypes": ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"],
    "durationSeconds": 3600
  }
}
```

## 🎯 前端组件使用

### OSS直传图片组件
```vue
<template>
  <OssDirectUpload
    v-model="imageUrl"
    category="image"
    :show-preview="true"
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
  />
</template>

<script setup>
import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";
</script>
```

### OSS直传视频组件
```vue
<template>
  <OssVideoUpload
    v-model="videoUrl"
    :show-url-input="true"
    @upload-success="handleVideoUploadSuccess"
    @video-loaded="handleVideoLoaded"
  />
</template>

<script setup>
import OssVideoUpload from "@/components/VideoUpload/OssVideoUpload.vue";
</script>
```

## 🔒 安全注意事项

### 1. 权限控制
- STS角色权限应该最小化
- 只授予必要的OSS操作权限
- 定期检查和更新权限策略

### 2. 访问控制
- 临时凭证有效期不宜过长
- 路径前缀限制上传目录
- 文件类型和大小验证

### 3. 监控和审计
- 启用OSS访问日志
- 监控异常上传行为
- 定期清理过期文件

## 🐛 故障排除

### 常见问题

#### 1. STS凭证获取失败
```
错误：获取STS临时凭证失败
解决：检查RAM角色ARN是否正确，确认角色权限配置
```

#### 2. 上传权限被拒绝
```
错误：AccessDenied
解决：检查STS角色是否有PutObject权限，确认路径前缀配置
```

#### 3. 文件上传失败
```
错误：文件上传失败
解决：检查网络连接，确认OSS endpoint配置正确
```

## 📈 性能优化

### 1. 上传优化
- 使用分片上传处理大文件
- 启用断点续传功能
- 合理设置并发上传数量

### 2. 网络优化
- 选择就近的OSS地域
- 使用CDN加速上传
- 启用传输加速

### 3. 用户体验优化
- 显示上传进度和速度
- 提供取消上传功能
- 实现文件预览功能

## 📝 更新日志

- **v1.0.0**: 初始版本，支持基本的STS直传功能
- **v1.1.0**: 添加视频专用上传组件
- **v1.2.0**: 集成到课程创建页面
- **v1.3.0**: 优化用户体验和错误处理
