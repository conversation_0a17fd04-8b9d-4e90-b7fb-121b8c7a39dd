# 客服搜索功能实现文档

## 📋 功能概述

客服管理系统新增搜索功能，支持按姓名和联系方式（手机号、微信、邮箱、QQ）进行模糊查询，联系方式支持 OR 逻辑查询。

## 🎯 功能特点

### 1. 搜索条件
- **客服姓名**：支持模糊查询
- **联系方式**：支持 OR 查询，一个输入框可搜索手机号、微信、邮箱、QQ
- **状态**：支持精确查询（启用/禁用）

### 2. 查询逻辑
```sql
-- 基本查询条件
WHERE is_del = 0

-- 姓名搜索
AND name LIKE '%搜索词%'

-- 联系方式 OR 查询
AND (phone LIKE '%搜索词%' 
     OR wechat LIKE '%搜索词%' 
     OR email LIKE '%搜索词%' 
     OR qq LIKE '%搜索词%')

-- 状态查询
AND status = true/false
```

## 🚀 前端实现

### 1. 搜索表单
```vue
<div class="search-bar">
  <n-form inline :label-width="80">
    <n-form-item label="客服姓名">
      <n-input
        v-model:value="searchParams.name"
        clearable
        placeholder="请输入客服姓名"
      />
    </n-form-item>
    <n-form-item label="联系方式">
      <n-input
        v-model:value="searchParams.contact"
        clearable
        placeholder="请输入手机号、微信、邮箱或QQ"
      />
    </n-form-item>
    <n-form-item label="状态">
      <n-select
        v-model:value="searchParams.status"
        :options="statusOptions"
        clearable
        placeholder="请选择状态"
        style="width: 120px"
      />
    </n-form-item>
    <n-form-item>
      <n-button type="primary" @click="loadData">
        <template #icon>
          <n-icon><SearchOutline /></n-icon>
        </template>
        搜索
      </n-button>
      <n-button class="m-l-10" @click="resetSearch">
        <template #icon>
          <n-icon><RefreshOutline /></n-icon>
        </template>
        重置
      </n-button>
    </n-form-item>
  </n-form>
</div>
```

### 2. 搜索参数
```javascript
// 搜索参数
const searchParams = reactive({
  name: "",      // 客服姓名
  contact: "",   // 联系方式（合并查询）
  status: null,  // 状态
});

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "禁用", value: false },
];
```

### 3. 重置搜索
```javascript
const resetSearch = () => {
  Object.keys(searchParams).forEach((key) => {
    searchParams[key] = "";
  });
  searchParams.status = null;
  pagination.page = 1;
  loadData();
};
```

## 🔧 后端实现

### 1. 控制器方法
```java
@ApiOperation("分页查询客服信息")
@GetMapping("/page")
public R<IPage<CustomerServiceContacts>> page(PageParam pageParam,
                                             @RequestParam(required = false) String name,
                                             @RequestParam(required = false) String contact,
                                             @RequestParam(required = false) Boolean status) {
    // 创建分页对象
    Page<CustomerServiceContacts> page = 
        new Page<>(pageParam.getPageNum(), pageParam.getPageSize());

    // 构建查询条件
    QueryWrapper<CustomerServiceContacts> queryWrapper = new QueryWrapper<>();
    
    // 只查询未删除的记录
    queryWrapper.eq("is_del", 0);

    // 客服姓名模糊查询
    if (StringUtils.hasText(name)) {
        queryWrapper.like("name", name);
    }

    // 联系方式模糊查询（OR查询）
    if (StringUtils.hasText(contact)) {
        queryWrapper.and(wrapper -> wrapper
            .like("phone", contact)
            .or()
            .like("wechat", contact)
            .or()
            .like("email", contact)
            .or()
            .like("qq", contact)
        );
    }

    // 状态查询
    if (status != null) {
        queryWrapper.eq("status", status);
    }

    // 默认按创建时间降序
    queryWrapper.orderByDesc("created_at");

    // 执行分页查询
    IPage<CustomerServiceContacts> result = service.page(page, queryWrapper);

    // 后置处理（设置头像完整URL）
    postProcessPageResult(result);

    return R.ok(result);
}
```

### 2. 数据库字段映射
```xml
<!-- CustomerServiceContactsMapper.xml -->
<resultMap id="BaseResultMap" type="pox.com.dianfeng.entity.CustomerServiceContacts">
    <id column="id" property="id" />
    <result column="name" property="name" />
    <result column="avatar" property="avatar" />
    <result column="phone" property="phone" />
    <result column="wechat" property="wechat" />
    <result column="email" property="email" />
    <result column="qq" property="qq" />
    <result column="remark" property="remark" />
    <result column="is_del" property="isDel" />
    <result column="status" property="status" />
    <result column="created_at" property="createdAt" />
    <result column="updated_at" property="updatedAt" />
</resultMap>
```

## 📊 API 接口

### 1. 请求示例
```bash
# 基本查询
GET /api/customer-service-contacts/page?pageNum=1&pageSize=10

# 按姓名搜索
GET /api/customer-service-contacts/page?pageNum=1&pageSize=10&name=张三

# 按联系方式搜索
GET /api/customer-service-contacts/page?pageNum=1&pageSize=10&contact=138

# 按状态搜索
GET /api/customer-service-contacts/page?pageNum=1&pageSize=10&status=true

# 组合搜索
GET /api/customer-service-contacts/page?pageNum=1&pageSize=10&name=张三&contact=138&status=true
```

### 2. 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "张三客服",
        "avatar": "avatars/customer-service/avatar1.jpg",
        "avatarFullUrl": "https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/avatars/customer-service/avatar1.jpg",
        "phone": "13812345678",
        "wechat": "zhangsan_wechat",
        "email": "<EMAIL>",
        "qq": "123456789",
        "remark": "负责课程咨询",
        "status": true,
        "createdAt": "2025-01-20T10:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

## 🧪 测试方案

### 1. 使用测试脚本
```bash
# 运行测试脚本
./scripts/test_customer_service_search.sh
```

### 2. 前端测试步骤
1. 打开客服管理页面
2. 在搜索栏输入客服姓名，点击搜索
3. 在联系方式输入框中输入手机号/微信/邮箱/QQ，验证 OR 查询
4. 选择不同状态进行筛选
5. 使用组合条件搜索
6. 点击重置按钮验证重置功能

### 3. 测试用例
| 测试项目 | 输入条件 | 期望结果 |
|---------|---------|---------|
| 姓名搜索 | name="张" | 返回姓名包含"张"的客服 |
| 手机号搜索 | contact="138" | 返回手机号包含"138"的客服 |
| 微信搜索 | contact="wechat" | 返回微信包含"wechat"的客服 |
| 邮箱搜索 | contact="@qq.com" | 返回邮箱包含"@qq.com"的客服 |
| QQ搜索 | contact="123" | 返回QQ包含"123"的客服 |
| 状态筛选 | status=true | 返回启用状态的客服 |
| 组合搜索 | name="张"&contact="138"&status=true | 返回符合所有条件的客服 |
| 重置功能 | 点击重置按钮 | 清空所有搜索条件，显示全部数据 |

## 📝 注意事项

### 1. 性能考虑
- 联系方式 OR 查询可能对性能有影响，建议添加相关字段索引
- 建议对搜索结果进行分页限制

### 2. 数据验证
- 前端对搜索条件进行基本验证
- 后端对参数进行安全检查，防止 SQL 注入

### 3. 用户体验
- 搜索支持实时清空
- 提供搜索提示和占位符
- 搜索结果为空时显示友好提示

## 🔄 扩展功能

### 1. 高级搜索
- 支持创建时间范围搜索
- 支持更多字段的组合查询

### 2. 搜索优化
- 添加搜索历史记录
- 支持搜索关键词高亮显示
- 添加搜索建议功能

### 3. 导出功能
- 支持搜索结果导出
- 支持按搜索条件导出指定数据 