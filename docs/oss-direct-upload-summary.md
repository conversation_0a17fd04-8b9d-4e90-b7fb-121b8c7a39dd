# 阿里云OSS直传功能实现总结

## 📋 项目概述

为了解决大文件上传时的 413 Payload Too Large 错误，我们实现了阿里云OSS的STS（Security Token Service）直传功能。这样前端可以直接上传文件到OSS，不经过后端服务器，避免文件大小限制。

## 🎯 解决的问题

### 原始问题
- **413 Payload Too Large**: 大文件通过后端服务器上传时超出限制
- **服务器压力**: 大文件上传占用服务器带宽和资源
- **上传速度**: 经过服务器中转导致上传速度慢
- **用户体验**: 大文件上传时间长，容易失败

### 解决方案
- **OSS直传**: 前端直接上传到阿里云OSS
- **STS临时凭证**: 使用临时访问凭证确保安全
- **权限控制**: 限制上传路径和文件大小
- **进度显示**: 实时显示上传进度和速度

## 🏗️ 架构设计

### 后端架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端服务      │    │   阿里云STS     │
│                 │    │                 │    │                 │
│ 1. 请求STS凭证  │───▶│ 2. 调用STS API  │───▶│ 3. 返回临时凭证 │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              ┌─────────────────┐            │
         │              │   阿里云OSS     │            │
         │              │                 │            │
         └─────────────▶│ 4. 直接上传文件 │◀───────────┘
                        │                 │
                        └─────────────────┘
```

### 组件架构
```
前端组件层
├── OssDirectUpload.vue      # 通用OSS直传组件
├── OssVideoUpload.vue       # 专用视频上传组件
└── api/
    ├── oss.js              # OSS直传API
    └── file.js             # 原有文件上传API

后端服务层
├── StsController.java       # STS凭证控制器
├── StsService.java         # STS服务接口
├── StsServiceImpl.java     # STS服务实现
└── config/
    └── AliyunStsConfig.java # STS配置类
```

## 🔧 技术实现

### 1. 后端STS服务

#### 依赖配置
```xml
<!-- 阿里云OSS SDK -->
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.17.4</version>
</dependency>

<!-- 阿里云STS SDK -->
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>aliyun-java-sdk-sts</artifactId>
    <version>3.1.2</version>
</dependency>
```

#### 核心接口
- `GET /api/sts/token?category=video` - 获取STS临时凭证
- `GET /api/sts/token/custom` - 获取自定义STS凭证

#### 权限策略
```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["oss:PutObject", "oss:PutObjectAcl"],
      "Resource": "acs:oss:*:*:bucket-name/path-prefix*",
      "Condition": {
        "NumericLessThanEquals": {
          "oss:content-length": 524288000
        }
      }
    }
  ]
}
```

### 2. 前端OSS直传

#### 核心组件
- **OssDirectUpload**: 通用文件上传组件
- **OssVideoUpload**: 专用视频上传组件

#### 技术特点
- 拖拽上传支持
- 实时进度显示
- 文件预览功能
- 错误处理和重试
- 文件类型和大小验证

#### 依赖库
```json
{
  "ali-oss": "^6.18.1"
}
```

## 📊 功能特性

### 安全特性
- ✅ STS临时凭证（有效期1小时）
- ✅ 权限最小化原则
- ✅ 路径前缀限制
- ✅ 文件大小限制
- ✅ 文件类型验证

### 用户体验
- ✅ 拖拽上传
- ✅ 实时进度显示
- ✅ 上传速度显示
- ✅ 文件预览
- ✅ 错误提示和恢复

### 性能优化
- ✅ 直连OSS，无服务器中转
- ✅ 支持大文件上传（最大500MB视频）
- ✅ 断点续传支持
- ✅ 并发上传控制

## 🔧 配置要求

### 阿里云RAM角色配置
1. 创建RAM角色：`OSSUploadRole`
2. 配置信任策略允许STS服务
3. 添加OSS上传权限策略
4. 获取角色ARN：`acs:ram::账号ID:role/OSSUploadRole`

### 应用配置
```yaml
aliyun:
  sts:
    access-key-id: ${ALIYUN_ACCESS_KEY_ID}
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
    region-id: cn-chengdu
    role-arn: acs:ram::1282081849547244:role/OSSUploadRole
    duration-seconds: 3600
```

## 📈 性能对比

### 上传方式对比
| 特性 | 服务器中转 | OSS直传 |
|------|------------|---------|
| 文件大小限制 | 受服务器限制 | 无限制 |
| 上传速度 | 较慢 | 快速 |
| 服务器压力 | 高 | 低 |
| 网络带宽 | 占用服务器带宽 | 直连OSS |
| 可靠性 | 依赖服务器稳定性 | OSS高可用 |

### 支持的文件类型和大小
- **图片**: jpg,jpeg,png,gif,webp - 最大10MB
- **视频**: mp4,avi,mov,wmv,flv,mkv,webm - 最大500MB
- **音频**: mp3,wav,aac,flac,ogg,m4a - 最大50MB
- **文档**: pdf,doc,docx,xls,xlsx,ppt,pptx,txt - 最大20MB

## 🚀 部署步骤

### 1. 后端部署
1. 配置阿里云RAM角色
2. 更新application.yaml配置
3. 部署后端服务
4. 测试STS接口

### 2. 前端部署
1. 安装依赖：`pnpm add ali-oss`
2. 集成OSS上传组件
3. 配置API接口
4. 测试上传功能

## 🐛 问题排查

### 常见问题

#### 1. STS凭证获取失败
```
错误：获取STS临时凭证失败
原因：RAM角色ARN配置错误或权限不足
解决：检查角色ARN格式，确认角色权限
```

#### 2. 上传权限被拒绝
```
错误：AccessDenied
原因：STS角色缺少OSS上传权限
解决：为角色添加oss:PutObject权限
```

#### 3. 文件上传失败
```
错误：XHR error
原因：使用模拟STS凭证或网络问题
解决：配置真实STS凭证，检查网络连接
```

## 🔄 当前状态

### 已完成功能
- ✅ STS服务端实现（支持真实和模拟凭证）
- ✅ 前端OSS直传组件
- ✅ 文件类型和大小验证
- ✅ 进度显示和错误处理
- ✅ 课程创建页面集成
- ✅ 配置脚本和测试工具
- ✅ 详细的文档和指南

### 当前状态
- ⚠️ **使用模拟STS凭证**：当前返回模拟数据，前端会提示配置真实角色
- ✅ **功能完整**：所有代码已实现，只需配置真实RAM角色即可使用
- ✅ **错误处理**：前端会检测模拟凭证并给出明确提示

### 快速启用真实功能
```bash
# 1. 运行配置脚本
./scripts/setup-sts.sh

# 2. 按照提示配置RAM角色ARN

# 3. 重启后端服务

# 4. 测试配置
./scripts/test-sts.sh
```

### 下一步计划
1. ✅ 配置真实的阿里云RAM角色（用户操作）
2. ✅ 更新STS服务使用真实凭证（自动切换）
3. ✅ 测试完整的上传流程（测试脚本）
4. 🔄 优化错误处理和用户体验（持续改进）
5. 🔄 添加上传日志和监控（后续功能）

## 📚 参考文档

- [阿里云OSS STS临时访问凭证](https://help.aliyun.com/zh/oss/developer-reference/use-temporary-access-credentials-provided-by-sts-to-access-oss)
- [阿里云RAM角色管理](https://help.aliyun.com/zh/ram/user-guide/create-a-ram-role-for-a-trusted-alibaba-cloud-service)
- [OSS JavaScript SDK](https://help.aliyun.com/zh/oss/developer-reference/installation-11)

## 🎉 总结

通过实现OSS直传功能，我们成功解决了大文件上传的问题，提升了用户体验和系统性能。当前系统已具备完整的直传能力，只需配置真实的RAM角色即可投入生产使用。
