# 首页推荐管理功能错误修复文档 🔧

## 🐛 修复的问题

### 1. 运行时错误修复

**错误信息：**
```
Cannot read properties of undefined (reading 'error')
TypeError: Cannot read properties of undefined (reading 'error')
```

**原因分析：**
- `window.$message` 和 `window.$dialog` 未正确初始化
- HTTP请求使用了不存在的 `window.$http`

**修复方案：**

#### 1.1 全局Message和Dialog设置
在 `front/admin_front/src/layout/index.vue` 中添加：

```javascript
onMounted(() => {
  // 设置全局的message和dialog
  const message = useMessage();
  const dialog = useDialog();
  
  window.$message = message;
  window.$dialog = dialog;
});
```

#### 1.2 HTTP请求修复
替换 `window.$http` 为正确的 `request` 导入：

```javascript
import request from "@/api/request";

// API请求函数
const api = {
  getList: (params) => request.get("/homepage-recommendations", { params }),
  create: (data) => request.post("/homepage-recommendations", data),
  // ... 其他API方法
};
```

#### 1.3 添加降级处理
为防止初始化失败，添加降级处理：

```javascript
const message = window.$message || {
  success: (msg) => console.log('Success:', msg),
  error: (msg) => console.error('Error:', msg),
  warning: (msg) => console.warn('Warning:', msg),
  info: (msg) => console.info('Info:', msg)
};

const dialog = window.$dialog || {
  warning: (options) => {
    if (confirm(options.content)) {
      options.onPositiveClick && options.onPositiveClick();
    }
  }
};
```

### 2. 字段映射修复

**问题：**
- 前端使用的字段名与后端实体类不匹配
- 图片字段命名不一致

**修复内容：**

#### 2.1 统一字段命名
- `imageUrl` → `coverImage` (相对路径)
- `imageUrl` → `coverImageFullUrl` (完整URL)
- `createTime` → `createdAt`

#### 2.2 表单验证路径修复
```javascript
// 修复前
<n-form-item label="封面图片" path="imageUrl">

// 修复后  
<n-form-item label="封面图片" path="coverImage">
```

### 3. 组件功能完善

#### 3.1 关联选择功能
- 添加链接类型选择（课程/直播/外部链接）
- 实现动态表单项显示
- 添加课程和直播选择器（支持搜索和预览）

#### 3.2 表单验证增强
```javascript
const rules = {
  title: [
    { required: true, message: "请输入推荐标题", trigger: "blur" },
    { min: 1, max: 100, message: "标题长度必须在1-100个字符之间", trigger: "blur" }
  ],
  linkType: [
    { required: true, message: "请选择链接类型", trigger: "change" }
  ],
  linkTargetId: [
    {
      validator: (rule, value) => {
        if (formData.linkType === 1 || formData.linkType === 2) {
          if (!value) {
            return new Error(formData.linkType === 1 ? "请选择关联课程" : "请选择关联直播");
          }
        }
        return true;
      },
      trigger: "change"
    }
  ],
  // ... 其他验证规则
};
```

## ✅ 修复结果

1. **运行时错误已解决** - 页面可以正常加载和运行
2. **API调用正常** - 使用正确的HTTP请求方法
3. **字段映射一致** - 前后端字段名统一
4. **功能完整** - 支持封面上传、关联选择、表单验证等

## 🚀 使用说明

1. **启动后端服务**：确保数据库表已创建
2. **启动前端服务**：`npm run serve` 或 `yarn serve`
3. **访问页面**：导航到 `直播推荐 > 首页推荐`
4. **测试功能**：
   - 点击"添加推荐"创建新推荐
   - 上传封面图片
   - 选择关联课程或直播
   - 保存并查看列表

## 📝 注意事项

1. **数据库表**：确保已执行 `scripts/add_homepage_recommendations.sql`
2. **图片上传**：目前使用本地预览，生产环境需要集成OSS
3. **关联数据**：课程和直播选择目前使用模拟数据
4. **权限控制**：根据需要添加操作权限验证

## 🔄 后续优化

1. 集成真实的课程和直播API
2. 添加图片OSS上传功能
3. 优化错误处理和用户体验
4. 添加更多的数据验证
