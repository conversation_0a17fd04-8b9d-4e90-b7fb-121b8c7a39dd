# 测试验证指南

## 🧪 测试步骤

### 1. 后端测试

#### 启动后端服务
```bash
cd back
mvn spring-boot:run
```

#### 测试文件上传API
```bash
# 测试图片上传
curl -X POST http://localhost:8080/api/file/upload \
  -F "file=@test-image.jpg" \
  -F "category=image"

# 预期返回格式：
{
  "code": 200,
  "data": {
    "fileName": "image/2025/01/15/abc123.jpg",  // 相对路径
    "fileUrl": "image/2025/01/15/abc123.jpg",   // 相对路径
    "previewUrl": "https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/image/2025/01/15/abc123.jpg", // 完整URL
    "originalFileName": "test-image.jpg",
    "fileSize": 12345,
    "contentType": "image/jpeg",
    "category": "image"
  }
}
```

#### 测试课程创建API
```bash
# 测试课程创建
curl -X POST http://localhost:8080/api/courses-manage \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试课程",
    "subtitle": "测试副标题",
    "description": "测试描述",
    "coverImage": "image/2025/01/15/abc123.jpg",
    "teacherId": 1,
    "categoryId": 1,
    "price": 99.00,
    "level": 1,
    "ageGroup": 1
  }'

# 预期返回格式：
{
  "code": 200,
  "data": {
    "id": 123,  // 课程ID
    "title": "测试课程",
    "coverImage": "image/2025/01/15/abc123.jpg",  // 相对路径
    // ... 其他字段
  }
}
```

### 2. 前端测试

#### 启动前端服务
```bash
cd front/admin_front
pnpm dev
```

#### 测试文件上传组件
1. 访问课程创建页面：`http://localhost:3000/course/create`
2. 上传课程封面图片
3. 检查浏览器开发者工具：
   - Network标签：查看上传请求和响应
   - Console标签：查看日志输出

#### 验证数据存储
1. 完成课程创建流程
2. 检查数据库中的数据：
```sql
-- 检查课程表中的封面图片字段
SELECT id, title, cover_image FROM courses ORDER BY id DESC LIMIT 5;

-- 预期结果：cover_image字段存储的是相对路径
-- 例如：image/2025/01/15/abc123.jpg
```

### 3. 功能验证

#### 文件显示测试
1. 创建包含封面的课程
2. 在课程列表页面查看封面是否正常显示
3. 编辑课程时封面预览是否正常

#### 课程创建流程测试
1. 填写基本信息（包括上传封面）
2. 添加章节和课时
3. 提交创建
4. 验证课程、章节、课时是否正确关联

## 🔍 验证要点

### 数据库验证
- [ ] 文件路径存储为相对路径（不包含域名）
- [ ] 课程创建后返回正确的ID
- [ ] 章节和课时正确关联到课程

### 前端验证
- [ ] 文件上传后预览正常显示
- [ ] 已有文件的预览正常显示
- [ ] 课程创建流程完整无错误

### API验证
- [ ] 文件上传API返回相对路径和预览URL
- [ ] 课程创建API返回完整的课程对象
- [ ] 章节和课时创建API正常工作

## 🐛 常见问题排查

### 文件显示问题
如果文件无法显示，检查：
1. OSS配置是否正确
2. 文件路径格式是否正确
3. 网络连接是否正常

### 课程创建问题
如果课程创建失败，检查：
1. 必填字段是否完整
2. 数据类型是否正确
3. 后端日志是否有错误信息

### 数据关联问题
如果章节课时关联失败，检查：
1. 课程ID是否正确返回
2. 章节ID是否正确获取
3. 数据库外键约束是否正确

## 📊 性能测试

### 文件上传性能
```bash
# 使用ab工具测试并发上传
ab -n 10 -c 2 -p test-file.jpg -T multipart/form-data \
  http://localhost:8080/api/file/upload
```

### 课程创建性能
```bash
# 测试课程创建响应时间
time curl -X POST http://localhost:8080/api/courses-manage \
  -H "Content-Type: application/json" \
  -d @test-course.json
```

## ✅ 验收标准

### 功能完整性
- [x] 文件上传功能正常
- [x] 文件预览功能正常
- [x] 课程创建功能正常
- [x] 数据关联功能正常

### 数据一致性
- [x] 数据库存储格式正确
- [x] 前端显示格式正确
- [x] API返回格式正确

### 性能要求
- [x] 文件上传响应时间 < 5秒
- [x] 课程创建响应时间 < 2秒
- [x] 文件预览加载时间 < 3秒

## 🚀 部署前检查

1. **代码审查**：确保所有修改都已提交
2. **测试覆盖**：确保所有功能都已测试
3. **数据备份**：部署前备份生产数据库
4. **回滚方案**：准备回滚脚本和流程
5. **监控准备**：配置相关监控和告警
