# 文件上传组件使用指南

## 概述

我们为前端项目创建了两个通用的文件上传组件，支持进度显示、预览图片和视频等功能。

## 组件列表

### 1. FileUpload - 通用文件上传组件

**位置**: `@/components/FileUpload`

**功能特点**:
- 支持拖拽上传
- 实时上传进度显示
- 图片和视频预览
- 文件类型和大小验证
- 支持多种文件分类（image/video/audio/document）

**基本用法**:
```vue
<template>
  <FileUpload
    v-model="fileUrl"
    category="image"
    :show-preview="true"
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
  />
</template>

<script setup>
import FileUpload from "@/components/FileUpload";

const fileUrl = ref("");

const handleUploadSuccess = (fileData) => {
  console.log("上传成功:", fileData);
};

const handleUploadError = (error) => {
  console.error("上传失败:", error);
};
</script>
```

**Props**:
- `modelValue` (String): 文件URL，支持v-model
- `category` (String): 文件分类，必填，可选值：image/video/audio/document
- `multiple` (Boolean): 是否支持多文件上传，默认false
- `showPreview` (Boolean): 是否显示预览，默认true
- `disabled` (Boolean): 是否禁用，默认false
- `maxSize` (Number): 最大文件大小（MB），默认null（使用服务器配置）
- `accept` (String): 接受的文件类型，默认""（使用服务器配置）

**Events**:
- `update:modelValue`: 文件URL更新
- `upload-success`: 上传成功，参数：fileData
- `upload-error`: 上传失败，参数：error
- `upload-progress`: 上传进度，参数：progress (0-100)
- `file-remove`: 文件移除

### 2. VideoUpload - 专用视频上传组件

**位置**: `@/components/VideoUpload`

**功能特点**:
- 专为视频上传优化
- 视频预览播放
- 上传速度显示
- 视频信息展示（文件名、大小、时长）
- 支持URL直接输入
- 重新上传和删除功能

**基本用法**:
```vue
<template>
  <VideoUpload
    v-model="videoUrl"
    :show-url-input="true"
    @upload-success="handleVideoUploadSuccess"
    @upload-error="handleVideoUploadError"
    @video-loaded="handleVideoLoaded"
  />
</template>

<script setup>
import VideoUpload from "@/components/VideoUpload";

const videoUrl = ref("");

const handleVideoUploadSuccess = (fileData) => {
  console.log("视频上传成功:", fileData);
};

const handleVideoUploadError = (error) => {
  console.error("视频上传失败:", error);
};

const handleVideoLoaded = (videoData) => {
  console.log("视频加载完成:", videoData);
  // 可以获取视频时长等信息
};
</script>
```

**Props**:
- `modelValue` (String): 视频URL，支持v-model
- `disabled` (Boolean): 是否禁用，默认false
- `showUrlInput` (Boolean): 是否显示URL输入框，默认true
- `maxSize` (Number): 最大文件大小（MB），默认null（使用服务器配置）

**Events**:
- `update:modelValue`: 视频URL更新
- `upload-success`: 上传成功，参数：fileData
- `upload-error`: 上传失败，参数：error
- `upload-progress`: 上传进度，参数：progress (0-100)
- `video-loaded`: 视频加载完成，参数：videoData (包含duration等信息)
- `video-remove`: 视频移除

## API配置

### 文件上传API

**接口地址**: `POST /api/file/upload`

**请求参数**:
- `file`: 文件对象（multipart/form-data）
- `category`: 文件分类（image/video/audio/document）

**返回数据**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "fileName": "image/2025/06/05/uuid.jpg",
    "originalFileName": "image.jpg",
    "fileSize": 1024,
    "contentType": "image/jpeg",
    "bucketName": "dianfeng-class",
    "fileUrl": "https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com/dianfeng-class/image/2025/06/05/uuid.jpg",
    "previewUrl": null,
    "uploadTime": 1749102452308,
    "category": "image"
  }
}
```

### 配置信息API

**接口地址**: `GET /api/file/info`

**返回数据**:
```json
{
  "code": 200,
  "data": {
    "maxImageSize": "10MB",
    "maxVideoSize": "500MB",
    "maxAudioSize": "50MB",
    "maxDocumentSize": "20MB",
    "supportedImageTypes": "jpg,jpeg,png,gif,webp",
    "supportedVideoTypes": "mp4,avi,mov,wmv,flv",
    "supportedAudioTypes": "mp3,wav,aac,flac",
    "supportedDocumentTypes": "pdf,doc,docx,xls,xlsx,ppt,pptx,txt"
  }
}
```

## 在课程创建页面中的应用

在 `@/views/course/create.vue` 中，我们已经集成了这两个组件：

1. **课程封面上传**: 使用 `FileUpload` 组件，category设置为"image"
2. **课时视频上传**: 使用 `VideoUpload` 组件，支持URL输入和文件上传

## 技术特点

### 安全性
- 文件类型验证
- 文件大小限制
- 服务器端配置验证

### 用户体验
- 拖拽上传支持
- 实时进度显示
- 文件预览功能
- 错误提示和处理

### 性能优化
- 大文件上传超时设置
- 上传速度显示
- 内存优化（及时释放blob URL）

### 兼容性
- 支持现代浏览器
- 响应式设计
- 移动端友好

## 注意事项

1. **文件大小限制**: 组件会自动从服务器获取配置信息，请确保后端API正常运行
2. **文件类型**: 严格按照服务器配置的文件类型进行验证
3. **网络环境**: 大文件上传建议在稳定的网络环境下进行
4. **浏览器兼容**: 需要支持 FileReader API 和 FormData API

## 故障排除

### 常见问题

1. **上传失败**: 检查网络连接和服务器状态
2. **文件类型不支持**: 确认文件扩展名在支持列表中
3. **文件过大**: 检查文件大小是否超过限制
4. **预览失败**: 确认浏览器支持相应的媒体格式

### 调试方法

1. 打开浏览器开发者工具查看网络请求
2. 检查控制台错误信息
3. 验证服务器端文件上传配置
4. 确认阿里云OSS配置正确

## 更新日志

- **v1.0.0**: 初始版本，支持基本的文件上传功能
- **v1.1.0**: 添加视频专用上传组件
- **v1.2.0**: 集成到课程创建页面
