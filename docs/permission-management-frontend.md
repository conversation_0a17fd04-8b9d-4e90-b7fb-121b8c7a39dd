# 权限管理前端功能文档

## 📋 功能概述

权限管理系统提供了完整的用户课程权限管理功能，包括权限查看、授予、批量操作、统计分析等。

## 🎯 主要功能

### 1. 权限列表管理 (`/permission/course-access`)

#### 功能特性
- ✅ **多条件搜索**：支持用户ID、课程ID、权限类型、获取方式等多种筛选条件
- ✅ **权限状态显示**：清晰显示权限类型、买断状态、过期时间等信息
- ✅ **状态标签**：使用不同颜色标签区分权限状态（有效/失效/已退款）
- ✅ **过期提醒**：自动标识即将过期和已过期的权限
- ✅ **操作按钮**：查看详情、激活/停用、退款处理等操作

#### 表格列说明
| 列名 | 说明 | 特殊标识 |
|------|------|----------|
| ID | 权限记录ID | - |
| 用户ID | 拥有权限的用户 | - |
| 课程ID | 权限对应的课程 | - |
| 权限类型 | 课程/章节/课时权限 | 不同类型用不同颜色 |
| 获取方式 | 购买/免费/积分等 | 购买为红色标签 |
| 是否买断 | 买断权限优先级最高 | 买断为绿色标签 |
| 支付金额 | 实际支付的金额 | 免费显示为"免费" |
| 过期时间 | 权限过期时间 | 永久有效/即将过期/已过期 |
| 状态 | 权限当前状态 | 有效/失效/已退款 |
| 激活状态 | 是否激活 | 已激活/未激活 |

### 2. 授予权限功能

#### 单个授权
- **触发方式**：点击"授予权限"按钮
- **支持类型**：
  - 购买权限：需要填写支付信息、订单ID等
  - 免费权限：可设置过期时间
  - 积分兑换：需要填写积分数量
  - 优惠券兑换：需要填写优惠券ID
  - 管理员赠送：需要填写管理员ID

#### 批量授权
- **触发方式**：点击"批量授权"按钮
- **用户输入**：支持逗号分隔的用户ID列表
- **权限设置**：统一的权限类型和获取方式
- **适用场景**：推广活动、批量赠送等

### 3. 权限统计分析 (`/permission/course-access/statistics`)

#### 全局统计
- **总权限数**：系统中所有权限记录数量
- **有效权限**：当前有效的权限数量
- **买断权限**：买断类型的权限数量
- **即将过期**：7天内即将过期的权限数量

#### 分布统计
- **权限类型分布**：课程/章节/课时权限的比例
- **获取方式分布**：购买/免费/积分等方式的比例

#### 用户权限查询
- **查询功能**：输入用户ID查询该用户的权限统计
- **统计维度**：总权限、有效权限、买断权限、课程数等

### 4. 权限详情查看

#### 详情信息
- **基本信息**：用户ID、课程ID、权限类型等
- **支付信息**：支付金额、原价、支付方式、订单ID等
- **时间信息**：创建时间、更新时间、过期时间等
- **状态信息**：激活状态、权限状态、是否买断等
- **退款信息**：退款时间、退款金额、退款原因等

### 5. 权限操作功能

#### 激活/停用
- **激活权限**：将未激活的权限设为激活状态
- **停用权限**：将激活的权限设为停用状态
- **应用场景**：临时禁用用户权限、恢复权限等

#### 退款处理
- **退款条件**：仅支持购买获得的有效权限
- **退款金额**：可设置退款金额，不超过原支付金额
- **退款原因**：提供多种预设原因，支持自定义
- **确认机制**：二次确认防止误操作

## 🎨 UI设计特点

### 1. 现代化设计
- **Naive UI组件**：使用Naive UI组件库，界面美观统一
- **响应式布局**：支持不同屏幕尺寸的设备
- **色彩系统**：统一的色彩搭配，提升用户体验

### 2. 交互优化
- **状态反馈**：操作后及时提供成功/失败反馈
- **加载状态**：异步操作显示加载动画
- **确认对话框**：重要操作提供二次确认

### 3. 数据可视化
- **状态标签**：不同状态使用不同颜色的标签
- **进度条**：统计页面使用进度条显示比例
- **图标系统**：统一的图标使用，提升识别度

## 🔧 技术实现

### 1. 组件结构
```
/views/permission/course-access/
├── list.vue                    # 主列表页面
├── statistics.vue              # 统计分析页面
└── components/
    ├── GrantAccessModal.vue    # 授予权限弹窗
    ├── BatchGrantModal.vue     # 批量授权弹窗
    ├── AccessDetailModal.vue   # 权限详情弹窗
    └── RefundModal.vue         # 退款处理弹窗
```

### 2. API接口
```javascript
// 权限查询
getUserAccessList(params)       // 获取权限列表
checkAccess(params)            // 检查权限

// 权限授予
grantPurchaseAccess(data)      // 授予购买权限
grantFreeAccess(data)          // 授予免费权限
grantPointsAccess(data)        // 积分兑换权限
batchGrantAccess(data)         // 批量授权

// 权限操作
activateAccess(accessId)       // 激活权限
deactivateAccess(data)         // 停用权限
processRefund(data)            // 处理退款

// 统计查询
getUserAccessStatistics(userId) // 用户权限统计
getGlobalAccessStatistics()    // 全局权限统计
```

### 3. 路由配置
```javascript
{
  path: "/permission",
  component: Layout,
  meta: { title: "权限管理", icon: "Shield" },
  children: [
    {
      path: "course-access",
      name: "CourseAccessList",
      component: () => import("@/views/permission/course-access/list.vue"),
      meta: { title: "课程权限" }
    },
    {
      path: "course-access/statistics",
      name: "CourseAccessStatistics", 
      component: () => import("@/views/permission/course-access/statistics.vue"),
      meta: { title: "权限统计" }
    }
  ]
}
```

## 📱 使用指南

### 1. 查看权限列表
1. 进入"权限管理" → "课程权限"
2. 使用搜索条件筛选需要的权限记录
3. 查看权限状态和详细信息

### 2. 授予单个权限
1. 点击"授予权限"按钮
2. 填写用户ID、课程ID等基本信息
3. 选择权限类型（课程/章节/课时）
4. 选择获取方式并填写相关信息
5. 点击"确认授予"完成操作

### 3. 批量授权
1. 点击"批量授权"按钮
2. 输入多个用户ID（逗号分隔）
3. 设置统一的权限参数
4. 确认批量授权

### 4. 查看统计数据
1. 进入"权限管理" → "权限统计"
2. 查看全局统计数据
3. 输入用户ID查询特定用户的权限统计

### 5. 处理退款
1. 在权限列表中找到需要退款的记录
2. 点击"退款"按钮（仅购买权限可退款）
3. 设置退款金额和原因
4. 确认退款处理

## 🚀 最佳实践

### 1. 权限管理
- **权限层级**：优先使用课程权限，特殊情况使用章节/课时权限
- **买断设置**：重要课程建议设置买断选项
- **过期管理**：定期检查即将过期的权限，及时提醒用户续费

### 2. 批量操作
- **用户验证**：批量授权前确认用户ID的有效性
- **权限冲突**：注意避免重复授权同一权限
- **操作记录**：重要的批量操作建议添加详细备注

### 3. 数据分析
- **定期统计**：定期查看权限统计数据，了解业务情况
- **用户行为**：关注用户权限获取方式，优化营销策略
- **过期预警**：建立过期权限预警机制

## 🔍 故障排查

### 1. 常见问题
- **权限不生效**：检查权限状态和激活状态
- **无法授权**：确认用户ID和课程ID的有效性
- **退款失败**：确认权限状态为有效且获取方式为购买

### 2. 错误处理
- **网络错误**：检查后端服务状态
- **权限错误**：确认管理员权限
- **数据错误**：检查输入数据的格式和有效性

## 📞 技术支持

如有问题，请参考：
- 后端API文档：`docs/user-course-access-system.md`
- 前端组件文档：各组件内的注释说明
- 测试用例：`UserCourseAccessServiceTest.java`
