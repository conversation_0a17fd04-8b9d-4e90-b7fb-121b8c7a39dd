-- 数据库更新脚本
-- 为直播课程表添加封面、讲师和分类字段

-- 1. 为直播课程表添加封面图片字段
ALTER TABLE `live_courses` 
ADD COLUMN `cover_image` varchar(255) DEFAULT NULL COMMENT '直播封面图片URL' AFTER `description`;

-- 2. 为直播课程表添加讲师ID字段
ALTER TABLE `live_courses` 
ADD COLUMN `teacher_id` int DEFAULT NULL COMMENT '讲师ID' AFTER `cover_image`;

-- 3. 为直播课程表添加分类ID字段
ALTER TABLE `live_courses` 
ADD COLUMN `category_id` int DEFAULT NULL COMMENT '分类ID' AFTER `teacher_id`;

-- 4. 为直播课程表添加价格字段
ALTER TABLE `live_courses` 
ADD COLUMN `price` decimal(10,2) DEFAULT '0.00' COMMENT '直播价格' AFTER `category_id`;

-- 5. 为直播课程表添加直播间ID字段
ALTER TABLE `live_courses` 
ADD COLUMN `room_id` varchar(100) DEFAULT NULL COMMENT '直播间ID' AFTER `price`;

-- 6. 为直播课程表添加直播间密码字段
ALTER TABLE `live_courses` 
ADD COLUMN `room_password` varchar(100) DEFAULT NULL COMMENT '直播间密码' AFTER `room_id`;

-- 7. 添加外键约束（可选，根据需要决定是否添加）
-- ALTER TABLE `live_courses` 
-- ADD CONSTRAINT `fk_live_courses_teacher` FOREIGN KEY (`teacher_id`) REFERENCES `teachers` (`id`);

-- ALTER TABLE `live_courses` 
-- ADD CONSTRAINT `fk_live_courses_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`);

-- 5. 为课程分类表添加标签配置支持（如果还没有的话）
-- 检查是否已存在课程分类的标签配置
INSERT IGNORE INTO `tag_configs` (`category`, `label`, `value`, `sort_order`, `is_system`, `status`) VALUES
-- 课程分类
('course_category', '编程开发', '1', 1, 1, 1),
('course_category', '设计创意', '2', 2, 1, 1),
('course_category', '产品运营', '3', 3, 1, 1),
('course_category', '职业技能', '4', 4, 1, 1),
('course_category', '生活兴趣', '5', 5, 1, 1),
('course_category', '语言学习', '6', 6, 1, 1),
('course_category', '学历考试', '7', 7, 1, 1),
('course_category', '亲子教育', '8', 8, 1, 1);

-- 6. 更新现有数据（如果需要的话）
-- UPDATE `live_courses` SET `teacher_id` = 1 WHERE `teacher_id` IS NULL;
-- UPDATE `live_courses` SET `category_id` = 1 WHERE `category_id` IS NULL;
