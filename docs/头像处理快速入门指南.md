# 头像处理快速入门指南

## 🚀 5分钟快速上手

### 第一步：了解原理
系统会自动为所有 **分页查询**、**列表查询**、**详情查询** 接口添加完整的头像URL字段。

### 第二步：查看字段变化
```json
// 原来的返回格式
{
    "avatar": "users/avatars/zhangsan.jpg"
}

// 现在的返回格式  
{
    "avatar": "users/avatars/zhangsan.jpg",
    "avatarFullUrl": "https://your-oss-domain.com/users/avatars/zhangsan.jpg"
}
```

### 第三步：前端使用
```javascript
// 直接使用完整URL显示头像
<img src={user.avatarFullUrl || '/default-avatar.png'} alt="头像" />
```

## 🎯 核心优势

✅ **零配置** - 所有查询接口自动处理  
✅ **零重复** - 无需在每个接口手动拼接URL  
✅ **向后兼容** - 原有字段保持不变  
✅ **统一规范** - 所有头像字段处理逻辑一致  

## 📋 适用接口列表

### 用户相关
- `GET /users/page` - 用户分页查询
- `GET /users/list` - 用户列表查询  
- `GET /users/{id}` - 用户详情查询
- `GET /users/getByUsername` - 根据用户名查询
- `GET /users/getByPhone` - 根据手机号查询

### 讲师相关
- `GET /teachers/page` - 讲师分页查询
- `GET /teachers/list` - 讲师列表查询
- `GET /teachers/{id}` - 讲师详情查询
- `GET /teachers/recommended` - 推荐讲师列表
- `GET /teachers/getByRating` - 按评分排序的讲师
- `GET /teachers/getByStudentCount` - 按学生数排序的讲师

## 💡 开发建议

### 前端开发
```javascript
// ✅ 推荐：优先使用完整URL
const avatarUrl = user.avatarFullUrl || '/default-avatar.png';

// ❌ 不推荐：手动拼接URL
const avatarUrl = `https://oss-domain.com/${user.avatar}`;
```

### 新增实体
如果要为新的实体添加头像处理功能：

1. **实体类添加字段**：
```java
@TableField(exist = false)
private String avatarFullUrl;
```

2. **控制器重写方法**：
```java
@Override
protected void postProcessSingleResult(YourEntity entity) {
    if (entity != null) {
        entity.setAvatarFullUrl(ossUrlService.buildFullUrl(entity.getAvatar()));
    }
}
```

## 🔧 故障排查

### 问题1：avatarFullUrl 字段为空
**原因**：实体类没有继承 BaseController 或没有重写 postProcessSingleResult 方法  
**解决**：检查控制器是否正确配置

### 问题2：URL拼接错误
**原因**：OssUrlService 配置问题  
**解决**：检查 OSS 域名配置

### 问题3：性能问题
**原因**：大量数据查询时URL转换耗时  
**解决**：考虑添加缓存或批量处理优化

## 📚 更多文档

- [通用头像处理机制使用文档](./通用头像处理机制使用文档.md) - 详细使用说明
- [头像处理API接口文档](./头像处理API接口文档.md) - 接口返回格式说明

---

🎉 **恭喜！** 您已经掌握了头像处理机制的核心用法。现在可以开始在项目中使用这套便捷的头像处理功能了！ 