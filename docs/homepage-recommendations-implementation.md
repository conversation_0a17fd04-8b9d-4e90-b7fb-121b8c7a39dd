# 首页推荐管理功能实现文档 🎯

## 📋 功能概述

完善了首页推荐管理功能，包括：
- 封面图片上传和预览
- 关联课程/直播选择
- 完整的表单验证规则
- 真实API接口集成
- 支持新增、编辑、删除、批量操作

## 🗄️ 数据库变更

### 1. 执行SQL脚本

请手动执行以下SQL语句来创建首页推荐表：

```sql
-- 执行脚本文件
source scripts/add_homepage_recommendations.sql;
```

或者手动执行：

```sql
USE dianfeng_class;

-- 创建首页推荐表
DROP TABLE IF EXISTS `homepage_recommendations`;
CREATE TABLE `homepage_recommendations` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '推荐ID，主键',
  `title` varchar(255) NOT NULL COMMENT '推荐标题',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '推荐副标题',
  `description` text COMMENT '推荐描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL（相对路径）',
  `link_type` tinyint(1) DEFAULT '1' COMMENT '链接类型：1-课程，2-直播，3-外部链接',
  `link_target_id` int DEFAULT NULL COMMENT '关联目标ID（课程ID或直播ID）',
  `link_url` varchar(500) DEFAULT NULL COMMENT '跳转链接URL',
  `sort_order` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_link_type` (`link_type`),
  KEY `idx_link_target_id` (`link_target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='首页推荐表';
```

### 2. 字段说明

- `cover_image`: 存储封面图片的相对路径
- `link_type`: 链接类型（1-课程，2-直播，3-外部链接）
- `link_target_id`: 关联的课程ID或直播ID
- `link_url`: 外部链接URL或内部路由

## 🔧 后端实现

### 1. 新增文件

- `HomepageRecommendations.java` - 实体类
- `HomepageRecommendationsMapper.java` - Mapper接口
- `IHomepageRecommendationsService.java` - Service接口
- `HomepageRecommendationsServiceImpl.java` - Service实现
- `HomepageRecommendationsController.java` - 控制器
- `HomepageRecommendationsMapper.xml` - MyBatis映射文件

### 2. API接口

#### 基础CRUD接口（继承自BaseController）
- `GET /api/homepage-recommendations` - 分页查询
- `POST /api/homepage-recommendations` - 新增推荐
- `PUT /api/homepage-recommendations/{id}` - 更新推荐
- `DELETE /api/homepage-recommendations/{id}` - 删除推荐

#### 扩展接口
- `GET /api/homepage-recommendations/enabled` - 获取启用的推荐列表（前台使用）
- `PUT /api/homepage-recommendations/toggle-status/{id}` - 切换推荐状态
- `DELETE /api/homepage-recommendations/batch` - 批量删除推荐

### 3. 数据处理

控制器实现了 `postProcessSingleResult` 方法，自动处理：
- 封面图片完整URL生成
- 链接类型标签设置
- 关联课程/直播信息获取

## 🎨 前端实现

### 1. 页面结构

- `homepage.vue` - 主列表页面
- `HomepageDetailCard.vue` - 详情/编辑组件

### 2. 主要功能

#### 封面图片上传
- 支持拖拽上传
- 本地预览功能
- 图片格式验证
- 文件大小限制（5MB）

#### 关联选择
- 课程选择：支持搜索过滤，显示课程封面
- 直播选择：支持搜索过滤，显示直播封面
- 外部链接：URL格式验证

#### 表单验证
- 标题：必填，1-100字符
- 副标题：可选，最多200字符
- 链接类型：必选
- 关联目标：根据链接类型动态验证
- 外部链接：URL格式验证
- 排序值：必填，非负数
- 描述：可选，最多500字符

### 3. 数据流

1. **列表加载**：调用API获取分页数据
2. **新增/编辑**：表单验证后提交API
3. **状态切换**：调用专用API接口
4. **删除操作**：支持单个和批量删除

## 🚀 使用指南

### 1. 启动后端服务

确保数据库表已创建，启动Spring Boot应用。

### 2. 访问管理页面

在管理后台导航到：`直播推荐 > 首页推荐`

### 3. 创建推荐

1. 点击"添加推荐"按钮
2. 填写基本信息（标题、副标题、描述）
3. 上传封面图片
4. 选择链接类型：
   - **课程**：选择关联的课程
   - **直播**：选择关联的直播
   - **外部链接**：输入完整URL
5. 设置排序值和状态
6. 保存

### 4. 管理推荐

- **查看详情**：点击"查看"按钮
- **编辑**：点击"编辑"按钮或在详情页切换到编辑模式
- **状态切换**：点击"启用/禁用"按钮
- **删除**：单个删除或批量删除

## 🔍 注意事项

1. **图片上传**：目前使用本地预览，生产环境需要集成OSS上传
2. **关联数据**：课程和直播选择目前使用模拟数据，需要连接真实API
3. **权限控制**：根据需要添加操作权限验证
4. **数据验证**：后端需要添加相应的数据验证逻辑

## 📝 后续优化

1. 集成真实的课程和直播API
2. 添加图片OSS上传功能
3. 优化搜索和过滤功能
4. 添加推荐效果统计
5. 支持推荐位置管理（轮播图、推荐位等）
