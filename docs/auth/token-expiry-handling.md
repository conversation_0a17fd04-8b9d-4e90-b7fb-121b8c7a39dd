# Sa-Token 失效自动退出登录配置

## 📋 概述

本文档描述了如何配置Sa-Token token失效时前端自动退出登录到登录页面的功能。

## 🔧 后端配置

### 1. Sa-Token 配置调整

**文件位置：** `back/src/main/resources/application.yaml` 和 `application-dev.yaml`

```yaml
# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s (生产环境7天，开发环境1天)
  timeout: 604800  # 生产环境：7天
  # timeout: 86400   # 开发环境：1天
  # token临时有效期 (指定时间内无操作就过期) 单位: 秒
  active-timeout: 7200   # 生产环境：2小时
  # active-timeout: 14400  # 开发环境：4小时
  # 是否允许同一账号并发登录
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
```

### 2. 全局异常处理器

**文件位置：** `back/src/main/java/pox/com/dianfeng/exception/GlobalExceptionHandler.java`

添加了Sa-Token相关异常处理：

```java
/**
 * 处理Sa-Token未登录异常
 */
@ExceptionHandler(NotLoginException.class)
public ResponseEntity<R<String>> handleNotLoginException(NotLoginException e) {
    log.warn("用户未登录或token已失效: {}", e.getMessage());
    
    String message = "用户未登录或登录已过期，请重新登录";
    
    // 根据异常类型提供更具体的错误信息
    switch (e.getType()) {
        case NotLoginException.NOT_TOKEN:
            message = "未提供登录凭证，请先登录";
            break;
        case NotLoginException.INVALID_TOKEN:
            message = "登录凭证无效，请重新登录";
            break;
        case NotLoginException.TOKEN_TIMEOUT:
            message = "登录已过期，请重新登录";
            break;
        case NotLoginException.BE_REPLACED:
            message = "账号在其他地方登录，请重新登录";
            break;
        case NotLoginException.KICK_OUT:
            message = "账号已被强制下线，请重新登录";
            break;
        default:
            message = "登录状态异常，请重新登录";
            break;
    }
    
    R<String> result = R.error(401, message);
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(result);
}
```

## 💻 前端配置

### 1. Token 工具函数增强

**文件位置：** `front/admin_front/src/utils/token.js`

新增了统一的登录失效处理函数：

```javascript
/**
 * 处理登录失效，清除token并跳转到登录页
 */
export function handleLoginExpired(message = '登录已过期，请重新登录') {
  // 清除所有认证信息
  removeToken();
  
  // 显示提示信息
  if (window.$message) {
    window.$message.warning(message);
  }
  
  // 使用Vue Router进行跳转（如果可用）
  if (window.$router) {
    window.$router.push('/login');
  } else {
    // 降级使用window.location
    window.location.href = '/#/login';
  }
}
```

### 2. 请求拦截器优化

**文件位置：** 
- `front/admin_front/src/utils/request.js`
- `front/admin_front/src/api/request.js`

统一使用 `handleLoginExpired` 函数处理401错误：

```javascript
// 401: 未登录或token过期
if (res.code === 401) {
  // 使用统一的登录失效处理
  handleLoginExpired(res.msg || "登录已过期，请重新登录");
}
```

### 3. 全局变量设置

**文件位置：** 
- `front/admin_front/src/main.js`
- `front/admin_front/src/App.vue`

设置全局的 `$router` 和 `$message` 变量：

```javascript
// main.js
window.$router = router;

// App.vue
onMounted(() => {
  const message = useMessage();
  window.$message = message;
});
```

## 🔄 工作流程

1. **用户操作触发请求** → 前端发送带token的请求
2. **Sa-Token验证失败** → 后端抛出NotLoginException
3. **全局异常处理** → 返回401状态码和错误信息
4. **前端拦截器处理** → 检测到401状态码
5. **统一失效处理** → 调用handleLoginExpired函数
6. **清理和跳转** → 清除token、显示提示、跳转登录页

## ⚙️ 配置说明

### Token 过期时间设置

- **timeout**: token的绝对过期时间
  - 生产环境：7天 (604800秒)
  - 开发环境：1天 (86400秒)

- **active-timeout**: token的活跃过期时间（无操作自动过期）
  - 生产环境：2小时 (7200秒)
  - 开发环境：4小时 (14400秒)

### 异常类型说明

- `NOT_TOKEN`: 未提供token
- `INVALID_TOKEN`: token格式无效
- `TOKEN_TIMEOUT`: token已过期
- `BE_REPLACED`: 账号在其他地方登录
- `KICK_OUT`: 账号被强制下线

## 🧪 测试验证

### 手动测试方法

1. **登录系统**
2. **等待token过期** 或 **手动删除token**
3. **执行任意需要认证的操作**
4. **验证是否自动跳转到登录页**

### 开发测试

可以临时调整开发环境的token过期时间为较短时间（如60秒）进行快速测试：

```yaml
# 开发测试配置
sa-token:
  timeout: 60        # 1分钟过期
  active-timeout: 30 # 30秒无操作过期
```

## 📝 注意事项

1. **生产环境安全**: 确保token过期时间设置合理，平衡用户体验和安全性
2. **用户体验**: 提供友好的过期提示信息
3. **数据保护**: token失效时确保清除所有本地存储的敏感信息
4. **降级处理**: 当Vue Router不可用时，使用window.location作为降级方案

## 🔍 故障排查

### 常见问题

1. **token失效但未跳转**
   - 检查全局异常处理器是否正确配置
   - 确认前端拦截器是否正确处理401状态码

2. **跳转后仍显示登录状态**
   - 确认removeToken函数是否正确清除所有存储
   - 检查路由守卫是否正确验证token

3. **提示信息不显示**
   - 确认window.$message是否正确设置
   - 检查Naive UI的message provider是否正确配置
