# 通用头像处理机制使用文档

## 📖 概述

本文档介绍了基于 BaseController 的通用头像处理机制，该机制能够自动将相对路径的头像字段转换为完整的 OSS URL，适用于所有继承 BaseController 的控制器。

## 🏗️ 架构设计

### 核心组件

1. **BaseController** - 提供通用后置处理机制
2. **OssUrlService** - 负责 URL 转换逻辑
3. **实体类** - 包含 `avatar`（相对路径）和 `avatarFullUrl`（完整URL）字段

### 工作流程

```mermaid
graph TD
    A[客户端请求] --> B[Controller方法]
    B --> C[执行数据库查询]
    C --> D[BaseController后置处理]
    D --> E[调用postProcessSingleResult]
    E --> F[子类重写处理逻辑]
    F --> G[设置完整URL]
    G --> H[返回结果给客户端]
```

## 🚀 使用指南

### 1. 实体类配置

在需要头像处理的实体类中添加 `avatarFullUrl` 字段：

```java
@Getter
@Setter
@TableName("your_table")
public class YourEntity extends Model<YourEntity> {
    
    /**
     * 头像URL（相对路径）
     */
    @TableField("avatar")
    @ApiModelProperty("头像URL（相对路径）")
    private String avatar;

    /**
     * 头像完整URL（不存储到数据库）
     */
    @TableField(exist = false)
    @ApiModelProperty("头像完整URL")
    private String avatarFullUrl;
    
    // 其他字段...
}
```

**注意事项：**
- `avatar` 字段存储相对路径，保存到数据库
- `avatarFullUrl` 字段使用 `@TableField(exist = false)` 标注，不保存到数据库
- `avatarFullUrl` 字段由系统自动填充

### 2. 控制器配置

创建继承 BaseController 的控制器：

```java
@Api(tags = "实体管理")
@RestController
@RequestMapping("/your-entity")
public class YourEntityController extends BaseController<IYourEntityService, YourEntity> {

    @Autowired
    private OssUrlService ossUrlService;

    /**
     * 重写单个实体后置处理，设置头像完整URL
     */
    @Override
    protected void postProcessSingleResult(YourEntity entity) {
        if (entity != null) {
            entity.setAvatarFullUrl(ossUrlService.buildFullUrl(entity.getAvatar()));
        }
    }
    
    // 其他业务方法...
}
```

### 3. 自动处理的接口

继承 BaseController 后，以下接口会自动进行头像处理：

| 接口类型 | 路径 | 说明 |
|---------|------|------|
| 分页查询 | `GET /your-entity/page` | 自动处理分页结果中所有记录的头像 |
| 列表查询 | `GET /your-entity/list` | 自动处理列表中所有记录的头像 |
| 详情查询 | `GET /your-entity/{id}` | 自动处理单个记录的头像 |

## 💡 最佳实践

### 1. 统一头像字段命名

建议在所有需要头像处理的实体中使用统一的字段命名：
- `avatar` - 相对路径
- `avatarFullUrl` - 完整URL

### 2. 空值处理

在 `postProcessSingleResult` 方法中要做好空值判断：

```java
@Override
protected void postProcessSingleResult(YourEntity entity) {
    if (entity != null && entity.getAvatar() != null) {
        entity.setAvatarFullUrl(ossUrlService.buildFullUrl(entity.getAvatar()));
    }
}
```

### 3. 批量处理优化

对于有大量数据的场景，可以考虑批量处理优化：

```java
@Override
protected void postProcessListResult(List<YourEntity> list) {
    if (list != null && !list.isEmpty()) {
        // 批量处理，提高性能
        ossUrlService.setEntityFullUrls(list);
    }
}
```

## 📝 现有实现示例

### Users 实体

```java
// 实体类已配置
public class Users extends Model<Users> {
    @TableField("avatar")
    private String avatar;
    
    @TableField(exist = false)
    private String avatarFullUrl;
}

// 控制器实现
@RestController
@RequestMapping("/users")
public class UserController extends BaseController<IUsersService, Users> {
    
    @Autowired
    private OssUrlService ossUrlService;

    @Override
    protected void postProcessSingleResult(Users user) {
        if (user != null) {
            user.setAvatarFullUrl(ossUrlService.buildFullUrl(user.getAvatar()));
        }
    }
}
```

### Teachers 实体

```java
// 实体类已配置
public class Teachers extends Model<Teachers> {
    @TableField("avatar")
    private String avatar;
    
    @TableField(exist = false)
    private String avatarFullUrl;
}

// 控制器实现
@RestController
@RequestMapping("/teachers")
public class TeachersController extends BaseController<ITeachersService, Teachers> {
    
    @Autowired
    private OssUrlService ossUrlService;

    @Override
    protected void postProcessSingleResult(Teachers teacher) {
        if (teacher != null) {
            ossUrlService.setTeacherFullUrls(teacher);
        }
    }
}
```

## 🔧 扩展功能

### 自定义后置处理

除了头像处理，还可以在 `postProcessSingleResult` 中添加其他自定义逻辑：

```java
@Override
protected void postProcessSingleResult(YourEntity entity) {
    if (entity != null) {
        // 1. 头像处理
        entity.setAvatarFullUrl(ossUrlService.buildFullUrl(entity.getAvatar()));
        
        // 2. 其他自定义处理
        entity.setCustomField(calculateCustomValue(entity));
        
        // 3. 状态转换
        entity.setStatusText(getStatusText(entity.getStatus()));
    }
}
```

### 多字段URL处理

如果实体有多个需要URL转换的字段：

```java
@Override
protected void postProcessSingleResult(YourEntity entity) {
    if (entity != null) {
        // 头像处理
        if (entity.getAvatar() != null) {
            entity.setAvatarFullUrl(ossUrlService.buildFullUrl(entity.getAvatar()));
        }
        
        // 封面图处理
        if (entity.getCoverImage() != null) {
            entity.setCoverImageFullUrl(ossUrlService.buildFullUrl(entity.getCoverImage()));
        }
    }
}
```

## ⚠️ 注意事项

1. **性能考虑**：大量数据查询时，URL 转换可能影响性能，建议在业务层做缓存优化
2. **空值处理**：务必做好空值判断，避免 NullPointerException
3. **一致性**：确保所有相关实体的字段命名和处理逻辑保持一致
4. **测试覆盖**：新增实体后要编写相应的测试用例验证头像处理功能

## 🎯 总结

通过这套通用头像处理机制，您可以：

✅ **零重复代码** - 所有实体使用相同的处理模式  
✅ **自动化处理** - 无需在每个接口中手动处理URL转换  
✅ **统一规范** - 保证所有头像字段的处理逻辑一致  
✅ **易于扩展** - 新增实体只需简单配置即可  
✅ **维护简单** - 集中的处理逻辑，便于统一维护  

这套机制大大提升了开发效率和代码质量！🚀 