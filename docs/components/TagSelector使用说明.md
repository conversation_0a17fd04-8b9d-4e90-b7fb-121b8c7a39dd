# TagSelector 标签选择器组件

## 概述

`TagSelector` 是一个基于 Naive UI 的可配置标签下拉框组件，支持动态新增标签功能。该组件将标签选择和标签管理功能整合在一起，提供了良好的用户体验。

## 功能特性

- ✅ 支持配置不同的标签类型（category）
- ✅ 可选择是否允许动态新增标签
- ✅ 自动加载标签选项
- ✅ 支持搜索过滤
- ✅ 支持清空选择
- ✅ 新增标签后自动选择
- ✅ 可自定义标签显示名称
- ✅ 提供事件回调

## 基本用法

```vue
<template>
  <TagSelector
    v-model:value="selectedTag"
    category="course_category"
    tag-label="课程分类"
    placeholder="请选择课程分类"
    :allow-add="true"
    @change="handleTagChange"
    @add-success="handleAddSuccess"
  />
</template>

<script setup>
import { ref } from 'vue'
import TagSelector from '@/components/Selector/TagSelector.vue'

const selectedTag = ref(null)

const handleTagChange = (value) => {
  console.log('标签变化:', value)
}

const handleAddSuccess = (newTag) => {
  console.log('新标签添加成功:', newTag)
}
</script>
```

## Props 参数

| 参数名 | 类型 | 默认值 | 是否必填 | 说明 |
|--------|------|--------|----------|------|
| `value` | String \| Number | `null` | 否 | 当前选中的值，支持 v-model |
| `category` | String | `"course_category"` | 是 | 标签类型，对应 TagConfig 表的 category 字段 |
| `tagLabel` | String | `"标签"` | 否 | 标签显示名称，用于界面文本 |
| `placeholder` | String | `"请选择标签"` | 否 | 输入框占位符 |
| `filterable` | Boolean | `true` | 否 | 是否可搜索 |
| `clearable` | Boolean | `true` | 否 | 是否可清空 |
| `allowAdd` | Boolean | `true` | 否 | 是否允许动态新增标签 |
| `autoLoad` | Boolean | `true` | 否 | 是否自动加载标签选项 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:value` | `value: String \| Number` | 选中值变化时触发，用于 v-model |
| `change` | `value: String \| Number` | 选中值变化时触发 |
| `add-success` | `newTag: Object` | 新增标签成功时触发 |

## Methods 方法

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `loadOptions` | - | 重新加载标签选项 |
| `refresh` | - | 刷新标签选项（同 loadOptions） |

## 使用示例

### 基础用法

```vue
<template>
  <n-form-item label="课程分类" path="categoryId">
    <TagSelector
      v-model:value="formModel.categoryId"
      category="course_category"
      tag-label="分类"
    />
  </n-form-item>
</template>
```

### 只读模式（不允许新增）

```vue
<template>
  <TagSelector
    v-model:value="selectedTag"
    category="course_category"
    tag-label="课程分类"
    :allow-add="false"
  />
</template>
```

### 不同标签类型的使用

```vue
<template>
  <!-- 课程分类 -->
  <TagSelector
    v-model:value="courseCategory"
    category="course_category"
    tag-label="课程分类"
  />

  <!-- 用户标签 -->
  <TagSelector
    v-model:value="userTag"
    category="user_tag"
    tag-label="用户标签"
  />

  <!-- 年龄段 -->
  <TagSelector
    v-model:value="ageGroup"
    category="age_group"
    tag-label="年龄段"
  />
</template>
```

### 手动控制加载

```vue
<template>
  <TagSelector
    ref="tagSelectorRef"
    v-model:value="selectedTag"
    category="course_category"
    :auto-load="false"
  />
  <n-button @click="loadTags">加载标签</n-button>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const tagSelectorRef = ref(null)

const loadTags = () => {
  tagSelectorRef.value?.loadOptions()
}

onMounted(() => {
  // 延迟加载
  setTimeout(() => {
    loadTags()
  }, 1000)
})
</script>
```

## 样式定制

组件使用了 scoped 样式，可以通过以下方式自定义：

```vue
<template>
  <div class="custom-tag-selector">
    <TagSelector
      v-model:value="selectedTag"
      category="course_category"
    />
  </div>
</template>

<style scoped>
.custom-tag-selector :deep(.n-select) {
  /* 自定义选择器样式 */
}
</style>
```

## 注意事项

1. **必须引入组件**：使用前需要先引入 `TagSelector` 组件
2. **API 依赖**：组件依赖 `@/api/tagConfig` 中的接口，确保相关 API 可用
3. **权限控制**：新增标签功能需要相应的后端权限支持
4. **数据格式**：组件返回的值为 TagConfig 中的 `value` 字段
5. **错误处理**：组件内部已处理常见错误，会显示相应的错误提示

## 后端数据结构

组件依赖的 TagConfig 数据结构：

```json
{
  "id": 1,
  "category": "course_category",
  "label": "编程课程",
  "value": "programming",
  "remark": "编程相关课程标签",
  "status": 1,
  "isSystem": 0,
  "sortOrder": 1
}
```

## 完整示例

参考 `/views/live/list.vue` 中的使用方式，了解在实际项目中的集成方法。 