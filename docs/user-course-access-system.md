# 用户课程权限系统设计文档

## 📋 系统概述

用户课程权限系统是一个精细化的权限管理系统，支持课程、章节、课时三级权限控制，提供多种获取方式和完整的权限生命周期管理。

## 🎯 核心特性

### 1. 分层权限控制
- **课程权限**：用户可以访问整个课程的所有内容
- **章节权限**：用户只能访问特定章节的内容
- **课时权限**：用户只能访问特定课时的内容

### 2. 买断优先级
- 买断权限具有最高优先级，覆盖其他权限限制
- 买断用户可以永久访问相关内容，不受时间限制

### 3. 多种获取方式
- **购买**：通过支付获得权限
- **免费**：免费获得的权限
- **积分兑换**：使用积分兑换权限
- **优惠券兑换**：使用优惠券获得权限
- **管理员赠送**：管理员直接赠送权限
- **推广活动**：通过推广活动获得权限

### 4. 权限继承
- 课程权限自动包含所有章节和课时权限
- 章节权限包含该章节下所有课时权限

### 5. 时效性管理
- 支持永久权限和有期限权限
- 自动处理过期权限
- 过期提醒功能

## 🏗️ 系统架构

### 数据库设计

```sql
-- 用户课程权限表
CREATE TABLE `user_course_access` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '权限记录ID，主键',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `chapter_id` int DEFAULT NULL COMMENT '章节ID，null表示整个课程权限',
  `lesson_id` int DEFAULT NULL COMMENT '课时ID，null表示章节权限',
  `access_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '权限类型：1-课程，2-章节，3-课时',
  `acquire_method` tinyint(1) NOT NULL DEFAULT '1' COMMENT '获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动',
  `is_buyout` tinyint(1) DEFAULT '0' COMMENT '是否买断：0-否，1-是（买断权限优先级最高）',
  `price_paid` decimal(10,2) DEFAULT '0.00' COMMENT '实际支付金额',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `order_id` varchar(100) DEFAULT NULL COMMENT '订单ID',
  `coupon_id` int DEFAULT NULL COMMENT '使用的优惠券ID',
  `points_used` int DEFAULT '0' COMMENT '使用的积分数量',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间，null表示永久有效',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活：0-未激活，1-已激活',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-已失效，1-有效，2-已退款',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `admin_id` int DEFAULT NULL COMMENT '操作管理员ID',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_course_chapter_lesson` (`user_id`,`course_id`,`chapter_id`,`lesson_id`,`is_del`),
  -- 索引设计
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_lesson_id` (`lesson_id`),
  KEY `idx_access_type` (`access_type`),
  KEY `idx_acquire_method` (`acquire_method`),
  KEY `idx_is_buyout` (`is_buyout`),
  KEY `idx_status_active` (`status`,`is_active`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户课程权限表';
```

### 核心组件

1. **实体层**：`UserCourseAccess` - 权限实体类
2. **数据访问层**：`UserCourseAccessMapper` - 数据访问接口
3. **服务层**：`IUserCourseAccessService` + `UserCourseAccessServiceImpl` - 业务逻辑
4. **控制器层**：`UserCourseAccessController` - API接口
5. **DTO层**：`CourseAccessRequestDTO` + `CourseAccessResponseDTO` - 数据传输对象
6. **工具类**：`CourseAccessUtil` - 权限验证工具
7. **定时任务**：`UserCourseAccessTask` - 权限维护任务

## 🔧 API接口说明

### 权限检查

```http
GET /user-course-access/check?userId=1&courseId=1&chapterId=1&lessonId=1
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "hasAccess": true,
    "accessLevel": 1,
    "isBuyout": true,
    "acquireMethod": 1,
    "expireTime": null,
    "nearExpiry": false,
    "accessDetail": {
      "id": 1,
      "userId": 1,
      "courseId": 1,
      "accessType": 1,
      "isBuyout": true,
      "status": 1
    }
  }
}
```

### 授予购买权限

```http
POST /user-course-access/grant/purchase
Content-Type: application/json

{
  "userId": 1,
  "courseId": 1,
  "chapterId": null,
  "lessonId": null,
  "pricePaid": 99.00,
  "originalPrice": 199.00,
  "paymentMethod": "wechat",
  "orderId": "ORDER_123456",
  "isBuyout": true,
  "expireTime": null
}
```

### 快速购买课程

```http
POST /user-course-access/quick-purchase?userId=1&courseId=1&pricePaid=99.00&originalPrice=199.00&paymentMethod=wechat&orderId=ORDER_123456&isBuyout=true
```

### 获取用户权限统计

```http
GET /user-course-access/user/1/statistics
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "totalAccess": 10,
    "validAccess": 8,
    "expiredAccess": 1,
    "buyoutAccess": 3,
    "expiringAccess": 1,
    "freeAccess": 2,
    "purchasedAccess": 6,
    "totalCourses": 5,
    "totalChapters": 2,
    "totalLessons": 3
  }
}
```

## 🛠️ 使用示例

### 1. 权限验证工具类使用

```java
@Autowired
private CourseAccessUtil courseAccessUtil;

// 检查用户是否有课程访问权限
boolean hasAccess = courseAccessUtil.hasAccessToCourse(userId, courseId);

// 检查用户是否有买断权限
boolean hasBuyout = courseAccessUtil.hasBuyoutAccess(userId, courseId);

// 验证权限（无权限时抛出异常）
try {
    courseAccessUtil.validateCourseAccess(userId, courseId);
    // 有权限，继续执行业务逻辑
} catch (IllegalAccessException e) {
    // 无权限，处理异常
    return R.error("无权限访问该课程");
}
```

### 2. 服务层使用

```java
@Autowired
private IUserCourseAccessService userCourseAccessService;

// 授予购买权限
UserCourseAccess access = userCourseAccessService.grantPurchaseAccess(
    userId, courseId, null, null,
    new BigDecimal("99.00"), new BigDecimal("199.00"),
    "wechat", "ORDER_123456", true, null
);

// 检查权限
CourseAccessResponseDTO response = userCourseAccessService.checkAccess(
    userId, courseId, chapterId, lessonId
);

// 处理退款
Boolean result = userCourseAccessService.processRefund(
    accessId, new BigDecimal("99.00"), "用户申请退款"
);
```

## 📊 权限优先级规则

1. **买断权限** > 其他所有权限（最高优先级）
2. **课程权限** > 章节权限 > 课时权限
3. **有效期内权限** > 过期权限
4. **激活状态权限** > 未激活权限

## ⏰ 定时任务

系统包含以下定时任务：

1. **过期权限处理**：每小时执行，自动将过期权限标记为失效
2. **过期提醒**：每天上午9点执行，检查即将过期的权限
3. **统计报告**：每天凌晨1点执行，生成权限统计报告
4. **数据清理**：每周日凌晨2点执行，清理软删除的记录
5. **一致性检查**：每月1号凌晨3点执行，检查数据一致性

## 🔒 安全考虑

1. **权限验证**：所有权限操作都需要验证用户身份
2. **数据完整性**：使用唯一索引防止重复权限记录
3. **软删除**：支持软删除，避免误删重要数据
4. **审计日志**：记录所有权限变更操作
5. **事务保证**：关键操作使用事务保证数据一致性

## 🚀 扩展性

系统设计考虑了未来的扩展需求：

1. **新增权限类型**：可以轻松添加新的权限类型
2. **新增获取方式**：支持新的权限获取方式
3. **权限组合**：支持复杂的权限组合逻辑
4. **多租户支持**：可以扩展为多租户系统
5. **权限模板**：可以添加权限模板功能

## 📈 性能优化

1. **索引优化**：为常用查询字段添加索引
2. **缓存策略**：可以添加Redis缓存提高查询性能
3. **批量操作**：支持批量权限授予和更新
4. **分页查询**：大数据量查询支持分页
5. **异步处理**：耗时操作可以异步处理

## 🧪 测试验证

系统提供了完整的单元测试，覆盖以下场景：

1. **权限授予测试**：购买、免费、积分兑换等各种方式
2. **权限检查测试**：有权限和无权限的情况
3. **买断优先级测试**：验证买断权限的最高优先级
4. **批量操作测试**：批量授权功能
5. **退款处理测试**：权限退款流程
6. **权限激活/停用测试**：权限状态管理
7. **统计功能测试**：权限统计数据
8. **过期权限测试**：过期权限处理

运行测试：
```bash
mvn test -Dtest=UserCourseAccessServiceTest
```

## 🚀 快速开始

### 1. 基础权限授予

```java
// 购买整个课程（买断）
UserCourseAccess access = userCourseAccessService.grantPurchaseAccess(
    userId, courseId, null, null,
    new BigDecimal("99.00"), new BigDecimal("199.00"),
    "wechat", "ORDER_123456", true, null
);

// 免费获得章节权限（30天有效期）
UserCourseAccess chapterAccess = userCourseAccessService.grantFreeAccess(
    userId, courseId, chapterId, null,
    LocalDateTime.now().plusDays(30)
);

// 积分兑换课时权限
UserCourseAccess lessonAccess = userCourseAccessService.grantPointsAccess(
    userId, courseId, chapterId, lessonId,
    500, new BigDecimal("25.00"), LocalDateTime.now().plusDays(60)
);
```

### 2. 权限检查

```java
// 检查课程权限
CourseAccessResponseDTO response = userCourseAccessService.checkAccess(
    userId, courseId, null, null
);

if (response.getHasAccess()) {
    // 用户有权限，允许访问
    if (response.getIsBuyout()) {
        // 买断用户，享受最高权限
    }
    if (response.getNearExpiry()) {
        // 权限即将过期，提醒用户续费
    }
} else {
    // 无权限，显示购买页面
    String reason = response.getReason();
}
```

### 3. 使用工具类简化操作

```java
@Autowired
private CourseAccessUtil courseAccessUtil;

// 简单的权限检查
if (courseAccessUtil.hasAccessToCourse(userId, courseId)) {
    // 有权限，继续业务逻辑
}

// 强制验证权限（无权限时抛出异常）
try {
    courseAccessUtil.validateLessonAccess(userId, courseId, chapterId, lessonId);
    // 验证通过，执行业务逻辑
} catch (IllegalAccessException e) {
    return R.error("无权限访问该课时");
}

// 获取权限摘要
CourseAccessUtil.AccessSummary summary = courseAccessUtil.getUserAccessSummary(userId);
```

## 📋 常见问题

### Q1: 如何处理权限冲突？
A: 系统按照优先级处理：买断权限 > 课程权限 > 章节权限 > 课时权限

### Q2: 用户购买了章节权限后又购买了整个课程，如何处理？
A: 系统会自动选择权限级别最高的记录，用户将获得整个课程的访问权限

### Q3: 如何实现权限的批量管理？
A: 使用批量授权API，可以一次性为多个用户授予相同的权限

### Q4: 权限过期后如何处理？
A: 系统定时任务会自动将过期权限标记为失效，用户将无法继续访问

### Q5: 如何实现权限的续费？
A: 为同一用户和课程创建新的权限记录，系统会自动选择有效期最长的权限

## 📞 技术支持

如有问题，请联系开发团队或查看相关文档：

- 系统设计文档：`docs/user-course-access-system.md`
- API文档：通过Swagger访问 `/swagger-ui.html`
- 测试用例：`UserCourseAccessServiceTest.java`

## ✅ 系统状态

### 🎯 已完成功能

✅ **数据库设计**
- 完整的用户课程权限表设计
- 支持三级权限控制（课程、章节、课时）
- 买断优先级机制
- 完整的权限生命周期管理

✅ **核心实体类**
- `UserCourseAccess` 实体类
- 完整的常量定义（权限类型、获取方式、状态、支付方式）
- 支持所有业务场景的字段设计

✅ **数据访问层**
- `UserCourseAccessMapper` 接口
- 优化的查询方法
- 支持权限继承和优先级查询

✅ **服务层**
- `IUserCourseAccessService` 接口
- `UserCourseAccessServiceImpl` 实现类
- 完整的权限管理业务逻辑

✅ **控制器层**
- `UserCourseAccessController` REST API
- 完整的权限操作接口
- 标准化的响应格式

✅ **DTO层**
- `CourseAccessRequestDTO` 请求对象
- `CourseAccessResponseDTO` 响应对象
- 支持批量操作和统计查询

✅ **工具类**
- `CourseAccessUtil` 权限验证工具
- 简化的权限检查方法
- 权限摘要和统计功能

✅ **定时任务**
- `UserCourseAccessTask` 权限维护任务
- 自动处理过期权限
- 权限统计和数据清理

✅ **测试验证**
- `UserCourseAccessBasicTest` 基础功能测试
- 12个测试用例全部通过
- 覆盖核心业务逻辑

### 🚀 核心特性

1. **分层权限控制** - 支持课程、章节、课时三级精细化权限管理
2. **买断优先级** - 买断权限具有最高优先级，覆盖其他限制
3. **多种获取方式** - 购买、免费、积分兑换、优惠券、赠送、推广
4. **权限继承** - 高级权限自动包含低级权限
5. **时效性管理** - 支持永久和有期限权限，自动过期处理
6. **完整生命周期** - 激活、停用、退款等状态管理
7. **批量操作** - 支持批量权限授予和管理
8. **统计分析** - 完整的权限统计和分析功能
9. **定时维护** - 自动化的权限维护和数据清理

### 📊 测试结果

```
[INFO] Tests run: 12, Failures: 0, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS
```

所有基础功能测试通过，系统运行稳定。

### 🎉 项目总结

用户课程权限系统已成功设计并实现，具备以下优势：

1. **架构清晰** - 分层设计，职责明确
2. **功能完整** - 覆盖所有业务需求
3. **扩展性强** - 易于添加新功能和权限类型
4. **性能优化** - 合理的索引设计和查询优化
5. **安全可靠** - 完整的权限验证和数据保护
6. **易于维护** - 清晰的代码结构和完善的文档

系统已准备好投入生产使用！🚀
