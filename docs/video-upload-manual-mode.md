# 视频上传组件手动模式使用指南

## 概述
视频上传组件现在支持手动上传模式，可以避免用户频繁选择文件导致的OSS存储空间浪费。

## 功能特性

### 1. 手动上传模式
- 用户选择文件后仅进行本地预览
- 不会立即上传到OSS云端
- 需要手动触发上传操作

### 2. 受控上传
- 父组件可通过ref调用上传方法
- 支持隐藏内置的上传按钮
- 完全由父组件控制上传时机

### 3. 智能流量控制
- 本地视频：预加载元数据，快速响应
- 线上视频：不预加载，节省流量
- 清晰的视觉提示区分不同模式

## 使用方法

### 基础手动上传模式
```vue
<template>
  <OssVideoUpload
    v-model="videoUrl"
    :manual-upload="true"
    @upload-success="handleUploadSuccess"
  />
</template>

<script setup>
const videoUrl = ref('')

const handleUploadSuccess = (result) => {
  console.log('上传成功:', result)
}
</script>
```

### 受控上传模式（隐藏上传按钮）
```vue
<template>
  <OssVideoUpload
    ref="videoUploadRef"
    v-model="videoUrl"
    :manual-upload="true"
    :hide-upload-button="true"
    @upload-success="handleUploadSuccess"
  />
  
  <n-button @click="triggerUpload" type="primary">
    手动上传视频
  </n-button>
</template>

<script setup>
const videoUploadRef = ref(null)
const videoUrl = ref('')

const triggerUpload = async () => {
  if (videoUploadRef.value?.hasFileReady()) {
    try {
      const result = await videoUploadRef.value.upload()
      console.log('上传成功:', result)
    } catch (error) {
      console.error('上传失败:', error)
    }
  }
}
</script>
```

### 批量上传示例
```vue
<template>
  <div v-for="(item, index) in videos" :key="index">
    <OssVideoUpload
      :ref="(el) => setVideoRef(el, index)"
      v-model="item.url"
      :manual-upload="true"
      :hide-upload-button="true"
    />
  </div>
  
  <n-button @click="batchUpload" type="primary">
    批量上传所有视频
  </n-button>
</template>

<script setup>
const videos = ref([
  { url: '' },
  { url: '' },
  { url: '' }
])

const videoRefs = ref(new Map())

const setVideoRef = (el, index) => {
  if (el) {
    videoRefs.value.set(index, el)
  }
}

const batchUpload = async () => {
  const pendingUploads = []
  
  for (const [index, ref] of videoRefs.value) {
    if (ref.hasFileReady()) {
      pendingUploads.push(ref.upload())
    }
  }
  
  if (pendingUploads.length > 0) {
    try {
      await Promise.all(pendingUploads)
      console.log('所有视频上传完成')
    } catch (error) {
      console.error('批量上传失败:', error)
    }
  }
}
</script>
```

## API 参考

### Props
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | String | '' | 视频URL |
| `manualUpload` | Boolean | false | 是否启用手动上传模式 |
| `hideUploadButton` | Boolean | false | 是否隐藏内置上传按钮 |
| `showUrlInput` | Boolean | true | 是否显示URL输入框 |
| `disabled` | Boolean | false | 是否禁用 |

### 暴露的方法
| 方法名 | 返回值 | 说明 |
|--------|--------|------|
| `upload()` | Promise | 手动触发上传 |
| `hasFileReady()` | Boolean | 检查是否有待上传文件 |
| `getStatus()` | Object | 获取当前状态信息 |
| `clear()` | void | 清除文件 |
| `reselect()` | void | 重新选择文件 |

### 事件
| 事件名 | 参数 | 说明 |
|--------|------|------|
| `upload-success` | result | 上传成功 |
| `upload-error` | error | 上传失败 |
| `upload-progress` | progress | 上传进度 |
| `video-loaded` | videoData | 视频加载完成 |
| `video-remove` | - | 视频删除 |
| `duration-change` | duration | 时长变化 |

## 实际应用场景

### 课程编辑页面
在课程编辑页面中，我们使用手动上传模式来避免用户在编辑过程中频繁上传导致的空间浪费：

1. **选择视频**: 用户选择视频文件后立即显示本地预览
2. **编辑内容**: 用户可以继续编辑其他课时内容
3. **统一上传**: 在保存课程时统一上传所有待上传的视频
4. **智能提示**: 按钮显示待上传视频数量，提供清晰反馈

### 优势
- ✅ **节省空间**: 避免不必要的文件上传
- ✅ **节省流量**: 线上视频不预加载
- ✅ **用户体验**: 本地预览响应快速
- ✅ **批量操作**: 支持多个文件统一上传
- ✅ **状态透明**: 清晰的视觉反馈和状态提示

## 注意事项

1. **本地预览**: 选择文件后使用本地blob URL进行预览，关闭页面时会自动清理
2. **状态管理**: 组件会自动管理上传状态，避免重复上传
3. **错误处理**: 上传失败时会保持本地预览状态，用户可以重试
4. **内存管理**: 大文件预览时注意浏览器内存使用情况 