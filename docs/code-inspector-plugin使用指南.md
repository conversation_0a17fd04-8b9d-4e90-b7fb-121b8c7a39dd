# Code Inspector Plugin 使用指南

## 简介

code-inspector-plugin 是一款基于 webpack/vite/rspack/nextjs/nuxt/umijs plugin 的提升开发效率的工具。

## 功能特性

1. **点击跳转**：点击页面上的 DOM 元素，自动打开 IDE 并将光标定位到对应的源代码位置
2. **遮罩预览**：在页面上按住组合键时，鼠标移动会在 DOM 上显示遮罩层和相关信息
3. **精确定位**：点击遮罩层将自动打开 IDE 并定位到元素对应的代码位置

## 配置说明

### 已配置的IDE

当前配置为使用 **Cursor** 编辑器：
```javascript
editor: "/Applications/Cursor.app/Contents/MacOS/Cursor"
```

### 切换到其他IDE

如需切换到 Windsurf，修改 `vue.config.js` 中的配置：
```javascript
// 取消注释下面这行，并注释掉Cursor的配置
editor: "/Users/<USER>/.codeium/windsurf/bin/windsurf"
```

### 支持的IDE及配置

| IDE | 配置值 |
|-----|--------|
| Visual Studio Code | `code` |
| Visual Studio Code - Insiders | `code_insiders` |
| WebStorm | `webstorm` |
| Atom | `atom` |
| HBuilderX | `hbuilder` |
| PhpStorm | `phpstorm` |
| PyCharm | `pycharm` |
| IntelliJ IDEA | `idea` |

## 使用方法

### 开发模式启动

```bash
cd front/admin_front
pnpm serve
# 或
npm run serve
```

### 使用技巧

1. **激活遮罩模式**：
   - Mac: `Command + Shift` 
   - Windows/Linux: `Ctrl + Shift`

2. **点击跳转**：
   - 按住组合键，移动鼠标到想要查看的元素上
   - 会显示蓝色遮罩层和元素信息
   - 点击遮罩层或直接点击元素，即可跳转到对应代码

3. **查看元素信息**：
   - 遮罩层会显示：
     - 组件名称
     - 文件路径
     - 行号信息

## 环境配置

### 仅在开发环境启用

当前配置为仅在开发环境启用：
```javascript
dev: true,      // 开发环境启用
build: false,   // 生产环境禁用
```

### 性能优化

- 插件仅在开发模式下加载，不会影响生产构建
- 不会增加生产包的大小
- 热重载友好，修改配置后自动生效

## 注意事项

1. **IDE路径**：确保IDE的可执行文件路径正确
2. **权限设置**：首次使用时可能需要授权IDE打开外部文件
3. **端口冲突**：确保开发服务器正常运行在8088端口
4. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari等）

## 故障排除

### 无法跳转到IDE

1. 检查IDE路径是否正确
2. 确认IDE是否已安装并可正常启动
3. 检查系统权限设置

### 遮罩层不显示

1. 确认开发服务器正常运行
2. 检查浏览器控制台是否有错误信息
3. 尝试刷新页面

### 定位不准确

1. 确保源码映射（source map）正确生成
2. 检查Vue组件的template部分是否正确
3. 避免使用过多的高阶组件包装

## 更多信息

- 官方文档：https://inspector.fe-dev.cn/
- GitHub：https://github.com/zh-lx/code-inspector
- 配置指南：https://inspector.fe-dev.cn/guide/ide.html 