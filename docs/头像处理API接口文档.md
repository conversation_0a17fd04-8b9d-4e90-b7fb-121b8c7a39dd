# 头像处理 API 接口文档

## 📋 概述

本文档说明了集成头像处理机制后，用户和讲师相关API接口的返回格式变化。所有接口现在都会自动返回完整的头像URL。

## 👤 用户相关接口

### 1. 用户分页查询

**接口地址：** `GET /users/page`

**请求参数：**
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "username": "搜索用户名（可选）",
    "nickname": "搜索昵称（可选）",
    "phone": "搜索手机号（可选）",
    "status": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "username": "zhang<PERSON>",
                "nickname": "张三",
                "avatar": "users/avatars/zhangsan.jpg",
                "avatarFullUrl": "https://your-oss-domain.com/users/avatars/zhangsan.jpg",
                "phone": "13800138000",
                "email": "<EMAIL>",
                "gender": 1,
                "age": 25,
                "bio": "这是个人简介",
                "grade": 12,
                "school": "某某中学",
                "location": "北京市",
                "status": true,
                "lastLoginTime": "2025-01-15T10:30:00",
                "createdAt": "2025-01-01T08:00:00",
                "updatedAt": "2025-01-15T10:30:00"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```

### 2. 用户列表查询

**接口地址：** `GET /users/list`

**请求参数：**
```json
{
    "username": "搜索条件（可选）",
    "status": 1
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "username": "zhangsan",
            "nickname": "张三",
            "avatar": "users/avatars/zhangsan.jpg",
            "avatarFullUrl": "https://your-oss-domain.com/users/avatars/zhangsan.jpg",
            "phone": "13800138000",
            "email": "<EMAIL>",
            "status": true,
            "createdAt": "2025-01-01T08:00:00"
        }
    ]
}
```

### 3. 用户详情查询

**接口地址：** `GET /users/{id}`

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "zhangsan",
        "nickname": "张三",
        "avatar": "users/avatars/zhangsan.jpg",
        "avatarFullUrl": "https://your-oss-domain.com/users/avatars/zhangsan.jpg",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "gender": 1,
        "age": 25,
        "bio": "这是个人简介",
        "grade": 12,
        "school": "某某中学",
        "location": "北京市",
        "status": true,
        "lastLoginTime": "2025-01-15T10:30:00",
        "createdAt": "2025-01-01T08:00:00",
        "updatedAt": "2025-01-15T10:30:00"
    }
}
```

### 4. 根据用户名查询用户

**接口地址：** `GET /users/getByUsername`

**请求参数：** `username=zhangsan`

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "zhangsan",
        "avatar": "users/avatars/zhangsan.jpg",
        "avatarFullUrl": "https://your-oss-domain.com/users/avatars/zhangsan.jpg",
        "nickname": "张三",
        "phone": "13800138000",
        "status": true
    }
}
```

### 5. 根据手机号查询用户

**接口地址：** `GET /users/getByPhone`

**请求参数：** `phone=13800138000`

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "username": "zhangsan",
        "avatar": "users/avatars/zhangsan.jpg",
        "avatarFullUrl": "https://your-oss-domain.com/users/avatars/zhangsan.jpg",
        "nickname": "张三",
        "phone": "13800138000",
        "status": true
    }
}
```

## 🎓 讲师相关接口

### 1. 讲师分页查询

**接口地址：** `GET /teachers/page`

**请求参数：**
```json
{
    "pageNum": 1,
    "pageSize": 10,
    "name": "搜索讲师姓名（可选）",
    "title": "搜索职称（可选）",
    "expertise": "搜索专业领域（可选）"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "name": "李老师",
                "avatar": "teachers/avatars/li-teacher.jpg",
                "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/li-teacher.jpg",
                "title": "高级讲师",
                "introduction": "专业的数学老师",
                "experience": "10年教学经验",
                "location": "北京市",
                "expertise": "高中数学",
                "rating": 4.8,
                "studentCount": 1500,
                "courseCount": 25,
                "status": 1,
                "createdAt": "2025-01-01T08:00:00",
                "updatedAt": "2025-01-15T10:30:00"
            }
        ],
        "total": 50,
        "size": 10,
        "current": 1,
        "pages": 5
    }
}
```

### 2. 讲师列表查询

**接口地址：** `GET /teachers/list`

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "name": "李老师",
            "avatar": "teachers/avatars/li-teacher.jpg",
            "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/li-teacher.jpg",
            "title": "高级讲师",
            "expertise": "高中数学",
            "rating": 4.8,
            "studentCount": 1500,
            "status": 1
        }
    ]
}
```

### 3. 讲师详情查询

**接口地址：** `GET /teachers/{id}`

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "name": "李老师",
        "avatar": "teachers/avatars/li-teacher.jpg",
        "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/li-teacher.jpg",
        "title": "高级讲师",
        "introduction": "专业的数学老师，擅长高中数学教学",
        "experience": "拥有10年丰富的教学经验",
        "location": "北京市",
        "expertise": "高中数学、竞赛数学",
        "rating": 4.8,
        "studentCount": 1500,
        "courseCount": 25,
        "status": 1,
        "createdAt": "2025-01-01T08:00:00",
        "updatedAt": "2025-01-15T10:30:00"
    }
}
```

### 4. 获取推荐讲师

**接口地址：** `GET /teachers/recommended`

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "name": "李老师",
            "avatar": "teachers/avatars/li-teacher.jpg",
            "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/li-teacher.jpg",
            "title": "高级讲师",
            "expertise": "高中数学",
            "rating": 4.9,
            "studentCount": 2000,
            "status": 1
        },
        {
            "id": 2,
            "name": "王老师",
            "avatar": "teachers/avatars/wang-teacher.jpg",
            "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/wang-teacher.jpg",
            "title": "特级教师",
            "expertise": "高中物理",
            "rating": 4.8,
            "studentCount": 1800,
            "status": 1
        }
    ]
}
```

### 5. 根据评分获取讲师

**接口地址：** `GET /teachers/getByRating`

**请求参数：**
- `pageNum`: 页码（可选，默认1）
- `pageSize`: 每页大小（可选，默认10）

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "name": "李老师",
                "avatar": "teachers/avatars/li-teacher.jpg",
                "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/li-teacher.jpg",
                "title": "高级讲师",
                "expertise": "高中数学",
                "rating": 4.9,
                "studentCount": 2000,
                "status": 1
            }
        ],
        "total": 30,
        "size": 10,
        "current": 1,
        "pages": 3
    }
}
```

### 6. 根据学生数量获取讲师

**接口地址：** `GET /teachers/getByStudentCount`

**请求参数：**
- `pageNum`: 页码（可选，默认1）
- `pageSize`: 每页大小（可选，默认10）

**响应示例：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "name": "李老师",
                "avatar": "teachers/avatars/li-teacher.jpg",
                "avatarFullUrl": "https://your-oss-domain.com/teachers/avatars/li-teacher.jpg",
                "title": "高级讲师",
                "expertise": "高中数学",
                "rating": 4.8,
                "studentCount": 2500,
                "status": 1
            }
        ],
        "total": 25,
        "size": 10,
        "current": 1,
        "pages": 3
    }
}
```

## 📋 字段说明

### 头像相关字段

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|---------|
| `avatar` | String | 头像相对路径，存储在数据库中 | `"users/avatars/zhangsan.jpg"` |
| `avatarFullUrl` | String | 头像完整URL，系统自动生成 | `"https://your-oss-domain.com/users/avatars/zhangsan.jpg"` |

### 使用建议

1. **前端显示**：优先使用 `avatarFullUrl` 字段显示头像
2. **上传处理**：上传时只需要保存 `avatar` 相对路径到数据库
3. **默认头像**：当 `avatar` 为空时，`avatarFullUrl` 也为空，前端应使用默认头像
4. **缓存策略**：完整URL包含OSS域名，建议前端做适当缓存

## 🔄 兼容性说明

- **向后兼容**：原有的 `avatar` 字段保持不变，确保现有功能正常
- **新增字段**：`avatarFullUrl` 为新增字段，不影响现有数据结构
- **自动处理**：所有返回的数据都会自动包含完整URL，无需前端额外处理

## ⚠️ 注意事项

1. **网络依赖**：完整URL依赖OSS服务可用性
2. **URL有效期**：如果使用签名URL，注意有效期管理
3. **HTTPS支持**：建议OSS配置HTTPS访问
4. **CDN加速**：推荐配置CDN加速图片访问

这套头像处理机制让前端开发更加便捷，无需关心URL拼接逻辑！🎯 