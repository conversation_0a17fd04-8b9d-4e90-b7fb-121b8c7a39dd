# 直播课程功能增强开发文档

## 📋 功能需求

1. **直播封面设置功能**：为直播课程添加封面上传功能
2. **完善页面规则**：添加表单验证规则
3. **Select添加filterable**：让下拉选择支持搜索过滤
4. **课程分类参考年龄段模式**：支持从后端读取并在前端动态添加
5. **完善讲师关联查询**：解决只显示ID的问题，显示完整讲师信息

## 🎯 实现方案

### 1. 后端修改

#### 1.1 数据库结构更新

**直播课程表添加字段**：
```sql
-- 为直播课程表添加封面图片字段
ALTER TABLE `live_courses` 
ADD COLUMN `cover_image` varchar(255) DEFAULT NULL COMMENT '直播封面图片URL' AFTER `description`;

-- 为直播课程表添加讲师ID字段
ALTER TABLE `live_courses` 
ADD COLUMN `teacher_id` int DEFAULT NULL COMMENT '讲师ID' AFTER `cover_image`;

-- 为直播课程表添加分类ID字段
ALTER TABLE `live_courses` 
ADD COLUMN `category_id` int DEFAULT NULL COMMENT '分类ID' AFTER `teacher_id`;
```

#### 1.2 实体类更新

**LiveCourses实体类**：
- 添加 `coverImage` 字段
- 添加 `teacherId` 字段  
- 添加 `categoryId` 字段

**Mapper映射文件**：
- 更新 `BaseResultMap` 包含新字段
- 更新 `Base_Column_List` 包含新字段

#### 1.3 DTO类创建

**CourseWithTeacherDTO**：
```java
public class CourseWithTeacherDTO {
    private Courses course;
    private Teachers teacher;
    private Categories category;
    private String teacherName;
    private String teacherAvatar;
    private String categoryName;
}
```

**LiveCourseWithTeacherDTO**：
```java
public class LiveCourseWithTeacherDTO {
    private LiveCourses liveCourse;
    private Teachers teacher;
    private Categories category;
    private String teacherName;
    private String teacherAvatar;
    private String categoryName;
}
```

#### 1.4 服务层增强

**课程服务接口扩展**：
```java
public interface ICoursesService extends IService<Courses> {
    IPage<CourseWithTeacherDTO> pageWithTeacher(Page<Courses> page, Courses entity);
    CourseWithTeacherDTO getWithTeacherById(Integer id);
}
```

**直播课程服务接口扩展**：
```java
public interface ILiveCoursesService extends IService<LiveCourses> {
    IPage<LiveCourseWithTeacherDTO> pageWithTeacher(Page<LiveCourses> page, LiveCourses entity);
    LiveCourseWithTeacherDTO getWithTeacherById(Integer id);
}
```

#### 1.5 控制器增强

**新增关联查询接口**：
- `GET /api/courses-manage/page/with-teacher` - 分页查询课程及关联信息
- `GET /api/courses-manage/with-teacher/{id}` - 根据ID查询课程及关联信息
- `GET /api/live-courses-manage/page/with-teacher` - 分页查询直播课程及关联信息
- `GET /api/live-courses-manage/with-teacher/{id}` - 根据ID查询直播课程及关联信息

### 2. 前端修改

#### 2.1 直播编辑页面增强

**封面上传功能**：
```vue
<n-card title="直播封面" size="small">
  <OssDirectUpload
    v-model="formModel.coverImage"
    category="image"
    :show-preview="true"
    @upload-success="handleCoverUploadSuccess"
    @upload-error="handleCoverUploadError"
  />
</n-card>
```

**下拉选择增强**：
```vue
<n-select
  v-model:value="formModel.teacherId"
  :options="teacherOptions"
  placeholder="请选择讲师"
  filterable
  clearable
/>
```

#### 2.2 课程创建页面增强

**分类动态管理**：
```vue
<n-select
  v-model:value="basicForm.categoryId"
  :options="categoryOptions"
  placeholder="请选择分类"
  filterable
  clearable
  @update:value="handleCategoryChange"
>
  <template #action>
    <n-button text type="primary" @click="showCustomCategoryModal = true">
      <template #icon><n-icon><AddOutline /></n-icon></template>
      添加新分类
    </n-button>
  </template>
</n-select>
```

**表单验证规则完善**：
```javascript
const basicRules = {
  title: [
    { required: true, message: "请输入课程标题", trigger: "blur" },
    { min: 2, max: 100, message: "课程标题长度应在2-100字符之间", trigger: "blur" }
  ],
  teacherId: [
    { required: true, message: "请选择讲师", trigger: "change" }
  ],
  categoryId: [
    { required: true, message: "请选择分类", trigger: "change" }
  ],
  price: [
    { required: true, message: "请输入课程价格", trigger: "blur" },
    { type: "number", min: 0, message: "价格不能为负数", trigger: "blur" }
  ],
  description: [
    { required: true, message: "请输入课程描述", trigger: "blur" },
    { min: 10, max: 2000, message: "课程描述长度应在10-2000字符之间", trigger: "blur" }
  ]
};
```

#### 2.3 分类管理功能

**从TagConfigs读取分类**：
```javascript
const loadCategoryOptions = async () => {
  try {
    const res = await getAvailableTagConfigByCategory("course_category");
    if (res.code === 200) {
      categoryOptions.value = res.data.map((item) => ({
        label: item.label,
        value: item.value,
      }));
    }
  } catch (error) {
    // 备用方案：从Categories表加载
    const fallbackRes = await getCategoryList();
    // ...
  }
};
```

**动态添加分类**：
```javascript
const handleAddCustomCategory = async () => {
  if (!customCategory.value.trim()) {
    message.error("请输入分类名称");
    return false;
  }

  const addRes = await createTagConfig({
    category: "course_category",
    label: customCategory.value,
    value: customCategory.value,
    remark: customCategoryRemark.value || null,
    isSystem: 0,
    status: 1
  });

  if (addRes.code === 200) {
    await loadCategoryOptions();
    basicForm.categoryId = addRes.data.value;
    message.success("分类添加成功");
    showCustomCategoryModal.value = false;
  }
};
```

## 🔧 技术实现细节

### 关联查询优化

**批量查询避免N+1问题**：
```java
// 提取所有讲师ID和分类ID
List<Integer> teacherIds = courses.stream()
        .map(Courses::getTeacherId)
        .filter(id -> id != null)
        .distinct()
        .collect(Collectors.toList());

// 批量查询讲师信息
Map<Integer, Teachers> teacherMap = teachersService.listByIds(teacherIds)
        .stream()
        .collect(Collectors.toMap(Teachers::getId, teacher -> teacher));
```

### 文件存储优化

**相对路径存储**：
- 数据库只存储相对路径：`image/2025/01/15/abc123.jpg`
- 前端显示时转换为完整URL：`https://domain.com/image/2025/01/15/abc123.jpg`

### 表单验证增强

**多层级验证**：
1. 前端实时验证（用户体验）
2. 提交前完整验证（数据完整性）
3. 后端验证（安全性）

## 📊 功能特性

### 1. 直播封面管理
- ✅ 支持图片上传预览
- ✅ 文件格式验证
- ✅ 文件大小限制
- ✅ 上传进度显示

### 2. 下拉选择增强
- ✅ 支持搜索过滤（filterable）
- ✅ 支持清空选择（clearable）
- ✅ 动态加载选项
- ✅ 自定义添加选项

### 3. 表单验证完善
- ✅ 必填字段验证
- ✅ 长度限制验证
- ✅ 格式验证（手机号等）
- ✅ 数值范围验证
- ✅ 实时验证反馈

### 4. 关联查询优化
- ✅ 批量查询避免N+1问题
- ✅ 返回完整关联信息
- ✅ 支持分页查询
- ✅ 性能优化

## 🚀 部署说明

### 数据库更新
1. 执行 `docs/database-updates.sql` 脚本
2. 初始化课程分类标签配置数据

### 前端部署
1. 确保文件工具类正确配置OSS域名
2. 验证上传组件功能正常
3. 测试表单验证规则

### 后端部署
1. 确保新增的DTO类正确编译
2. 验证关联查询接口正常
3. 测试文件上传功能

## 🔍 测试验证

### 功能测试
- [ ] 直播封面上传功能
- [ ] 分类动态添加功能
- [ ] 表单验证规则
- [ ] 关联查询接口
- [ ] 下拉选择过滤功能

### 性能测试
- [ ] 关联查询性能
- [ ] 文件上传性能
- [ ] 页面加载速度

### 兼容性测试
- [ ] 新旧数据兼容性
- [ ] 不同浏览器兼容性
- [ ] 移动端适配

## 📝 注意事项

1. **数据迁移**：现有数据需要适配新的字段结构
2. **向后兼容**：确保现有功能不受影响
3. **性能监控**：关注关联查询的性能表现
4. **错误处理**：完善异常情况的处理逻辑
5. **用户体验**：确保操作流程顺畅自然

## 🔄 后续优化

1. **缓存机制**：对频繁查询的数据进行缓存
2. **搜索功能**：增强分类和讲师的搜索能力
3. **批量操作**：支持批量设置分类、讲师等
4. **权限控制**：细化不同角色的操作权限
5. **数据统计**：添加相关的数据统计功能
