# 课程管理增强筛选功能说明

## 功能概述 📊

新的课程管理页面采用了左侧筛选面板 + 右侧内容区域的布局设计，提供了更强大和美观的筛选功能，支持多维度的课程查询和管理。

## 主要特性 ✨

### 1. 左侧筛选面板
- **折叠面板设计**：使用 Naive UI 的 Collapse 组件，默认展开所有筛选项
- **多选筛选**：支持同时选择多个条件进行组合筛选
- **实时筛选**：筛选条件变化时自动应用，无需手动点击搜索
- **响应式设计**：适配不同屏幕尺寸

### 2. 筛选维度

#### 📚 课程分类
- 支持多选分类进行筛选
- 数据来源：动态从后端分类API获取
- 实时更新：新增分类后自动在筛选中显示

#### 👥 适合年龄段
- 支持多选年龄段筛选
- 数据来源：TagConfig系统，支持动态配置
- 扩展性强：可通过标签配置管理添加新的年龄段

#### 📈 难度级别
- 支持多选难度级别筛选
- 数据来源：TagConfig系统
- 标准化：入门、初级、中级、高级等级别

#### 📊 课程状态
- 上架/下架状态筛选
- 支持多选状态组合

#### 💰 价格筛选
- **免费课程**：快速筛选免费课程
- **价格范围**：支持设置最低价格和最高价格区间
- **灵活组合**：可以同时使用免费选项和价格区间

#### 🎯 课程类型
- **直播课**：实时直播课程
- **推荐课程**：标记为推荐的精品课程
- **特训营**：集中培训类课程
- **一对一**：个性化辅导课程
- 支持多选组合

### 3. 布局设计

#### 左侧筛选面板 (280px)
```
├── 课程分类 (默认展开)
├── 适合年龄段 (默认展开)  
├── 难度级别 (默认展开)
├── 课程状态 (默认展开)
├── 价格范围
└── 课程类型
```

#### 右侧内容区域
- **搜索栏**：课程名称、讲师快速搜索
- **操作栏**：添加课程、批量删除
- **数据表格**：课程列表展示

## 技术实现 🔧

### 前端实现

#### 1. 组件结构
```vue
<template>
  <div class="course-management">
    <!-- 左侧筛选面板 -->
    <div class="filter-panel">
      <n-collapse :default-expanded-names="['category', 'ageGroup', 'level', 'status']">
        <!-- 各种筛选项 -->
      </n-collapse>
    </div>
    
    <!-- 右侧内容区域 -->
    <div class="content-area">
      <!-- 搜索、操作、表格 -->
    </div>
  </div>
</template>
```

#### 2. 响应式参数
```javascript
const searchParams = reactive({
  title: "",
  teacherId: null,
  // 多选筛选参数
  categoryIds: [],
  ageGroupIds: [],
  levelIds: [],
  statusIds: [],
  courseTypes: [],
  // 价格筛选
  isFree: false,
  minPrice: null,
  maxPrice: null,
});
```

#### 3. 自动筛选监听
```javascript
// 监听筛选条件变化，自动应用筛选
watch(
  () => [
    searchParams.categoryIds,
    searchParams.ageGroupIds,
    searchParams.levelIds,
    searchParams.statusIds,
    searchParams.courseTypes,
    searchParams.isFree
  ],
  () => {
    applyFilters();
  },
  { deep: true }
);
```

### 后端实现

#### 1. 增强查询接口
```java
@GetMapping("/page/enhanced")
public R<IPage<Courses>> pageEnhanced(
    @RequestParam(defaultValue = "1") Integer pageNum,
    @RequestParam(defaultValue = "10") Integer pageSize,
    @RequestParam(required = false) String title,
    @RequestParam(required = false) Integer teacherId,
    @RequestParam(required = false) String categoryIds,
    @RequestParam(required = false) String ageGroupIds,
    // ... 其他参数
) {
    // 构建查询条件并执行分页查询
}
```

#### 2. 多选参数处理
```java
// 多选分类筛选
if (categoryIds != null && !categoryIds.isEmpty()) {
    String[] categoryArray = categoryIds.split(",");
    if (categoryArray.length > 0) {
        queryWrapper.in("category_id", Arrays.asList(categoryArray));
    }
}
```

#### 3. 复杂条件处理
```java
// 免费课程筛选
if (isFree != null && isFree) {
    queryWrapper.and(wrapper -> wrapper.eq("price", 0).or().isNull("price"));
}

// 价格范围筛选
if (minPrice != null && minPrice >= 0) {
    queryWrapper.ge("price", minPrice);
}
if (maxPrice != null && maxPrice >= 0) {
    queryWrapper.le("price", maxPrice);
}
```

## API接口 🔌

### 增强查询接口
```
GET /courses-manage/page/enhanced
```

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页条数，默认10 |
| title | String | 否 | 课程标题关键词 |
| teacherId | Integer | 否 | 讲师ID |
| categoryIds | String | 否 | 分类ID列表，逗号分隔 |
| ageGroupIds | String | 否 | 年龄段ID列表，逗号分隔 |
| levelIds | String | 否 | 难度级别ID列表，逗号分隔 |
| statusIds | String | 否 | 状态列表，逗号分隔 |
| isFree | Boolean | 否 | 是否免费课程 |
| minPrice | Double | 否 | 最低价格 |
| maxPrice | Double | 否 | 最高价格 |
| isLive | Boolean | 否 | 是否直播课 |
| isFeatured | Boolean | 否 | 是否推荐课程 |
| isSpecialTraining | Boolean | 否 | 是否特训营 |
| isOneOnOne | Boolean | 否 | 是否一对一 |

#### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "title": "JavaScript基础教程",
        "teacherName": "张老师",
        "categoryName": "前端开发",
        "price": 99.00,
        "level": 1,
        "ageGroup": 2,
        "status": 1,
        "createdAt": "2025-01-15T10:30:00"
      }
    ],
    "total": 50,
    "pages": 5,
    "current": 1,
    "size": 10
  }
}
```

## 样式设计 🎨

### 左侧筛选面板样式
```scss
.filter-panel {
  width: 280px;
  flex-shrink: 0;
}

.filter-card {
  position: sticky;
  top: 20px;
  max-height: calc(100vh - 140px);
  overflow-y: auto;
}

.filter-section {
  padding: 8px 0;
}
```

### 价格范围输入样式
```scss
.price-range {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.price-separator {
  color: #666;
  font-size: 12px;
}
```

### 响应式设计
```scss
@media (max-width: 1200px) {
  .course-management {
    flex-direction: column;
  }
  
  .filter-panel {
    width: 100%;
  }
  
  .filter-card {
    position: relative;
    max-height: none;
  }
}
```

## 使用方法 📖

### 1. 基础筛选
1. 在左侧面板选择需要的筛选条件
2. 系统自动应用筛选并更新结果
3. 可以在右侧搜索栏进一步搜索

### 2. 组合筛选
- **分类 + 年龄段**：选择特定分类下适合某年龄段的课程
- **价格 + 类型**：筛选特定价格范围内的直播课程
- **状态 + 难度**：查看已上架的入门级课程

### 3. 筛选重置
- **单项重置**：取消勾选对应的筛选项
- **整体重置**：点击"重置筛选"按钮
- **搜索重置**：点击搜索栏的"重置"按钮

### 4. 移动端使用
在小屏幕设备上，筛选面板会调整为垂直布局，保持良好的使用体验。

## 数据来源 📄

### 1. 静态选项
- 课程状态：上架/下架
- 课程类型：直播课、推荐课程、特训营、一对一

### 2. 动态选项  
- **课程分类**：`/categories/page` API
- **讲师列表**：`/teachers/page` API
- **难度级别**：`/tag-configs/getAvailableByCategory/level` API
- **年龄段**：`/tag-configs/getAvailableByCategory/age_group` API

### 3. 扩展性
通过TagConfig系统可以轻松扩展：
- 新增年龄段选项
- 新增难度级别
- 新增其他标签类型

## 性能优化 ⚡

### 1. 前端优化
- **懒加载**：选项数据按需加载
- **防抖处理**：输入框防抖搜索
- **缓存机制**：选项数据本地缓存

### 2. 后端优化
- **索引优化**：为常用筛选字段添加索引
- **查询优化**：使用MyBatis-Plus的高效查询
- **分页优化**：合理的分页大小设置

### 3. 网络优化
- **参数压缩**：多选参数使用逗号分隔传输
- **响应压缩**：启用gzip压缩
- **缓存策略**：合理的HTTP缓存头设置

## 未来扩展 🚀

### 1. 高级筛选
- 时间范围筛选（创建时间、更新时间）
- 评分范围筛选
- 学习人数范围筛选

### 2. 个性化筛选
- 用户常用筛选保存
- 智能推荐筛选条件
- 筛选历史记录

### 3. 批量操作
- 基于筛选结果的批量操作
- 筛选条件导出
- 数据统计分析

## 注意事项 ⚠️

1. **性能考虑**：大量数据时建议限制同时选择的筛选条件数量
2. **数据一致性**：确保前后端筛选逻辑一致
3. **用户体验**：避免过于复杂的筛选组合
4. **移动端适配**：注意小屏幕设备的使用体验
5. **权限控制**：根据用户权限显示相应的筛选选项

## 相关文件 📁

### 前端文件
- `front/admin_front/src/views/course/list.vue` - 主页面组件
- `front/admin_front/src/api/course.js` - API接口定义

### 后端文件  
- `back/src/main/java/pox/com/dianfeng/controller/CoursesController.java` - 控制器
- `back/src/main/java/pox/com/dianfeng/entity/Courses.java` - 实体类

### 标签配置系统
- `back/src/main/java/pox/com/dianfeng/controller/TagConfigsController.java`
- `front/admin_front/src/api/tagConfig.js` 