# 阿里云RAM角色配置指南

## 🎯 目标

为OSS直传功能创建一个RAM角色，用于STS临时访问凭证的生成。

## 📋 前置条件

- 拥有阿里云账号
- 具有RAM管理权限
- 已开通OSS服务
- 知道OSS存储桶名称：`dianfeng-class`

## 🔧 配置步骤

### 1. 登录阿里云控制台

1. 访问 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 登录你的阿里云账号
3. 搜索并进入 "访问控制 RAM"

### 2. 创建RAM角色

#### 2.1 进入角色管理
- 在RAM控制台左侧菜单中，点击 "身份管理" → "角色"
- 点击 "创建角色" 按钮

#### 2.2 选择角色类型
- **可信实体类型**: 选择 "阿里云服务"
- **角色类型**: 选择 "普通服务角色"
- **选择受信服务**: 选择 "STS"

#### 2.3 配置角色信息
- **角色名称**: `OSSUploadRole`
- **备注**: `用于前端直传OSS的STS角色`

#### 2.4 配置信任策略
系统会自动生成信任策略，内容如下：
```json
{
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Effect": "Allow",
      "Principal": {
        "Service": [
          "sts.aliyuncs.com"
        ]
      }
    }
  ],
  "Version": "1"
}
```

### 3. 创建权限策略

#### 3.1 创建自定义策略
- 在RAM控制台中，点击 "权限管理" → "权限策略管理"
- 点击 "创建权限策略"

#### 3.2 配置策略信息
- **策略名称**: `OSSUploadPolicy`
- **配置模式**: 选择 "脚本配置"
- **策略内容**: 复制以下JSON内容

```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "oss:PutObject",
        "oss:PutObjectAcl"
      ],
      "Resource": [
        "acs:oss:*:*:dianfeng-class/*"
      ]
    }
  ]
}
```

#### 3.3 完成策略创建
- 点击 "确定" 完成策略创建

### 4. 为角色授权

#### 4.1 返回角色管理
- 在角色列表中找到刚创建的 `OSSUploadRole`
- 点击角色名称进入详情页

#### 4.2 添加权限
- 点击 "添加权限" 按钮
- **授权范围**: 选择 "云账号全部资源"
- **选择权限**: 搜索并选择刚创建的 `OSSUploadPolicy`
- 点击 "确定" 完成授权

### 5. 获取角色ARN

#### 5.1 查看角色信息
- 在角色详情页面，找到 "基本信息" 部分
- 复制 "ARN" 字段的值

#### 5.2 ARN格式示例
```
acs:ram::1282081849547244:role/OSSUploadRole
```

其中：
- `1282081849547244` 是你的阿里云账号ID
- `OSSUploadRole` 是角色名称

### 6. 更新应用配置

#### 6.1 修改配置文件
编辑 `back/src/main/resources/application.yaml` 文件：

```yaml
aliyun:
  sts:
    # 替换为你的真实角色ARN
    role-arn: acs:ram::你的账号ID:role/OSSUploadRole
```

#### 6.2 重启应用
重启后端服务使配置生效。

## 🧪 测试配置

### 1. 测试STS接口
```bash
curl -X GET "http://localhost:8082/api/sts/token?category=image"
```

### 2. 检查返回结果
成功的响应应该包含真实的STS凭证：
```json
{
  "code": 200,
  "data": {
    "accessKeyId": "STS.NUxxxxxxxx",
    "accessKeySecret": "xxxxxxxx",
    "securityToken": "xxxxxxxx",
    "expiration": "2025-06-05T12:00:00Z",
    "bucketName": "dianfeng-class",
    "endpoint": "https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com",
    "region": "cn-chengdu",
    "pathPrefix": "image/2025/06/05/",
    "maxFileSize": 10485760,
    "allowedFileTypes": ["jpg", "jpeg", "png", "gif", "webp", "bmp"],
    "durationSeconds": 3600
  }
}
```

### 3. 测试文件上传
在前端页面测试文件上传功能，确认可以成功上传到OSS。

## 🔒 安全注意事项

### 1. 权限最小化
- 只授予必要的OSS权限
- 限制资源范围到特定存储桶
- 定期审查和更新权限策略

### 2. 监控和审计
- 启用RAM操作日志
- 监控STS令牌使用情况
- 定期检查角色使用情况

### 3. 凭证管理
- 不要在代码中硬编码访问密钥
- 使用环境变量或配置文件管理敏感信息
- 定期轮换访问密钥

## 🐛 常见问题

### 1. 角色ARN格式错误
```
错误：InvalidParameter.RoleArn
解决：检查ARN格式，确保包含正确的账号ID和角色名称
```

### 2. 权限不足
```
错误：NoPermission
解决：检查角色是否已授权OSSUploadPolicy权限策略
```

### 3. 信任关系错误
```
错误：The role can not be assumed by caller
解决：检查角色的信任策略是否允许STS服务
```

### 4. 资源访问被拒绝
```
错误：AccessDenied
解决：检查权限策略中的资源ARN是否正确
```

## 📚 参考文档

- [阿里云RAM角色管理](https://help.aliyun.com/zh/ram/user-guide/create-a-ram-role-for-a-trusted-alibaba-cloud-service)
- [STS临时访问凭证](https://help.aliyun.com/zh/oss/developer-reference/use-temporary-access-credentials-provided-by-sts-to-access-oss)
- [OSS权限策略](https://help.aliyun.com/zh/oss/user-guide/overview-of-ram-policies)

## ✅ 配置检查清单

- [ ] 创建RAM角色 `OSSUploadRole`
- [ ] 创建权限策略 `OSSUploadPolicy`
- [ ] 为角色授权权限策略
- [ ] 获取角色ARN
- [ ] 更新应用配置文件
- [ ] 重启后端服务
- [ ] 测试STS接口
- [ ] 测试文件上传功能

完成以上步骤后，OSS直传功能就可以正常使用了！🎉
