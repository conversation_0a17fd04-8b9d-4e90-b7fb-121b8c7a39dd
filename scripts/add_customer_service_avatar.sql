-- 为客服联系信息表添加头像字段
-- 执行时间：2025-01-16

USE dianfeng_class;

-- 添加头像字段到客服联系信息表
ALTER TABLE `customer_service_contacts` 
ADD COLUMN `avatar` VARCHAR(255) DEFAULT NULL COMMENT '客服头像URL（相对路径）' 
AFTER `name`;

-- 创建索引以提高查询性能
CREATE INDEX `idx_customer_service_avatar` ON `customer_service_contacts` (`avatar`);

-- 更新一些示例数据（可选）
-- INSERT INTO `customer_service_contacts` 
-- (`name`, `avatar`, `phone`, `wechat`, `remark`, `status`) 
-- VALUES 
-- ('客服小王', 'image/avatar/customer1.jpg', '13800138000', 'wechat_xiaowang', '技术支持专家', 1),
-- ('客服小李', 'image/avatar/customer2.jpg', '13800138001', 'wechat_xiaoli', '课程咨询专员', 1),
-- ('客服小张', 'image/avatar/customer3.jpg', '13800138002', 'wechat_xiaozhang', '售后服务专员', 1);

-- 显示表结构确认修改
DESCRIBE `customer_service_contacts`; 