#!/bin/bash

# OSS CORS配置脚本
# 用于配置阿里云OSS Bucket的CORS规则

BUCKET_NAME="dianfeng-class"
REGION="oss-cn-chengdu"

echo "🔧 开始配置OSS CORS规则..."

# 创建CORS配置文件
cat > cors-config.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<CORSConfiguration>
    <CORSRule>
        <AllowedOrigin>*</AllowedOrigin>
        <AllowedMethod>GET</AllowedMethod>
        <AllowedMethod>POST</AllowedMethod>
        <AllowedMethod>PUT</AllowedMethod>
        <AllowedMethod>DELETE</AllowedMethod>
        <AllowedMethod>HEAD</AllowedMethod>
        <AllowedHeader>*</AllowedHeader>
        <ExposeHeader>ETag</ExposeHeader>
        <ExposeHeader>x-oss-request-id</ExposeHeader>
        <MaxAgeSeconds>3600</MaxAgeSeconds>
    </CORSRule>
    <CORSRule>
        <AllowedOrigin>http://localhost:8088</AllowedOrigin>
        <AllowedOrigin>http://localhost:8090</AllowedOrigin>
        <AllowedOrigin>http://127.0.0.1:8088</AllowedOrigin>
        <AllowedOrigin>http://127.0.0.1:8090</AllowedOrigin>
        <AllowedMethod>GET</AllowedMethod>
        <AllowedMethod>POST</AllowedMethod>
        <AllowedMethod>PUT</AllowedMethod>
        <AllowedMethod>DELETE</AllowedMethod>
        <AllowedMethod>HEAD</AllowedMethod>
        <AllowedMethod>OPTIONS</AllowedMethod>
        <AllowedHeader>*</AllowedHeader>
        <ExposeHeader>ETag</ExposeHeader>
        <ExposeHeader>x-oss-request-id</ExposeHeader>
        <ExposeHeader>x-oss-version-id</ExposeHeader>
        <MaxAgeSeconds>3600</MaxAgeSeconds>
    </CORSRule>
</CORSConfiguration>
EOF

# 使用阿里云CLI配置CORS
if command -v aliyun &> /dev/null; then
    echo "📡 使用阿里云CLI配置CORS..."
    aliyun oss api PutBucketCors --bucket $BUCKET_NAME --body "$(cat cors-config.xml)"
    
    if [ $? -eq 0 ]; then
        echo "✅ CORS配置成功！"
    else
        echo "❌ CORS配置失败，请检查阿里云CLI配置"
    fi
else
    echo "⚠️  未找到阿里云CLI，请手动配置CORS规则"
    echo "📋 CORS配置内容已保存到 cors-config.xml"
    echo "请在阿里云控制台手动配置CORS规则"
fi

# 清理临时文件
rm -f cors-config.xml

echo "🎯 CORS配置完成！"
echo ""
echo "📝 手动配置步骤："
echo "1. 访问 https://oss.console.aliyun.com/"
echo "2. 选择Bucket: $BUCKET_NAME"
echo "3. 权限管理 → 跨域设置"
echo "4. 添加规则："
echo "   - 来源: *"
echo "   - 方法: GET, POST, PUT, DELETE, HEAD, OPTIONS"
echo "   - 允许Headers: *"
echo "   - 暴露Headers: ETag, x-oss-request-id"
echo "   - 缓存时间: 3600"
