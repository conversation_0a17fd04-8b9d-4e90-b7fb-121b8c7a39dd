#!/bin/bash

# 客服搜索功能测试脚本

BASE_URL="http://localhost:8080/api"

echo "🔍 测试客服搜索功能"
echo "======================"

echo ""
echo "1. 测试无参数查询（获取所有客服）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10" | jq '.'

echo ""
echo "2. 测试按姓名搜索"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&name=客服" | jq '.'

echo ""
echo "3. 测试按联系方式搜索（手机号）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&contact=138" | jq '.'

echo ""
echo "4. 测试按联系方式搜索（微信）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&contact=wechat" | jq '.'

echo ""
echo "5. 测试按联系方式搜索（邮箱）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&contact=@qq.com" | jq '.'

echo ""
echo "6. 测试按状态搜索（启用）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&status=true" | jq '.'

echo ""
echo "7. 测试按状态搜索（禁用）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&status=false" | jq '.'

echo ""
echo "8. 测试组合搜索（姓名+联系方式+状态）"
curl -s "${BASE_URL}/customer-service-contacts/page?pageNum=1&pageSize=10&name=客服&contact=138&status=true" | jq '.'

echo ""
echo "✅ 测试完成！" 