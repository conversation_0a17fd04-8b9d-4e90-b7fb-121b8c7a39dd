#!/bin/bash

# STS配置测试脚本
# 用于验证STS配置是否正确

set -e

echo "🧪 STS配置测试脚本"
echo "===================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 后端服务地址
BACKEND_URL="http://localhost:8082"

echo -e "${BLUE}📋 开始测试STS配置...${NC}"

# 1. 检查后端服务是否运行
echo ""
echo -e "${BLUE}1. 检查后端服务状态...${NC}"

if curl -s --connect-timeout 5 "$BACKEND_URL/api/sts/token?category=image" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端服务正在运行${NC}"
else
    echo -e "${RED}❌ 后端服务未运行或无法访问${NC}"
    echo "请确保后端服务已启动并监听在端口8082"
    exit 1
fi

# 2. 测试图片类型STS凭证
echo ""
echo -e "${BLUE}2. 测试图片类型STS凭证...${NC}"

image_response=$(curl -s "$BACKEND_URL/api/sts/token?category=image")
echo "响应: $image_response"

if echo "$image_response" | grep -q '"code":200'; then
    echo -e "${GREEN}✅ STS接口响应正常${NC}"
    
    # 检查是否为模拟凭证
    if echo "$image_response" | grep -q "STS.MOCK"; then
        echo -e "${YELLOW}⚠️  返回模拟STS凭证${NC}"
        echo "原因: 未配置真实的RAM角色ARN"
        echo "解决: 运行 ./scripts/setup-sts.sh 配置真实角色"
        mock_credential=true
    else
        echo -e "${GREEN}✅ 返回真实STS凭证${NC}"
        mock_credential=false
    fi
else
    echo -e "${RED}❌ STS接口返回错误${NC}"
    echo "请检查后端日志获取详细错误信息"
    exit 1
fi

# 3. 测试视频类型STS凭证
echo ""
echo -e "${BLUE}3. 测试视频类型STS凭证...${NC}"

video_response=$(curl -s "$BACKEND_URL/api/sts/token?category=video")
echo "响应: $video_response"

if echo "$video_response" | grep -q '"code":200'; then
    echo -e "${GREEN}✅ 视频STS接口响应正常${NC}"
else
    echo -e "${RED}❌ 视频STS接口返回错误${NC}"
fi

# 4. 测试音频类型STS凭证
echo ""
echo -e "${BLUE}4. 测试音频类型STS凭证...${NC}"

audio_response=$(curl -s "$BACKEND_URL/api/sts/token?category=audio")
echo "响应: $audio_response"

if echo "$audio_response" | grep -q '"code":200'; then
    echo -e "${GREEN}✅ 音频STS接口响应正常${NC}"
else
    echo -e "${RED}❌ 音频STS接口返回错误${NC}"
fi

# 5. 检查配置文件
echo ""
echo -e "${BLUE}5. 检查配置文件...${NC}"

CONFIG_FILE="back/src/main/resources/application.yaml"

if [ -f "$CONFIG_FILE" ]; then
    current_arn=$(grep "role-arn:" "$CONFIG_FILE" | awk '{print $2}')
    echo -e "当前角色ARN: ${YELLOW}$current_arn${NC}"
    
    if [ "$current_arn" = "REPLACE_WITH_REAL_ARN" ]; then
        echo -e "${YELLOW}⚠️  使用占位符ARN，需要配置真实角色${NC}"
    else
        echo -e "${GREEN}✅ 已配置角色ARN${NC}"
    fi
else
    echo -e "${RED}❌ 配置文件不存在${NC}"
fi

# 6. 生成测试报告
echo ""
echo -e "${BLUE}📊 测试报告${NC}"
echo "============"

if [ "$mock_credential" = true ]; then
    echo -e "${YELLOW}状态: 使用模拟凭证${NC}"
    echo -e "${YELLOW}建议: 配置真实RAM角色以启用完整功能${NC}"
    echo ""
    echo "配置步骤:"
    echo "1. 运行: ./scripts/setup-sts.sh"
    echo "2. 按照提示配置RAM角色ARN"
    echo "3. 重启后端服务"
    echo "4. 重新运行此测试脚本"
else
    echo -e "${GREEN}状态: 配置正常${NC}"
    echo -e "${GREEN}建议: 可以正常使用OSS直传功能${NC}"
fi

echo ""
echo -e "${BLUE}📚 相关文档:${NC}"
echo "- RAM角色配置: docs/setup-ram-role.md"
echo "- 功能总结: docs/oss-direct-upload-summary.md"

echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
