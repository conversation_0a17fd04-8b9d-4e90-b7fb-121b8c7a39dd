#!/bin/bash

# 阿里云OSS STS配置脚本
# 用于快速配置RAM角色ARN

set -e

echo "🚀 阿里云OSS STS配置脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
CONFIG_FILE="back/src/main/resources/application.yaml"

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}❌ 配置文件不存在: $CONFIG_FILE${NC}"
    exit 1
fi

echo -e "${BLUE}📋 当前配置状态检查...${NC}"

# 检查当前配置
current_arn=$(grep "role-arn:" "$CONFIG_FILE" | awk '{print $2}')
echo -e "当前角色ARN: ${YELLOW}$current_arn${NC}"

if [ "$current_arn" = "REPLACE_WITH_REAL_ARN" ]; then
    echo -e "${YELLOW}⚠️  当前使用占位符，需要配置真实的角色ARN${NC}"
    need_config=true
else
    echo -e "${GREEN}✅ 已配置角色ARN${NC}"
    need_config=false
fi

# 询问是否需要配置
if [ "$need_config" = true ]; then
    echo ""
    echo -e "${BLUE}🔧 开始配置RAM角色ARN...${NC}"
    echo ""
    echo "请按照以下步骤操作："
    echo "1. 登录阿里云控制台"
    echo "2. 进入访问控制RAM"
    echo "3. 创建角色 'OSSUploadRole'"
    echo "4. 配置权限策略"
    echo "5. 获取角色ARN"
    echo ""
    echo -e "${YELLOW}详细步骤请参考: docs/setup-ram-role.md${NC}"
    echo ""
    
    read -p "是否已完成RAM角色创建？(y/n): " confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        echo ""
        echo "请输入你的角色ARN（格式：acs:ram::账号ID:role/OSSUploadRole）:"
        read -p "角色ARN: " role_arn
        
        # 验证ARN格式
        if [[ $role_arn =~ ^acs:ram::[0-9]+:role/[a-zA-Z0-9_-]+$ ]]; then
            echo -e "${GREEN}✅ ARN格式验证通过${NC}"
            
            # 备份原配置文件
            cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
            echo -e "${BLUE}📁 已备份原配置文件${NC}"
            
            # 更新配置文件
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i '' "s|role-arn: REPLACE_WITH_REAL_ARN|role-arn: $role_arn|g" "$CONFIG_FILE"
            else
                # Linux
                sed -i "s|role-arn: REPLACE_WITH_REAL_ARN|role-arn: $role_arn|g" "$CONFIG_FILE"
            fi
            
            echo -e "${GREEN}✅ 配置文件已更新${NC}"
            echo -e "新的角色ARN: ${GREEN}$role_arn${NC}"
            
        else
            echo -e "${RED}❌ ARN格式错误，请检查格式${NC}"
            echo "正确格式: acs:ram::账号ID:role/角色名称"
            exit 1
        fi
    else
        echo -e "${YELLOW}⏸️  请先完成RAM角色创建，然后重新运行此脚本${NC}"
        exit 0
    fi
else
    read -p "是否需要重新配置角色ARN？(y/n): " reconfig
    
    if [ "$reconfig" = "y" ] || [ "$reconfig" = "Y" ]; then
        echo ""
        echo "请输入新的角色ARN:"
        read -p "角色ARN: " role_arn
        
        # 验证ARN格式
        if [[ $role_arn =~ ^acs:ram::[0-9]+:role/[a-zA-Z0-9_-]+$ ]]; then
            echo -e "${GREEN}✅ ARN格式验证通过${NC}"
            
            # 备份原配置文件
            cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
            echo -e "${BLUE}📁 已备份原配置文件${NC}"
            
            # 更新配置文件
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i '' "s|role-arn: .*|role-arn: $role_arn|g" "$CONFIG_FILE"
            else
                # Linux
                sed -i "s|role-arn: .*|role-arn: $role_arn|g" "$CONFIG_FILE"
            fi
            
            echo -e "${GREEN}✅ 配置文件已更新${NC}"
            echo -e "新的角色ARN: ${GREEN}$role_arn${NC}"
            
        else
            echo -e "${RED}❌ ARN格式错误，请检查格式${NC}"
            echo "正确格式: acs:ram::账号ID:role/角色名称"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ 保持当前配置不变${NC}"
    fi
fi

echo ""
echo -e "${BLUE}🧪 测试配置...${NC}"

# 检查后端服务是否运行
if curl -s http://localhost:8082/api/sts/token?category=image > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端服务正在运行${NC}"
    
    # 测试STS接口
    echo "正在测试STS接口..."
    response=$(curl -s http://localhost:8082/api/sts/token?category=image)
    
    if echo "$response" | grep -q "STS.MOCK"; then
        echo -e "${YELLOW}⚠️  仍在使用模拟STS凭证${NC}"
        echo "请检查角色ARN配置是否正确，并重启后端服务"
    elif echo "$response" | grep -q "STS."; then
        echo -e "${GREEN}✅ STS接口返回真实凭证${NC}"
        echo "配置成功！"
    else
        echo -e "${RED}❌ STS接口测试失败${NC}"
        echo "响应: $response"
    fi
else
    echo -e "${YELLOW}⚠️  后端服务未运行或无法访问${NC}"
    echo "请启动后端服务后再测试"
fi

echo ""
echo -e "${BLUE}📝 下一步操作:${NC}"
echo "1. 重启后端服务使配置生效"
echo "2. 测试前端文件上传功能"
echo "3. 检查OSS存储桶中是否有上传的文件"

echo ""
echo -e "${GREEN}🎉 配置脚本执行完成！${NC}"
