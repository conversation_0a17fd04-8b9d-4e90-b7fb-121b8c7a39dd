-- 首页推荐管理表
-- 执行时间：2025-01-16

USE dianfeng_class;

-- 创建首页推荐表
DROP TABLE IF EXISTS `homepage_recommendations`;
CREATE TABLE `homepage_recommendations` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '推荐ID，主键',
  `title` varchar(255) NOT NULL COMMENT '推荐标题',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '推荐副标题',
  `description` text COMMENT '推荐描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL（相对路径）',
  `link_type` tinyint(1) DEFAULT '1' COMMENT '链接类型：1-课程，2-直播，3-外部链接',
  `link_target_id` int DEFAULT NULL COMMENT '关联目标ID（课程ID或直播ID）',
  `link_url` varchar(500) DEFAULT NULL COMMENT '跳转链接URL',
  `sort_order` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_link_type` (`link_type`),
  KEY `idx_link_target_id` (`link_target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='首页推荐表';

-- 插入示例数据
INSERT INTO `homepage_recommendations` (`title`, `subtitle`, `description`, `cover_image`, `link_type`, `link_target_id`, `link_url`, `sort_order`, `status`) VALUES
('春季新课程上线', '精品课程，限时优惠', '春季新课程全面上线，涵盖多个学科，专业讲师授课，限时优惠中！', 'image/homepage/spring_courses.jpg', 1, 1, '/course/list', 1, 1),
('名师直播课', '互动教学，实时答疑', '名师在线直播授课，实时互动答疑，提升学习效果。', 'image/homepage/live_courses.jpg', 2, 1, '/live/list', 2, 1),
('学习打卡活动', '坚持学习，赢取奖励', '参与学习打卡活动，坚持学习可获得丰厚奖励。', 'image/homepage/checkin_activity.jpg', 3, NULL, '/statistics/checkin', 3, 0);

-- 显示表结构确认创建
DESCRIBE `homepage_recommendations`;
