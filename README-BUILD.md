# 🏗️ 鼎峰课堂构建指南

## 快速开始

### 🚀 一键构建所有环境

```bash
# 开发环境 - 包含前端和后端
./deploy/build-all.sh dev

# 生产环境 - 包含前端和后端  
./deploy/build-all.sh prod

# 测试环境 - 只构建前端
./deploy/build-all.sh test frontend

# 预发布环境 - 只构建后端
./deploy/build-all.sh staging backend
```

## 📋 支持的环境

| 环境 | 说明 | API地址 | 数据库 |
|------|------|---------|--------|
| `dev` | 开发环境 | http://localhost:8082/api | 本地MySQL |
| `test` | 测试环境 | http://test-api.dianfeng.com/api | 测试服务器 |
| `staging` | 预发布环境 | http://staging-api.dianfeng.com/api | 预发布服务器 |
| `prod` | 生产环境 | https://api.dianfeng.com/api | 生产服务器 |

## 🎯 分别构建

### 前端构建

```bash
# 管理后台
cd front/admin_front
pnpm run build:dev      # 开发环境
pnpm run build:test     # 测试环境
pnpm run build:staging  # 预发布环境
pnpm run build:prod     # 生产环境

# 移动端
cd front/use_front
pnpm run build:web:dev   # 开发环境Web版
pnpm run build:web:prod  # 生产环境Web版
pnpm run build:android:prod  # Android应用
pnpm run build:ios:prod      # iOS应用
```

### 后端构建

```bash
# 使用脚本构建
./deploy/build-backend.sh dev   # 开发环境
./deploy/build-backend.sh prod  # 生产环境

# 或直接使用Maven
cd back
mvn clean package -Pdev   # 开发环境
mvn clean package -Pprod  # 生产环境
```

## 🐳 Docker部署

```bash
# 开发环境（包含数据库）
docker-compose -f deploy/docker-compose.dev.yml up -d

# 生产环境
docker-compose -f deploy/docker-compose.prod.yml up -d

# 生产环境 + 监控
docker-compose -f deploy/docker-compose.prod.yml --profile monitoring up -d
```

## 📦 构建产物

构建完成后，产物位置：

- **管理后台**: `front/admin_front/dist-{环境}/`
- **移动端**: `front/use_front/dist/`
- **后端JAR**: `back/target/back-0.0.1-SNAPSHOT-{环境}.jar`
- **启动脚本**: `back/start-{环境}.sh`

## 🔧 环境配置

每个环境都有独立的配置文件：

### 前端配置
- `front/admin_front/.env.{环境}`
- `front/use_front/.env.{环境}`

### 后端配置
- `back/src/main/resources/application-{环境}.yaml`

## ⚡ 快速启动

构建完成后启动应用：

```bash
# 启动后端（自动生成的脚本）
./back/start-dev.sh     # 开发环境
./back/start-prod.sh    # 生产环境

# 或手动启动
java -jar back/target/back-0.0.1-SNAPSHOT-prod.jar --spring.profiles.active=prod
```

## 📊 构建脚本功能

- ✅ 自动环境检测和验证
- ✅ 依赖检查和安装
- ✅ 多环境配置切换
- ✅ 构建产物大小统计
- ✅ 构建时间统计
- ✅ 自动生成启动脚本
- ✅ 彩色输出和进度提示
- ✅ 错误处理和回滚

## 🛠️ 故障排除

### 常见问题

1. **权限错误**: `chmod +x deploy/*.sh`
2. **Node.js版本**: 确保使用Node.js 16+
3. **Java版本**: 确保使用Java 17
4. **Maven路径**: 脚本会自动检测Maven路径

### 检查构建状态

```bash
# 查看构建日志
tail -f back/logs/dianfeng-class-{环境}.log

# 检查服务状态
curl http://localhost:8082/api/health
```

---

💡 **提示**: 首次构建建议使用开发环境进行测试，确认无误后再构建其他环境。

📚 **详细文档**: 查看 `docs/deployment/环境配置与构建指南.md` 获取完整配置说明。
