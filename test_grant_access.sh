#!/bin/bash

# 测试授权功能修复
echo "🧪 测试授权功能修复..."

# 测试数据
USER_ID=51
COURSE_ID=5
TOKEN="ad93073f-e892-4177-a57d-2612d575c06f"
BASE_URL="http://localhost:8082"

echo "📋 测试参数:"
echo "  用户ID: $USER_ID"
echo "  课程ID: $COURSE_ID"
echo "  服务地址: $BASE_URL"
echo ""

# 测试1: 包含cascaderValue字段的请求（应该成功）
echo "🔍 测试1: 包含cascaderValue字段的请求"
curl -s -X POST "$BASE_URL/api/user-course-access/grant/purchase" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": '$USER_ID',
    "courseId": '$COURSE_ID',
    "chapterId": "",
    "lessonId": "",
    "cascaderValue": null,
    "accessType": 1,
    "acquireMethod": 1,
    "isBuyout": true,
    "pricePaid": 123,
    "originalPrice": 333,
    "paymentMethod": "alipay",
    "orderId": "test-'$(date +%s)'",
    "pointsUsed": null,
    "couponId": "",
    "adminId": "",
    "expireTime": null,
    "remark": "测试授权功能修复"
  }' | jq '.'

echo ""
echo "✅ 测试完成！"
echo ""
echo "📝 预期结果:"
echo "  - 不应该出现 'Unrecognized field cascaderValue' 错误"
echo "  - 应该返回成功响应或具体的业务错误信息"
