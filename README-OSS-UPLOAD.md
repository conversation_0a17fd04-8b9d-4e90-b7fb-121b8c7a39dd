# 🚀 OSS直传功能使用指南

## 📋 问题解决

### 原始问题
```
main.js:2 上传失败: Error: 文件上传失败: XHR error (req "error"), 
PUT https://dianfeng-class.demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com/image/2025/06/05/f6f16f87-b084-4eee-9814-9c6e121406f3.jpg -1 
(connected: false, keepalive socket: false)
```

### 问题原因
当前使用的是**模拟STS凭证**，无法进行真实的OSS上传。需要配置真实的阿里云RAM角色。

## 🎯 解决方案

### 方案1: 快速配置（推荐）

```bash
# 1. 运行自动配置脚本
./scripts/setup-sts.sh

# 2. 按照提示在阿里云控制台创建RAM角色

# 3. 输入真实的角色ARN

# 4. 重启后端服务

# 5. 测试配置
./scripts/test-sts.sh
```

### 方案2: 手动配置

1. **创建RAM角色**
   - 参考文档：`docs/setup-ram-role.md`
   - 角色名称：`OSSUploadRole`
   - 权限策略：OSS上传权限

2. **更新配置文件**
   ```yaml
   # back/src/main/resources/application.yaml
   aliyun:
     sts:
       role-arn: acs:ram::你的账号ID:role/OSSUploadRole
   ```

3. **重启服务**
   ```bash
   cd back
   mvn spring-boot:run
   ```

## 🧪 测试验证

### 检查当前状态
```bash
./scripts/test-sts.sh
```

### 预期结果

#### 配置前（模拟凭证）
```json
{
  "accessKeyId": "STS.MOCK1749119646151",
  "accessKeySecret": "MOCK_SECRET_1749119646151",
  "securityToken": "MOCK_TOKEN_1749119646151"
}
```

#### 配置后（真实凭证）
```json
{
  "accessKeyId": "STS.NUxxxxxxxx",
  "accessKeySecret": "xxxxxxxx", 
  "securityToken": "xxxxxxxx"
}
```

## 📁 项目结构

```
├── back/                           # 后端服务
│   ├── src/main/java/pox/com/dianfeng/
│   │   ├── controller/
│   │   │   └── StsController.java  # STS接口控制器
│   │   ├── service/
│   │   │   ├── StsService.java     # STS服务接口
│   │   │   └── impl/
│   │   │       └── StsServiceImpl.java # STS服务实现
│   │   ├── config/
│   │   │   └── AliyunStsConfig.java    # STS配置类
│   │   └── dto/
│   │       └── StsTokenResponse.java   # STS响应DTO
│   └── src/main/resources/
│       └── application.yaml        # 应用配置
├── front/admin_front/              # 前端应用
│   ├── src/
│   │   ├── api/
│   │   │   └── oss.js             # OSS直传API
│   │   ├── components/
│   │   │   ├── OssDirectUpload.vue # 通用上传组件
│   │   │   └── OssVideoUpload.vue  # 视频上传组件
│   │   └── views/course/
│   │       └── create.vue         # 课程创建页面
├── docs/                          # 文档
│   ├── oss-direct-upload-summary.md # 功能总结
│   └── setup-ram-role.md         # RAM角色配置指南
└── scripts/                      # 工具脚本
    ├── setup-sts.sh             # 配置脚本
    └── test-sts.sh              # 测试脚本
```

## 🔧 技术架构

### 上传流程
```
前端 → 获取STS凭证 → 直传OSS → 返回文件URL
  ↓         ↓           ↓         ↓
页面    后端STS服务   阿里云OSS   数据库
```

### 安全机制
- ✅ STS临时凭证（1小时有效期）
- ✅ 权限最小化（仅上传权限）
- ✅ 路径前缀限制
- ✅ 文件大小和类型验证

## 📊 支持的文件类型

| 类型 | 格式 | 大小限制 |
|------|------|----------|
| 图片 | jpg, jpeg, png, gif, webp, bmp | 10MB |
| 视频 | mp4, avi, mov, wmv, flv, mkv, webm | 500MB |
| 音频 | mp3, wav, aac, flac, ogg, m4a | 50MB |
| 文档 | pdf, doc, docx, xls, xlsx, ppt, pptx, txt | 20MB |

## 🐛 常见问题

### 1. 上传失败：模拟凭证错误
```
错误：当前使用模拟STS凭证，请配置真实的阿里云RAM角色
解决：运行 ./scripts/setup-sts.sh 配置真实角色
```

### 2. 权限被拒绝
```
错误：AccessDenied
解决：检查RAM角色是否有OSS上传权限
```

### 3. 角色ARN格式错误
```
错误：InvalidParameter.RoleArn
解决：确保ARN格式为 acs:ram::账号ID:role/角色名称
```

### 4. STS凭证过期
```
错误：SecurityTokenExpired
解决：重新获取STS凭证（自动处理）
```

## 🚀 快速开始

### 1. 启动后端服务
```bash
cd back
mvn spring-boot:run
```

### 2. 启动前端服务
```bash
cd front/admin_front
pnpm dev
```

### 3. 配置OSS直传
```bash
# 运行配置脚本
./scripts/setup-sts.sh

# 测试配置
./scripts/test-sts.sh
```

### 4. 测试上传功能
1. 访问课程创建页面
2. 上传课程封面或视频
3. 检查上传进度和结果

## 📚 相关文档

- [功能总结](docs/oss-direct-upload-summary.md) - 详细的技术实现说明
- [RAM角色配置](docs/setup-ram-role.md) - 阿里云控制台操作指南
- [阿里云OSS文档](https://help.aliyun.com/zh/oss/) - 官方文档

## 🎉 总结

通过实现OSS直传功能，我们成功解决了：
- ✅ 413 Payload Too Large 错误
- ✅ 大文件上传性能问题
- ✅ 服务器资源占用问题
- ✅ 用户上传体验问题

### 🔧 当前状态
- **后端服务**: ✅ 已实现STS服务，支持模拟和真实凭证自动切换
- **前端组件**: ✅ 已实现OSS直传组件，支持拖拽、进度显示、错误处理
- **配置工具**: ✅ 提供自动化配置脚本和测试工具
- **文档完整**: ✅ 详细的配置指南和故障排除文档

### 🚀 立即使用
当前系统已具备完整的直传能力，只需按照上述步骤配置真实的RAM角色即可投入使用！

```bash
# 一键配置
./scripts/setup-sts.sh

# 一键测试
./scripts/test-sts.sh
```

**注意**: 在配置真实RAM角色之前，系统会使用模拟凭证，前端会显示友好的配置提示。
