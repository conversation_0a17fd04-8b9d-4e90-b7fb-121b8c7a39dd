# code-inspector-plugin
# 这是一款基于 webpack/vite/rspack/nextjs/nuxt/umijs plugin 的提升开发效率的工具。
# 功能：
#   1. 点击页面上的 DOM，自动打开 IDE 并将光标定位到对应的源代码位置。
#   2. 在页面上按住组合键时，鼠标移动会在 DOM 上显示遮罩层和相关信息。
#   3. 点击遮罩层将自动打开 IDE 并定位到元素对应的代码位置。

# 指定 IDE 为 vscode
# 详细文档：https://inspector.fe-dev.cn/guide/ide.html

# 代码跳转设置
CODE_EDITOR=/Applications/Cursor.app/Contents/MacOS/Cursor
# CODE_EDITOR=/Users/<USER>/.codeium/windsurf/bin/windsurf

# 支持的 IDE 及其对应的设置值：
# Visual Studio Code (vscode)           code
# Visual Studio Code - Insiders         code_insiders
# WebStorm                              webstorm
# Atom                                  atom
# HBuilderX                             hbuilder
# PhpStorm                              phpstorm
# Pycharm                               pycharm
# IntelliJ IDEA                         idea