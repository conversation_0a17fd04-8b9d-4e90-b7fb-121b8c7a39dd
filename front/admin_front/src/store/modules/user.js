import { defineStore } from "pinia";
import { getUserInfo } from "@/api/user";

export const useUserStore = defineStore("user", {
  state: () => ({
    token: localStorage.getItem("token") || "",
    userInfo: null,
    permissions: [],
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.userInfo?.username || "",
    nickname: (state) => state.userInfo?.nickname || "",
    avatar: (state) => state.userInfo?.avatar || "",
  },

  actions: {
    // 设置Token
    setToken(token) {
      this.token = token;
      localStorage.setItem("token", token);
    },

    // 清除Token
    clearToken() {
      this.token = "";
      localStorage.removeItem("token");
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await getUserInfo();
        if (res.code === 200) {
          this.userInfo = res.data;
          this.permissions = res.data.permissions || [];
          return res.data;
        }
      } catch (error) {
        console.error("获取用户信息失败:", error);
        return null;
      }
    },

    // 登出
    logout() {
      this.clearToken();
      this.userInfo = null;
      this.permissions = [];
    },
  },

  persist: {
    key: "user-store",
    storage: localStorage,
    paths: ["token"],
  },
});
