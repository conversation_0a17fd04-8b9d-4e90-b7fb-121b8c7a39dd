import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import "./styles/index.scss";
import 'xgplayer/dist/index.min.css';

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

const app = createApp(App);
app.use(router);
app.use(pinia);

// 设置全局变量，供工具函数使用
app.config.globalProperties.$router = router;

// 挂载应用
app.mount("#app");

// 设置全局router变量，供token工具使用
window.$router = router;
