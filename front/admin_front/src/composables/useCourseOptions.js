import { ref, onMounted } from "vue";
import { useMessage } from "naive-ui";
import { getAllTeachers } from "@/api/teacher";
import { getCategoryList } from "@/api/course";
import { getAvailableTagConfigByCategory } from "@/api/tagConfig";

/**
 * 课程选项数据管理组合式函数
 * 用于管理讲师、分类、难度级别、年龄段等选项数据
 */
export function useCourseOptions() {
  const message = useMessage();

  // 选项数据
  const teacherOptions = ref([]);
  const categoryOptions = ref([]);
  const levelOptions = ref([]);
  const ageGroupOptions = ref([]);

  // 加载分类选项
  const loadCategoryOptions = async () => {
    try {
      // 从TagConfigs加载课程分类
      const res = await getAvailableTagConfigByCategory("course_category");
      if (res.code === 200) {
        categoryOptions.value = res.data.map((item) => ({
          label: item.label,
          value: item.id,
        }));
      }
    } catch (error) {
      console.error("加载分类失败:", error);
      // 如果TagConfigs加载失败，尝试从Categories表加载
      try {
        const fallbackRes = await getCategoryList({
          pageNum: 1,
          pageSize: 1000,
        });
        if (fallbackRes.code === 200) {
          categoryOptions.value = fallbackRes.data.records.map((item) => ({
            label: item.name,
            value: Number(item.id),
          }));
        }
      } catch (fallbackError) {
        console.error("加载分类失败（备用方案）:", fallbackError);
      }
    }
  };

  // 加载所有选项数据
  const loadOptions = async () => {
    try {
      // 获取讲师列表（获取所有数据用于下拉选项）
      const teachersRes = await getAllTeachers({ pageNum: 1, pageSize: 1000 });
      if (teachersRes.code === 200) {
        teacherOptions.value = teachersRes.data.records.map((item) => ({
          label: item.name,
          value: Number(item.id),
        }));
      }

      // 获取分类列表（从TagConfigs获取）
      await loadCategoryOptions();

      // 获取难度级别选项
      const levelRes = await getAvailableTagConfigByCategory("level");
      if (levelRes.code === 200) {
        levelOptions.value = levelRes.data.map((item) => ({
          label: item.label,
          value: Number(item.id),
        }));
      }

      // 获取年龄段选项
      const ageGroupRes = await getAvailableTagConfigByCategory("age_group");
      if (ageGroupRes.code === 200) {
        ageGroupOptions.value = ageGroupRes.data.map((item) => ({
          label: item.label,
          value: Number(item.id),
        }));
        // 添加自定义年龄段选项
        ageGroupOptions.value.push({ label: "添加更多...", value: "add_more" });
      }
    } catch (error) {
      console.error("加载选项数据失败:", error);
      message.error("加载选项数据失败");
    }
  };

  // 自动加载选项数据
  onMounted(() => {
    loadOptions();
  });

  return {
    teacherOptions,
    categoryOptions,
    levelOptions,
    ageGroupOptions,
    loadOptions,
    loadCategoryOptions,
  };
}
