/**
 * 课程表单验证规则组合式函数
 * 提供统一的表单验证规则
 */
export function useCourseFormRules() {
  const rules = {
    title: [
      { required: true, message: "请输入课程标题", trigger: "blur" },
      {
        min: 2,
        max: 100,
        message: "课程标题长度应在2-100字符之间",
        trigger: "blur",
      },
    ],
    subtitle: [
      { max: 200, message: "副标题长度不能超过200字符", trigger: "blur" },
    ],
    teacherId: [
      {
        required: true,
        message: "请选择讲师",
        trigger: "change",
        validator: (_rule, value) => {
          return value !== null && value !== undefined && value !== "";
        },
      },
    ],
    categoryId: [
      {
        required: true,
        message: "请选择分类",
        trigger: "change",
        validator: (_rule, value) => {
          return value !== null && value !== undefined && value !== "";
        },
      },
    ],
    price: [
      { required: true, message: "请输入课程价格", type: "number", trigger: "blur" },
      { type: "number", min: 0, message: "价格不能为负数", trigger: "blur" },
    ],
    originalPrice: [
      { type: "number", min: 0, message: "原价不能为负数", trigger: "blur" },
    ],
    level: [
      { required: true, message: "请选择难度级别", trigger: "change", type: "number" }
    ],
    ageGroup: [
      { required: true, message: "请选择适合年龄段", trigger: "change", type: "number" },
    ],
    description: [
      { required: true, message: "请输入课程描述", trigger: "blur" },
      {
        min: 10,
        max: 2000,
        message: "课程描述长度应在10-2000字符之间",
        trigger: "blur",
      },
    ],
    contactInfoPhone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    contactInfoWechat: [
      { max: 50, message: "微信号长度不能超过50字符", trigger: "blur" },
    ],
    contactInfoRemark: [
      { max: 500, message: "联系备注长度不能超过500字符", trigger: "blur" },
    ],
  };

  return {
    rules,
  };
}
