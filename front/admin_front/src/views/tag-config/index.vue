<template>
  <div class="page-container">
    <n-card title="标签配置管理" class="search-card">
      <n-form
        ref="searchFormRef"
        :model="searchForm"
        :inline="true"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item label="分类">
          <n-select
            v-model:value="searchForm.category"
            :options="categoryOptions"
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          />
        </n-form-item>
        <n-form-item label="标签名称">
          <n-input
            v-model:value="searchForm.label"
            placeholder="请输入标签名称"
            clearable
            style="width: 200px"
          />
        </n-form-item>
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleSearch">搜索</n-button>
            <n-button @click="handleReset">重置</n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>

    <n-card title="标签列表" class="table-card m-t-20">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="handleAdd">新增标签</n-button>
          <n-button
            type="error"
            :disabled="!selectedRowKeys.length"
            @click="handleBatchDelete"
          >
            批量删除
          </n-button>
        </n-space>
      </template>

      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        :pagination="paginationReactive"
        :row-key="(row) => row.id"
        @update:checked-row-keys="handleCheckedRowKeys"
        remote
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        flex-height
        style="height: 500px"
      />
    </n-card>

    <n-modal
      v-model:show="showModal"
      :title="modalTitle"
      preset="dialog"
      :show-icon="false"
      style="width: 500px"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="80px"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="分类" path="category">
          <n-select
            v-model:value="formData.category"
            :options="categoryOptions"
            placeholder="请选择分类"
          />
        </n-form-item>
        <n-form-item label="标签名称" path="label">
          <n-input
            v-model:value="formData.label"
            placeholder="请输入标签名称"
          />
        </n-form-item>
        <n-form-item label="标签值" path="value">
          <n-input
            v-model:value="formData.value"
            placeholder="请输入标签值"
          />
        </n-form-item>
        <n-form-item label="排序值" path="sortOrder">
          <n-input-number
            v-model:value="formData.sortOrder"
            :min="0"
            placeholder="数值越小越靠前"
          />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <n-switch
            v-model:value="formData.status"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from "vue";
import { useMessage, useDialog } from "naive-ui";
import {
  getTagConfigPage,
  createTagConfig,
  updateTagConfig,
  deleteTagConfig,
  batchDeleteTagConfig,
  checkValueExists,
} from "@/api/tagConfig";

const message = useMessage();
const dialog = useDialog();

// 分类选项
const categoryOptions = [
  { label: "难度级别", value: "level" },
  { label: "年龄段", value: "age_group" },
];

// 搜索表单
const searchFormRef = ref(null);
const searchForm = reactive({
  category: null,
  label: "",
  status: null,
});

// 表格相关
const tableRef = ref(null);
const tableData = ref([]);
const tableLoading = ref(false);
const selectedRowKeys = ref([]);

// 分页配置
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 项`,
});

// 表格列配置
const columns = [
  {
    type: "selection",
    width: 50,
  },
  {
    title: "ID",
    key: "id",
    width: 80,
  },
  {
    title: "分类",
    key: "category",
    width: 120,
    render(row) {
      const categoryMap = {
        level: "难度级别",
        age_group: "年龄段",
      };
      return categoryMap[row.category] || row.category;
    },
  },
  {
    title: "标签名称",
    key: "label",
    width: 150,
  },
  {
    title: "标签值",
    key: "value",
    width: 100,
  },
  {
    title: "排序值",
    key: "sortOrder",
    width: 100,
  },
  {
    title: "状态",
    key: "status",
    width: 100,
    render(row) {
      return h(
        "n-tag",
        {
          type: row.status ? "success" : "error",
          size: "small",
        },
        { default: () => (row.status ? "正常" : "禁用") }
      );
    },
  },
  {
    title: "系统内置",
    key: "isSystem",
    width: 100,
    render(row) {
      return h(
        "n-tag",
        {
          type: row.isSystem ? "warning" : "default",
          size: "small",
        },
        { default: () => (row.isSystem ? "是" : "否") }
      );
    },
  },
  {
    title: "创建时间",
    key: "createdAt",
    width: 180,
    render(row) {
      return row.createdAt
        ? new Date(row.createdAt).toLocaleString()
        : "-";
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 150,
    fixed: "right",
    render(row) {
      return h(
        "n-space",
        { size: "small" },
        {
          default: () => [
            h(
              "n-button",
              {
                size: "small",
                type: "primary",
                quaternary: true,
                onClick: () => handleEdit(row),
              },
              { default: () => "编辑" }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: "error",
                quaternary: true,
                disabled: row.isSystem,
                onClick: () => handleDelete(row),
              },
              { default: () => "删除" }
            ),
          ],
        }
      );
    },
  },
];

// 弹窗相关
const showModal = ref(false);
const modalTitle = ref("");
const isEdit = ref(false);
const formRef = ref(null);
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  id: null,
  category: null,
  label: "",
  value: "",
  sortOrder: 1,
  status: true,
  remark: "",
});

// 表单验证规则
const formRules = {
  category: [{ required: true, message: "请选择分类", trigger: "change" }],
  label: [{ required: true, message: "请输入标签名称", trigger: "blur" }],
  value: [{ required: true, message: "请输入标签值", trigger: "blur" }],
  sortOrder: [{ required: true, message: "请输入排序值", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 加载表格数据
const loadTableData = async () => {
  try {
    tableLoading.value = true;
    const params = {
      pageNum: paginationReactive.page,
      pageSize: paginationReactive.pageSize,
      ...searchForm,
    };

    const res = await getTagConfigPage(params);
    if (res.code === 200) {
      tableData.value = res.data.records;
      paginationReactive.itemCount = res.data.total;
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    message.error("加载数据失败");
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  paginationReactive.page = 1;
  loadTableData();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    category: null,
    label: "",
    status: null,
  });
  handleSearch();
};

// 分页变化
const handlePageChange = (page) => {
  paginationReactive.page = page;
  loadTableData();
};

const handlePageSizeChange = (pageSize) => {
  paginationReactive.pageSize = pageSize;
  paginationReactive.page = 1;
  loadTableData();
};

// 选择行
const handleCheckedRowKeys = (keys) => {
  selectedRowKeys.value = keys;
};

// 新增
const handleAdd = () => {
  showModal.value = true;
  modalTitle.value = "新增标签";
  isEdit.value = false;
  resetFormData();
};

// 编辑
const handleEdit = (row) => {
  showModal.value = true;
  modalTitle.value = "编辑标签";
  isEdit.value = true;
  Object.assign(formData, { ...row });
};

// 删除
const handleDelete = (row) => {
  if (row.isSystem) {
    message.error("系统内置标签不允许删除");
    return;
  }

  dialog.warning({
    title: "确认删除",
    content: `确定要删除标签 "${row.label}" 吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await deleteTagConfig(row.id);
        if (res.code === 200) {
          message.success("删除成功");
          loadTableData();
        } else {
          message.error(res.msg || "删除失败");
        }
      } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败");
      }
    },
  });
};

// 批量删除
const handleBatchDelete = () => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个标签吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await batchDeleteTagConfig(selectedRowKeys.value);
        if (res.code === 200) {
          message.success("批量删除成功");
          selectedRowKeys.value = [];
          loadTableData();
        } else {
          message.error(res.msg || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除失败:", error);
        message.error("批量删除失败");
      }
    },
  });
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;

    const apiCall = isEdit.value ? updateTagConfig : createTagConfig;
    const res = await apiCall(formData);

    if (res.code === 200) {
      message.success(isEdit.value ? "编辑成功" : "新增成功");
      showModal.value = false;
      loadTableData();
    } else {
      message.error(res.msg || (isEdit.value ? "编辑失败" : "新增失败"));
    }
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: null,
    category: null,
    label: "",
    value: "",
    sortOrder: 1,
    status: true,
    remark: "",
  });
  formRef.value?.restoreValidation();
};

// 生命周期
onMounted(() => {
  loadTableData();
});
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 20px;
}

.m-t-20 {
  margin-top: 20px;
}
</style> 