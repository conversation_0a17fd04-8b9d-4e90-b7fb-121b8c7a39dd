<template>
  <div class="page-container">
    <n-card title="用户管理" class="content-card">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <n-form inline :label-width="80">
          <n-form-item label="用户名">
            <n-input
              v-model:value="searchParams.username"
              clearable
              placeholder="请输入用户名"
            />
          </n-form-item>
          <n-form-item label="手机号">
            <n-input
              v-model:value="searchParams.phone"
              clearable
              placeholder="请输入手机号"
            />
          </n-form-item>
          <n-form-item label="学校">
            <n-input
              v-model:value="searchParams.school"
              clearable
              placeholder="请输入学校"
            />
          </n-form-item>
          <n-form-item label="所在地">
            <n-input
              v-model:value="searchParams.location"
              clearable
              placeholder="请输入所在地"
            />
          </n-form-item>
          <!-- <n-form-item label="状态">
            <n-select
              v-model:value="searchParams.status"
              :options="statusOptions"
              clearable
              placeholder="请选择状态"
              style="width: 150px"
            />
          </n-form-item> -->
          <n-form-item>
            <n-button type="primary" @click="loadData">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button class="m-l-10" @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-form-item>
        </n-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加用户
        </n-button>
        <n-button
          class="m-l-10"
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        remote
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:sorter="handleSorterChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />
    </n-card>
  </div>
</template>

<script setup>
  import { h, ref, reactive, computed, onMounted } from "vue";
  import {
    SearchOutline,
    RefreshOutline,
    AddOutline,
    TrashOutline,
    PencilOutline,
    PersonOutline,
    CloseCircleOutline,
    CheckmarkCircleOutline,
    MaleOutline,
    FemaleOutline,
    HelpCircleOutline,
  } from "@vicons/ionicons5";
  import {
    getUserList,
    getUserDetail,
    createUser,
    updateUser,
    deleteUser,
    batchDeleteUsers,
    enableUser,
    disableUser,
  } from "@/api/user";
  import dayjs from "dayjs";
  import UserDetailCard from "./UserDetailCard.vue";

  const message = useMessage();
  const dialog = useDialog();
  const dialogInstRef = ref(null); // 添加对话框实例引用

  // 表格数据
  const loading = ref(false);
  const tableData = ref([]);
  const selectedRowKeys = ref([]);

  // 搜索参数
  const searchParams = reactive({
    username: "",
    phone: "",
    school: "",
    location: "",
    status: null,
  });

  // 分页
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [10, 20, 30, 50],
    sortField: null,
    sortOrder: null,
    // 移除重复的事件处理器，避免双重请求
    // onChange 和 onUpdatePageSize 已通过表格组件的事件监听器处理
  });

  // 状态选项
  const statusOptions = [
    { label: "启用", value: true },
    { label: "禁用", value: false },
  ];

  // 年级选项
  const gradeOptions = [
    { label: "一年级", value: 1 },
    { label: "二年级", value: 2 },
    { label: "三年级", value: 3 },
    { label: "四年级", value: 4 },
    { label: "五年级", value: 5 },
    { label: "六年级", value: 6 },
    { label: "初一", value: 7 },
    { label: "初二", value: 8 },
    { label: "初三", value: 9 },
    { label: "高一", value: 10 },
    { label: "高二", value: 11 },
    { label: "高三", value: 12 },
    { label: "大学", value: 13 },
    { label: "其他", value: 0 },
  ];

  // 保存加载状态
  const submitLoading = ref(false);

  // 表格列
  const createColumns = () => {
    return [
      {
        type: "selection",
        align: "center",
      },
      {
        title: "ID",
        key: "id",
        sorter: false,
        width: 80,
        align: "center",
      },
      {
        title: "用户名",
        key: "username",
        width: 140,
        align: "center",
      },
      {
        title: "昵称",
        key: "nickname",
        width: 140,
        align: "center",
      },
      {
        title: "性别",
        key: "gender",
        width: 100,
        align: "center",
        render(row) {
          const genderMap = {
            0: {
              text: "未知",
              type: "default",
              color: "#909399",
              icon: "HelpCircleOutline",
            },
            1: {
              text: "男",
              type: "info",
              color: "#4080ff",
              icon: "MaleOutline",
            },
            2: {
              text: "女",
              type: "success",
              color: "#ff6a9c",
              icon: "FemaleOutline",
            },
          };

          const info = genderMap[row.gender] || genderMap[0];
          const iconComponent =
            info.icon === "MaleOutline"
              ? MaleOutline
              : info.icon === "FemaleOutline"
              ? FemaleOutline
              : HelpCircleOutline;

          return h(
            "n-tag",
            {
              type: info.type,
              size: "medium",
              style: `background-color: ${info.color}; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;`,
            },
            {
              default: () => info.text,
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 16,
                    style: "margin-right: 4px;",
                  },
                  {
                    default: () => h(iconComponent),
                  }
                ),
            }
          );
        },
      },
      {
        title: "年龄",
        key: "age",
        width: 100,
        align: "center",
        render(row) {
          return h(
            "n-tag",
            {
              type: "warning",
              size: "small",
              round: true,
              style: `background-color: #f8f8f8; color: #ff9900; border: 1px solid #ffe6b3; padding: 0 10px; font-weight: 500;`,
            },
            { default: () => (row.age || "-") + "岁" }
          );
        },
      },
      {
        title: "年级",
        key: "grade",
        width: 120,
        align: "center",
        sorter: {
          multiple: false,
          compare: () => {
            // 这个比较函数不会被使用，因为我们在前端处理排序
            return 0;
          },
        },
        sortOrder: computed(() => {
          if (pagination.sortField === "grade") {
            return pagination.sortOrder;
          }
          return false;
        }),
        render(row) {
          const gradeColors = {
            1: "#4caf50", // 一年级 - 绿色
            2: "#8bc34a", // 二年级 - 浅绿
            3: "#cddc39", // 三年级 - 黄绿
            4: "#ffeb3b", // 四年级 - 黄色
            5: "#ffc107", // 五年级 - 琥珀色
            6: "#ff9800", // 六年级 - 橙色
            7: "#03a9f4", // 初一 - 浅蓝
            8: "#2196f3", // 初二 - 蓝色
            9: "#3f51b5", // 初三 - 靛蓝
            10: "#9c27b0", // 高一 - 紫色
            11: "#e91e63", // 高二 - 粉红
            12: "#f44336", // 高三 - 红色
            13: "#607d8b", // 大学 - 蓝灰
            0: "#9e9e9e", // 其他 - 灰色
          };

          const gradeName = getGradeName(row.grade);
          const color = gradeColors[row.grade] || "#9e9e9e";

          return h(
            "n-tag",
            {
              type: "success",
              size: "small",
              style: `background-color: ${color}; color: white; border: none; padding: 2px 10px; border-radius: 12px; font-weight: 500;`,
            },
            { default: () => gradeName }
          );
        },
      },
      {
        title: "学校",
        key: "school",
        width: 160,
        align: "center",
      },

      {
        title: "状态",
        key: "status",
        width: 100,
        align: "center",
        render(row) {
          return h(
            "n-tag",
            {
              type: row.status ? "success" : "error",
              size: "medium",
              style: row.status
                ? "background-color: #18a058; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;"
                : "background-color: #d03050; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;",
            },
            {
              default: () => (row.status ? "启用" : "禁用"),
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 16,
                    style: "margin-right: 4px;",
                  },
                  {
                    default: () =>
                      h(
                        row.status ? CheckmarkCircleOutline : CloseCircleOutline
                      ),
                  }
                ),
            }
          );
        },
      },
      {
        title: "操作",
        key: "actions",
        width: 250,
        fixed: "right",
        align: "center",
        render(row) {
          return h(
            "div",
            { style: "display: flex; justify-content: center; gap: 10px;" },
            [
              h(
                "n-button",
                {
                  size: "small",
                  type: "primary",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
                  onClick: () => handleView(row),
                },
                {
                  default: () => "查看",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(PersonOutline),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: "info",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
                  onClick: () => handleEdit(row),
                },
                {
                  default: () => "编辑",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(PencilOutline),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: row.status ? "error" : "success",
                  style: row.status
                    ? "padding: 0 12px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;"
                    : "padding: 0 12px; border-radius: 15px; background-color: #52c41a; border: none; color: white;",
                  onClick: () => handleToggleStatus(row),
                },
                {
                  default: () => (row.status ? "禁用" : "启用"),
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () =>
                          h(
                            row.status
                              ? CloseCircleOutline
                              : CheckmarkCircleOutline
                          ),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: "error",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
                  onClick: () => handleDelete(row),
                },
                {
                  default: () => "删除",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(TrashOutline),
                      }
                    ),
                }
              ),
            ]
          );
        },
      },
    ];
  };

  const columns = createColumns();

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      const params = {
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
        ...searchParams,
      };

      // 添加排序参数
      if (pagination.sortField && pagination.sortOrder) {
        params.orderField = pagination.sortField;
        params.orderType = pagination.sortOrder === "ascend" ? "asc" : "desc";
      }

      // 调用API
      const res = await getUserList(params);

      // 更新数据
      let records = res.data.records;

      // 如果是年级排序，需要前端处理排序逻辑
      if (pagination.sortField === "grade" && pagination.sortOrder) {
        records = sortByGrade(records, pagination.sortOrder);
      }

      tableData.value = records;
      pagination.pageCount = res.data.pages;
      pagination.page = res.data.current;
      pagination.pageSize = res.data.size;
      pagination.itemCount = res.data.total;
    } catch (error) {
      console.error("加载用户数据失败:", error);
      message.error("加载用户数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 年级排序函数
  const sortByGrade = (records, sortOrder) => {
    // 定义年级排序顺序：1-12(小学到高中), 13(大学), 0(其他)
    const gradeOrder = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0];

    return records.sort((a, b) => {
      const aIndex = gradeOrder.indexOf(a.grade);
      const bIndex = gradeOrder.indexOf(b.grade);

      // 如果年级不在预定义列表中，放到最后
      const aOrder = aIndex === -1 ? gradeOrder.length : aIndex;
      const bOrder = bIndex === -1 ? gradeOrder.length : bIndex;

      if (sortOrder === "ascend") {
        return aOrder - bOrder;
      } else {
        return bOrder - aOrder;
      }
    });
  };

  // 重置搜索
  const resetSearch = () => {
    Object.keys(searchParams).forEach((key) => {
      searchParams[key] = "";
    });
    searchParams.status = null;
    pagination.page = 1;
    pagination.sortField = null;
    pagination.sortOrder = null;
    loadData();
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page;
    loadData();
  };

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadData();
  };

  // 处理排序变化
  const handleSorterChange = (sorter) => {
    if (sorter && sorter.columnKey === "grade") {
      // 年级排序的三状态循环：无排序 → 升序 → 降序 → 无排序
      const currentOrder = pagination.sortOrder;

      if (!currentOrder || currentOrder === null) {
        // 当前无排序，切换到升序
        pagination.sortField = "grade";
        pagination.sortOrder = "ascend";
      } else if (currentOrder === "ascend") {
        // 当前升序，切换到降序
        pagination.sortField = "grade";
        pagination.sortOrder = "descend";
      } else {
        // 当前降序，切换到无排序
        pagination.sortField = null;
        pagination.sortOrder = null;
      }
    } else if (sorter) {
      // 其他列的正常排序
      pagination.sortField = sorter.columnKey;
      pagination.sortOrder = sorter.order;
    } else {
      pagination.sortField = null;
      pagination.sortOrder = null;
    }
    loadData();
  };

  // 处理选中行变化
  const handleCheckedRowKeysChange = (keys) => {
    selectedRowKeys.value = keys;
  };

  // 处理添加 - 修改为使用UserDetailCard组件，参考讲师添加功能
  const handleAdd = () => {
    const newUser = {
      id: null,
      username: "",
      nickname: "",
      avatar: "",
      phone: "",
      email: "",
      gender: 0,
      age: null,
      bio: "",
      grade: 0,
      school: "",
      location: "",
      status: true,
      createdAt: null,
      lastLoginTime: null,
    };

    // 打开编辑对话框
    dialogInstRef.value = dialog.info({
      title: "添加用户",
      content: () => {
        return h(UserDetailCard, {
          userData: newUser,
          gradeOptions,
          initialEditMode: true,
          onSave: (userData) => {
            handleSaveUser(userData);
          },
          onClose: () => {
            dialogInstRef.value?.destroy();
          },
        });
      },
      closable: true,
      maskClosable: false,
      positiveText: null,
      negativeText: null,
      style: { width: "700px", maxWidth: "90vw" },
    });
  };

  // 处理编辑
  const handleEdit = async (row) => {
    try {
      const res = await getUserDetail(row.id);
      if (res.code === 200) {
        // 使用UserDetailCard组件打开编辑模式
        handleOpenUserDetailCardInEditMode(res.data);
      } else {
        message.error("获取用户详情失败");
      }
    } catch (error) {
      message.error("获取用户详情失败");
    }
  };

  // 处理查看详情
  const handleView = (row) => {
    // 确保去除URL中可能存在的空格
    // const userData = { ...row };

    // 如果头像URL为空，使用默认头像
    if (!row.avatar) {
      row.avatar =
        "https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg";
    }

    // 保存对话框实例以便后续关闭
    dialogInstRef.value = dialog.info({
      title: "用户详情",
      content: () => {
        return h(UserDetailCard, {
          userData: row,
          gradeOptions,
          onEdit: () => handleEdit(row),
          onToggleStatus: () => handleToggleStatus(row),
          onDelete: () => handleDelete(row),
          onSave: (updatedData) => {
            // 处理保存逻辑
            handleSaveUser(row);
          },
          onClose: () => {
            dialogInstRef.value?.destroy();
          },
        });
      },
      closable: true,
      maskClosable: true,
      positiveText: null,
      negativeText: null,
      style: { width: "700px", maxWidth: "90vw" },
    });
  };

  // 打开UserDetailCard并直接进入编辑模式
  const handleOpenUserDetailCardInEditMode = (userData) => {
    // 确保去除URL中可能存在的空格
    if (!userData.avatar) {
      userData.avatar =
        "https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg";
    }

    // 保存对话框实例以便后续关闭
    dialogInstRef.value = dialog.info({
      title: "编辑用户",
      content: () => {
        return h(UserDetailCard, {
          userData,
          gradeOptions,
          initialEditMode: true, // 直接进入编辑模式
          onEdit: () => {}, // 编辑模式下不需要这个事件
          onToggleStatus: () => handleToggleStatus(userData),
          onDelete: () => handleDelete(userData),
          onSave: (updatedData) => {
            handleSaveUser(updatedData);
          },
          onClose: () => {
            dialogInstRef.value?.destroy();
          },
        });
      },
      closable: true,
      maskClosable: true,
      positiveText: null,
      negativeText: null,
      style: { width: "700px", maxWidth: "90vw" },
    });
  };

  // 处理删除
  const handleDelete = (row) => {
    dialog.warning({
      title: "确认删除",
      content: `确定要删除用户 "${row.username}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const res = await deleteUser(row.id);
          if (res.code === 200) {
            message.success("删除成功");
            loadData();
          } else {
            message.error(res.message || "删除失败");
          }
        } catch (error) {
          message.error("删除失败");
        }
      },
    });
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning("请至少选择一条记录");
      return;
    }

    dialog.warning({
      title: "确认批量删除",
      content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const res = await batchDeleteUsers(selectedRowKeys.value);
          if (res.code === 200) {
            message.success("批量删除成功");
            loadData();
            selectedRowKeys.value = [];
          } else {
            message.error(res.message || "批量删除失败");
          }
        } catch (error) {
          message.error("批量删除失败");
        }
      },
    });
  };

  // 处理状态切换
  const handleToggleStatus = (row) => {
    const action = row.status ? "禁用" : "启用";
    dialog.warning({
      title: `确认${action}`,
      content: `确定要${action}用户 "${row.username}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const res = row.status
            ? await disableUser(row.id)
            : await enableUser(row.id);

          if (res.code === 200) {
            message.success(`${action}成功`);
            // 更新本地数据状态
            row.status = !row.status;
            // 刷新数据以确保同步
            loadData();
            // 如果弹窗打开，关闭弹窗重新打开以更新状态
            if (dialogInstRef.value) {
              dialogInstRef.value.destroy();
            }
          } else {
            message.error(res.message || `${action}失败`);
          }
        } catch (error) {
          message.error(`${action}失败`);
        }
      },
    });
  };

  // 处理保存用户
  const handleSaveUser = async (userData) => {
    submitLoading.value = true;
    try {
      const saveData = { ...userData };

      // 判断是新增还是编辑
      const isCreating = !saveData.id;

      let res;
      if (isCreating) {
        // 新增用户时设置默认密码
        if (!saveData.password) {
          saveData.password = "123456"; // 默认密码
        }
        res = await createUser(saveData);
      } else {
        // 编辑用户时，如果没有修改密码，则不提交密码字段
        if (saveData.password === "") {
          delete saveData.password;
        }
        res = await updateUser(saveData);
      }

      if (res.code === 200) {
        message.success(isCreating ? "创建成功" : "更新成功");
        loadData(); // 刷新数据
        // 关闭弹窗
        dialogInstRef.value?.destroy();
      } else {
        message.error(res.message || (isCreating ? "创建失败" : "更新失败"));
      }
    } catch (error) {
      console.error("保存用户失败:", error);
      message.error(isCreating ? "创建失败" : "更新失败");
    } finally {
      submitLoading.value = false;
    }
  };

  // 获取年级名称
  const getGradeName = (grade) => {
    const gradeItem = gradeOptions.find((item) => item.value === grade);
    return gradeItem ? gradeItem.label : "未知";
  };

  onMounted(() => {
    loadData();
  });
</script>

<style lang="scss" scoped>
  .content-card {
    margin-bottom: 20px;
  }

  .search-bar {
    margin-bottom: 0px;
  }

  .action-bar {
    margin-bottom: 20px;
  }
</style>
