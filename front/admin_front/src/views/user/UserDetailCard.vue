<template>
  <div class="user-detail-card">
    <!-- 顶部信息区 -->
    <div class="user-header">
      <div class="user-avatar-container" @click="handleAvatarClick">
        <n-avatar
          :src="formData.avatarFullUrl || defaultAvatar"
          :fallback-src="defaultAvatar"
          round
          :size="80"
          class="user-avatar"
        />
        <div class="upload-overlay">
          <n-icon size="22"><CloudUploadOutline /></n-icon>
        </div>
        <div
          class="user-status-badge"
          :class="{ active: localUserData.status }"
          v-if="localUserData.id"
        >
          {{ localUserData.status ? "已启用" : "已禁用" }}
        </div>
      </div>

      <div class="user-basic-info" v-if="localUserData.id">
        <h2>{{ localUserData.nickname || localUserData.username }}</h2>
        <div class="user-id">ID: {{ localUserData.id }}</div>
        <div class="user-tags">
          <n-tag
            size="small"
            :color="getGenderColor(localUserData.gender)"
            class="info-tag"
          >
            <template #icon>
              <n-icon
                ><component :is="getGenderIcon(localUserData.gender)"
              /></n-icon>
            </template>
            {{ getGenderText(localUserData.gender) }}
          </n-tag>
          <n-tag
            v-if="localUserData.grade !== undefined && localUserData.grade !== null"
            size="small"
            :color="getGradeColor(localUserData.grade)"
            class="info-tag"
          >
            {{ getGradeText(localUserData.grade) }}
          </n-tag>
          <n-tag
            v-if="localUserData.age"
            size="small"
            color="#ff9900"
            class="info-tag"
          >
            {{ localUserData.age }}岁
          </n-tag>
        </div>
      </div>
      <div class="user-basic-info" v-else>新增用户-上传头像-填写信息</div>
    </div>

    <!-- 内容区 -->
    <n-divider />

    <!-- 查看模式 -->
    <div v-if="!editMode" class="user-detail-content">
      <!-- 第一行：账户与联系方式 -->
      <div class="info-section">
        <h3 class="section-title">账户与联系方式</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><PersonOutline /></n-icon
              >用户名
            </div>
            <div class="info-value">{{ localUserData.username }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><CallOutline /></n-icon>手机号
            </div>
            <div class="info-value">{{ localUserData.phone || "-" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><MailOutline /></n-icon>邮箱
            </div>
            <div class="info-value">{{ localUserData.email || "-" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><TimeOutline /></n-icon
              >注册时间
            </div>
            <div class="info-value">{{ formatDate(localUserData.createdAt) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><LogInOutline /></n-icon
              >最后登录
            </div>
            <div class="info-value">
              {{
                localUserData.lastLoginTime
                  ? formatDate(localUserData.lastLoginTime)
                  : "-"
              }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"
                ><CheckmarkCircleOutline /></n-icon
              >状态
            </div>
            <div class="info-value">
              <n-tag :type="localUserData.status ? 'success' : 'error'" size="small">
                {{ localUserData.status ? "启用" : "禁用" }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行：学校与地理信息 -->
      <div class="info-section">
        <h3 class="section-title">学校与地理信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><SchoolOutline /></n-icon>学校
            </div>
            <div class="info-value highlight-text">
              {{ localUserData.school || "-" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><BookOutline /></n-icon>年级
            </div>
            <div class="info-value">
              {{ getGradeText(localUserData.grade) || "-" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><LocationOutline /></n-icon
              >所在地
            </div>
            <div class="info-value highlight-text">
              {{ localUserData.location || "-" }}
            </div>
          </div>
        </div>
      </div>

      <!-- 第三行：个人简介 -->
      <div class="info-section bio-section">
        <h3 class="section-title">个人简介</h3>
        <div class="bio-content">
          {{ localUserData.bio || "暂无个人简介" }}
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="user-detail-content edit-mode">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <!-- 第一行：账户与联系方式 -->
        <div class="info-section">
          <h3 class="section-title">账户与联系方式</h3>
          <div class="form-grid">
            <n-form-item label="用户名" path="username">
              <n-input
                v-model:value="formData.username"
                placeholder="请输入用户名"
              />
            </n-form-item>
            <n-form-item label="手机号" path="phone">
              <n-input
                v-model:value="formData.phone"
                placeholder="请输入手机号"
              />
            </n-form-item>
            <n-form-item label="邮箱" path="email">
              <n-input
                v-model:value="formData.email"
                placeholder="请输入邮箱"
              />
            </n-form-item>
            <n-form-item label="昵称" path="nickname">
              <n-input
                v-model:value="formData.nickname"
                placeholder="请输入昵称"
              />
            </n-form-item>
            <!-- 仅在新增用户时显示密码字段 -->
            <n-form-item v-if="!formData.id" label="密码" path="password">
              <n-input
                v-model:value="formData.password"
                type="password"
                show-password-on="click"
                placeholder="请输入密码（默认：123456）"
              />
            </n-form-item>
            <n-form-item label="性别" path="gender">
              <n-radio-group v-model:value="formData.gender">
                <n-space>
                  <n-radio :value="1">男</n-radio>
                  <n-radio :value="2">女</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="年龄" path="age">
              <n-input-number
                v-model:value="formData.age"
                :min="1"
                :max="120"
              />
            </n-form-item>
            <n-form-item label="状态" path="status">
              <n-switch v-model:value="formData.status">
                <template #checked>启用</template>
                <template #unchecked>禁用</template>
              </n-switch>
            </n-form-item>
          </div>
        </div>

        <!-- 第二行：学校与地理信息 -->
        <div class="info-section">
          <h3 class="section-title">学校与地理信息</h3>
          <div class="form-grid">
            <n-form-item label="学校" path="school">
              <n-input
                v-model:value="formData.school"
                placeholder="请输入学校"
              />
            </n-form-item>
            <n-form-item label="年级" path="grade">
              <n-select
                v-model:value="formData.grade"
                :options="gradeOptions"
                placeholder="请选择年级"
              />
            </n-form-item>
            <n-form-item label="所在地" path="location">
              <n-input
                v-model:value="formData.location"
                placeholder="请输入所在地"
              />
            </n-form-item>
          </div>
        </div>

        <!-- 第三行：个人简介 -->
        <div class="info-section">
          <h3 class="section-title">个人简介</h3>
          <n-form-item path="bio">
            <n-input
              v-model:value="formData.bio"
              type="textarea"
              placeholder="请输入个人简介"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </n-form-item>
        </div>
      </n-form>
    </div>

    <!-- 底部操作区 -->
    <div class="action-footer">
      <n-space>
        <template v-if="editMode">
          <n-button type="primary" @click="handleSave" :loading="saveLoading">
            <template #icon
              ><n-icon><CheckmarkOutline /></n-icon
            ></template>
            保存
          </n-button>
          <n-button @click="cancelEdit">
            <template #icon
              ><n-icon><CloseOutline /></n-icon
            ></template>
            取消
          </n-button>
        </template>
        <template v-else>
          <n-button
            type="info"
            @click="handleToggleStatus"
            :disabled="saveLoading"
          >
            <template #icon
              ><n-icon><RefreshCircleOutline /></n-icon
            ></template>
            {{ localUserData.status ? "禁用" : "启用" }}
          </n-button>
          <n-button type="error" @click="handleDelete" :disabled="saveLoading">
            <template #icon
              ><n-icon><TrashOutline /></n-icon
            ></template>
            删除
          </n-button>
          <n-button @click="close">
            <template #icon
              ><n-icon><CloseOutline /></n-icon
            ></template>
            关闭
          </n-button>
        </template>
      </n-space>
    </div>

    <!-- 头像上传对话框 -->
    <n-modal
      v-model:show="showAvatarUploadModal"
      preset="card"
      title="修改头像"
      style="width: 300px"
    >
      <OssDirectUpload
        v-model="tempAvatar"
        category="image"
        :show-preview="true"
        @upload-success="handleAvatarUploadSuccess"
        @upload-error="handleAvatarUploadError"
      />

      <template #footer>
        <div style="text-align: right">
          <n-button
            style="margin-right: 8px"
            @click="showAvatarUploadModal = false"
            >取消</n-button
          >
          <n-button
            type="primary"
            :disabled="!tempAvatar"
            @click="confirmAvatarUpload"
            >确认使用</n-button
          >
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
  import {
    PersonOutline,
    CallOutline,
    MailOutline,
    TimeOutline,
    LogInOutline,
    SchoolOutline,
    BookOutline,
    LocationOutline,
    PencilOutline,
    TrashOutline,
    CheckmarkCircleOutline,
    CloseCircleOutline,
    MaleOutline,
    FemaleOutline,
    HelpCircleOutline,
    CheckmarkOutline,
    CloseOutline,
    CloudUploadOutline,
    RefreshCircleOutline,
  } from "@vicons/ionicons5";
  import { computed, ref, reactive, onMounted, nextTick, watch, toRef } from "vue";
  import { useMessage } from "naive-ui";
  import dayjs from "dayjs";
  import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";

  const props = defineProps({
    userData: {
      type: Object,
      required: true,
    },
    gradeOptions: {
      type: Array,
      default: () => [],
    },
    initialEditMode: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits([
    "edit",
    "toggle-status",
    "delete",
    "close",
    "save",
  ]);

  const defaultAvatar =
    "https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg";

  // 表单相关
  const editMode = ref(false);
  const formRef = ref(null);
  const saveLoading = ref(false);

  // 头像上传相关
  const showAvatarUploadModal = ref(false);
  const tempAvatar = ref("");
  const message = useMessage();

  // 创建本地响应式用户数据副本
  const localUserData = reactive({ ...props.userData });

  // 表单数据
  const formData = reactive({
    id: null,
    username: "",
    nickname: "",
    avatar: "",
    phone: "",
    email: "",
    password: "", // 添加密码字段
    gender: 0,
    age: null,
    bio: "",
    grade: 0,
    school: "",
    location: "",
    status: true,
    avatarFullUrl: "", // 添加头像完整URL字段
  });

  // 表单验证规则
  const rules = {
    username: [
      { required: true, message: "请输入用户名", trigger: "blur" },
      {
        min: 3,
        max: 20,
        message: "用户名长度必须在3-20个字符之间",
        trigger: "blur",
      },
    ],
    password: [
      {
        required: computed(() => !formData.id), // 只有新增时密码才是必填的
        message: "请输入密码",
        trigger: "blur",
      },
      {
        min: 6,
        message: "密码长度不能小于6个字符",
        trigger: "blur",
      },
    ],
    phone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    email: [
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
    ],
  };

  // 开始编辑
  const startEdit = () => {
    // 复制用户数据到表单
    Object.keys(formData).forEach((key) => {
      if (key in props.userData) {
        formData[key] = props.userData[key];
      }
    });

    // 使用nextTick确保DOM更新完成后再切换编辑模式
    nextTick(() => {
      editMode.value = true;
    });
  };

  // 取消编辑
  const cancelEdit = () => {
    editMode.value = false;
  };

  // 关闭弹窗
  const close = () => {
    emit("close");
  };

  // 切换启用/禁用状态
  const handleToggleStatus = () => {
    emit("toggle-status", localUserData);
  };

  // 删除用户
  const handleDelete = () => {
    emit("delete", localUserData);
  };

  // 保存编辑
  const handleSave = () => {
    formRef.value?.validate((errors) => {
      if (!errors) {
        saveLoading.value = true;
        // 触发保存事件
        emit("save", { ...formData });
        // 实际应用中，这里会有异步保存操作，完成后再关闭编辑模式
        // 这里简化处理
        setTimeout(() => {
          saveLoading.value = false;
          editMode.value = false;
        }, 500);
      } else {
        message.error("表单验证失败，请检查必填项");
      }
    });
  };

  // 日期格式化
  const formatDate = (date) => {
    return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
  };

  // 获取性别文本
  const getGenderText = (gender) => {
    const genderMap = {
      1: "男",
      2: "女",
    };
    return genderMap[gender] || "未知";
  };

  // 获取性别图标
  const getGenderIcon = (gender) => {
    if (gender === 1) return MaleOutline;
    if (gender === 2) return FemaleOutline;
    return HelpCircleOutline;
  };

  // 获取性别颜色
  const getGenderColor = (gender) => {
    const colors = {
      0: { color: "#909399", textColor: "#ffffff" },
      1: { color: "#4080ff", textColor: "#ffffff" },
      2: { color: "#ff6a9c", textColor: "#ffffff" },
    };
    return colors[gender] || colors[0];
  };

  // 获取年级文本
  const getGradeText = (grade) => {
    const gradeItem = props.gradeOptions.find((item) => item.value === grade);
    return gradeItem ? gradeItem.label : "未知年级";
  };

  // 获取年级颜色
  const getGradeColor = (grade) => {
    const gradeColors = {
      1: { color: "#4caf50", textColor: "#ffffff" }, // 一年级 - 绿色
      2: { color: "#8bc34a", textColor: "#ffffff" }, // 二年级 - 浅绿
      3: { color: "#cddc39", textColor: "#333333" }, // 三年级 - 黄绿
      4: { color: "#ffeb3b", textColor: "#333333" }, // 四年级 - 黄色
      5: { color: "#ffc107", textColor: "#333333" }, // 五年级 - 琥珀色
      6: { color: "#ff9800", textColor: "#ffffff" }, // 六年级 - 橙色
      7: { color: "#03a9f4", textColor: "#ffffff" }, // 初一 - 浅蓝
      8: { color: "#2196f3", textColor: "#ffffff" }, // 初二 - 蓝色
      9: { color: "#3f51b5", textColor: "#ffffff" }, // 初三 - 靛蓝
      10: { color: "#9c27b0", textColor: "#ffffff" }, // 高一 - 紫色
      11: { color: "#e91e63", textColor: "#ffffff" }, // 高二 - 粉红
      12: { color: "#f44336", textColor: "#ffffff" }, // 高三 - 红色
      13: { color: "#607d8b", textColor: "#ffffff" }, // 大学 - 蓝灰
      0: { color: "#9e9e9e", textColor: "#ffffff" }, // 其他 - 灰色
    };
    return gradeColors[grade] || gradeColors[0];
  };

  // 头像上传相关方法
  const handleAvatarClick = () => {
    showAvatarUploadModal.value = true;
    // 初始化临时头像
    tempAvatar.value = formData.avatar || "";
  };

  // 处理头像上传成功
  const handleAvatarUploadSuccess = (fileData) => {
    console.log("头像上传成功:", fileData);
    tempAvatar.value = fileData.objectKey;
    formData.avatarFullUrl = fileData.baseUrl + fileData.objectKey;
  };

  // 处理头像上传失败
  const handleAvatarUploadError = (error) => {
    console.error("头像上传失败:", error);
  };

  const confirmAvatarUpload = () => {
    if (tempAvatar.value) {
      // 更新表单数据中的头像URL
      formData.avatar = tempAvatar.value;
      // 如果不是编辑模式，自动开启编辑模式
      if (!editMode.value) {
        startEdit();
      }
      showAvatarUploadModal.value = false;
      tempAvatar.value = "";
      message.success("头像已更新，请点击保存以应用更改");
    }
  };

  // 监听userData变化，同步更新localUserData和formData
  watch(() => props.userData, (newUserData) => {
    // 更新本地用户数据副本
    Object.assign(localUserData, newUserData);
    
    // 更新表单数据
    Object.keys(formData).forEach((key) => {
      if (key in newUserData) {
        formData[key] = newUserData[key];
      }
    });
  }, { deep: true, immediate: true });

  // 如果initialEditMode为true，初始化时自动进入编辑模式
  onMounted(() => {
    if (props.initialEditMode) {
      startEdit();
    }
  });
</script>

<style lang="scss" scoped>
  .user-detail-card {
    padding: 0;
    background-color: #ffffff;
    border-radius: 18px;
    width: 100%;

    .user-header {
      display: flex;
      padding: 16px 24px;
      border-radius: 8px 8px 0 0;
      background: #f5f7fa;

      .user-avatar-container {
        position: relative;
        margin-right: 20px;
        cursor: pointer;

        &:hover .upload-overlay {
          opacity: 1;
        }

        .user-avatar {
          border: 3px solid #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .upload-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
          color: white;
        }

        .user-status-badge {
          position: absolute;
          bottom: 1px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 12px;
          padding: 1px 8px;
          border-radius: 10px;
          background-color: #d03050;
          color: white;
          font-weight: 500;
          white-space: nowrap;

          &.active {
            background-color: #18a058;
          }
        }
      }

      .user-basic-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        h2 {
          margin: 0 0 4px 0;
          font-size: 20px;
          color: #333;
          font-weight: 600;
        }

        .user-id {
          color: #8c8c8c;
          font-size: 15px;
          margin-bottom: 8px;
        }

        .user-tags {
          display: flex;
          gap: 8px;

          .info-tag {
            border-radius: 12px;
            padding: 0 10px;
          }
        }
      }
    }

    .user-detail-content {
      padding: 16px 2px 2px;

      &.edit-mode {
        padding: 16px 2px 0;
      }

      .info-section {
        margin-bottom: 16px;

        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
          border-left: 3px solid #1890ff;
          padding-left: 8px;
          display: flex;
          align-items: center;

          &::before {
            content: "";
            margin-right: 8px;
          }
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
          }

          @media (max-width: 480px) {
            grid-template-columns: 1fr;
          }

          .info-item {
            .info-label {
              color: #666;
              font-size: 15px;
              margin-bottom: 4px;
              display: flex;
              align-items: center;

              .info-icon {
                margin-right: 5px;
                color: #909399;
              }
            }

            .info-value {
              color: #333;
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &.highlight-text {
                color: #1890ff;
              }
            }
          }
        }

        .form-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
          }

          @media (max-width: 480px) {
            grid-template-columns: 1fr;
          }

          :deep(.n-form-item) {
            margin-bottom: 0;

            &.n-form-item--top-labelled {
              grid-column: span 3;
            }
          }
        }
      }

      .bio-section {
        margin-bottom: 0;

        .bio-content {
          background-color: #f9f9f9;
          border-radius: 6px;
          padding: 12px;
          color: #555;
          line-height: 1.6;
          min-height: 80px;
        }
      }
    }

    .action-footer {
      display: flex;
      justify-content: center;
      padding: 16px;
      border-top: 1px solid #f0f0f0;
      margin-top: 12px;
    }
  }

  .n-divider {
    margin: 0;
  }

  .preview-avatar {
    border: 2px solid #eee;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
</style>
