<template>
  <div class="homepage-detail-card">
    <!-- 顶部信息区 - 仅在查看模式或编辑已有记录时显示 -->
    <div v-if="data.id && !editMode" class="homepage-header">
      <div class="cover-image-container" @click="handleCoverClick">
        <n-image
          :src="formData.coverImageFullUrl || formData.coverImage || defaultCover"
          :fallback-src="defaultCover"
          :width="120"
          :height="80"
          object-fit="cover"
          class="cover-image"
        />
        <div v-if="editMode" class="upload-overlay">
          <n-icon size="22"><CloudUploadOutline /></n-icon>
        </div>
        <div class="status-badge" :class="{ active: data.status }">
          {{ data.status ? "已启用" : "已禁用" }}
        </div>
      </div>
      <div class="homepage-basic-info">
        <h2>{{ data.title || "新推荐" }}</h2>
        <div class="homepage-id">ID: {{ data.id || "新建" }}</div>
        <div class="homepage-tags">
          <n-tag
            size="small"
            :type="data.status ? 'success' : 'error'"
            class="info-tag"
          >
            {{ data.status ? "启用" : "禁用" }}
          </n-tag>
          <n-tag
            v-if="data.sortOrder !== undefined"
            size="small"
            type="info"
            class="info-tag"
          >
            排序: {{ data.sortOrder }}
          </n-tag>
        </div>
      </div>
    </div>

    <!-- 内容区 -->
    <n-divider v-if="data.id && !editMode" />

    <!-- 查看模式 -->
    <div v-if="!editMode" class="homepage-detail-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"
                ><DocumentTextOutline /></n-icon
              >标题
            </div>
            <div class="info-value">{{ data.title || "-" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><BookmarkOutline /></n-icon
              >副标题
            </div>
            <div class="info-value">{{ data.subtitle || "-" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><LinkOutline /></n-icon
              >跳转链接
            </div>
            <div class="info-value">{{ data.linkUrl || "-" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"
                ><ReorderThreeOutline /></n-icon
              >排序
            </div>
            <div class="info-value">{{ data.sortOrder }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><TimeOutline /></n-icon
              >创建时间
            </div>
            <div class="info-value">{{ formatDate(data.createdAt) }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"
                ><CheckmarkCircleOutline /></n-icon
              >状态
            </div>
            <div class="info-value">
              <n-tag :type="data.status ? 'success' : 'error'" size="small">
                {{ data.status ? "启用" : "禁用" }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 详情描述 -->
      <div class="info-section description-section">
        <h3 class="section-title">详情描述</h3>
        <div class="description-content">
          {{ data.description || "暂无详情描述" }}
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="homepage-detail-content edit-mode">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-grid">
            <n-form-item label="标题" path="title">
              <n-input
                v-model:value="formData.title"
                placeholder="请输入推荐标题"
              />
            </n-form-item>
            <n-form-item label="副标题" path="subtitle">
              <n-input
                v-model:value="formData.subtitle"
                placeholder="请输入副标题"
              />
            </n-form-item>
            <n-form-item label="封面图片" path="coverImage">
              <OssDirectUpload
                v-model="formData.coverImage"
                category="image"
                :show-preview="true"
                accept="image/*"
                @upload-success="handleCoverUploadSuccess"
                @upload-error="handleCoverUploadError"
              />
            </n-form-item>
            <n-form-item label="链接类型" path="linkType">
              <n-select
                v-model:value="formData.linkType"
                :options="linkTypeOptions"
                placeholder="请选择链接类型"
                @update:value="handleLinkTypeChange"
              />
            </n-form-item>
            <n-form-item
              v-if="formData.linkType === 1"
              label="关联课程"
              path="linkTargetId"
            >
              <n-select
                v-model:value="formData.linkTargetId"
                :options="courseOptions"
                placeholder="请选择关联课程"
                filterable
                remote
                :loading="courseLoading"
                @search="handleCourseSearch"
                clearable
                :render-label="renderCourseLabel"
              />
            </n-form-item>
            <n-form-item
              v-if="formData.linkType === 2"
              label="关联直播"
              path="linkTargetId"
            >
              <n-select
                v-model:value="formData.linkTargetId"
                :options="liveOptions"
                placeholder="请选择关联直播"
                filterable
                remote
                :loading="liveLoading"
                @search="handleLiveSearch"
                clearable
                :render-label="renderLiveLabel"
              />
            </n-form-item>
            <n-form-item
              v-if="formData.linkType === 3"
              label="跳转链接"
              path="linkUrl"
            >
              <n-input
                v-model:value="formData.linkUrl"
                placeholder="请输入跳转链接URL"
              />
            </n-form-item>
            <n-form-item label="排序" path="sortOrder">
              <n-input-number
                v-model:value="formData.sortOrder"
                :min="0"
                placeholder="数字越小排序越靠前"
              />
            </n-form-item>
            <n-form-item label="状态" path="status">
              <n-switch v-model:value="formData.status">
                <template #checked>启用</template>
                <template #unchecked>禁用</template>
              </n-switch>
            </n-form-item>
          </div>
        </div>

        <!-- 详情描述 -->
        <div class="info-section">
          <h3 class="section-title">详情描述</h3>
          <n-form-item path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请输入详情描述"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </n-form-item>
        </div>
      </n-form>
    </div>

    <!-- 底部操作区 -->
    <div class="action-footer">
      <n-space>
        <template v-if="editMode">
          <n-button type="primary" @click="handleSave" :loading="saveLoading">
            <template #icon>
              <n-icon><CheckmarkOutline /></n-icon>
            </template>
            保存
          </n-button>
          <n-button @click="cancelEdit">
            <template #icon>
              <n-icon><CloseOutline /></n-icon>
            </template>
            取消
          </n-button>
        </template>
        <template v-else>
          <n-button type="info" @click="startEdit">
            <template #icon>
              <n-icon><PencilOutline /></n-icon>
            </template>
            编辑
          </n-button>
          <n-button
            type="warning"
            @click="handleToggleStatus"
            :disabled="saveLoading"
          >
            <template #icon>
              <n-icon><RefreshCircleOutline /></n-icon>
            </template>
            {{ data.status ? "禁用" : "启用" }}
          </n-button>
          <n-button type="error" @click="handleDelete" :disabled="saveLoading">
            <template #icon>
              <n-icon><TrashOutline /></n-icon>
            </template>
            删除
          </n-button>
          <n-button @click="close">
            <template #icon>
              <n-icon><CloseOutline /></n-icon>
            </template>
            关闭
          </n-button>
        </template>
      </n-space>
    </div>

    <!-- 封面上传对话框 -->
    <n-modal
      v-model:show="showCoverUploadModal"
      preset="card"
      title="修改封面"
      style="width: 400px"
    >
      <n-upload
        ref="coverUploadRef"
        :default-upload="false"
        :max="1"
        accept="image/*"
        list-type="image-card"
        :on-before-upload="beforeCoverUpload"
        @change="handleCoverUploadChange"
      >
        <n-upload-dragger>
          <div style="padding: 20px 0; text-align: center">
            <n-icon size="48" :depth="3">
              <CloudUploadOutline />
            </n-icon>
            <div style="margin-top: 8px">点击上传封面图片</div>
          </div>
        </n-upload-dragger>
      </n-upload>

      <template #footer>
        <div style="text-align: right">
          <n-button
            style="margin-right: 8px"
            @click="showCoverUploadModal = false"
            >取消</n-button
          >
          <n-button
            type="primary"
            :disabled="!previewCover"
            @click="confirmCoverUpload"
            >确认上传</n-button
          >
        </div>
      </template>
    </n-modal>

    <!-- 图片预览对话框 -->
    <n-modal v-model:show="showImagePreview" preset="card" title="图片预览">
      <div class="image-preview">
        <n-image
          :src="previewImageUrl"
          :fallback-src="defaultCover"
          style="max-width: 100%; max-height: 400px"
        />
      </div>
    </n-modal>
  </div>
</template>

<script setup>
import {
  DocumentTextOutline,
  BookmarkOutline,
  LinkOutline,
  ReorderThreeOutline,
  TimeOutline,
  CheckmarkCircleOutline,
  CheckmarkOutline,
  CloseOutline,
  CloudUploadOutline,
  PencilOutline,
  TrashOutline,
  RefreshCircleOutline,
} from "@vicons/ionicons5";
import { h, ref, reactive, watch } from "vue";
import dayjs from "dayjs";
import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  initialEditMode: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["save", "edit", "close", "toggle-status", "delete"]);

const defaultCover = "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg";

// 表单相关
const editMode = ref(false);
const formRef = ref(null);
const saveLoading = ref(false);

// 封面上传相关
const showCoverUploadModal = ref(false);
const coverUploadRef = ref(null);
const previewCover = ref("");

// 图片预览
const showImagePreview = ref(false);
const previewImageUrl = ref("");

const message = window.$message || {
  success: (msg) => console.log('Success:', msg),
  error: (msg) => console.error('Error:', msg),
  warning: (msg) => console.warn('Warning:', msg),
  info: (msg) => console.info('Info:', msg)
};

// 表单数据
const formData = reactive({
  id: null,
  title: "",
  subtitle: "",
  coverImage: "",
  coverImageFullUrl: "",
  linkType: 1, // 默认为课程
  linkTargetId: null,
  linkUrl: "",
  sortOrder: 0,
  status: true,
  description: "",
});

// 课程和直播选项
const courseOptions = ref([]);
const liveOptions = ref([]);
const courseLoading = ref(false);
const liveLoading = ref(false);

// 链接类型选项
const linkTypeOptions = [
  { label: "课程", value: 1 },
  { label: "直播", value: 2 },
  { label: "外部链接", value: 3 },
];

// 表单验证规则
const rules = {
  title: [
    { required: true, message: "请输入推荐标题", trigger: "blur" },
    {
      min: 1,
      max: 100,
      message: "标题长度必须在1-100个字符之间",
      trigger: "blur",
    },
  ],
  subtitle: [
    {
      max: 200,
      message: "副标题长度不能超过200个字符",
      trigger: "blur",
    },
  ],
  linkType: [
    { required: true, message: "请选择链接类型", trigger: "change" },
  ],
  linkTargetId: [
    {
      validator: (_rule, value) => {
        if (formData.linkType === 1 || formData.linkType === 2) {
          if (!value) {
            return new Error(formData.linkType === 1 ? "请选择关联课程" : "请选择关联直播");
          }
        }
        return true;
      },
      trigger: "change",
    },
  ],
  linkUrl: [
    {
      validator: (_rule, value) => {
        if (formData.linkType === 3) {
          if (!value) {
            return new Error("请输入跳转链接URL");
          }
          // 简单的URL格式验证
          const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
          if (!urlPattern.test(value)) {
            return new Error("请输入有效的URL格式");
          }
        }
        return true;
      },
      trigger: "blur",
    },
  ],
  sortOrder: [
    { required: true, message: "请输入排序值", trigger: "blur" },
    { type: "number", min: 0, message: "排序值不能小于0", trigger: "blur" },
  ],
  description: [
    {
      max: 500,
      message: "描述长度不能超过500个字符",
      trigger: "blur",
    },
  ],
};

// 开始编辑
const startEdit = () => {
  // 复制数据到表单
  Object.keys(formData).forEach((key) => {
    if (key in props.data) {
      formData[key] = props.data[key];
    }
  });
  editMode.value = true;
};

// 取消编辑
const cancelEdit = () => {
  editMode.value = false;
};

// 关闭弹窗
const close = () => {
  emit("close");
};

// 切换启用/禁用状态
const handleToggleStatus = () => {
  emit("toggle-status", props.data);
};

// 删除推荐
const handleDelete = () => {
  emit("delete", props.data);
};

// 保存编辑
const handleSave = () => {
  formRef.value?.validate((errors) => {
    if (!errors) {
      saveLoading.value = true;
      // 触发保存事件
      emit("save", { ...formData });
      // 实际应用中，这里会有异步保存操作，完成后再关闭编辑模式
      setTimeout(() => {
        saveLoading.value = false;
        editMode.value = false;
      }, 500);
    }
  });
};

// 日期格式化
const formatDate = (date) => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm:ss") : "-";
};

// 封面上传相关方法
const handleCoverClick = () => {
  if (editMode.value) {
    showCoverUploadModal.value = true;
    const imageUrl = formData.coverImageFullUrl || formData.coverImage;
    if (imageUrl) {
      previewCover.value = imageUrl;
    }
  }
};

const beforeCoverUpload = (data) => {
  if (!data.file.type.startsWith("image/")) {
    message.error("只能上传图片文件");
    return false;
  }
  if (data.file.size > 5 * 1024 * 1024) {
    message.error("图片大小不能超过5MB");
    return false;
  }
  return true;
};

const handleCoverUploadChange = ({ file }) => {
  if (file.status === "finished" || file.status === undefined) {
    const reader = new FileReader();
    reader.onload = (e) => {
      previewCover.value = e.target.result;
    };
    reader.readAsDataURL(file.file);
  }
};

const confirmCoverUpload = () => {
  if (previewCover.value) {
    formData.coverImage = previewCover.value;
    formData.coverImageFullUrl = previewCover.value;
    showCoverUploadModal.value = false;
    message.success("封面已更新");
  }
};



// OSS直传上传成功处理
const handleCoverUploadSuccess = (result) => {
  console.log('封面上传成功:', result);

  // 设置相对路径到表单数据（用于保存到数据库）
  formData.coverImage = result.fileUrl;

  // 设置完整URL用于预览
  if (result.previewUrl) {
    formData.coverImageFullUrl = result.previewUrl;
  } else {
    // 如果没有预览URL，使用OSS基础URL + 相对路径
    const baseUrl = 'https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/';
    formData.coverImageFullUrl = baseUrl + result.fileUrl;
  }

  message.success("封面上传成功");
};

// OSS直传上传失败处理
const handleCoverUploadError = (error) => {
  console.error('封面上传失败:', error);
  message.error("封面上传失败: " + (error.message || "未知错误"));
};

// 链接类型变化处理
const handleLinkTypeChange = (value) => {
  formData.linkTargetId = null;
  formData.linkUrl = "";
  if (value === 1) {
    loadCourseOptions();
  } else if (value === 2) {
    loadLiveOptions();
  }
};

// 加载课程选项
const loadCourseOptions = async (keyword = "") => {
  courseLoading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));

    const mockCourses = [
      { label: "Java基础入门课程", value: 1, coverImage: "course1.jpg" },
      { label: "Python数据分析", value: 2, coverImage: "course2.jpg" },
      { label: "前端开发实战", value: 3, coverImage: "course3.jpg" },
      { label: "机器学习算法", value: 4, coverImage: "course4.jpg" },
    ];

    courseOptions.value = keyword
      ? mockCourses.filter(course => course.label.includes(keyword))
      : mockCourses;
  } catch (error) {
    message.error("加载课程列表失败");
  } finally {
    courseLoading.value = false;
  }
};

// 加载直播选项
const loadLiveOptions = async (keyword = "") => {
  liveLoading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500));

    const mockLives = [
      { label: "名师直播：算法解析", value: 1, coverImage: "live1.jpg" },
      { label: "实时答疑：前端技术", value: 2, coverImage: "live2.jpg" },
      { label: "职场技能分享", value: 3, coverImage: "live3.jpg" },
    ];

    liveOptions.value = keyword
      ? mockLives.filter(live => live.label.includes(keyword))
      : mockLives;
  } catch (error) {
    message.error("加载直播列表失败");
  } finally {
    liveLoading.value = false;
  }
};

// 课程搜索
const handleCourseSearch = (query) => {
  loadCourseOptions(query);
};

// 直播搜索
const handleLiveSearch = (query) => {
  loadLiveOptions(query);
};

// 渲染课程标签
const renderCourseLabel = (option) => {
  return h("div", { style: "display: flex; align-items: center;" }, [
    h("n-avatar", {
      size: "small",
      src: option.coverImage || "default-course.jpg",
      style: "margin-right: 8px;",
    }),
    h("span", option.label),
  ]);
};

// 渲染直播标签
const renderLiveLabel = (option) => {
  return h("div", { style: "display: flex; align-items: center;" }, [
    h("n-avatar", {
      size: "small",
      src: option.coverImage || "default-live.jpg",
      style: "margin-right: 8px;",
    }),
    h("span", option.label),
  ]);
};

// 监听props变化，更新表单数据
watch(
  () => props.data,
  (newData) => {
    Object.keys(formData).forEach((key) => {
      if (key in newData) {
        formData[key] = newData[key];
      }
    });
  },
  { immediate: true, deep: true }
);

// 初始化时设置编辑模式
watch(
  () => props.initialEditMode,
  (newVal) => {
    if (newVal) {
      editMode.value = true;
      // 如果是新增模式，加载选项数据
      if (formData.linkType === 1) {
        loadCourseOptions();
      } else if (formData.linkType === 2) {
        loadLiveOptions();
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.homepage-detail-card {
  padding: 0;
  background-color: #ffffff;
  border-radius: 18px;
  width: 100%;

  .homepage-header {
    display: flex;
    padding: 16px 24px;
    border-radius: 8px 8px 0 0;
    background: #f5f7fa;

    .cover-image-container {
      position: relative;
      margin-right: 20px;
      cursor: pointer;

      &:hover .upload-overlay {
        opacity: 1;
      }

      .cover-image {
        border: 3px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
      }

      .upload-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        color: white;
      }

      .status-badge {
        position: absolute;
        bottom: 4px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 10px;
        background-color: #d03050;
        color: white;
        font-weight: 500;
        white-space: nowrap;

        &.active {
          background-color: #18a058;
        }
      }
    }

    .homepage-basic-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      h2 {
        margin: 0 0 4px 0;
        font-size: 20px;
        color: #333;
        font-weight: 600;
      }

      .homepage-id {
        color: #8c8c8c;
        font-size: 15px;
        margin-bottom: 8px;
      }

      .homepage-tags {
        display: flex;
        gap: 8px;

        .info-tag {
          border-radius: 12px;
          padding: 0 10px;
        }
      }
    }
  }

  .homepage-detail-content {
    padding: 16px 2px 2px;

    &.edit-mode {
      padding: 16px 2px 0;
    }

    .info-section {
      margin-bottom: 16px;

      .section-title {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        border-left: 3px solid #1890ff;
        padding-left: 8px;
        display: flex;
        align-items: center;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;

        @media (max-width: 768px) {
          grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 480px) {
          grid-template-columns: 1fr;
        }

        .info-item {
          .info-label {
            color: #666;
            font-size: 15px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            font-weight: 500;

            .info-icon {
              margin-right: 6px;
              color: #1890ff;
            }
          }

          .info-value {
            color: #333;
            font-size: 15px;
            word-break: break-all;
            line-height: 1.4;
          }
        }
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
        }

        .n-form-item {
          margin-bottom: 0;
        }
      }

      &.description-section {
        .description-content {
          background: #f8f9fa;
          padding: 12px;
          border-radius: 6px;
          color: #333;
          line-height: 1.6;
          min-height: 60px;
          white-space: pre-wrap;
        }
      }
    }
  }

  .action-footer {
    padding: 16px 0 0;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .upload-container {
    width: 100%;

    .upload-area {
      width: 100%;
      min-height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      transition: border-color 0.3s;

      &:hover {
        border-color: #1890ff;
      }

      .preview-image {
        border-radius: 6px;
      }

      .upload-placeholder {
        text-align: center;
        color: #999;
      }
    }

    .upload-actions {
      margin-top: 8px;
      display: flex;
      gap: 8px;
    }
  }

  .image-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
}
</style>
