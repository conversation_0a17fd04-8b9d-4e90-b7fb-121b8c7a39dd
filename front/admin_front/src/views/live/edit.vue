<template>
  <div class="page-container">
    <n-card :title="formTitle" class="content-card">
      <n-spin :show="loading">
        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="100px"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="24" :x-gap="24">
            <!-- 左侧表单区域 -->
            <n-gi :span="16">
              <n-form-item label="直播标题" path="title">
                <n-input
                  v-model:value="formModel.title"
                  placeholder="请输入直播标题"
                />
              </n-form-item>

              <n-form-item label="直播讲师" path="teacherId">
                <n-select
                  v-model:value="formModel.teacherId"
                  :options="teacherOptions"
                  placeholder="请选择讲师"
                  filterable
                  clearable
                />
              </n-form-item>

              <n-form-item label="课程分类" path="categoryId">
                <n-select
                  v-model:value="formModel.categoryId"
                  :options="categoryOptions"
                  placeholder="请选择分类"
                  filterable
                  clearable
                />
              </n-form-item>

              <n-form-item label="开始时间" path="startTime">
                <n-date-picker
                  v-model:value="formModel.startTime"
                  type="datetime"
                  placeholder="请选择开始时间"
                />
              </n-form-item>

              <n-form-item label="结束时间" path="endTime">
                <n-date-picker
                  v-model:value="formModel.endTime"
                  type="datetime"
                  placeholder="请选择结束时间"
                />
              </n-form-item>

              <n-form-item label="直播间ID" path="roomId">
                <n-input
                  v-model:value="formModel.roomId"
                  placeholder="请输入直播间ID"
                />
              </n-form-item>

              <n-form-item label="直播间密码" path="roomPassword">
                <n-input
                  v-model:value="formModel.roomPassword"
                  placeholder="请输入直播间密码（可选）"
                />
              </n-form-item>

              <n-form-item label="价格" path="price">
                <n-input-number
                  v-model:value="formModel.price"
                  :min="0"
                  :precision="2"
                  placeholder="请输入价格"
                >
                  <template #prefix>¥</template>
                </n-input-number>
              </n-form-item>

              <n-form-item label="状态" path="status">
                <n-select
                  v-model:value="formModel.status"
                  :options="statusOptions"
                  placeholder="请选择状态"
                />
              </n-form-item>

              <n-form-item label="直播简介" path="description">
                <n-input
                  v-model:value="formModel.description"
                  type="textarea"
                  placeholder="请输入直播简介"
                  :autosize="{
                    minRows: 3,
                    maxRows: 5,
                  }"
                />
              </n-form-item>
            </n-gi>

            <!-- 右侧上传区域 -->
            <n-gi :span="8">
              <n-card title="直播封面" size="small">
                <OssDirectUpload
                  v-model="formModel.coverImage"
                  category="image"
                  :show-preview="true"
                  @upload-success="handleCoverUploadSuccess"
                  @upload-error="handleCoverUploadError"
                />
              </n-card>

              <n-card title="直播设置" size="small" class="m-t-20">
                <n-space vertical>
                  <n-checkbox v-model:checked="formModel.isRecorded">
                    自动录制直播
                  </n-checkbox>
                  <n-checkbox v-model:checked="formModel.needSignIn">
                    需要签到
                  </n-checkbox>
                  <n-checkbox v-model:checked="formModel.isReplayEnabled">
                    开启回放
                  </n-checkbox>
                  <n-checkbox v-model:checked="formModel.isInteractive">
                    开启互动功能
                  </n-checkbox>
                </n-space>
              </n-card>

              <n-card title="参考资料" size="small" class="m-t-20">
                <n-upload
                  :default-file-list="materialFileList"
                  :custom-request="handleMaterialUpload"
                  @remove="handleMaterialRemove"
                >
                  点击上传
                </n-upload>
              </n-card>
            </n-gi>
          </n-grid>

          <!-- 按钮组 -->
          <div class="action-bar">
            <n-space>
              <n-button
                type="primary"
                :loading="submitLoading"
                @click="handleSubmit"
              >
                保存
              </n-button>
              <n-button @click="handleCancel">取消</n-button>
            </n-space>
          </div>
        </n-form>
      </n-spin>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useMessage } from "naive-ui";
import {
  getLiveCourseDetail,
  createLiveCourse,
  updateLiveCourse,
} from "@/api/live";
import { getCategoryList } from "@/api/course";
import { getAllTeachers } from "@/api/teacher";
import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";

const message = useMessage();
const route = useRoute();
const router = useRouter();

// 直播ID
const liveId = computed(() => route.params.id);

// 表单标题
const formTitle = computed(() => (liveId.value ? "编辑直播" : "创建直播"));

// 加载状态
const loading = ref(false);
const submitLoading = ref(false);

// 表单引用
const formRef = ref(null);

// 表单数据
const formModel = reactive({
  id: null,
  title: "",
  teacherId: null,
  categoryId: null,
  startTime: null,
  endTime: null,
  roomId: "",
  roomPassword: "",
  price: 0,
  status: 0, // 改为数字：0-未开始，1-直播中，2-已结束
  description: "",
  coverImage: "", // 改为coverImage

  isRecorded: false,
  needSignIn: false,
  isReplayEnabled: false,
  isInteractive: true,
  materials: "",
});

// 状态选项
const statusOptions = [
  { label: "未开始", value: 0 },
  { label: "直播中", value: 1 },
  { label: "已结束", value: 2 },
];

// 选项
const categoryOptions = ref([]);
const teacherOptions = ref([]);

// 文件列表
const coverFileList = ref([]);
const materialFileList = ref([]);

// 表单验证规则
const rules = {
  title: [{ required: true, message: "请输入直播标题", trigger: "blur" }],
  teacherId: [{ required: true, message: "请选择讲师", trigger: "change" }],
  categoryId: [{ required: true, message: "请选择分类", trigger: "change" }],
  startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  roomId: [{ required: true, message: "请输入直播间ID", trigger: "blur" }],
};

// 加载直播详情
const loadLiveDetail = async () => {
  if (!liveId.value) return;

  loading.value = true;
  try {
    const res = await getLiveCourseDetail(liveId.value);
    if (res.code === 200) {
      // 更新表单数据
      Object.keys(formModel).forEach((key) => {
        if (res.data[key] !== undefined) {
          formModel[key] = res.data[key];
        }
      });

      // 封面图片已经通过v-model自动绑定，无需额外处理

      // 设置资料预览
      if (formModel.materials) {
        const materials = formModel.materials.split(",");
        materialFileList.value = materials.map((item, index) => {
          const parts = item.split("|");
          return {
            id: `material-${index}`,
            name: parts[0] || `资料${index + 1}`,
            status: "finished",
            url: parts[1] || item,
          };
        });
      }
    } else {
      message.error(res.message || "获取直播详情失败");
    }
  } catch (error) {
    message.error("获取直播详情失败");
  } finally {
    loading.value = false;
  }
};

// 加载分类选项
const loadCategoryOptions = async () => {
  try {
    const res = await getCategoryList({ pageNum: 1, pageSize: 1000 });
    if (res.code === 200) {
      categoryOptions.value = res.data.records.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } else {
      message.error(res.message || "获取分类选项失败");
    }
  } catch (error) {
    message.error("获取分类选项失败");
  }
};

// 加载讲师选项
const loadTeacherOptions = async () => {
  try {
    const res = await getAllTeachers({ pageNum: 1, pageSize: 1000 });
    if (res.code === 200) {
      teacherOptions.value = res.data.records.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } else {
      message.error(res.message || "获取讲师选项失败");
    }
  } catch (error) {
    message.error("获取讲师选项失败");
  }
};

// 处理封面上传成功
const handleCoverUploadSuccess = (fileData) => {
  console.log("封面上传成功:", fileData);
  message.success("封面上传成功");
};

// 处理封面上传失败
const handleCoverUploadError = (error) => {
  console.error("封面上传失败:", error);
  message.error("封面上传失败");
};

// 处理资料上传
const handleMaterialUpload = ({ file, onFinish, onError }) => {
  // 实际项目中，这里应该调用上传API
  try {
    const reader = new FileReader();
    reader.readAsDataURL(file.file);
    reader.onload = () => {
      const newMaterial = `${file.file.name}|${reader.result}`;

      // 如果已有资料，追加
      if (formModel.materials) {
        formModel.materials += "," + newMaterial;
      } else {
        formModel.materials = newMaterial;
      }

      // 更新资料文件列表
      materialFileList.value.push({
        id: `material-${materialFileList.value.length}`,
        name: file.file.name,
        status: "finished",
        url: reader.result,
      });

      onFinish();
    };
    reader.onerror = () => {
      onError();
    };
  } catch (error) {
    onError();
  }
};

// 处理资料移除
const handleMaterialRemove = (options) => {
  const { file } = options;
  const materials = formModel.materials.split(",");
  const index = materialFileList.value.findIndex((item) => item.id === file.id);

  if (index !== -1) {
    materials.splice(index, 1);
    formModel.materials = materials.join(",");
    materialFileList.value = materialFileList.value.filter(
      (item) => item.id !== file.id
    );
  }
};

// 处理提交
const handleSubmit = () => {
  formRef.value.validate(async (errors) => {
    if (!errors) {
      submitLoading.value = true;
      try {
        const saveData = { ...formModel };

        // 调用API
        const res = saveData.id
          ? await updateLiveCourse(saveData)
          : await createLiveCourse(saveData);

        if (res.code === 200) {
          message.success(saveData.id ? "更新成功" : "创建成功");
          router.push("/live/list");
        } else {
          message.error(res.message || (saveData.id ? "更新失败" : "创建失败"));
        }
      } catch (error) {
        message.error(formModel.id ? "更新失败" : "创建失败");
      } finally {
        submitLoading.value = false;
      }
    } else {
      message.error("请完善表单信息");
    }
  });
};

// 处理取消
const handleCancel = () => {
  router.push("/live/list");
};

onMounted(async () => {
  await Promise.all([loadCategoryOptions(), loadTeacherOptions()]);

  await loadLiveDetail();
});
</script>

<style lang="scss" scoped>
.content-card {
  margin-bottom: 20px;
}

.action-bar {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.upload-container {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-20 {
  margin-top: 20px;
}
</style>
