<template>
  <div class="page-container">
    <n-card title="直播课程管理" class="content-card">
      <!-- 搜索区域 -->
      <n-grid :cols="4" :x-gap="16">
        <n-gi>
          <n-form-item label="课程标题">
            <n-input
              v-model:value="searchParams.title"
              placeholder="请输入课程标题"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="讲师">
            <n-input
              v-model:value="searchParams.teacherName"
              placeholder="请输入讲师姓名"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="直播状态">
            <n-select
              v-model:value="searchParams.status"
              :options="statusOptions"
              placeholder="请选择直播状态"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-space>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </n-gi>
      </n-grid>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加直播
        </n-button>
        <n-button
          class="m-l-10"
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
        <n-button class="m-l-10" type="warning" @click="loadData">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />

      <!-- 添加/编辑直播对话框 -->
      <n-modal
        v-model:show="showModal"
        :title="modalTitle"
        preset="card"
        :style="{ width: '800px' }"
      >
        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="2" :x-gap="16">
            <n-gi :span="2">
              <n-form-item label="直播标题" path="title">
                <n-input
                  v-model:value="formModel.title"
                  placeholder="请输入直播标题"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="2">
              <n-form-item label="封面图" path="coverImage">
                <OssDirectUpload
                  v-model="formModel.coverImage"
                  category="image"
                  :show-preview="true"
                  @upload-success="handleCoverUploadSuccess"
                  @upload-error="handleCoverUploadError"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="直播讲师" path="teacherId">
                <!-- <n-select
                  v-model:value="formModel.teacherId"
                  :options="teacherOptions"
                  placeholder="请选择讲师"
                  filterable
                  clearable
                /> -->
                <TeacherSelector v-model:value="formModel.teacherId" />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="课程分类" path="categoryId">
                <TagSelector
                  v-model:value="formModel.categoryId"
                  category="course_category"
                  tag-label="分类"
                  placeholder="请选择分类"
                  :allow-add="true"
                  @change="handleCategoryChange"
                  @add-success="handleCategoryAddSuccess"
                />
              </n-form-item>
            </n-gi>
            <n-gi :span="2">
              <n-form-item label="直播时间" path="timeRange">
                <n-date-picker
                  v-model:value="formModel.timeRange"
                  type="datetimerange"
                  style="width: 100%;"
                  placeholder="请选择直播时间范围"
                  separator="至"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="直播间ID" path="roomId">
                <n-input
                  v-model:value="formModel.roomId"
                  placeholder="请输入直播间ID"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="直播间密码" path="roomPassword">
                <n-input
                  v-model:value="formModel.roomPassword"
                  placeholder="请输入直播间密码（可选）"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="价格" path="price">
                <n-input-number
                  v-model:value="formModel.price"
                  placeholder="请输入价格"
                  :min="0"
                  :precision="2"
                />
              </n-form-item>
            </n-gi>
  
            <n-gi>
              <n-form-item label="状态" path="status">
                <n-select
                  v-model:value="formModel.status"
                  :options="formStatusOptions"
                  placeholder="请选择状态"
                />
              </n-form-item>
            </n-gi>
            <n-gi span="2">
              <n-form-item label="直播简介" path="description">
                <n-input
                  v-model:value="formModel.description"
                  type="textarea"
                  placeholder="请输入直播简介"
                  :autosize="{
                    minRows: 3,
                    maxRows: 5,
                  }"
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showModal = false">取消</n-button>
            <n-button
              type="primary"
              :loading="submitLoading"
              @click="handleSubmit"
              >确定</n-button
            >
          </n-space>
        </template>
      </n-modal>


    </n-card>
  </div>
</template>

<script setup>
import { h, ref, reactive, computed, onMounted } from "vue";
import {
  AddOutline,
  RefreshOutline,
  SearchOutline,
  PencilOutline,
  TrashOutline,
  PlayCircleOutline,
  StopCircleOutline,
  TimeOutline,
  CheckmarkCircleOutline,
  EyeOutline,
} from "@vicons/ionicons5";
import {
  getLiveCourseList,
  createLiveCourse,
  updateLiveCourse,
  deleteLiveCourse,
  batchDeleteLiveCourses,
  startLive,
  endLive,
} from "@/api/live";
import { getAllTeachers } from "@/api/teacher";
import { useMessage, useDialog, NImage } from "naive-ui";
import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";
import TeacherSelector from "@/components/Selector/TeacherSelector.vue";
import TagSelector from "@/components/Selector/TagSelector.vue";

const message = useMessage();
const dialog = useDialog();

// 表格数据
const loading = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 搜索参数
const searchParams = reactive({
  title: "",
  teacherName: "",
  status: null,
});

// 状态选项
const statusOptions = [
  { label: "未开始", value: 0 },
  { label: "直播中", value: 1 },
  { label: "已结束", value: 2 },
];

// 表单状态选项
const formStatusOptions = [
  { label: "未开始", value: 0 },
  { label: "直播中", value: 1 },
  { label: "已结束", value: 2 },
];

// 讲师选项
const teacherOptions = ref([]);



// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// 表单
const showModal = ref(false);
const modalTitle = computed(() => (formModel.id ? "编辑直播" : "添加直播"));
const formRef = ref(null);
const submitLoading = ref(false);

// 表单数据
const formModel = reactive({
  id: null,
  title: "",
  coverImage: "",
  teacherId: null,
  categoryId: null,
  timeRange: null,
  roomId: "",
  roomPassword: "",
  price: 0,
  description: "",
  status: 0,
});

// 表单验证规则
const rules = {
  title: [{ required: true, message: "请输入直播标题", trigger: "blur" }],
  teacherId: [{ required: true, message: "请选择讲师", trigger: "change",type: "number" }],
  categoryId: [{ required: true, message: "请选择分类", trigger: "change",type: "number" }],
  timeRange: [
    { required: true, message: "请选择直播时间范围", trigger: "change", type: "array" },
    {
      validator: (rule, value) => {
        if (!value || !Array.isArray(value) || value.length !== 2) {
          return new Error("请选择完整的时间范围");
        }
        const [startTime, endTime] = value;
        if (!startTime || !endTime) {
          return new Error("请选择完整的时间范围");
        }
        if (startTime >= endTime) {
          return new Error("结束时间必须大于开始时间");
        }
        // 检查开始时间是否早于当前时间（仅在创建新直播时）
        if (!formModel.id && startTime < Date.now()) {
          return new Error("开始时间不能早于当前时间");
        }
        return true;
      },
      trigger: "change",
      type: "array"
    }
  ],
  roomId: [{ required: true, message: "请输入直播间ID", trigger: "blur" }],
};

// 获取状态标签类型
const getStatusType = (status) => {
  switch (status) {
    case 1: // 直播中
      return "success";
    case 0: // 未开始
      return "info";
    case 2: // 已结束
      return "error";
    default:
      return "default";
  }
};

// 获取状态标签文本
const getStatusText = (status) => {
  switch (status) {
    case 1: // 直播中
      return "直播中";
    case 0: // 未开始
      return "未开始";
    case 2: // 已结束
      return "已结束";
    default:
      return status;
  }
};

// 获取状态图标
const getStatusIcon = (status) => {
  switch (status) {
    case 1: // 直播中
      return PlayCircleOutline;
    case 0: // 未开始
      return TimeOutline;
    case 2: // 已结束
      return StopCircleOutline;
    default:
      return CheckmarkCircleOutline;
  }
};

// 格式化时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 表格列
const createColumns = () => {
  return [
    {
      type: "selection",
    },
    {
      title: "ID",
      key: "id",
      width: 80,
    },
    {
      title: "直播标题",
      key: "title",
      width: 200,
    },
    {
      title: "封面",
      key: "coverImageFullUrl",
      width: 100,
      render(row) {
        if (!row.coverImageFullUrl) return null;
        return h(NImage, {
          src: row.coverImageFullUrl,
          width: 120,
          height: 120,
          objectFit: "cover",
          borderRadius: "4px",
        });
      },
    },
    {
      title: "讲师",
      key: "teacherLabel",
      width: 120,
    },
    {
      title: "分类",
      key: "categoryLabel",
      width: 120,
    },
    {
      title: "直播时间",
      key: "startTime",
      width: 300,
      render(row) {
        return formatDateTime(row.startTime)+'~'+formatDateTime(row.endTime);
      },
    },

    {
      title: "价格",
      key: "price",
      width: 100,
      render(row) {
        return `¥${row.price.toFixed(2)}`;
      },
    },
    {
      title: "状态",
      key: "status",
      width: 100,
      render(row) {
        return h(
          "n-tag",
          {
            type: getStatusType(row.status),
            size: "small",
          },
          {
            default: () => getStatusText(row.status),
            icon: () =>
              h(
                "n-icon",
                {
                  size: 14,
                },
                {
                  default: () => h(getStatusIcon(row.status)),
                }
              ),
          }
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 320,
      fixed: "right",
      align: "center",
      render(row) {
        return h("div", { style: "display: flex; justify-content: center; gap: 10px;" }, [
          h(
            "n-button",
            {
              size: "small",
              style: "padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
              onClick: () => handleView(row),
            },
            {
              default: () => "查看",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(EyeOutline),
                  }
                ),
            }
          ),
          h(
            "n-button",
            {
              size: "small",
              style: "padding: 0 12px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
              onClick: () => handleEdit(row),
            },
            {
              default: () => "编辑",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(PencilOutline),
                  }
                ),
            }
          ),
          row.status === 0 &&
            h(
              "n-button",
              {
                size: "small",
                style: "padding: 0 12px; border-radius: 15px; background-color: #52c41a; border: none; color: white;",
                onClick: () => handleStartLive(row),
              },
              {
                default: () => "开始直播",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(PlayCircleOutline),
                    }
                  ),
              }
            ),
          row.status === 1 &&
            h(
              "n-button",
              {
                size: "small",
                style: "padding: 0 12px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;",
                onClick: () => handleEndLive(row),
              },
              {
                default: () => "结束直播",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(StopCircleOutline),
                    }
                  ),
              }
            ),
          h(
            "n-button",
            {
              size: "small",
              style: "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
              onClick: () => handleDelete(row),
            },
            {
              default: () => "删除",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(TrashOutline),
                  }
                ),
            }
          ),
        ]);
      },
    },
  ];
};

const columns = createColumns();

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      ...searchParams,
    };

    // 调用API
    const res = await getLiveCourseList(params);

    // 更新数据
    tableData.value = res.data.records;
    pagination.pageCount = res.data.pages;
    pagination.page = res.data.current;
    pagination.pageSize = res.data.size;
    pagination.itemCount = res.data.total;
  } catch (error) {
    console.error("加载直播课程数据失败:", error);
    message.error("加载直播课程数据失败");
  } finally {
    loading.value = false;
  }
};

// 加载讲师选项
const loadOptions = async () => {
  try {
    // 加载讲师（获取所有数据用于下拉选项）
    const teachersRes = await getAllTeachers({ pageNum: 1, pageSize: 1000 });
    if (teachersRes.code === 200) {
      teacherOptions.value = teachersRes.data.records.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error("加载选项数据失败:", error);
    message.error("加载选项数据失败");
  }
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach((key) => {
    searchParams[key] = key === "status" ? null : "";
  });
  pagination.page = 1;
  loadData();
};

// 处理添加
const handleAdd = () => {
  Object.keys(formModel).forEach((key) => {
    if (key === "price") {
      formModel[key] = 0;
    } else if (key === "status") {
      formModel[key] = 0;
    } else if (key === "timeRange") {
      formModel[key] = null;
    } else if (key !== "id") {
      formModel[key] = null;
    }
  });
  formModel.id = null;
  formModel.title = "";
  formModel.coverImage = "";
  formModel.roomId = "";
  formModel.roomPassword = "";
  formModel.description = "";
  showModal.value = true;
};

// 处理查看
const handleView = (row) => {
  // 复制数据到表单模型用于查看
  Object.keys(formModel).forEach((key) => {
    if (key !== 'timeRange') {
      formModel[key] = row[key];
    }
  });
  // 将开始时间和结束时间组合为时间范围数组
  if (row.startTime && row.endTime) {
    // 确保时间戳是有效的数字
    const startTime = typeof row.startTime === 'number' ? row.startTime : new Date(row.startTime).getTime();
    const endTime = typeof row.endTime === 'number' ? row.endTime : new Date(row.endTime).getTime();

    if (!isNaN(startTime) && !isNaN(endTime)) {
      formModel.timeRange = [startTime, endTime];
    } else {
      formModel.timeRange = null;
    }
  } else {
    formModel.timeRange = null;
  }

  // 显示详情信息
  dialog.info({
    title: "直播课程详情",
    content: () => h("div", { style: "max-height: 400px; overflow-y: auto;" }, [
      h("p", {}, `标题：${row.title || '-'}`),
      h("p", {}, `讲师：${row.teacherLabel || '-'}`),
      h("p", {}, `分类：${row.categoryLabel || '-'}`),
      h("p", {}, `价格：¥${row.price ? row.price.toFixed(2) : '0.00'}`),
      h("p", {}, `状态：${getStatusText(row.status)}`),
      h("p", {}, `开始时间：${formatDateTime(row.startTime)}`),
      h("p", {}, `结束时间：${formatDateTime(row.endTime)}`),
      h("p", {}, `直播间ID：${row.roomId || '-'}`),
      h("p", {}, `描述：${row.description || '-'}`),
    ]),
    positiveText: "确定",
  });
};

// 处理编辑
const handleEdit = (row) => {
  Object.keys(formModel).forEach((key) => {
    if (key !== 'timeRange') {
      formModel[key] = row[key];
    }
  });
  // 将开始时间和结束时间组合为时间范围数组
  if (row.startTime && row.endTime) {
    // 确保时间戳是有效的数字
    const startTime = typeof row.startTime === 'number' ? row.startTime : new Date(row.startTime).getTime();
    const endTime = typeof row.endTime === 'number' ? row.endTime : new Date(row.endTime).getTime();

    if (!isNaN(startTime) && !isNaN(endTime)) {
      formModel.timeRange = [startTime, endTime];
    } else {
      formModel.timeRange = null;
    }
  } else {
    formModel.timeRange = null;
  }
  showModal.value = true;
};

// 处理开始直播
const handleStartLive = (row) => {
  dialog.warning({
    title: "确认开始直播",
    content: `确定要开始 "${row.title}" 的直播吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await startLive(row.id);
        if (res.code === 200) {
          message.success("直播已开始");
          loadData();
        } else {
          message.error(res.message || "开始直播失败");
        }
      } catch (error) {
        message.error("开始直播失败");
      }
    },
  });
};

// 处理结束直播
const handleEndLive = (row) => {
  dialog.warning({
    title: "确认结束直播",
    content: `确定要结束 "${row.title}" 的直播吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await endLive(row.id);
        if (res.code === 200) {
          message.success("直播已结束");
          loadData();
        } else {
          message.error(res.message || "结束直播失败");
        }
      } catch (error) {
        message.error("结束直播失败");
      }
    },
  });
};

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除直播课程 "${row.title}" 吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await deleteLiveCourse(row.id);
        if (res.code === 200) {
          message.success("删除成功");
          loadData();
        } else {
          message.error(res.message || "删除失败");
        }
      } catch (error) {
        message.error("删除失败");
      }
    },
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请至少选择一条记录");
    return;
  }

  dialog.warning({
    title: "确认批量删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await batchDeleteLiveCourses(selectedRowKeys.value);
        if (res.code === 200) {
          message.success("批量删除成功");
          selectedRowKeys.value = [];
          loadData();
        } else {
          message.error(res.message || "批量删除失败");
        }
      } catch (error) {
        message.error("批量删除失败");
      }
    },
  });
};

// 处理封面上传成功
const handleCoverUploadSuccess = (fileData) => {
  console.log("封面上传成功:", fileData);
  message.success("封面上传成功");
};

// 处理封面上传失败
const handleCoverUploadError = (error) => {
  console.error("封面上传失败:", error);
  message.error("封面上传失败");
};

// 处理分类变化
const handleCategoryChange = (value) => {
  console.log("分类变化:", value);
};

// 处理分类添加成功
const handleCategoryAddSuccess = (newTag) => {
  console.log("新分类添加成功:", newTag);
  message.success("分类添加成功并已自动选择");
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (errors) => {
    if (!errors) {
      submitLoading.value = true;
      try {
        const saveData = { ...formModel };
        
        // 将时间范围数组拆分为开始时间和结束时间
        if (saveData.timeRange && Array.isArray(saveData.timeRange) && saveData.timeRange.length === 2) {
          saveData.startTime = saveData.timeRange[0];
          saveData.endTime = saveData.timeRange[1];
        } else {
          // 如果没有选择时间范围，删除这些字段
          delete saveData.startTime;
          delete saveData.endTime;
        }
        // 删除timeRange字段，因为后端不需要这个字段
        delete saveData.timeRange;

        const res = saveData.id
          ? await updateLiveCourse(saveData)
          : await createLiveCourse(saveData);

        if (res.code === 200) {
          message.success(saveData.id ? "更新成功" : "创建成功");
          showModal.value = false;
          loadData();
        } else {
          message.error(res.message || (saveData.id ? "更新失败" : "创建失败"));
        }
      } catch (error) {
        message.error(formModel.id ? "更新失败" : "创建失败");
      } finally {
        submitLoading.value = false;
      }
    } else {
      message.error("请完善表单信息");
    }
  });
};

onMounted(() => {
  loadData();
  loadOptions();
});
</script>

<style lang="scss" scoped>
.content-card {
  margin-bottom: 20px;
}

.action-bar {
  margin: 20px 0;
}

.flex-start {
  display: flex;
  align-items: center;
  gap: 8px;
}

.m-l-10 {
  margin-left: 10px;
}
</style>
