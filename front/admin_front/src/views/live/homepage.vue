<template>
  <div class="page-container">
    <n-card title="首页推荐管理" class="content-card">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <n-form inline :label-width="80">
          <n-form-item label="标题">
            <n-input
              v-model:value="searchParams.title"
              clearable
              placeholder="请输入标题"
            />
          </n-form-item>
          <n-form-item label="状态">
            <n-select
              v-model:value="searchParams.status"
              :options="statusOptions"
              clearable
              placeholder="请选择状态"
              style="width: 150px"
            />
          </n-form-item>
          <n-form-item>
            <n-button type="primary" @click="loadData">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button class="m-l-10" @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-form-item>
        </n-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加推荐
        </n-button>
        <n-button
          class="m-l-10"
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        remote
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:sorter="handleSorterChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />

      <!-- 推荐详情对话框 -->
      <n-modal
        v-model:show="showDetailModal"
        preset="card"
        :title="
          currentData.id
            ? currentData.id && !currentData.title
              ? '添加推荐'
              : '推荐详情'
            : '添加推荐'
        "
        style="width: 800px; max-height: 80vh"
        :closable="true"
        :mask-closable="false"
      >
        <HomepageDetailCard
          :data="currentData"
          :initial-edit-mode="!currentData.id"
          @save="handleSave"
          @toggle-status="handleToggleStatusFromDetail"
          @delete="handleDeleteFromDetail"
          @close="handleCloseDetail"
        />
      </n-modal>

      <!-- 图片预览对话框 -->
      <n-modal v-model:show="showImagePreview" preset="card" title="图片预览">
        <div class="image-preview">
          <n-image
            :src="previewImageUrl"
            :fallback-src="'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTgiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIiBmaWxsPSIjOTk5Ij7ml6DmtZXliqDovb08L3RleHQ+PC9zdmc+'"
            style="max-width: 100%; max-height: 400px"
          />
        </div>
      </n-modal>
    </n-card>
  </div>
</template>

<script setup>
import { h, ref, reactive, computed, onMounted } from "vue";
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  TrashOutline,
  PencilOutline,
  EyeOutline,
  CloseCircleOutline,
  CheckmarkCircleOutline,
  ImageOutline,
  LinkOutline,
} from "@vicons/ionicons5";
import { NImage } from "naive-ui";
import dayjs from "dayjs";
import HomepageDetailCard from "./components/HomepageDetailCard.vue";
import request from "@/api/request";

const message = window.$message || {
  success: (msg) => console.log('Success:', msg),
  error: (msg) => console.error('Error:', msg),
  warning: (msg) => console.warn('Warning:', msg),
  info: (msg) => console.info('Info:', msg)
};

const dialog = window.$dialog || {
  warning: (options) => {
    if (confirm(options.content)) {
      options.onPositiveClick && options.onPositiveClick();
    }
  }
};

// 表格数据
const loading = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 搜索参数
const searchParams = reactive({
  title: "",
  status: null,
});

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  sortField: null,
  sortOrder: null,
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "禁用", value: false },
];

// 详情/编辑对话框
const showDetailModal = ref(false);
const currentData = ref({});

// 图片预览
const showImagePreview = ref(false);
const previewImageUrl = ref("");

// 表格列
const createColumns = () => {
  return [
    {
      type: "selection",
      align: "center",
    },
    {
      title: "ID",
      key: "id",
      width: 80,
      align: "center",
    },
    {
      title: "封面图片",
      key: "coverImageFullUrl",
      width: 120,
      align: "center",
      render(row) {
        return h(
          "div",
          {
            style:
              "display: flex; justify-content: center; align-items: center;",
          },
          [
            h(NImage, {
              src:
                row.coverImageFullUrl ||
                "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg",
              width: 80,
              height: 50,
              objectFit: "cover",
              style: "border-radius: 4px; cursor: pointer;",
              onClick: () => {
                previewImageUrl.value =
                  row.coverImageFullUrl ||
                  "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg";
                showImagePreview.value = true;
              },
              fallbackSrc:
                "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg",
            }),
          ]
        );
      },
    },
    {
      title: "标题",
      key: "title",
      width: 200,
      align: "center",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "副标题",
      key: "subtitle",
      width: 180,
      align: "center",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "排序",
      key: "sortOrder",
      width: 100,
      align: "center",
      sorter: true,
      render(row) {
        return h(
          "n-tag",
          {
            type: "info",
            size: "small",
            round: true,
            style:
              "background-color: #f0f9ff; color: #0369a1; border: 1px solid #bae6fd; padding: 0 10px; font-weight: 500;",
          },
          { default: () => row.sortOrder }
        );
      },
    },
    {
      title: "状态",
      key: "status",
      width: 100,
      align: "center",
      render(row) {
        return h(
          "n-tag",
          {
            type: row.status ? "success" : "error",
            size: "medium",
            style: row.status
              ? "background-color: #18a058; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;"
              : "background-color: #d03050; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;",
          },
          {
            default: () => (row.status ? "启用" : "禁用"),
            icon: () =>
              h(
                "n-icon",
                {
                  size: 16,
                  style: "margin-right: 4px;",
                },
                {
                  default: () =>
                    h(row.status ? CheckmarkCircleOutline : CloseCircleOutline),
                }
              ),
          }
        );
      },
    },
    {
      title: "创建时间",
      key: "createdAt",
      width: 180,
      align: "center",
      render(row) {
        return dayjs(row.createdAt).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 250,
      fixed: "right",
      align: "center",
      render(row) {
        return h(
          "div",
          { style: "display: flex; justify-content: center; gap: 8px;" },
          [
            h(
              "n-button",
              {
                size: "small",
                type: "primary",
                style:
                  "padding: 0 10px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
                onClick: () => handleView(row),
              },
              {
                default: () => "查看",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(EyeOutline),
                    }
                  ),
              }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: "info",
                style:
                  "padding: 0 10px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
                onClick: () => handleEdit(row),
              },
              {
                default: () => "编辑",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(PencilOutline),
                    }
                  ),
              }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: row.status ? "error" : "success",
                style: row.status
                  ? "padding: 0 10px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;"
                  : "padding: 0 10px; border-radius: 15px; background-color: #52c41a; border: none; color: white;",
                onClick: () => handleToggleStatus(row),
              },
              {
                default: () => (row.status ? "禁用" : "启用"),
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () =>
                        h(
                          row.status
                            ? CloseCircleOutline
                            : CheckmarkCircleOutline
                        ),
                    }
                  ),
              }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: "error",
                style:
                  "padding: 0 10px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
                onClick: () => handleDelete(row),
              },
              {
                default: () => "删除",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(TrashOutline),
                    }
                  ),
              }
            ),
          ]
        );
      },
    },
  ];
};

const columns = createColumns();

// API请求函数
const api = {
  // 获取推荐列表
  getList: (params) => {
    return request.get("/homepage-recommendations", { params });
  },
  // 创建推荐
  create: (data) => {
    return request.post("/homepage-recommendations", data);
  },
  // 更新推荐
  update: (id, data) => {
    return request.put(`/homepage-recommendations/${id}`, data);
  },
  // 删除推荐
  delete: (id) => {
    return request.delete(`/homepage-recommendations/${id}`);
  },
  // 批量删除
  batchDelete: (ids) => {
    return request.delete("/homepage-recommendations/batch", { data: ids });
  },
  // 切换状态
  toggleStatus: (id) => {
    return request.put(`/homepage-recommendations/toggle-status/${id}`);
  },
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      title: searchParams.title || undefined,
      status: searchParams.status !== null ? searchParams.status : undefined,
      sortField: pagination.sortField || undefined,
      sortOrder: pagination.sortOrder || undefined,
    };

    const response = await api.getList(params);

    if (response.code === 200) {
      tableData.value = response.data.records || response.data || [];
      pagination.pageCount = Math.ceil((response.data.total || tableData.value.length) / pagination.pageSize);
      pagination.itemCount = response.data.total || tableData.value.length;
    } else {
      message.error(response.message || "加载数据失败");
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    message.error("加载数据失败");

    // 如果API失败，使用模拟数据作为后备
    const mockData = [
      {
        id: 1,
        title: "春季新课程上线",
        subtitle: "精品课程，限时优惠",
        coverImageFullUrl: "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg",
        linkType: 1,
        linkUrl: "/course/list",
        sortOrder: 1,
        status: true,
        description: "春季新课程全面上线，涵盖多个学科，专业讲师授课，限时优惠中！",
        createdAt: "2024-01-15 10:30:00",
      },
      {
        id: 2,
        title: "名师直播课",
        subtitle: "互动教学，实时答疑",
        coverImageFullUrl: "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg",
        linkType: 2,
        linkUrl: "/live/list",
        sortOrder: 2,
        status: true,
        description: "名师在线直播授课，实时互动答疑，提升学习效果。",
        createdAt: "2024-01-14 14:20:00",
      },
      {
        id: 3,
        title: "学习打卡活动",
        subtitle: "坚持学习，赢取奖励",
        coverImageFullUrl: "https://pic.616pic.com/bg_w1180/00/06/22/MkNBjTIKte.jpg",
        linkType: 3,
        linkUrl: "/statistics/checkin",
        sortOrder: 3,
        status: false,
        description: "参与学习打卡活动，坚持学习可获得丰厚奖励。",
        createdAt: "2024-01-13 09:15:00",
      },
    ];

    tableData.value = mockData;
    pagination.pageCount = 1;
    pagination.itemCount = mockData.length;
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach((key) => {
    searchParams[key] = "";
  });
  searchParams.status = null;
  pagination.page = 1;
  pagination.sortField = null;
  pagination.sortOrder = null;
  loadData();
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 处理排序变化
const handleSorterChange = (sorter) => {
  if (sorter) {
    pagination.sortField = sorter.columnKey;
    pagination.sortOrder = sorter.order;
  } else {
    pagination.sortField = null;
    pagination.sortOrder = null;
  }
  loadData();
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 查看推荐
const handleView = (row) => {
  currentData.value = { ...row };
  showDetailModal.value = true;
};

// 添加推荐
const handleAdd = () => {
  currentData.value = {
    id: null,
    title: "",
    subtitle: "",
    coverImage: "",
    coverImageFullUrl: "",
    linkType: 1,
    linkTargetId: null,
    linkUrl: "",
    sortOrder: 0,
    status: true,
    description: "",
    createdAt: new Date().toISOString(),
  };
  showDetailModal.value = true;
};

// 链接类型选项
const linkTypeOptions = [
  { label: "课程", value: 1 },
  { label: "直播", value: 2 },
  { label: "外部链接", value: 3 },
];

// 编辑推荐
const handleEdit = (row) => {
  currentData.value = { ...row };
  showDetailModal.value = true;
};

// 保存数据
const handleSave = async (data) => {
  try {
    let response;
    if (data.id) {
      // 更新现有推荐
      response = await api.update(data.id, data);
    } else {
      // 添加新推荐
      response = await api.create(data);
    }

    if (response.code === 200) {
      message.success(data.id ? "推荐更新成功" : "推荐添加成功");
      showDetailModal.value = false;
      loadData();
    } else {
      message.error(response.message || (data.id ? "更新失败" : "添加失败"));
    }
  } catch (error) {
    console.error("保存失败:", error);
    message.error(data.id ? "更新失败" : "添加失败");
  }
};

// 切换状态（从详情页调用）
const handleToggleStatusFromDetail = (data) => {
  const index = tableData.value.findIndex((item) => item.id === data.id);
  if (index !== -1) {
    tableData.value[index].status = !tableData.value[index].status;
    message.success(`推荐已${tableData.value[index].status ? "启用" : "禁用"}`);
    currentData.value.status = tableData.value[index].status;
  }
};

// 删除推荐（从详情页调用）
const handleDeleteFromDetail = (data) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除推荐"${data.title}"吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      const index = tableData.value.findIndex((item) => item.id === data.id);
      if (index !== -1) {
        tableData.value.splice(index, 1);
        message.success("推荐删除成功");
        showDetailModal.value = false;
        loadData();
      }
    },
  });
};

// 关闭详情弹窗
const handleCloseDetail = () => {
  showDetailModal.value = false;
};

// 切换状态
const handleToggleStatus = (row) => {
  const action = row.status ? "禁用" : "启用";
  dialog.warning({
    title: `${action}推荐`,
    content: `确定要${action}这个推荐吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const response = await api.toggleStatus(row.id);
        if (response.code === 200) {
          message.success(`${action}成功`);
          loadData();
        } else {
          message.error(response.message || `${action}失败`);
        }
      } catch (error) {
        console.error(`${action}失败:`, error);
        message.error(`${action}失败`);
      }
    },
  });
};

// 删除推荐
const handleDelete = (row) => {
  dialog.warning({
    title: "删除推荐",
    content: "确定要删除这个推荐吗？删除后无法恢复。",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const response = await api.delete(row.id);
        if (response.code === 200) {
          message.success("删除成功");
          loadData();
        } else {
          message.error(response.message || "删除失败");
        }
      } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败");
      }
    },
  });
};

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning("请选择要删除的推荐");
    return;
  }

  dialog.warning({
    title: "批量删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个推荐吗？删除后无法恢复。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const response = await api.batchDelete(selectedRowKeys.value);
        if (response.code === 200) {
          message.success("批量删除成功");
          selectedRowKeys.value = [];
          loadData();
        } else {
          message.error(response.message || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除失败:", error);
        message.error("批量删除失败");
      }
    },
  });
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;

  .content-card {
    .search-bar {
      margin-bottom: 16px;
      border-radius: 6px;
    }

    .action-bar {
      margin-bottom: 16px;
    }
  }
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.m-l-10 {
  margin-left: 10px;
}
</style>
