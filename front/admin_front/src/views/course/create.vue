<template>
  <div class="page-container">
    <n-card title="创建课程" class="content-card">
      <n-steps
        :current="currentStep + 1"
        :status="currentError ? 'error' : undefined"
      >
        <n-step title="基本信息" description="填写课程基本信息" />
        <n-step title="章节设置" description="设置课程章节和课时" />
      </n-steps>

      <div class="step-content m-t-20">
        <!-- 步骤1: 基本信息 -->
        <div v-show="currentStep === 0">
          <CourseForm
            ref="courseFormRef"
            v-model:form="basicForm"
            :is-edit="false"
            @cover-upload-success="handleCoverUploadSuccess"
            @cover-upload-error="handleCoverUploadError"
          />
        </div>

        <!-- 步骤2: 章节设置 -->
        <div v-show="currentStep === 1">
          <ChapterManager
            v-model:chapters="chapterForm.chapters"
            v-model:expanded="expandedChapters"
            :course-id="null"
            @video-upload-success="handleVideoUploadSuccess"
            @video-upload-error="handleVideoUploadError"
            @video-loaded="handleVideoLoaded"
            @duration-change="handleDurationChange"
          />
        </div>
      </div>

      <!-- 步骤导航按钮 -->
      <div class="step-actions m-t-20">
        <n-space justify="center">
          <n-button v-if="currentStep > 0" @click="prevStep"> 上一步 </n-button>
          <n-button v-if="currentStep < 1" type="primary" @click="nextStep">
            下一步
          </n-button>
          <n-button
            v-else
            type="success"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            提交
          </n-button>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup>
  import { ref, reactive } from "vue";
  import { useRouter } from "vue-router";
  import { useMessage } from "naive-ui";
  import {
    createCourse,
    createChapter,
    createLesson,
  } from "@/api/course";
  import CourseForm from "@/components/Course/CourseForm.vue";
  import ChapterManager from "@/components/Course/ChapterManager.vue";

  const router = useRouter();
  const message = useMessage();

  // 当前步骤
  const currentStep = ref(0);
  const currentError = ref(false);
  const courseFormRef = ref(null);
  const submitLoading = ref(false);

  // 章节展开状态控制
  const expandedChapters = ref([]);

  // 基本信息表单数据
  const basicForm = reactive({
    title: "",
    subtitle: "",
    description: "",
    coverImage: "",
    teacherId: null,
    categoryId: null,
    price: 0,
    originalPrice: 0,
    level: 1,
    ageGroup: 0,
    isLive: false,
    isFeatured: false,
    isSpecialTraining: false,
    isOneOnOne: false,
    status: 1,
    contactInfoPhone: "",
    contactInfoWechat: "",
    contactInfoRemark: "",
  });

  // 章节信息表单数据
  const chapterForm = reactive({
    chapters: [],
  });

  // 步骤导航

  // 下一步
  const nextStep = async () => {
    if (currentStep.value === 0) {
      // 先验证表单
      try {
        await courseFormRef.value.validate();
        currentError.value = false;
        currentStep.value++;
      } catch (errors) {
        console.log('表单验证失败:', errors);
        currentError.value = true;
      }
    } else {
      currentStep.value++;
    }
  };

  // 上一步
  const prevStep = () => {
    currentStep.value--;
  };

  // 处理封面上传成功
  const handleCoverUploadSuccess = (fileData) => {
    console.log("封面上传成功:", fileData);
    message.success("封面上传成功");
  };

  // 处理封面上传失败
  const handleCoverUploadError = (error) => {
    console.error("封面上传失败:", error);
    message.error("封面上传失败");
  };

  // 处理视频上传成功
  const handleVideoUploadSuccess = (fileData, chapterIndex, lessonIndex) => {
    console.log(
      "视频上传成功:",
      fileData,
      "章节:",
      chapterIndex,
      "课时:",
      lessonIndex
    );

    // 自动设置视频时长
    if (
      fileData.duration &&
      chapterIndex !== undefined &&
      lessonIndex !== undefined
    ) {
      chapterForm.chapters[chapterIndex].lessons[lessonIndex].duration =
        Math.round(fileData.duration);
      message.success(
        `视频上传成功，时长：${fileData.durationFormatted || "未知"}`
      );
    } else {
      message.success("视频上传成功");
    }
  };

  // 处理视频上传失败
  const handleVideoUploadError = (error) => {
    console.error("视频上传失败:", error);
    message.error("视频上传失败");
  };

  // 处理视频加载完成
  const handleVideoLoaded = (videoData, chapterIndex, lessonIndex) => {
    console.log(
      "视频加载完成:",
      videoData,
      "章节:",
      chapterIndex,
      "课时:",
      lessonIndex
    );

    // 自动设置视频时长
    if (
      videoData.duration &&
      chapterIndex !== undefined &&
      lessonIndex !== undefined
    ) {
      chapterForm.chapters[chapterIndex].lessons[lessonIndex].duration =
        Math.round(videoData.duration);
    }
  };

  // 处理视频时长变化
  const handleDurationChange = (duration, chapterIndex, lessonIndex) => {
    console.log(
      "视频时长变化:",
      duration,
      "章节:",
      chapterIndex,
      "课时:",
      lessonIndex
    );

    if (duration && chapterIndex !== undefined && lessonIndex !== undefined) {
      chapterForm.chapters[chapterIndex].lessons[lessonIndex].duration =
        Math.round(duration);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      submitLoading.value = true;

      // 构建提交数据
      const courseData = {
        ...basicForm,
        isLive: basicForm.isLive ? 1 : 0,
        isFeatured: basicForm.isFeatured ? 1 : 0,
        isSpecialTraining: basicForm.isSpecialTraining ? 1 : 0,
        isOneOnOne: basicForm.isOneOnOne ? 1 : 0,
      };

      // 提交课程信息
      const courseRes = await createCourse(courseData);

      if (courseRes.code === 200) {
        // 现在后端返回完整的课程对象，获取课程ID
        const courseId = courseRes.data.id;

        // 提交章节和课时信息
        for (const chapter of chapterForm.chapters) {
          // 创建章节
          const chapterData = {
            courseId,
            title: chapter.title,
            description: chapter.description,
            sortOrder: chapter.sortOrder,
          };

          const chapterRes = await createChapter(chapterData);

          if (chapterRes.code === 200) {
            // 现在后端返回完整的章节对象，获取章节ID
            const chapterId = chapterRes.data.id;

            // 创建课时
            if (chapter.lessons && chapter.lessons.length > 0) {
              for (const lesson of chapter.lessons) {
                const lessonData = {
                  courseId,
                  chapterId,
                  title: lesson.title,
                  description: lesson.description,
                  videoUrl: lesson.videoUrl,
                  duration: lesson.duration,
                  isFree: lesson.isFree,
                  sortOrder: lesson.sortOrder,
                };

                await createLesson(lessonData);
              }
            }
          }
        }

        message.success("课程创建成功");
        router.push("/course/list");
      } else {
        message.error(courseRes.message || "创建失败");
      }
    } catch (error) {
      console.error("提交失败:", error);
      message.error("提交失败");
    } finally {
      submitLoading.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  .content-card {
    margin-bottom: 20px;
  }

  .step-content {
    min-height: 300px;
    padding: 20px 0;
  }

  .m-t-10 {
    margin-top: 10px;
  }

  .m-t-20 {
    margin-top: 20px;
  }

  .upload-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .chapter-card {
    margin-top: 20px;
  }

  .chapter-action-bar {
    margin-bottom: 20px;
  }

  .lesson-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .lesson-title {
    font-weight: 500;
    font-size: 16px;
  }

  .lesson-edit-form {
    margin-top: 10px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  .step-actions {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
</style>
