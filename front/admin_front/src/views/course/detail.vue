<template>
  <div class="page-container">
    <n-card title="课程详情" class="content-card">
      <!-- 顶部操作按钮 -->
      <template #header-extra>
        <n-space>
          <n-button @click="goBack">
            <template #icon>
              <n-icon><ArrowBackOutline /></n-icon>
            </template>
            返回列表
          </n-button>
          <n-button type="primary" @click="goToEdit">
            <template #icon>
              <n-icon><PencilOutline /></n-icon>
            </template>
            编辑课程
          </n-button>
        </n-space>
      </template>

      <n-tabs type="line" >
        <n-tab-pane name="1" tab="基本信息">
          <n-grid :cols="24" :x-gap="24">
            <!-- 左侧信息区域 -->
            <n-gi :span="16">
              <n-descriptions
                :column="2"
                bordered
                label-placement="left"
                class="course-descriptions"
              >
                <n-descriptions-item label="课程ID">
                  {{ courseData.id }}
                </n-descriptions-item>
                <n-descriptions-item label="课程标题">
                  {{ courseData.title }}
                </n-descriptions-item>
                <n-descriptions-item label="课程副标题">
                  {{ courseData.subtitle || "-" }}
                </n-descriptions-item>
                <n-descriptions-item label="讲师">
                  {{ courseData.teacherName || "-" }}
                </n-descriptions-item>
                <n-descriptions-item label="分类">
                  {{ courseData.categoryName || "-" }}
                </n-descriptions-item>
                <n-descriptions-item label="价格">
                  <n-tag v-if="courseData.price && courseData.price > 0" type="error">
                    ¥{{ courseData.price?.toFixed(2) }}
                  </n-tag>
                  <n-tag v-else type="success">免费</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="原价">
                  {{ courseData.originalPrice ? `¥${courseData.originalPrice.toFixed(2)}` : "-" }}
                </n-descriptions-item>
                <n-descriptions-item label="难度级别">
                  <n-tag type="info">{{ courseData.levelLabel || "-" }}</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="适合年龄段">
                  <n-tag type="warning">{{ courseData.ageGroupLabel || "-" }}</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="状态">
                  <n-tag :type="courseData.status == true ? 'success' : 'error'">
                    {{ courseData.status == true ? "上架" : "下架" }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="课程总时长">
                  {{ formatDuration(courseData.duration) }}
                </n-descriptions-item>
                <n-descriptions-item label="课时总数">
                  {{ courseData.lessonCount || 0 }} 节
                </n-descriptions-item>
                <n-descriptions-item label="学习人数">
                  {{ courseData.studentCount || 0 }} 人
                </n-descriptions-item>
                <n-descriptions-item label="课程评分">
                  <n-rate :value="courseData.rating || 5" readonly size="small" />
                  <span class="m-l-8">{{ courseData.rating || 5 }}.0</span>
                </n-descriptions-item>
                <n-descriptions-item label="联系电话">
                  {{ courseData.contactInfoPhone || "-" }}
                </n-descriptions-item>
                <n-descriptions-item label="联系微信">
                  {{ courseData.contactInfoWechat || "-" }}
                </n-descriptions-item>
                <n-descriptions-item label="创建时间" :span="2">
                  {{ formatDate(courseData.createdAt) }}
                </n-descriptions-item>
              </n-descriptions>

              <!-- 课程特性 -->
              <n-card title="课程特性" size="small" class="m-t-20">
                <n-space>
                  <n-tag v-if="courseData.isLive" type="primary">直播课</n-tag>
                  <n-tag v-if="courseData.isFeatured" type="warning">推荐课程</n-tag>
                  <n-tag v-if="courseData.isSpecialTraining" type="error">特训营</n-tag>
                  <n-tag v-if="courseData.isOneOnOne" type="success">一对一</n-tag>
                  <n-tag v-if="!courseData.isLive && !courseData.isFeatured && !courseData.isSpecialTraining && !courseData.isOneOnOne" type="default">
                    普通课程
                  </n-tag>
                </n-space>
              </n-card>

              <!-- 课程描述 -->
              <n-card title="课程描述" size="small" class="m-t-20">
                <div class="course-description">
                  {{ courseData.description || "暂无描述" }}
                </div>
              </n-card>

              <!-- 联系备注 -->
              <n-card v-if="courseData.contactInfoRemark" title="联系备注" size="small" class="m-t-20">
                <div class="contact-remark">
                  {{ courseData.contactInfoRemark }}
                </div>
              </n-card>
            </n-gi>

            <!-- 右侧封面区域 -->
            <n-gi :span="8">
              <n-card title="课程封面" size="small">
                <div class="cover-preview">
                  <n-image
                    :src="courseData.fullCoverImageUrl || 'https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/default/course-cover.jpg'"
                    :fallback-src="'https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/default/course-cover.jpg'"
                    object-fit="scale-down"
                    class="cover-image"
                  />
                </div>
              </n-card>

              <!-- 统计信息 -->
              <n-card title="统计信息" size="small" class="m-t-20">
                <n-statistic
                  label="学习人数"
                  :value="courseData.studentCount || 0"
                  class="m-b-12"
                >
                  <template #suffix>人</template>
                </n-statistic>
                <n-statistic
                  label="课时数量"
                  :value="courseData.lessonCount || 0"
                  class="m-b-12"
                >
                  <template #suffix>节</template>
                </n-statistic>
                <n-statistic
                  label="课程评分"
                  :value="courseData.rating || 5"
                  class="m-b-12"
                />
                <n-statistic
                  label="评价数量"
                  :value="courseData.reviewCount || 0"
                >
                  <template #suffix>条</template>
                </n-statistic>
              </n-card>
            </n-gi>
          </n-grid>
        </n-tab-pane>

        <n-tab-pane name="2" tab="章节内容">
          <n-card title="章节与课时" size="small">
            <n-empty v-if="!chapters.length" description="暂无章节内容">
              <template #extra>
                <n-button size="small" @click="goToEdit">去添加章节</n-button>
              </template>
            </n-empty>
            
            <n-collapse v-else v-model:expanded-names="expandedChapters">
              <n-collapse-item
                v-for="(chapter, index) in chapters"
                :key="chapter.id"
                :name="index"
                :title="`第${index + 1}章：${chapter.title}`"
              >
                <template #header-extra>
                  <n-space size="small">
                    <n-tag size="small">{{ chapter.lessons?.length || 0 }}课时</n-tag>
                    <n-tag
                      v-if="getChapterVideoStats(chapter).videoCount > 0"
                      type="info"
                      size="small"
                    >
                      {{ getChapterVideoStats(chapter).videoCount }}个视频
                    </n-tag>
                    <n-tag
                      v-if="getChapterVideoStats(chapter).totalDuration > 0"
                      type="success"
                      size="small"
                    >
                      {{ getChapterVideoStats(chapter).totalDurationFormatted }}
                    </n-tag>
                  </n-space>
                </template>

                <div class="chapter-content">
                  <div v-if="chapter.description" class="chapter-description m-b-16">
                    {{ chapter.description }}
                  </div>

                  <n-list v-if="chapter.lessons?.length">
                    <n-list-item
                      v-for="(lesson, lessonIndex) in chapter.lessons"
                      :key="lesson.id"
                    >
                      <n-thing
                        :title="`${lessonIndex + 1}. ${lesson.title}`"
                        :description="lesson.description"
                      >
                        <template #header-extra>
                          <n-space>
                            <n-tag v-if="lesson.isFree" type="success" size="small">免费</n-tag>
                            <n-tag v-else type="warning" size="small">付费</n-tag>
                            <n-tag type="info" size="small">{{ formatDuration(lesson.duration) }}</n-tag>
                            <!-- 视频预览按钮 -->
                            <n-button
                              v-if="lesson.videoUrl"
                              size="small"
                              type="primary"
                              @click="previewVideo(lesson, chapter)"
                            >
                              <template #icon>
                                <n-icon><PlayCircleOutline /></n-icon>
                              </template>
                              预览
                            </n-button>
                            <!-- 视频树预览按钮 -->
                            <n-button
                              v-if="lesson.videoUrl"
                              size="small"
                              type="info"
                              @click="previewVideoTree(lesson, chapter)"
                            >
                              <template #icon>
                                <n-icon><VideocamOutline /></n-icon>
                              </template>
                              播放列表
                            </n-button>
                            <n-tag v-else type="default" size="small">
                              <n-icon size="12"><VideocamOutline /></n-icon>
                              无视频
                            </n-tag>
                          </n-space>
                        </template>
                      </n-thing>
                    </n-list-item>
                  </n-list>
                  
                  <n-empty v-else description="暂无课时" size="small" />
                </div>
              </n-collapse-item>
            </n-collapse>
          </n-card>
        </n-tab-pane>
      </n-tabs>
    </n-card>

    <!-- 视频预览模态框 -->
    <VideoPreviewModal
      v-model:show="showVideoPreview"
      :video-url="previewVideoUrl"
      :title="previewVideoTitle"
      :video-info="previewVideoInfo"
    />

    <!-- 视频树预览模态框 -->
    <VideoPreviewTreeModal
      v-model:show="showVideoTreePreview"
      :title="`${courseData.title} - 课程视频`"
      :video-tree="chapters"
      @video-change="handleVideoTreeChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useMessage } from "naive-ui";
import {
  ArrowBackOutline,
  PencilOutline,
  PlayCircleOutline,
  VideocamOutline,
} from "@vicons/ionicons5";
import { getCourseDetail } from "@/api/course";
import dayjs from "dayjs";
import VideoPreviewModal from "@/components/VideoPreview/VideoPreviewModal.vue";
import VideoPreviewTreeModal from "@/components/VideoPreview/VideoPreviewTreeModal.vue";
import { formatDurationSimple, calculateChapterVideoStats, calculateCourseVideoStats } from "@/utils/timeFormat";

const route = useRoute();
const router = useRouter();
const message = useMessage();

// 课程数据
const courseData = ref({});
const chapters = ref([]);
const loading = ref(false);

// 章节展开控制
const expandedChapters = ref([]);

// 视频预览相关
const showVideoPreview = ref(false);
const previewVideoUrl = ref("");
const previewVideoTitle = ref("");
const previewVideoInfo = ref({});

// 视频树预览相关
const showVideoTreePreview = ref(false);

// 获取课程详情
const loadCourseDetail = async () => {
  const courseId = route.params.id;
  if (!courseId) {
    message.error("课程ID不存在");
    return;
  }

  loading.value = true;
  try {
    const res = await getCourseDetail(courseId);
    if (res.code === 200) {
      // 处理新的数据结构：CourseWithTeacherDTO
      const dto = res.data;
      courseData.value = {
        ...dto.course, // 课程基本信息
        ageGroupLabel: dto.ageGroupLabel,
        levelLabel: dto.levelLabel,
        teacherName: dto.teacherName,
        teacherAvatar: dto.teacherAvatar,
        categoryName: dto.categoryName,
        fullCoverImageUrl: dto.course.fullCoverImageUrl || `https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/${dto.course.coverImage}`
      };
      // 设置章节信息 - 从DTO中获取chapters
      chapters.value = dto.chapters || [];

      // 自动展开章节（如果视频数量小于12）
      initializeExpandedChapters();
    } else {
      message.error(res.message || "获取课程详情失败");
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    message.error("获取课程详情失败");
  } finally {
    loading.value = false;
  }
};

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return "0分钟";
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  if (minutes > 0) {
    return seconds > 0 ? `${minutes}分${seconds}秒` : `${minutes}分钟`;
  }
  return `${seconds}秒`;
};

// 获取章节视频统计信息
const getChapterVideoStats = (chapter) => {
  return calculateChapterVideoStats(chapter.lessons || []);
};

// 初始化展开的章节
const initializeExpandedChapters = () => {
  if (!chapters.value || chapters.value.length === 0) return;

  // 计算总视频数量
  const courseStats = calculateCourseVideoStats(chapters.value);

  // 如果视频总数小于12，默认展开所有章节
  if (courseStats.videoCount < 12) {
    expandedChapters.value = chapters.value.map((_, index) => index);
  } else {
    // 否则只展开第一个章节
    expandedChapters.value = [0];
  }
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return "-";
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
};

// 返回列表
const goBack = () => {
  router.push("/course/list");
};

// 跳转编辑
const goToEdit = () => {
  router.push(`/course/edit/${route.params.id}`);
};

// 预览视频
const previewVideo = (lesson, chapter) => {
  if (!lesson.videoUrl) {
    message.warning("该课时暂无视频内容");
    return;
  }

  // 构建完整的视频URL
  let videoUrl = lesson.videoUrl;
  if (!videoUrl.startsWith('http')) {
    // 如果是相对路径，构建完整的OSS URL
    videoUrl = `https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/${lesson.videoUrl}`;
  }

  previewVideoUrl.value = videoUrl;
  previewVideoTitle.value = `${lesson.title} - 视频预览`;
  previewVideoInfo.value = {
    lessonTitle: lesson.title,
    chapterTitle: chapter.title,
    duration: lesson.duration || 0,
    isFree: lesson.isFree === 1 || lesson.isFree === true,
  };

  showVideoPreview.value = true;
};

// 预览视频树
const previewVideoTree = (lesson, chapter) => {
  showVideoTreePreview.value = true;
  // 如果指定了课时，设置为默认选中
  if (lesson) {
    // 这里可以传递默认选中的视频ID
  }
};

// 处理视频树中视频变化
const handleVideoTreeChange = ({ lesson, chapter, videoUrl }) => {
  console.log('🎬 视频树中选择了新视频:', { lesson: lesson.title, chapter: chapter.title, videoUrl });
};

onMounted(() => {
  loadCourseDetail();
});
</script>

<style lang="scss" scoped>
.course-descriptions {
  .n-descriptions-item {
    padding: 12px 0;
  }
}

.cover-preview {
  text-align: center;
  
  .cover-image {
    height: 200px; // 设置固定高度
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.course-description,
.contact-remark {
  line-height: 1.6;
  color: #666;
  white-space: pre-wrap;
}

.chapter-content {
  .chapter-description {
    color: #666;
    line-height: 1.6;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
  }
}

.m-b-12 {
  margin-bottom: 12px;
}

.m-b-16 {
  margin-bottom: 16px;
}

.m-l-8 {
  margin-left: 8px;
}

.m-t-20 {
  margin-top: 20px;
}
</style> 