<template>
  <div class="page-container course-management">
    <!-- 左侧筛选面板 -->
    <div class="filter-panel">
      <n-card title="筛选条件" size="small" class="filter-card">
        <!-- 筛选操作按钮 -->
        <n-space justify="" class="filter-actions">
          <n-button
            type="primary"
            @click="applyFilters"
            size="small"
            style="margin-bottom: 10px"
            block
          >
            <template #icon>
              <n-icon><SearchOutline /></n-icon>
            </template>
            应用筛选
          </n-button>
          <n-button @click="resetFilters" size="small" block class="m-t-8">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            重置筛选
          </n-button>
        </n-space>
        <n-collapse
          :default-expanded-names="['category', 'ageGroup', 'level', 'status']"
        >
          <!-- 分类筛选 -->
          <n-collapse-item title="课程分类" name="category">
            <div class="filter-section">
              <n-select
                v-model:value="searchParams.categoryId"
                :options="categoryOptions"
                clearable
                filterable
                placeholder="请选择课程分类"
                style="width: 100%"
              />
            </div>
          </n-collapse-item>

          <!-- 年龄段筛选 -->
          <n-collapse-item title="适合年龄段" name="ageGroup">
            <div class="filter-section">
              <n-radio-group v-model:value="searchParams.ageGroupId">
                <n-space>
                  <n-radio-button :value="null" label="全部年龄段" />
                  <n-radio-button
                    v-for="ageGroup in ageGroupOptions"
                    :key="ageGroup.value"
                    :value="ageGroup.value"
                    :label="ageGroup.label"
                  />
                </n-space>
              </n-radio-group>
            </div>
          </n-collapse-item>

          <!-- 难度级别筛选 -->
          <n-collapse-item title="难度级别" name="level">
            <div class="filter-section">
              <n-radio-group v-model:value="searchParams.levelId">
                <n-space>
                  <n-radio-button :value="null" label="全部难度" />
                  <n-radio-button
                    v-for="level in levelOptions"
                    :key="level.value"
                    :value="level.value"
                    :label="level.label"
                  />
                </n-space>
              </n-radio-group>
            </div>
          </n-collapse-item>

          <!-- 课程状态筛选 -->
          <n-collapse-item title="课程状态" name="status">
            <div class="filter-section">
              <n-radio-group v-model:value="searchParams.statusId">
                <n-space>
                  <n-radio-button :value="null" label="全部状态" />
                  <n-radio-button :value="1" label="上架" />
                  <n-radio-button :value="0" label="下架" />
                </n-space>
              </n-radio-group>
            </div>
          </n-collapse-item>

          <!-- 价格筛选 -->
          <n-collapse-item title="价格范围" name="price">
            <div class="filter-section">
              <n-space size="small">
                <n-checkbox
                  v-model:checked="searchParams.isFree"
                  label="免费课程"
                />
                <div class="price-range">
                  <n-input-number
                    v-model:value="searchParams.minPrice"
                    placeholder="最低价格"
                    :min="0"
                    size="small"
                    style="width: 100%"
                  />
                  <span class="price-separator">-</span>
                  <n-input-number
                    v-model:value="searchParams.maxPrice"
                    placeholder="最高价格"
                    :min="0"
                    size="small"
                    style="width: 100%"
                  />
                </div>
              </n-space>
            </div>
          </n-collapse-item>

          <!-- 课程类型筛选 -->
          <n-collapse-item title="课程类型" name="courseType">
            <div class="filter-section">
              <n-radio-group v-model:value="searchParams.courseType">
                <n-space>
                  <n-radio-button :value="null" label="全部类型" />
                  <n-radio-button value="live" label="直播课" />
                  <n-radio-button value="featured" label="推荐课程" />
                  <n-radio-button value="special_training" label="特训营" />
                  <n-radio-button value="one_on_one" label="一对一" />
                </n-space>
              </n-radio-group>
            </div>
          </n-collapse-item>
        </n-collapse>
      </n-card>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content-area">
      <n-card title="课程管理" class="content-card">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <n-form inline :label-width="80">
            <n-form-item label="课程名称">
              <n-input
                v-model:value="searchParams.title"
                clearable
                placeholder="请输入课程名称"
                @keyup.enter="loadData"
              />
            </n-form-item>
            <n-form-item label="讲师">
              <n-select
                v-model:value="searchParams.teacherId"
                :options="teacherOptions"
                clearable
                placeholder="请选择讲师"
                style="width: 180px"
              />
            </n-form-item>

            <n-form-item>
              <n-button type="primary" @click="loadData">
                <template #icon>
                  <n-icon><SearchOutline /></n-icon>
                </template>
                搜索
              </n-button>
              <n-button class="m-l-10" @click="resetSearch">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
                重置
              </n-button>
            </n-form-item>
          </n-form>
        </div>

        <!-- 操作按钮和视图切换 -->
        <div class="action-bar">
          <div class="action-left">
            <n-button type="primary" @click="handleAdd">
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              添加课程
            </n-button>
          </div>
          
          <div class="action-right">
            <n-space>
              <n-text depth="3">共 {{ totalCount }} 门课程</n-text>
              <n-button-group size="small">
                <n-button 
                  :type="viewMode === 'card' ? 'primary' : 'default'"
                  @click="viewMode = 'card'"
                >
                  <template #icon>
                    <n-icon><AppsOutline /></n-icon>
                  </template>
                  卡片
                </n-button>
                <n-button 
                  :type="viewMode === 'table' ? 'primary' : 'default'"
                  @click="viewMode = 'table'"
                >
                  <template #icon>
                    <n-icon><ListOutline /></n-icon>
                  </template>
                  列表
                </n-button>
              </n-button-group>
            </n-space>
          </div>
        </div>

        <!-- 卡片瀑布流展示 -->
        <div v-if="viewMode === 'card'" class="cards-container">
          <div v-if="loading && !courseData.length" class="loading-container">
            <n-spin size="large">
              <div class="loading-text">正在加载课程数据...</div>
            </n-spin>
          </div>
          
          <div v-else-if="!courseData.length && !loading" class="empty-container">
            <n-empty description="暂无课程数据">
              <template #extra>
                <n-button @click="handleAdd" type="primary">
                  <template #icon>
                    <n-icon><AddOutline /></n-icon>
                  </template>
                  添加第一门课程
                </n-button>
              </template>
            </n-empty>
          </div>
          
          <div v-else class="masonry-grid" ref="masonryContainer">
            <div
              v-for="(course, index) in courseData"
              :key="course.id"
              class="course-card-wrapper"
              :data-index="index"
            >
                           <CourseCard
               :course="course"
               @view="handleView"
               @edit="handleEdit"
               @toggle-status="handleToggleStatus"
               @delete="handleDelete"
             />
            </div>
          </div>
          
          <!-- 加载更多指示器 -->
          <div 
            v-if="courseData.length > 0"
            ref="loadMoreTrigger" 
            class="load-more-trigger"
          >
            <div v-if="loading" class="loading-more">
              <n-spin size="small" />
              <span class="loading-text">加载更多课程...</span>
            </div>
            <div v-else-if="hasMore" class="load-more-hint">
              滚动到此处加载更多
            </div>
            <div v-else class="no-more-data">
              已加载全部课程
            </div>
          </div>
        </div>

        <!-- 传统表格展示 -->
        <div v-else class="table-container">
          <n-data-table
            ref="table"
            remote
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :pagination="pagination"
            :row-key="(row) => row.id"
            :scroll-x="1600"
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
            @update:sorter="handleSorterChange"
            @update:checked-row-keys="handleCheckedRowKeysChange"
          />
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup>
  import { h, ref, reactive, onMounted, watch, nextTick, onUnmounted } from "vue";
  import { useRouter } from "vue-router";
  import { useMessage, useDialog, NImage } from "naive-ui";
  import {
    SearchOutline,
    RefreshOutline,
    AddOutline,
    TrashOutline,
    PencilOutline,
    EyeOutline,
    CloseCircleOutline,
    CheckmarkCircleOutline,
    AppsOutline,
    ListOutline,
  } from "@vicons/ionicons5";
  import {
    getCourseListEnhanced,
    deleteCourse,
    updateCourse,
  } from "@/api/course";
  import { getAllTeachers } from "@/api/teacher";
  import { getAvailableTagConfigByCategory } from "@/api/tagConfig";
  import { formatDuration } from "@/utils/timeFormat";
  import dayjs from "dayjs";
  import CourseCard from "./components/CourseCard.vue";

  const router = useRouter();
  const message = useMessage();
  const dialog = useDialog();

  // 视图模式
  const viewMode = ref('card');
  
  // 表格数据（用于表格模式）
  const loading = ref(false);
  const tableData = ref([]);
  const selectedRowKeys = ref([]);

  // 卡片数据（用于卡片模式）
  const courseData = ref([]);
  const totalCount = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(20);
  const hasMore = ref(true);
  
  // DOM引用
  const masonryContainer = ref(null);
  const loadMoreTrigger = ref(null);
  
  // Intersection Observer 实例
  let intersectionObserver = null;

  // 选项数据
  const teacherOptions = ref([]);
  const categoryOptions = ref([]);
  const levelOptions = ref([]);
  const ageGroupOptions = ref([]);

  // 搜索参数
  const searchParams = reactive({
    title: "",
    teacherId: null,
    // 单选筛选参数
    categoryId: null,
    ageGroupId: null,
    levelId: null,
    statusId: null,
    courseType: null,
    // 价格筛选
    isFree: false,
    minPrice: null,
    maxPrice: null,
  });

  // 分页
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [10, 20, 30, 50],
    onChange: (page) => {
      pagination.page = page;
    },
    onUpdatePageSize: (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
    },
  });

  // 获取选项数据
  const loadOptions = async () => {
    try {
      // 获取讲师列表
      const teachersRes = await getAllTeachers({ pageNum: 1, pageSize: 1000 });
      if (teachersRes.code === 200) {
        teacherOptions.value = teachersRes.data.records.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      }

      // 获取分类列表（从TagConfig系统获取course_category）
      const categoriesRes = await getAvailableTagConfigByCategory(
        "course_category"
      );
      if (categoriesRes.code === 200) {
        categoryOptions.value = categoriesRes.data.map((item) => ({
          label: item.label,
          value: Number(item.id),
        }));
      }

      // 获取难度级别选项
      const levelRes = await getAvailableTagConfigByCategory("level");
      if (levelRes.code === 200) {
        levelOptions.value = levelRes.data.map((item) => ({
          label: item.label,
          value: Number(item.id),
        }));
      }

      // 获取年龄段选项
      const ageGroupRes = await getAvailableTagConfigByCategory("age_group");
      if (ageGroupRes.code === 200) {
        ageGroupOptions.value = ageGroupRes.data.map((item) => ({
          label: item.label,
          value: Number(item.id),
        }));
      }
    } catch (error) {
      console.error("加载选项数据失败:", error);
      message.error("加载选项数据失败");
    }
  };

  // 表格列（用于表格模式）
  const createColumns = () => {
    return [
      {
        type: "selection",
        width: 50,
        fixed: "left",
      },
      {
        title: "课程ID",
        key: "id",
        width: 80,
        fixed: "left",
      },
      {
        title: "封面",
        key: "coverImage",
        width: 100,
        render(row) {
          if (!row.fullCoverImageUrl) {
            return "无封面";
          }
          return h(NImage, {
            width: 80,
            height: 50,
            src:
              row.fullCoverImageUrl ||
              "https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/default/course-cover.jpg",
            objectFit: "cover",
            borderRadius: "6px",
            previewSrc:
              row.fullCoverImageUrl ||
              "https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/default/course-cover.jpg",
            showToolbar: true,
            showToolbarTooltip: true,
            style: {
              cursor: "pointer",
              border: "1px solid #e0e0e0",
            },
          });
        },
      },
      {
        title: "课程名称",
        key: "title",
        width: 220,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: "讲师",
        key: "teacherName",
        width: 120,
        render(row) {
          return row.teacherName || "未分配讲师";
        },
      },
      {
        title: "分类",
        key: "categoryName",
        width: 120,
        render(row) {
          return row.categoryName ;
        },
      },
      {
        title: "价格(¥)",
        key: "price",
        width: 100,
        render(row) {
          return row.price > 0 ? `¥${row.price.toFixed(2)}` : "免费";
        },
      },
      {
        title: "难度",
        key: "level",
        width: 90,
        render(row) {
          return h(
            "n-tag",
            {
              type: "info",
              size: "small",
            },
            { default: () => row.levelLabel || "未知" }
          );
        },
      },
      {
        title: "年龄段",
        key: "ageGroup",
        width: 100,
        render(row) {
          return h(
            "n-tag",
            {
              type: "warning",
              size: "small",
            },
            { default: () => row.ageGroupLabel || "未知" }
          );
        },
      },
      {
        title: "课时数",
        key: "lessonCount",
        width: 80,
        align: "center",
      },
      {
        title: "学习人数",
        key: "studentCount",
        width: 100,
        align: "center",
      },
      {
        title: "状态",
        key: "status",
        width: 90,
        render(row) {
          return h(
            "n-tag",
            {
              type: row.status ? "success" : "error",
              size: "small",
            },
            {
              default: () => (row.status ? "上架" : "下架"),
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () =>
                      h(
                        row.status
                          ? CheckmarkCircleOutline
                          : CloseCircleOutline
                      ),
                  }
                ),
            }
          );
        },
      },
      {
        title: "创建时间",
        key: "createdAt",
        width: 160,
        render(row) {
          return dayjs(row.createdAt).format("YYYY-MM-DD HH:mm");
        },
      },
      {
        title: "操作",
        key: "actions",
        width: 300,
        fixed: "right",
        align: "center",
        render(row) {
          return h(
            "div",
            { style: "display: flex; justify-content: center; gap: 10px;" },
            [
              h(
                "n-button",
                {
                  size: "small",
                  type: "primary",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
                  onClick: () => handleView(row),
                },
                {
                  default: () => "查看",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(EyeOutline),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: "info",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
                  onClick: () => handleEdit(row),
                },
                {
                  default: () => "编辑",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(PencilOutline),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: row.status ? "warning" : "success",
                  style:
                    row.status
                      ? "padding: 0 12px; border-radius: 15px; background-color: #ff7875; border: none; color: white;"
                      : "padding: 0 12px; border-radius: 15px; background-color: #73d13d; border: none; color: white;",
                  onClick: () => handleToggleStatus(row),
                },
                {
                  default: () => (row.status ? "下架" : "上架"),
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () =>
                          h(
                            row.status
                              ? CloseCircleOutline
                              : CheckmarkCircleOutline
                          ),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: "error",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
                  onClick: () => handleDelete(row),
                },
                {
                  default: () => "删除",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(TrashOutline),
                      }
                    ),
                }
              ),
            ]
          );
        },
      },
    ];
  };

  const columns = createColumns();

  // 构建查询参数
  const buildQueryParams = (isLoadMore = false) => {
    const params = {
      pageNum: isLoadMore ? currentPage.value : 1,
      pageSize: viewMode.value === 'card' ? pageSize.value : pagination.pageSize,
    };

    // 基础搜索参数
    if (searchParams.title) {
      params.title = searchParams.title;
    }

    if (searchParams.teacherId) {
      params.teacherId = searchParams.teacherId;
    }

    // 单选筛选参数
    if (searchParams.categoryId !== null) {
      params.categoryIds = searchParams.categoryId;
    }

    if (searchParams.ageGroupId !== null) {
      params.ageGroupIds = searchParams.ageGroupId;
    }

    if (searchParams.levelId !== null) {
      params.levelIds = searchParams.levelId;
    }

    if (searchParams.statusId !== null) {
      params.statusIds = searchParams.statusId;
    }

    // 价格筛选
    if (searchParams.isFree) {
      params.isFree = true;
    }

    if (searchParams.minPrice !== null && searchParams.minPrice >= 0) {
      params.minPrice = searchParams.minPrice;
    }

    if (searchParams.maxPrice !== null && searchParams.maxPrice >= 0) {
      params.maxPrice = searchParams.maxPrice;
    }

    // 课程类型筛选
    if (searchParams.courseType !== null) {
      // 根据选择的类型设置对应参数
      if (searchParams.courseType === "live") {
        params.isLive = true;
      } else if (searchParams.courseType === "featured") {
        params.isFeatured = true;
      } else if (searchParams.courseType === "special_training") {
        params.isSpecialTraining = true;
      } else if (searchParams.courseType === "one_on_one") {
        params.isOneOnOne = true;
      }
    }

    return params;
  };

  // 加载数据
  const loadData = async (isLoadMore = false) => {
    if (loading.value && isLoadMore) return;
    
    loading.value = true;
    
    try {
      const params = buildQueryParams(isLoadMore);

      // 调用增强的查询API
      const res = await getCourseListEnhanced(params);

      if (viewMode.value === 'card') {
        // 卡片模式
        if (isLoadMore) {
          // 加载更多，追加数据
          courseData.value = [...courseData.value, ...res.data.records];
        } else {
          // 重新加载，替换数据
          courseData.value = res.data.records;
          currentPage.value = 1;
        }
        
        totalCount.value = res.data.total;
        hasMore.value = res.data.current < res.data.pages;
        
        if (isLoadMore) {
          currentPage.value = res.data.current + 1;
        }
      } else {
        // 表格模式
        tableData.value = res.data.records;
        pagination.pageCount = res.data.pages;
        pagination.page = res.data.current;
        pagination.pageSize = res.data.size;
        pagination.itemCount = res.data.total;
      }
    } catch (error) {
      console.error("加载课程数据失败:", error);
      message.error("加载课程数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 初始化无限滚动
  const initInfiniteScroll = () => {
    if (!loadMoreTrigger.value || intersectionObserver) return;

    intersectionObserver = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore.value && !loading.value) {
          loadData(true);
        }
      },
      {
        root: null,
        rootMargin: '100px',
        threshold: 0.1,
      }
    );

    intersectionObserver.observe(loadMoreTrigger.value);
  };

  // 销毁无限滚动
  const destroyInfiniteScroll = () => {
    if (intersectionObserver) {
      intersectionObserver.disconnect();
      intersectionObserver = null;
    }
  };

  // 监听视图模式变化
  watch(viewMode, (newMode) => {
    selectedRowKeys.value = [];
    
    if (newMode === 'card') {
      loadData();
      nextTick(() => {
        initInfiniteScroll();
      });
    } else {
      destroyInfiniteScroll();
      loadData();
    }
  });

  // 监听卡片数据变化，重新初始化无限滚动
  watch(courseData, () => {
    if (viewMode.value === 'card') {
      nextTick(() => {
        destroyInfiniteScroll();
        initInfiniteScroll();
      });
    }
  });

  // 应用筛选
  const applyFilters = () => {
    if (viewMode.value === 'card') {
      currentPage.value = 1;
      loadData();
    } else {
      pagination.page = 1;
      loadData();
    }
  };

  // 重置筛选
  const resetFilters = () => {
    searchParams.categoryId = null;
    searchParams.ageGroupId = null;
    searchParams.levelId = null;
    searchParams.statusId = null;
    searchParams.courseType = null;
    searchParams.isFree = false;
    searchParams.minPrice = null;
    searchParams.maxPrice = null;

    if (viewMode.value === 'card') {
      currentPage.value = 1;
      loadData();
    } else {
      pagination.page = 1;
      loadData();
    }
  };

  // 重置搜索
  const resetSearch = () => {
    searchParams.title = "";
    searchParams.teacherId = null;
    
    if (viewMode.value === 'card') {
      currentPage.value = 1;
      loadData();
    } else {
      pagination.page = 1;
      loadData();
    }
  };

  // 处理页码变化（表格模式）
  const handlePageChange = (page) => {
    pagination.page = page;
    loadData();
  };

  // 处理每页条数变化（表格模式）
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadData();
  };

  // 处理排序变化（表格模式）
  const handleSorterChange = (sorter) => {
    if (sorter) {
      pagination.sortField = sorter.columnKey;
      pagination.sortOrder = sorter.order;
    } else {
      pagination.sortField = null;
      pagination.sortOrder = null;
    }
    loadData();
  };

  // 处理选中行变化（表格模式）
  const handleCheckedRowKeysChange = (keys) => {
    selectedRowKeys.value = keys;
  };

  // 处理添加
  const handleAdd = () => {
    router.push("/course/create");
  };

  // 处理查看
  const handleView = (row) => {
    router.push(`/course/detail/${row.id}`);
  };

  // 处理编辑
  const handleEdit = (row) => {
    router.push(`/course/edit/${row.id}`);
  };

  // 处理状态切换
  const handleToggleStatus = (row) => {
    const newStatus = !row.status;
    const statusText = newStatus ? "上架" : "下架";

    dialog.warning({
      title: "确认操作",
      content: `确定要${statusText}课程 "${row.title}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          // 调用更新课程状态的API
          const updateData = {
            ...row,
            status: newStatus,
          };

          const res = await updateCourse(updateData);
          if (res.code === 200) {
            message.success(`课程${statusText}成功`);
            // 重新加载数据
            loadData();
          } else {
            message.error(res.message || `课程${statusText}失败`);
          }
        } catch (error) {
          console.error(`课程${statusText}失败:`, error);
          message.error(`课程${statusText}失败`);
        }
      },
    });
  };

  // 处理删除
  const handleDelete = (row) => {
    dialog.warning({
      title: "确认删除",
      content: `确定要删除课程 "${row.title}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const res = await deleteCourse(row.id);
          if (res.code === 200) {
            message.success("删除成功");
            loadData();
          } else {
            message.error(res.message || "删除失败");
          }
        } catch (error) {
          message.error("删除失败");
        }
      },
    });
  };



  // 监听筛选条件变化，自动应用筛选
  watch(
    () => [
      searchParams.categoryId,
      searchParams.ageGroupId,
      searchParams.levelId,
      searchParams.statusId,
      searchParams.courseType,
      searchParams.isFree,
    ],
    () => {
      applyFilters();
    },
    { deep: true }
  );

  onMounted(() => {
    loadOptions();
    loadData();
    
    if (viewMode.value === 'card') {
      nextTick(() => {
        initInfiniteScroll();
      });
    }
  });

  onUnmounted(() => {
    destroyInfiniteScroll();
  });
</script>

<style lang="scss" scoped>
  .course-management {
    display: flex;
    gap: 20px;
    min-height: calc(100vh - 120px);
  }

  .filter-panel {
    width: 280px;
    flex-shrink: 0;
  }

  .filter-card {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 140px);
    overflow-y: auto;
  }

  .filter-section {
    padding: 8px 0;
  }

  .price-range {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .price-separator {
    color: #666;
    font-size: 12px;
  }

  .filter-actions {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .content-area {
    flex: 1;
    min-width: 0;
  }

  .content-card {
    margin-bottom: 20px;
  }

  .search-bar {
    margin-bottom: 16px;
  }

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .action-left {
      display: flex;
      align-items: center;
    }

    .action-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  // 卡片容器样式
  .cards-container {
    min-height: 400px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;

    .loading-text {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  // 瀑布流网格布局
  .masonry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    align-items: start;
  }

  .course-card-wrapper {
    break-inside: avoid;
    margin-bottom: 0;
  }

  // 加载更多触发器
  .load-more-trigger {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    margin-top: 20px;

    .loading-more {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;
      font-size: 14px;

      .loading-text {
        color: #666;
      }
    }

    .load-more-hint {
      color: #999;
      font-size: 14px;
      text-align: center;
    }

    .no-more-data {
      color: #ccc;
      font-size: 14px;
      text-align: center;
      position: relative;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 60px;
        height: 1px;
        background-color: #e0e0e0;
      }

      &::before {
        left: -80px;
      }

      &::after {
        right: -80px;
      }
    }
  }

  // 表格容器
  .table-container {
    margin-top: 0;
  }

  // 响应式布局
  @media (max-width: 1400px) {
    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;
    }
  }

  @media (max-width: 1200px) {
    .course-management {
      flex-direction: column;
    }

    .filter-panel {
      width: 100%;
    }

    .filter-card {
      position: relative;
      max-height: none;
    }

    .masonry-grid {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .action-bar {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .action-right {
        justify-content: space-between;
      }
    }

    .masonry-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
</style>
