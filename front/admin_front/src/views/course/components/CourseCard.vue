<template>
  <div class="course-card">
    
    <!-- 课程封面 -->
    <div class="card-cover">
      <div 
        class="cover-image"
        :style="{
          backgroundImage: `url(${course.fullCoverImageUrl || defaultCoverUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }"
      />
    </div>
    
    <!-- 课程信息 -->
    <div class="card-content">
      <!-- 课程状态和标题区域 -->
      <div class="title-section">
        <div class="course-title" :title="course.title">
          {{ course.title }}
        </div>
        <div class="status-badge">
          <n-tag
            :type="course.status ? 'success' : 'error'"
            size="small"
            round
          >
            {{ course.status ? '上架' : '下架' }}
          </n-tag>
        </div>
      </div>
      
      <!-- 课程副标题 -->
      <div v-if="course.subtitle" class="course-subtitle" :title="course.subtitle">
        {{ course.subtitle }}
      </div>
      
      <!-- 讲师信息 -->
      <div class="teacher-info">
        <n-avatar
          v-if="course.teacherAvatar"
          :src="course.teacherAvatar"
          size="small"
          round
        />
        <n-icon v-else size="16" color="#999">
          <PersonOutline />
        </n-icon>
        <span class="teacher-name">{{ course.teacherName || '未分配讲师' }}</span>
      </div>
      
      <!-- 课程特性标签 -->
      <div v-if="getCourseTypes().length" class="course-features">
        <n-space size="small">
          <n-tag
            v-for="type in getCourseTypes()"
            :key="type.key"
            :type="type.type"
            size="small"
            round
          >
            {{ type.label }}
          </n-tag>
        </n-space>
      </div>
      
      <!-- 课程分类和难度 -->
      <div class="course-meta">
        <n-space size="small">
          <n-tag v-if="course.categoryName" type="info" size="small">
            {{ course.categoryName }}
          </n-tag>
          <n-tag v-if="course.levelLabel" type="warning" size="small">
            {{ course.levelLabel }}
          </n-tag>
          <n-tag v-if="course.ageGroupLabel" type="primary" size="small">
            {{ course.ageGroupLabel }}
          </n-tag>
        </n-space>
      </div>
      
      <!-- 价格信息 -->
      <div class="price-section">
        <div class="price-main">
          <span v-if="course.price > 0" class="current-price">
            ¥{{ course.price.toFixed(2) }}
          </span>
          <span v-else class="free-price">免费</span>
          
          <span
            v-if="course.originalPrice && course.originalPrice > course.price"
            class="original-price"
          >
            ¥{{ course.originalPrice.toFixed(2) }}
          </span>
        </div>
        
        <!-- 折扣标签 -->
        <div
          v-if="course.originalPrice && course.originalPrice > course.price"
          class="discount-badge"
        >
          <n-tag type="error" size="small" round>
            {{ Math.round((1 - course.price / course.originalPrice) * 100) }}折
          </n-tag>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <n-icon size="14" color="#666">
            <PeopleOutline />
          </n-icon>
          <span>{{ course.studentCount || 0 }}人学习</span>
        </div>
        
        <div class="stat-item">
          <n-icon size="14" color="#666">
            <TimeOutline />
          </n-icon>
          <span>{{ course.lessonCount || 0 }}课时</span>
        </div>
        
        <div v-if="course.duration" class="stat-item">
          <n-icon size="14" color="#666">
            <PlayCircleOutline />
          </n-icon>
          <span>{{ formatDuration(course.duration) }}</span>
        </div>
      </div>
      
      <!-- 评分 -->
      <div v-if="course.rating" class="rating-section">
        <n-rate :value="course.rating" readonly size="small" />
        <span class="rating-text">{{ course.rating }}.0</span>
      </div>
      
      <!-- 课程描述 -->
      <div v-if="course.description" class="course-description">
        {{ course.description }}
      </div>
      
      <!-- 创建时间 -->
      <div class="create-time">
        <n-icon size="12" color="#999">
          <CalendarOutline />
        </n-icon>
        <span>{{ formatDate(course.createdAt) }}</span>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="card-actions">
      <n-space justify="space-between">
        <n-space size="small">
          <n-button
            size="small"
            type="primary"
            @click="$emit('view', course)"
            style="border-radius: 12px; font-size: 12px;"
          >
            <template #icon>
              <n-icon size="12"><EyeOutline /></n-icon>
            </template>
            查看
          </n-button>
          
          <n-button
            size="small"
            type="info"
            @click="$emit('edit', course)"
            style="border-radius: 12px; font-size: 12px;"
          >
            <template #icon>
              <n-icon size="12"><PencilOutline /></n-icon>
            </template>
            编辑
          </n-button>
        </n-space>
        
        <n-space size="small">
          <n-button
            size="small"
            :type="course.status ? 'warning' : 'success'"
            @click="$emit('toggle-status', course)"
            style="border-radius: 12px; font-size: 12px;"
          >
            <template #icon>
              <n-icon size="12">
                <CheckmarkCircleOutline v-if="!course.status" />
                <CloseCircleOutline v-else />
              </n-icon>
            </template>
            {{ course.status ? '下架' : '上架' }}
          </n-button>
          
          <n-button
            size="small"
            type="error"
            @click="$emit('delete', course)"
            style="border-radius: 12px; font-size: 12px;"
          >
            <template #icon>
              <n-icon size="12"><TrashOutline /></n-icon>
            </template>
            删除
          </n-button>
        </n-space>
      </n-space>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import dayjs from 'dayjs';
import { formatDuration } from '@/utils/timeFormat';
import {
  PersonOutline,
  PeopleOutline,
  TimeOutline,
  PlayCircleOutline,
  CalendarOutline,
  EyeOutline,
  PencilOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  TrashOutline,
} from '@vicons/ionicons5';

// Props
const props = defineProps({
  course: {
    type: Object,
    required: true,
  },
});

// Emits
const emit = defineEmits(['view', 'edit', 'toggle-status', 'delete']);

// 默认封面
const defaultCoverUrl = 'https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/default/course-cover.jpg';

// 获取课程类型标签
const getCourseTypes = () => {
  const types = [];
  
  if (props.course.isLive) {
    types.push({ key: 'live', label: '直播课', type: 'primary' });
  }
  if (props.course.isFeatured) {
    types.push({ key: 'featured', label: '推荐', type: 'warning' });
  }
  if (props.course.isSpecialTraining) {
    types.push({ key: 'special', label: '特训营', type: 'error' });
  }
  if (props.course.isOneOnOne) {
    types.push({ key: 'oneOnOne', label: '一对一', type: 'success' });
  }
  
  return types;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD');
};
</script>

<style lang="scss" scoped>
.course-card {
  position: relative;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  border: 2px solid transparent;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}



.card-cover {
  position: relative;
  height: 180px;
  overflow: hidden;
  
  .cover-image {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5; // 加载时的背景色
  }
  

}

.card-content {
  padding: 16px;
  
  .title-section {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 12px;
    margin-bottom: 8px;
    
    .course-title {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      min-height: 44px;
    }
    
    .status-badge {
      flex-shrink: 0;
      margin-top: 2px;
    }
  }
  
  .course-subtitle {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .teacher-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    .teacher-name {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }
  
  .course-features {
    margin-bottom: 8px;
  }
  
  .course-meta {
    margin-bottom: 12px;
  }
  
  .price-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    
    .price-main {
      display: flex;
      align-items: baseline;
      gap: 8px;
      
      .current-price {
        font-size: 18px;
        font-weight: 700;
        color: #e74c3c;
      }
      
      .free-price {
        font-size: 16px;
        font-weight: 700;
        color: #27ae60;
      }
      
      .original-price {
        font-size: 14px;
        color: #999;
        text-decoration: line-through;
      }
    }
  }
  
  .stats-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    
    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #666;
    }
  }
  
  .rating-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    .rating-text {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }
  
  .course-description {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .create-time {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #999;
    margin-bottom: 12px;
  }
}

.card-actions {
  padding: 12px 16px 16px;
  border-top: 1px solid #f0f0f0;
  background: rgba(248, 249, 250, 0.5);
}

// 响应式调整
@media (max-width: 768px) {
  .course-card {
    border-radius: 12px;
  }
  
  .card-cover {
    height: 160px;
  }
  
  .card-content {
    padding: 12px;
    
    .course-title {
      font-size: 15px;
      min-height: 40px;
    }
    
    .stats-section {
      flex-direction: column;
      gap: 6px;
      align-items: flex-start;
    }
  }
  
  .card-actions {
    padding: 8px 12px 12px;
    
    .n-space {
      flex-direction: column;
      width: 100%;
      
      > div {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .course-card {
    background: #2d2d2d;
    border-color: #404040;
    
    &:hover {
      border-color: rgba(24, 160, 88, 0.3);
    }
    
    .card-content {
      .course-title {
        color: #e0e0e0;
      }
      
      .course-subtitle,
      .teacher-name {
        color: #b0b0b0;
      }
    }
    
    .card-actions {
      background: rgba(32, 32, 32, 0.5);
      border-color: #404040;
    }
  }
}
</style> 