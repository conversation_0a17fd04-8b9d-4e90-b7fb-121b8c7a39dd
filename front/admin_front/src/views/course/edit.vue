<template>
  <div class="page-container">
    <n-card title="编辑课程" class="content-card">
      <!-- 顶部操作按钮 -->
      <template #header-extra>
        <n-space>
          <n-button @click="goBack">
            <template #icon>
              <n-icon><ArrowBackOutline /></n-icon>
            </template>
            返回
          </n-button>
          <n-button type="primary" @click="goToDetail">
            <template #icon>
              <n-icon><EyeOutline /></n-icon>
            </template>
            查看详情
          </n-button>
        </n-space>
      </template>

      <n-steps
        :current="currentStep+1"
        :status="currentError ? 'error' : undefined"
      >
        <n-step title="基本信息" description="编辑课程基本信息" />
        <n-step title="章节设置" description="编辑课程章节和课时" />
      </n-steps>

      <div class="step-content m-t-20">
        <!-- 步骤1: 基本信息 -->
        <div v-show="currentStep === 0">
          <CourseForm
            ref="courseFormRef"
            v-model:form="basicForm"
            :is-edit="true"
            @cover-upload-success="handleCoverUploadSuccess"
            @cover-upload-error="handleCoverUploadError"
          />
        </div>

        <!-- 步骤2: 章节设置 -->
        <div v-show="currentStep === 1">‰
          <ChapterManager
            v-model:chapters="chapterForm.chapters"
            v-model:expanded="expandedChapters"
            :course-id="basicForm.id"
            @video-upload-success="handleVideoUploadSuccess"
            @video-upload-error="handleVideoUploadError"
            @video-loaded="handleVideoLoaded"
            @duration-change="handleDurationChange"
          />
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="step-actions m-t-20">
        <n-space justify="space-between">
          <n-button
            v-if="currentStep > 0"
            @click="prevStep"
          >
            上一步
          </n-button>
          <div v-else></div>

          <n-space>
            <n-button @click="saveAsDraft" :loading="saving">
              保存草稿
            </n-button>
            <n-button
              v-if="currentStep < 1"
              type="primary"
              @click="nextStep"
            >
              下一步
            </n-button>
            <n-button
              v-else
              type="primary"
              @click="saveCourse"
              :loading="saving"
            >
              保存课程
            </n-button>
          </n-space>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useMessage } from "naive-ui";
import {
  ArrowBackOutline,
  EyeOutline,
} from "@vicons/ionicons5";
import {
  getCourseDetail,
  updateCourse,
  createChapter,
  updateChapter,
  createLesson,
  updateLesson
} from "@/api/course";
import CourseForm from "@/components/Course/CourseForm.vue";
import ChapterManager from "@/components/Course/ChapterManager.vue";

const route = useRoute();
const router = useRouter();
const message = useMessage();

const currentStep = ref(0);
const currentError = ref(false);
const saving = ref(false);
const courseFormRef = ref(null);

// 章节展开状态控制
const expandedChapters = ref([]);

// 基本信息表单
const basicForm = reactive({
  id: null,
  title: "",
  subtitle: "",
  teacherId: null,
  categoryId: null,
  price: 0,
  originalPrice: null,
  level: null,
  ageGroup: null,
  contactInfoPhone: "",
  contactInfoWechat: "",
  description: "",
  contactInfoRemark: "",
  coverImage: "",
  status: 1,
  isLive: false,
  isFeatured: false,
  isSpecialTraining: false,
  isOneOnOne: false,
});

// 章节表单
const chapterForm = reactive({
  chapters: [],
});

// 加载课程详情

// 加载课程详情

// 加载课程详情
const loadCourseDetail = async () => {
  const courseId = route.params.id;
  if (!courseId) {
    message.error("课程ID不存在");
    return;
  }

  try {
    const res = await getCourseDetail(courseId);
    if (res.code === 200) {
      // 处理新的数据结构：CourseWithTeacherDTO
      const dto = res.data;
      const courseData = dto.course;
      
      // 填充基本信息表单
      Object.assign(basicForm, {
        id: courseData.id,
        title: courseData.title || "",
        subtitle: courseData.subtitle || "",
        teacherId: courseData.teacherId,
        categoryId: courseData.categoryId,
        price: courseData.price || 0,
        originalPrice: courseData.originalPrice,
        level: courseData.level,
        ageGroup: courseData.ageGroup,
        contactInfoPhone: courseData.contactInfoPhone || "",
        contactInfoWechat: courseData.contactInfoWechat || "",
        description: courseData.description || "",
        contactInfoRemark: courseData.contactInfoRemark || "",
        coverImage: courseData.coverImage || "",
        status: courseData.status,
        isLive: Boolean(courseData.isLive),
        isFeatured: Boolean(courseData.isFeatured),
        isSpecialTraining: Boolean(courseData.isSpecialTraining),
        isOneOnOne: Boolean(courseData.isOneOnOne),
        coverImageFullUrl:courseData.coverImageFullUrl
      });

      // 设置章节数据 - 从DTO中获取chapters
      chapterForm.chapters = dto.chapters || [];

      // 自动展开章节（如果视频数量小于12）
      initializeExpandedChapters();
    } else {
      message.error(res.message || "获取课程详情失败");
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    message.error("获取课程详情失败");
  }
};

// 步骤导航
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 先验证表单
    try {
      await courseFormRef.value.validate();
      currentError.value = false;
      currentStep.value++;
    } catch (errors) {
      console.log('表单验证失败:', errors);
      currentError.value = true;
    }
  } else {
    currentStep.value++;
  }
};

const prevStep = () => {
  currentStep.value--;
};

// 封面上传处理
const handleCoverUploadSuccess = (url) => {
  message.success("封面上传成功");
  basicForm.coverImage = url;
};

const handleCoverUploadError = (error) => {
  message.error("封面上传失败");
  console.error(error);
};

// 封面上传处理

// 处理视频上传成功
const handleVideoUploadSuccess = (fileData, chapterIndex, lessonIndex) => {
  console.log("视频上传成功:", fileData, "章节:", chapterIndex, "课时:", lessonIndex);

  // 自动设置视频时长
  if (fileData.duration && chapterIndex !== undefined && lessonIndex !== undefined) {
    chapterForm.chapters[chapterIndex].lessons[lessonIndex].duration = Math.round(fileData.duration);
    message.success(`视频上传成功，时长：${fileData.durationFormatted || formatDuration(fileData.duration)}`);
  } else {
    message.success("视频上传成功");
  }
};

// 处理视频上传失败
const handleVideoUploadError = (error) => {
  console.error("视频上传失败:", error);
  message.error("视频上传失败");
};

// 处理视频加载完成
const handleVideoLoaded = (videoData, chapterIndex, lessonIndex) => {
  console.log("视频加载完成:", videoData, "章节:", chapterIndex, "课时:", lessonIndex);

  // 自动设置视频时长
  if (videoData.duration && chapterIndex !== undefined && lessonIndex !== undefined) {
    chapterForm.chapters[chapterIndex].lessons[lessonIndex].duration = Math.round(videoData.duration);
    console.log(`已自动设置课时时长：${formatDuration(videoData.duration)}`);
  }
};

// 处理视频时长变化
const handleDurationChange = (duration, chapterIndex, lessonIndex) => {
  console.log("视频时长变化:", duration, "章节:", chapterIndex, "课时:", lessonIndex);

  if (duration && chapterIndex !== undefined && lessonIndex !== undefined) {
    chapterForm.chapters[chapterIndex].lessons[lessonIndex].duration = Math.round(duration);
  }
};

// 格式化视频时长
const formatDuration = (seconds) => {
  if (!seconds) return "0秒";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  let result = "";
  if (hours > 0) result += `${hours}小时`;
  if (minutes > 0) result += `${minutes}分钟`;
  if (remainingSeconds > 0) result += `${remainingSeconds}秒`;

  return result;
};

// 初始化展开的章节
const initializeExpandedChapters = () => {
  if (!chapterForm.chapters || chapterForm.chapters.length === 0) return;

  // 计算总视频数量
  const totalVideoCount = chapterForm.chapters.reduce((count, chapter) => {
    return count + (chapter.lessons?.filter(lesson => lesson.videoUrl)?.length || 0);
  }, 0);

  // 如果视频总数小于12，默认展开所有章节
  if (totalVideoCount < 12) {
    expandedChapters.value = chapterForm.chapters.map((_, index) => index);
  } else {
    // 否则只展开第一个章节
    expandedChapters.value = [0];
  }
};

// 保存章节和课时信息
const saveChaptersAndLessons = async () => {
  const courseId = basicForm.id;

  // 处理章节和课时的创建、更新、删除
  for (const chapter of chapterForm.chapters) {
    let chapterId = chapter.id;

    if (chapterId) {
      // 更新现有章节
      const chapterData = {
        id: chapterId,
        courseId,
        title: chapter.title,
        description: chapter.description,
        sortOrder: chapter.sortOrder,
      };

      const chapterRes = await updateChapter(chapterData);
      if (chapterRes.code !== 200) {
        throw new Error(`更新章节失败: ${chapterRes.message}`);
      }
    } else {
      // 创建新章节
      const chapterData = {
        courseId,
        title: chapter.title,
        description: chapter.description,
        sortOrder: chapter.sortOrder,
      };

      const chapterRes = await createChapter(chapterData);
      if (chapterRes.code === 200) {
        chapterId = chapterRes.data.id;
        // 更新本地数据的章节ID
        chapter.id = chapterId;
      } else {
        throw new Error(`创建章节失败: ${chapterRes.message}`);
      }
    }

    // 处理课时
    if (chapter.lessons && chapter.lessons.length > 0) {
      for (const lesson of chapter.lessons) {
        if (lesson.id) {
          // 更新现有课时
          const lessonData = {
            id: lesson.id,
            courseId,
            chapterId,
            title: lesson.title,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            duration: lesson.duration,
            isFree: lesson.isFree,
            sortOrder: lesson.sortOrder,
          };

          const lessonRes = await updateLesson(lessonData);
          if (lessonRes.code !== 200) {
            throw new Error(`更新课时失败: ${lessonRes.message}`);
          }
        } else {
          // 创建新课时
          const lessonData = {
            courseId,
            chapterId,
            title: lesson.title,
            description: lesson.description,
            videoUrl: lesson.videoUrl,
            duration: lesson.duration,
            isFree: lesson.isFree,
            sortOrder: lesson.sortOrder,
          };

          const lessonRes = await createLesson(lessonData);
          if (lessonRes.code === 200) {
            // 更新本地数据的课时ID
            lesson.id = lessonRes.data.id;
          } else {
            throw new Error(`创建课时失败: ${lessonRes.message}`);
          }
        }
      }
    }
  }
};

// 保存课程
const saveCourse = async () => {
  saving.value = true;
  try {
    // 构建课程基本信息数据（不包含chapters字段）
    const courseData = {
      ...basicForm,
      isLive: basicForm.isLive ? 1 : 0,
      isFeatured: basicForm.isFeatured ? 1 : 0,
      isSpecialTraining: basicForm.isSpecialTraining ? 1 : 0,
      isOneOnOne: basicForm.isOneOnOne ? 1 : 0,
    };

    // 更新课程基本信息
    const courseRes = await updateCourse(courseData);
    if (courseRes.code === 200) {
      // 课程基本信息更新成功后，处理章节和课时信息
      await saveChaptersAndLessons();

      message.success("课程更新成功");
      router.push("/course/list");
    } else {
      message.error(courseRes.message || "课程更新失败");
    }
  } catch (error) {
    console.error("课程更新失败:", error);
    message.error("课程更新失败");
  } finally {
    saving.value = false;
  }
};

// 保存草稿
const saveAsDraft = async () => {
  saving.value = true;
  try {
    // 构建课程基本信息数据（不包含chapters字段）
    const courseData = {
      ...basicForm,
      status: 0, // 设置为草稿状态
      isLive: basicForm.isLive ? 1 : 0,
      isFeatured: basicForm.isFeatured ? 1 : 0,
      isSpecialTraining: basicForm.isSpecialTraining ? 1 : 0,
      isOneOnOne: basicForm.isOneOnOne ? 1 : 0,
    };

    // 更新课程基本信息
    const courseRes = await updateCourse(courseData);
    if (courseRes.code === 200) {
      // 课程基本信息更新成功后，处理章节和课时信息
      await saveChaptersAndLessons();

      message.success("草稿保存成功");
    } else {
      message.error(courseRes.message || "草稿保存失败");
    }
  } catch (error) {
    console.error("草稿保存失败:", error);
    message.error("草稿保存失败");
  } finally {
    saving.value = false;
  }
};



// 导航方法
const goBack = () => {
  router.push("/course/list");
};

const goToDetail = () => {
  router.push(`/course/detail/${route.params.id}`);
};

onMounted(() => {
  loadCourseDetail();
});
</script>

<style lang="scss" scoped>
.step-content {
  min-height: 400px;
}

.current-cover {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.chapter-action-bar {
  margin-bottom: 20px;
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .lesson-title {
    font-weight: 500;
    color: #333;
  }
}

.lesson-edit-form {
  margin-top: 10px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.step-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.m-t-20 {
  margin-top: 20px;
}

.m-b-12 {
  margin-bottom: 12px;
}
</style> 