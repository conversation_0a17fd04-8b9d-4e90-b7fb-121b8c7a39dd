<template>
  <div class="page-container">
    <n-card title="学习记录统计" class="content-card">
      <n-grid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 统计卡片 -->
        <n-gi :span="6" v-for="(stat, index) in stats" :key="index">
          <n-card size="small" class="stat-card">
            <div class="stat-item">
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-value">{{ stat.value }}</div>
            </div>
          </n-card>
        </n-gi>

        <!-- 时间趋势图 -->
        <n-gi :span="24">
          <n-card title="每日学习时长趋势" size="small">
            <div class="chart-container" ref="studyTimeChartRef">
              <n-spin :show="chartLoading">
                <div style="height: 300px" ref="studyTimeChart"></div>
              </n-spin>
            </div>
          </n-card>
        </n-gi>

        <!-- 课程完成率排行 -->
        <n-gi :span="12">
          <n-card title="课程完成率排行" size="small">
            <n-data-table
              :columns="completionColumns"
              :data="completionData"
              :loading="tableLoading"
              :pagination="pagination"
              @update:page="handlePageChange"
            />
          </n-card>
        </n-gi>

        <!-- 活跃用户排行 -->
        <n-gi :span="12">
          <n-card title="用户学习时长排行" size="small">
            <n-data-table
              :columns="userColumns"
              :data="userData"
              :loading="tableLoading"
              :pagination="pagination"
              @update:page="handlePageChange"
            />
          </n-card>
        </n-gi>

        <!-- 学习记录列表 -->
        <n-gi :span="24">
          <n-card title="最近学习记录" size="small">
            <n-form ref="searchForm" inline>
              <n-form-item label="用户名">
                <n-input
                  v-model:value="searchForm.username"
                  placeholder="请输入用户名"
                  clearable
                />
              </n-form-item>
              <n-form-item label="课程名">
                <n-input
                  v-model:value="searchForm.courseName"
                  placeholder="请输入课程名"
                  clearable
                />
              </n-form-item>
              <n-form-item label="时间范围">
                <n-date-picker
                  v-model:value="searchForm.timeRange"
                  type="daterange"
                  clearable
                />
              </n-form-item>
              <n-form-item>
                <n-button type="primary" @click="handleSearch">
                  <template #icon>
                    <n-icon><SearchOutline /></n-icon>
                  </template>
                  搜索
                </n-button>
                <n-button
                  class="ml-10"
                  @click="() => (searchForm = { ...defaultSearchForm })"
                >
                  重置
                </n-button>
              </n-form-item>
            </n-form>

            <n-data-table
              :columns="recordColumns"
              :data="recordData"
              :loading="tableLoading"
              :pagination="recordPagination"
              @update:page="handleRecordPageChange"
            />
          </n-card>
        </n-gi>
      </n-grid>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { SearchOutline } from "@vicons/ionicons5";
import * as echarts from "echarts";
import {
  getLearningRecords,
  getUserLearningStats,
  getUserTotalLearningTime,
} from "@/api/learningRecord";

// 统计数据
const stats = ref([
  { title: "总学习人数", value: "0" },
  { title: "总学习时长", value: "0小时" },
  { title: "今日学习人数", value: "0" },
  { title: "今日学习时长", value: "0小时" },
]);

// 图表数据
const chartLoading = ref(false);
const studyTimeChart = ref(null);
const studyTimeChartRef = ref(null);
let myChart = null;

// 表格数据
const tableLoading = ref(false);
const recordData = ref([]);
const completionData = ref([]);
const userData = ref([]);

// 课程完成率表格列
const completionColumns = [
  {
    title: "排名",
    key: "rank",
    render(row, index) {
      return index + 1;
    },
    width: 80,
  },
  {
    title: "课程名称",
    key: "courseName",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "分类",
    key: "categoryName",
    width: 120,
  },
  {
    title: "完成率",
    key: "completionRate",
    render(row) {
      return `${row.completionRate}%`;
    },
    width: 100,
  },
  {
    title: "学习人数",
    key: "learnersCount",
    width: 100,
  },
];

// 用户学习表格列
const userColumns = [
  {
    title: "排名",
    key: "rank",
    render(row, index) {
      return index + 1;
    },
    width: 80,
  },
  {
    title: "用户名",
    key: "username",
  },
  {
    title: "学习时长",
    key: "totalTime",
    render(row) {
      return formatDuration(row.totalTime);
    },
    width: 120,
  },
  {
    title: "课程数量",
    key: "coursesCount",
    width: 100,
  },
  {
    title: "平均完成率",
    key: "avgCompletionRate",
    render(row) {
      return `${row.avgCompletionRate}%`;
    },
    width: 120,
  },
];

// 学习记录表格列
const recordColumns = [
  {
    title: "用户名",
    key: "username",
  },
  {
    title: "课程名称",
    key: "courseName",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "章节/课时",
    key: "lessonTitle",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "学习时长",
    key: "duration",
    render(row) {
      return formatDuration(row.duration);
    },
  },
  {
    title: "学习进度",
    key: "progress",
    render(row) {
      return `${row.progress}%`;
    },
  },
  {
    title: "学习时间",
    key: "learnTime",
    render(row) {
      return formatDateTime(row.learnTime);
    },
  },
];

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  itemCount: 0,
  pageCount: 1,
  showSizePicker: false,
});

const recordPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
});

// 搜索表单
const defaultSearchForm = {
  username: "",
  courseName: "",
  timeRange: null,
};

const searchForm = reactive({ ...defaultSearchForm });

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds) return "0分钟";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}小时${minutes > 0 ? minutes + "分钟" : ""}`;
  } else {
    return `${minutes}分钟`;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return "-";
  const date = new Date(dateTimeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 初始化图表
const initChart = () => {
  // 确保DOM已加载
  if (studyTimeChartRef.value) {
    if (myChart) {
      myChart.dispose();
    }

    myChart = echarts.init(studyTimeChart.value);

    const option = {
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${formatDuration(
            data.value
          )}`;
        },
      },
      xAxis: {
        type: "category",
        data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        axisLine: {
          lineStyle: {
            color: "#ccc",
          },
        },
        axisLabel: {
          color: "#666",
        },
      },
      yAxis: {
        type: "value",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "#666",
          formatter: function (value) {
            return Math.floor(value / 60) + "分钟";
          },
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#eee",
          },
        },
      },
      series: [
        {
          name: "学习时长",
          data: [2400, 3000, 2800, 3500, 4000, 3200, 2700],
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#3f84f3",
          },
          lineStyle: {
            width: 3,
            color: "#3f84f3",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(63, 132, 243, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(63, 132, 243, 0.1)",
                },
              ],
            },
          },
        },
      ],
    };

    myChart.setOption(option);

    // 响应式处理
    window.addEventListener("resize", () => myChart.resize());
  }
};

// 加载数据
const loadData = async () => {
  tableLoading.value = true;
  chartLoading.value = true;

  try {
    // 模拟数据加载
    // 实际项目中应该调用API获取数据
    stats.value = [
      { title: "总学习人数", value: "1254" },
      { title: "总学习时长", value: "8760小时" },
      { title: "今日学习人数", value: "128" },
      { title: "今日学习时长", value: "256小时" },
    ];

    // 加载记录数据
    const recordRes = await getLearningRecords({
      page: recordPagination.page,
      size: recordPagination.pageSize,
      username: searchForm.username,
      courseName: searchForm.courseName,
      startTime: searchForm.timeRange?.[0],
      endTime: searchForm.timeRange?.[1],
    });

    if (recordRes.code === 200) {
      recordData.value = recordRes.data.records || [];
      recordPagination.itemCount = recordRes.data.total || 0;
      recordPagination.pageCount = Math.ceil(
        recordPagination.itemCount / recordPagination.pageSize
      );
    }

    // 模拟课程完成率数据
    completionData.value = [
      {
        id: 1,
        courseName: "Python基础入门",
        categoryName: "编程",
        completionRate: 87,
        learnersCount: 1254,
      },
      {
        id: 2,
        courseName: "数据结构与算法",
        categoryName: "编程",
        completionRate: 75,
        learnersCount: 986,
      },
      {
        id: 3,
        courseName: "Web前端开发实战",
        categoryName: "前端",
        completionRate: 92,
        learnersCount: 1546,
      },
      {
        id: 4,
        courseName: "Java高级编程",
        categoryName: "编程",
        completionRate: 68,
        learnersCount: 875,
      },
      {
        id: 5,
        courseName: "人工智能导论",
        categoryName: "AI",
        completionRate: 63,
        learnersCount: 754,
      },
    ];

    // 模拟用户学习时长数据
    userData.value = [
      {
        id: 1,
        username: "user001",
        totalTime: 12600,
        coursesCount: 5,
        avgCompletionRate: 85,
      },
      {
        id: 2,
        username: "user002",
        totalTime: 10800,
        coursesCount: 4,
        avgCompletionRate: 92,
      },
      {
        id: 3,
        username: "user003",
        totalTime: 9000,
        coursesCount: 3,
        avgCompletionRate: 75,
      },
      {
        id: 4,
        username: "user004",
        totalTime: 7200,
        coursesCount: 3,
        avgCompletionRate: 68,
      },
      {
        id: 5,
        username: "user005",
        totalTime: 5400,
        coursesCount: 2,
        avgCompletionRate: 90,
      },
    ];
  } catch (error) {
    console.error("加载数据失败", error);
  } finally {
    tableLoading.value = false;
    chartLoading.value = false;
  }
};

// 页面切换
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 记录页面切换
const handleRecordPageChange = (page) => {
  recordPagination.page = page;
  loadData();
};

// 搜索
const handleSearch = () => {
  recordPagination.page = 1;
  loadData();
};

onMounted(() => {
  loadData();
  // 初始化图表需要在DOM渲染完成后
  setTimeout(() => {
    initChart();
  }, 100);
});

onUnmounted(() => {
  // 清理图表实例
  if (myChart) {
    window.removeEventListener("resize", myChart.resize);
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style lang="scss" scoped>
.content-card {
  margin-bottom: 20px;
}

.stat-card {
  height: 100%;
}

.stat-item {
  text-align: center;
  padding: 10px;

  .stat-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.chart-container {
  width: 100%;
  height: 100%;
}

.ml-10 {
  margin-left: 10px;
}
</style>
