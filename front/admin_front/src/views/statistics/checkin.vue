<template>
  <div class="page-container">
    <n-card title="用户打卡统计" class="content-card">
      <n-grid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 统计卡片 -->
        <n-gi :span="6" v-for="(stat, index) in stats" :key="index">
          <n-card size="small" class="stat-card">
            <div class="stat-item">
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-value">{{ stat.value }}</div>
            </div>
          </n-card>
        </n-gi>

        <!-- 打卡趋势图 -->
        <n-gi :span="24">
          <n-card title="每日打卡人数趋势" size="small">
            <div class="chart-container" ref="checkinChartRef">
              <n-spin :show="chartLoading">
                <div style="height: 300px" ref="checkinChart"></div>
              </n-spin>
            </div>
          </n-card>
        </n-gi>

        <!-- 连续打卡用户排行 -->
        <n-gi :span="12">
          <n-card title="连续打卡用户排行" size="small">
            <n-data-table
              :columns="consecutiveColumns"
              :data="consecutiveData"
              :loading="tableLoading"
              :pagination="pagination"
              @update:page="handlePageChange"
            />
          </n-card>
        </n-gi>

        <!-- 打卡次数排行 -->
        <n-gi :span="12">
          <n-card title="打卡次数排行" size="small">
            <n-data-table
              :columns="countColumns"
              :data="countData"
              :loading="tableLoading"
              :pagination="pagination"
              @update:page="handlePageChange"
            />
          </n-card>
        </n-gi>

        <!-- 打卡记录列表 -->
        <n-gi :span="24">
          <n-card title="最近打卡记录" size="small">
            <n-form ref="searchForm" inline>
              <n-form-item label="用户名">
                <n-input
                  v-model:value="searchForm.username"
                  placeholder="请输入用户名"
                  clearable
                />
              </n-form-item>
              <n-form-item label="时间范围">
                <n-date-picker
                  v-model:value="searchForm.timeRange"
                  type="daterange"
                  clearable
                />
              </n-form-item>
              <n-form-item>
                <n-button type="primary" @click="handleSearch">
                  <template #icon>
                    <n-icon><SearchOutline /></n-icon>
                  </template>
                  搜索
                </n-button>
                <n-button
                  class="ml-10"
                  @click="() => (searchForm = { ...defaultSearchForm })"
                >
                  重置
                </n-button>
              </n-form-item>
            </n-form>

            <n-data-table
              :columns="recordColumns"
              :data="recordData"
              :loading="tableLoading"
              :pagination="recordPagination"
              @update:page="handleRecordPageChange"
            />
          </n-card>
        </n-gi>
      </n-grid>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { SearchOutline } from "@vicons/ionicons5";
import * as echarts from "echarts";
import { getUserCheckins, getConsecutiveDays } from "@/api/userInteraction";

// 统计数据
const stats = ref([
  { title: "总打卡人数", value: "0" },
  { title: "总打卡次数", value: "0" },
  { title: "今日打卡人数", value: "0" },
  { title: "最高连续打卡", value: "0天" },
]);

// 图表数据
const chartLoading = ref(false);
const checkinChart = ref(null);
const checkinChartRef = ref(null);
let myChart = null;

// 表格数据
const tableLoading = ref(false);
const recordData = ref([]);
const consecutiveData = ref([]);
const countData = ref([]);

// 连续打卡表格列
const consecutiveColumns = [
  {
    title: "排名",
    key: "rank",
    render(row, index) {
      return index + 1;
    },
    width: 80,
  },
  {
    title: "用户名",
    key: "username",
  },
  {
    title: "连续打卡天数",
    key: "consecutiveDays",
    render(row) {
      return `${row.consecutiveDays}天`;
    },
    width: 120,
  },
  {
    title: "最近打卡时间",
    key: "lastCheckinTime",
    render(row) {
      return formatDate(row.lastCheckinTime);
    },
    width: 150,
  },
];

// 打卡次数表格列
const countColumns = [
  {
    title: "排名",
    key: "rank",
    render(row, index) {
      return index + 1;
    },
    width: 80,
  },
  {
    title: "用户名",
    key: "username",
  },
  {
    title: "打卡总次数",
    key: "checkinCount",
    width: 100,
  },
  {
    title: "首次打卡时间",
    key: "firstCheckinTime",
    render(row) {
      return formatDate(row.firstCheckinTime);
    },
    width: 150,
  },
];

// 打卡记录表格列
const recordColumns = [
  {
    title: "用户名",
    key: "username",
  },
  {
    title: "打卡时间",
    key: "checkinTime",
    render(row) {
      return formatDateTime(row.checkinTime);
    },
    width: 180,
  },
  {
    title: "打卡内容",
    key: "content",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "连续天数",
    key: "consecutiveDays",
    render(row) {
      return `${row.consecutiveDays}天`;
    },
    width: 100,
  },
  {
    title: "设备/IP",
    key: "device",
    width: 150,
  },
];

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  itemCount: 0,
  pageCount: 1,
  showSizePicker: false,
});

const recordPagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
});

// 搜索表单
const defaultSearchForm = {
  username: "",
  timeRange: null,
};

const searchForm = reactive({ ...defaultSearchForm });

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")}`;
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return "-";
  const date = new Date(dateTimeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 初始化图表
const initChart = () => {
  // 确保DOM已加载
  if (checkinChartRef.value) {
    if (myChart) {
      myChart.dispose();
    }

    myChart = echarts.init(checkinChart.value);

    // 获取最近30天的日期标签
    const dateLabels = [];
    const now = new Date();
    for (let i = 29; i >= 0; i--) {
      const d = new Date(now);
      d.setDate(d.getDate() - i);
      dateLabels.push(`${d.getMonth() + 1}/${d.getDate()}`);
    }

    const option = {
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${data.value}人`;
        },
      },
      xAxis: {
        type: "category",
        data: dateLabels,
        axisLine: {
          lineStyle: {
            color: "#ccc",
          },
        },
        axisLabel: {
          color: "#666",
          interval: 5, // 每5天显示一个标签
          rotate: 0,
        },
      },
      yAxis: {
        type: "value",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "#666",
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#eee",
          },
        },
      },
      series: [
        {
          name: "打卡人数",
          data: [
            120, 132, 101, 134, 90, 180, 210, 150, 180, 190, 220, 110, 85, 90,
            110, 130, 140, 170, 165, 180, 190, 210, 220, 200, 190, 240, 230,
            225, 215, 235,
          ],
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#19be6b",
          },
          lineStyle: {
            width: 3,
            color: "#19be6b",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(25, 190, 107, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(25, 190, 107, 0.1)",
                },
              ],
            },
          },
        },
      ],
    };

    myChart.setOption(option);

    // 响应式处理
    window.addEventListener("resize", () => myChart.resize());
  }
};

// 加载数据
const loadData = async () => {
  tableLoading.value = true;
  chartLoading.value = true;

  try {
    // 模拟数据加载
    // 实际项目中应该调用API获取数据
    stats.value = [
      { title: "总打卡人数", value: "1857" },
      { title: "总打卡次数", value: "43265" },
      { title: "今日打卡人数", value: "235" },
      { title: "最高连续打卡", value: "120天" },
    ];

    // 加载打卡记录数据
    const recordRes = await getUserCheckins({
      page: recordPagination.page,
      size: recordPagination.pageSize,
      username: searchForm.username,
      startTime: searchForm.timeRange?.[0],
      endTime: searchForm.timeRange?.[1],
    });

    if (recordRes.code === 200) {
      recordData.value = recordRes.data.records || [];
      recordPagination.itemCount = recordRes.data.total || 0;
      recordPagination.pageCount = Math.ceil(
        recordPagination.itemCount / recordPagination.pageSize
      );
    }

    // 模拟连续打卡数据
    consecutiveData.value = [
      {
        id: 1,
        username: "user007",
        consecutiveDays: 120,
        lastCheckinTime: "2023-05-20 08:23:15",
      },
      {
        id: 2,
        username: "user025",
        consecutiveDays: 98,
        lastCheckinTime: "2023-05-20 07:15:30",
      },
      {
        id: 3,
        username: "user013",
        consecutiveDays: 76,
        lastCheckinTime: "2023-05-20 06:45:22",
      },
      {
        id: 4,
        username: "user056",
        consecutiveDays: 65,
        lastCheckinTime: "2023-05-20 09:10:48",
      },
      {
        id: 5,
        username: "user032",
        consecutiveDays: 60,
        lastCheckinTime: "2023-05-20 12:20:15",
      },
    ];

    // 模拟打卡次数数据
    countData.value = [
      {
        id: 1,
        username: "user001",
        checkinCount: 356,
        firstCheckinTime: "2022-05-15",
      },
      {
        id: 2,
        username: "user007",
        checkinCount: 325,
        firstCheckinTime: "2022-06-22",
      },
      {
        id: 3,
        username: "user025",
        checkinCount: 312,
        firstCheckinTime: "2022-07-05",
      },
      {
        id: 4,
        username: "user013",
        checkinCount: 289,
        firstCheckinTime: "2022-08-17",
      },
      {
        id: 5,
        username: "user032",
        checkinCount: 267,
        firstCheckinTime: "2022-09-01",
      },
    ];
  } catch (error) {
    console.error("加载数据失败", error);
  } finally {
    tableLoading.value = false;
    chartLoading.value = false;
  }
};

// 页面切换
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 记录页面切换
const handleRecordPageChange = (page) => {
  recordPagination.page = page;
  loadData();
};

// 搜索
const handleSearch = () => {
  recordPagination.page = 1;
  loadData();
};

onMounted(() => {
  loadData();
  // 初始化图表需要在DOM渲染完成后
  setTimeout(() => {
    initChart();
  }, 100);
});

onUnmounted(() => {
  // 清理图表实例
  if (myChart) {
    window.removeEventListener("resize", myChart.resize);
    myChart.dispose();
    myChart = null;
  }
});
</script>

<style lang="scss" scoped>
.content-card {
  margin-bottom: 20px;
}

.stat-card {
  height: 100%;
}

.stat-item {
  text-align: center;
  padding: 10px;

  .stat-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }
}

.chart-container {
  width: 100%;
  height: 100%;
}

.ml-10 {
  margin-left: 10px;
}
</style>
