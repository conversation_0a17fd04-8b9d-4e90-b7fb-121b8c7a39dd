<template>
  <div class="page-container">
    <n-card title="客服管理" class="content-card">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <n-form inline :label-width="80">
          <n-form-item label="客服姓名">
            <n-input
              v-model:value="searchParams.name"
              clearable
              placeholder="请输入客服姓名"
            />
          </n-form-item>
          <n-form-item label="联系方式">
            <n-input
              v-model:value="searchParams.contact"
              clearable
              placeholder="请输入手机号、微信、邮箱或QQ"
            />
          </n-form-item>
          <n-form-item label="状态">
            <n-select
              v-model:value="searchParams.status"
              :options="statusOptions"
              clearable
              placeholder="请选择状态"
              style="width: 120px"
            />
          </n-form-item>
          <n-form-item>
            <n-button type="primary" @click="loadData">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button class="m-l-10" @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-form-item>
        </n-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加客服
        </n-button>
        <n-button
          class="m-l-10"
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
        <n-button class="m-l-10" type="warning" @click="loadData">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />

      <!-- 添加/编辑客服对话框 -->
      <n-modal
        v-model:show="showModal"
        :title="modalTitle"
        preset="card"
        :style="{ width: '600px' }"
      >
        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="1" :x-gap="16">
            <n-gi>
              <n-form-item label="客服姓名" path="name">
                <n-input
                  v-model:value="formModel.name"
                  placeholder="请输入客服姓名"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="头像" path="avatar">
                <OssDirectUpload
                  v-model="formModel.avatar"
                  category="image"
                  accept="image/*"
                  :show-preview="true"
                  @upload-success="handleAvatarUploadSuccess"
                  @upload-error="handleAvatarUploadError"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="手机号" path="phone">
                <n-input
                  v-model:value="formModel.phone"
                  placeholder="请输入手机号"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="微信" path="wechat">
                <n-input
                  v-model:value="formModel.wechat"
                  placeholder="请输入微信号"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="邮箱" path="email">
                <n-input
                  v-model:value="formModel.email"
                  placeholder="请输入邮箱地址"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="QQ" path="qq">
                <n-input
                  v-model:value="formModel.qq"
                  placeholder="请输入QQ号"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="备注信息" path="remark">
                <n-input
                  v-model:value="formModel.remark"
                  type="textarea"
                  placeholder="请输入备注信息"
                  :autosize="{ minRows: 3, maxRows: 5 }"
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="状态" path="status">
                <n-switch
                  v-model:value="formModel.status"
                  :checked-value="true"
                  :unchecked-value="false"
                >
                  <template #checked>启用</template>
                  <template #unchecked>禁用</template>
                </n-switch>
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showModal = false">取消</n-button>
            <n-button
              type="primary"
              :loading="submitLoading"
              @click="handleSubmit"
              >确定</n-button
            >
          </n-space>
        </template>
      </n-modal>
    </n-card>
  </div>
</template>

<script setup>
import { h, ref, reactive, computed, onMounted } from "vue";
import { NImage } from "naive-ui";
import {
  AddOutline,
  RefreshOutline,
  PencilOutline,
  TrashOutline,
  CloseCircleOutline,
  CheckmarkCircleOutline,
  CallOutline,
  ChatbubbleOutline,
  MailOutline,
  LogoWechat,
  SearchOutline,
} from "@vicons/ionicons5";
import {
  getCustomerServiceList,
  createCustomerService,
  updateCustomerService,
  deleteCustomerService,
  batchDeleteCustomerServices,
  enableCustomerService,
  disableCustomerService,
} from "@/api/customerService";
import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";

const message = useMessage();
const dialog = useDialog();

// 表格数据
const loading = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 搜索参数
const searchParams = reactive({
  name: "",
  contact: "",
  status: null,
});

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "禁用", value: false },
];

// 表单
const showModal = ref(false);
const modalTitle = computed(() => (formModel.id ? "编辑客服" : "添加客服"));
const formRef = ref(null);
const submitLoading = ref(false);

// 表单数据
const formModel = reactive({
  id: null,
  name: "",
  avatar: "",
  phone: "",
  wechat: "",
  email: "",
  qq: "",
  remark: "",
  status: true,
});

// 自定义验证规则：至少填写一个联系方式
const validateContactInfo = (rule, value) => {
  if (!formModel.phone && !formModel.wechat && !formModel.email && !formModel.qq) {
    return new Error("请至少填写一种联系方式（手机号、微信、邮箱或QQ）");
  }
  return true;
};

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入客服姓名", trigger: "blur" }],
  phone: [{ validator: validateContactInfo, trigger: "blur" }],
  wechat: [{ validator: validateContactInfo, trigger: "blur" }],
  email: [
    { validator: validateContactInfo, trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" }
  ],
  qq: [{ validator: validateContactInfo, trigger: "blur" }],
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach((key) => {
    searchParams[key] = "";
  });
  searchParams.status = null;
  pagination.page = 1;
  loadData();
};



// 表格列
const createColumns = () => {
  return [
    {
      type: "selection",
    },
    {
      title: "ID",
      key: "id",
      width: 80,
    },
    {
      title: "客服姓名",
      key: "name",
      width: 120,
    },
    {
      title: "头像",
      key: "avatar",
      width: 80,
      render(row) {
        if (!row.avatarFullUrl && !row.avatar) return null;
        const avatarUrl = row.avatarFullUrl || row.avatar;
        return h(NImage, {
          width: 40,
          height: 40,
          src: avatarUrl,
          style: "border-radius: 50%; object-fit: cover;",
          fallbackSrc: "/default-avatar.png",
          onError: () => {
            console.log("头像加载失败:", avatarUrl);
          }
        });
      },
    },
    {
      title: "联系方式",
      key: "contacts",
      width: 320,
      render(row) {
        const contactMethods = [
          { type: "phone", value: row.phone, icon: CallOutline, label: "电话", color: "#52c41a" },
          { type: "wechat", value: row.wechat, icon: LogoWechat, label: "微信", color: "#1890ff" },
          { type: "email", value: row.email, icon: MailOutline, label: "邮箱", color: "#fa8c16" },
          { type: "qq", value: row.qq, icon: ChatbubbleOutline, label: "QQ", color: "#722ed1" },
        ];

        const availableContacts = contactMethods.filter(contact => contact.value);
        
        if (availableContacts.length === 0) {
          return h("div", { 
            style: "display: flex; align-items: center; justify-content: center; padding: 8px; color: #999; font-size: 12px;" 
          }, "暂无联系方式");
        }

        return h("div", { class: "contact-cards-container" }, 
          availableContacts.map((contact) => 
            h("div", { 
              class: "contact-card",
              style: `border-left: 3px solid ${contact.color};`
            }, [
              h("div", { class: "contact-card-header" }, [
                h("n-icon", {
                  size: 12,
                  color: contact.color,
                }, {
                  default: () => h(contact.icon),
                }),
                h("span", { 
                  class: "contact-card-label",
                  style: `color: ${contact.color};`
                }, contact.label),
              ]),
              h("div", { class: "contact-card-value" }, contact.value),
            ])
          )
        );
      },
    },
    {
      title: "备注",
      key: "remark",
      width: 150,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.remark || "-";
      },
    },
    {
      title: "状态",
      key: "status",
      width: 100,
      render(row) {
        return h(
          "n-tag",
          {
            type: row.status ? "success" : "error",
            size: "small",
          },
          {
            default: () => (row.status ? "启用" : "禁用"),
            icon: () =>
              h(
                "n-icon",
                {
                  size: 14,
                },
                {
                  default: () =>
                    h(
                      row.status
                        ? CheckmarkCircleOutline
                        : CloseCircleOutline
                    ),
                }
              ),
          }
        );
      },
    },
    {
      title: "创建时间",
      key: "createdAt",
      width: 160,
      render(row) {
        return row.createdAt ? new Date(row.createdAt).toLocaleString() : "-";
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 200,
      fixed: "right",
      align: "center",
      render(row) {
        return h(
          "div",
          { style: "display: flex; justify-content: center; gap: 10px;" },
          [
            h(
              "n-button",
              {
                size: "small",
                type: "info",
                style:
                  "padding: 0 12px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
                onClick: () => handleEdit(row),
              },
              {
                default: () => "编辑",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(PencilOutline),
                    }
                  ),
              }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: row.status ? "error" : "success",
                style: row.status
                  ? "padding: 0 12px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;"
                  : "padding: 0 12px; border-radius: 15px; background-color: #52c41a; border: none; color: white;",
                onClick: () => handleToggleStatus(row),
              },
              {
                default: () => (row.status ? "禁用" : "启用"),
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () =>
                        h(
                          row.status
                            ? CloseCircleOutline
                            : CheckmarkCircleOutline
                        ),
                    }
                  ),
              }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: "error",
                style:
                  "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
                onClick: () => handleDelete(row),
              },
              {
                default: () => "删除",
                icon: () =>
                  h(
                    "n-icon",
                    {
                      size: 14,
                    },
                    {
                      default: () => h(TrashOutline),
                    }
                  ),
              }
            ),
          ]
        );
      },
    },
  ];
};

const columns = createColumns();

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      ...searchParams,
    };

    // 调用API
    const res = await getCustomerServiceList(params);

    // 更新数据
    tableData.value = res.data.records;
    pagination.pageCount = res.data.pages;
    pagination.page = res.data.current;
    pagination.pageSize = res.data.size;
    pagination.itemCount = res.data.total;
  } catch (error) {
    console.error("加载客服数据失败:", error);
    message.error("加载客服数据失败");
  } finally {
    loading.value = false;
  }
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 处理添加
const handleAdd = () => {
  Object.keys(formModel).forEach((key) => {
    if (key === "status") {
      formModel[key] = true;
    } else if (key !== "id") {
      formModel[key] = "";
    }
  });
  formModel.id = null;
  showModal.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  Object.keys(formModel).forEach((key) => {
    formModel[key] = row[key];
  });
  showModal.value = true;
};

// 处理头像上传成功
const handleAvatarUploadSuccess = (result) => {
  console.log("头像上传成功:", result);
  formModel.avatar = result.objectKey || result.fileUrl; // 使用相对路径
  message.success("头像上传成功");
};

// 处理头像上传失败
const handleAvatarUploadError = (error) => {
  console.error("头像上传失败:", error);
  message.error("头像上传失败");
};

// 处理切换状态
const handleToggleStatus = (row) => {
  const action = row.status ? "禁用" : "启用";
  dialog.warning({
    title: `确认${action}`,
    content: `确定要${action}客服 "${row.name}" 吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res =
          row.status
            ? await disableCustomerService(row.id)
            : await enableCustomerService(row.id);
        if (res.code === 200) {
          message.success(`${action}成功`);
          loadData();
        } else {
          message.error(res.message || `${action}失败`);
        }
      } catch (error) {
        message.error(`${action}失败`);
      }
    },
  });
};

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除客服 "${row.name}" 吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await deleteCustomerService(row.id);
        if (res.code === 200) {
          message.success("删除成功");
          loadData();
        } else {
          message.error(res.message || "删除失败");
        }
      } catch (error) {
        message.error("删除失败");
      }
    },
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请至少选择一条记录");
    return;
  }

  dialog.warning({
    title: "确认批量删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await batchDeleteCustomerServices(selectedRowKeys.value);
        if (res.code === 200) {
          message.success("批量删除成功");
          selectedRowKeys.value = [];
          loadData();
        } else {
          message.error(res.message || "批量删除失败");
        }
      } catch (error) {
        message.error("批量删除失败");
      }
    },
  });
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (errors) => {
    if (!errors) {
      submitLoading.value = true;
      try {
        const saveData = { ...formModel };

        const res = saveData.id
          ? await updateCustomerService(saveData)
          : await createCustomerService(saveData);

        if (res.code === 200) {
          message.success(saveData.id ? "更新成功" : "创建成功");
          showModal.value = false;
          loadData();
        } else {
          message.error(res.message || (saveData.id ? "更新失败" : "创建失败"));
        }
      } catch (error) {
        message.error(formModel.id ? "更新失败" : "创建失败");
      } finally {
        submitLoading.value = false;
      }
    } else {
      message.error("请完善表单信息");
    }
  });
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.content-card {
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
}

.flex-start {
  display: flex;
  align-items: center;
}

.m-l-10 {
  margin-left: 10px;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.search-bar {
  margin-bottom: 20px;
}

.contact-cards-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 4px 0;
}

.contact-card {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 6px 8px;
  margin-bottom: 2px;
  border-left-width: 3px;
  border-left-style: solid;
  transition: all 0.2s ease;
}

.contact-card:hover {
  background: #f0f2f5;
  transform: translateX(2px);
}

.contact-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.contact-card-label {
  font-size: 10px;
  font-weight: 600;
  margin-left: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-card-value {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  padding-left: 16px;
  word-break: break-all;
}
</style>
