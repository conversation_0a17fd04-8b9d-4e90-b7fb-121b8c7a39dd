<template>
  <div class="cascader-test">
    <n-card title="级联选择器测试">
      <n-space vertical>
        <!-- 课程选择 -->
        <n-form-item label="选择课程">
          <CourseSelector v-model:value="courseId" @change="handleCourseChange" />
        </n-form-item>

        <!-- 权限类型选择 -->
        <n-form-item label="权限类型">
          <n-select v-model:value="accessType" :options="accessTypeOptions" @update:value="handleAccessTypeChange" />
        </n-form-item>

        <!-- 章节课时选择 -->
        <n-form-item v-if="courseId && (accessType === 2 || accessType === 3)" :label="accessType === 2 ? '选择章节' : '选择课时'">
          <ChapterLessonCascader
            v-model:value="cascaderValue"
            :course-id="courseId"
            :access-type="accessType"
            :placeholder="accessType === 2 ? '请选择章节' : '请选择课时'"
            @change="handleCascaderChange"
          />
        </n-form-item>

        <!-- 显示选择结果 -->
        <n-form-item label="选择结果">
          <n-code :code="JSON.stringify({ courseId, accessType, cascaderValue, selectedData }, null, 2)" language="json" />
        </n-form-item>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CourseSelector from '@/components/Selector/CourseSelector.vue'
import ChapterLessonCascader from '@/components/Selector/ChapterLessonCascader.vue'

// 响应式数据
const courseId = ref(null)
const accessType = ref(2)
const cascaderValue = ref(null)
const selectedData = ref(null)

// 权限类型选项
const accessTypeOptions = [
  { label: '课程权限', value: 1 },
  { label: '章节权限', value: 2 },
  { label: '课时权限', value: 3 }
]

// 课程变化处理
const handleCourseChange = () => {
  cascaderValue.value = null
  selectedData.value = null
}

// 权限类型变化处理
const handleAccessTypeChange = () => {
  cascaderValue.value = null
  selectedData.value = null
}

// 级联选择器变化处理
const handleCascaderChange = (value, data) => {
  selectedData.value = data
  console.log('Cascader changed:', { value, data })
}
</script>

<style lang="scss" scoped>
.cascader-test {
  padding: 20px;
}

:deep(.n-code) {
  max-height: 300px;
  overflow-y: auto;
}
</style>
