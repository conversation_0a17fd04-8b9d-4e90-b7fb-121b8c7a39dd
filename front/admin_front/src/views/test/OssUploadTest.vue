<template>
  <div class="oss-upload-test">
    <n-card title="OSS PostObject上传测试" style="max-width: 800px; margin: 20px auto;">
      <n-space vertical size="large">
        <!-- 图片上传测试 -->
        <n-card title="图片上传测试" size="small">
          <OssDirectUpload
            category="image"
            accept="image/*"
            :max-size="10 * 1024 * 1024"
            @upload-success="handleImageUploadSuccess"
            @upload-error="handleUploadError"
          />
          <div v-if="imageResult" style="margin-top: 16px;">
            <n-alert type="success" title="上传成功">
              <p><strong>文件名:</strong> {{ imageResult.fileName }}</p>
              <p><strong>文件大小:</strong> {{ formatFileSize(imageResult.fileSize) }}</p>
              <p><strong>文件URL:</strong> 
                <n-button text tag="a" :href="imageResult.fileUrl" target="_blank">
                  {{ imageResult.fileUrl }}
                </n-button>
              </p>
            </n-alert>
            <div style="margin-top: 16px;">
              <img :src="imageResult.fileUrl" alt="上传的图片" style="max-width: 300px; max-height: 200px;" />
            </div>
          </div>
        </n-card>

        <!-- 视频上传测试 -->
        <n-card title="视频上传测试" size="small">
          <OssVideoUpload
            @upload-success="handleVideoUploadSuccess"
            @upload-error="handleUploadError"
          />
          <div v-if="videoResult" style="margin-top: 16px;">
            <n-alert type="success" title="上传成功">
              <p><strong>文件名:</strong> {{ videoResult.fileName }}</p>
              <p><strong>文件大小:</strong> {{ formatFileSize(videoResult.fileSize) }}</p>
              <p><strong>文件URL:</strong> 
                <n-button text tag="a" :href="videoResult.fileUrl" target="_blank">
                  {{ videoResult.fileUrl }}
                </n-button>
              </p>
            </n-alert>
          </div>
        </n-card>

        <!-- 错误信息 -->
        <div v-if="errorMessage">
          <n-alert type="error" title="上传失败">
            {{ errorMessage }}
          </n-alert>
        </div>

        <!-- API测试 -->
        <n-card title="API测试" size="small">
          <n-space>
            <n-button @click="testGetSignature" :loading="testing">
              测试获取签名
            </n-button>
            <n-button @click="clearResults">
              清空结果
            </n-button>
          </n-space>
          
          <div v-if="signatureResult" style="margin-top: 16px;">
            <n-alert type="info" title="签名信息">
              <pre style="white-space: pre-wrap; font-size: 12px;">{{ JSON.stringify(signatureResult, null, 2) }}</pre>
            </n-alert>
          </div>
        </n-card>
      </n-space>
    </n-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useMessage } from 'naive-ui';
import OssDirectUpload from '@/components/FileUpload/OssDirectUpload.vue';
import OssVideoUpload from '@/components/VideoUpload/OssVideoUpload.vue';
import { getOssSignature, formatFileSize } from '@/api/oss';

const message = useMessage();

// 响应式数据
const imageResult = ref(null);
const videoResult = ref(null);
const errorMessage = ref('');
const signatureResult = ref(null);
const testing = ref(false);

// 处理图片上传成功
const handleImageUploadSuccess = (result) => {
  console.log('图片上传成功:', result);
  imageResult.value = result;
  errorMessage.value = '';
  message.success('图片上传成功！');
};

// 处理视频上传成功
const handleVideoUploadSuccess = (result) => {
  console.log('视频上传成功:', result);
  videoResult.value = result;
  errorMessage.value = '';
  message.success('视频上传成功！');
};

// 处理上传错误
const handleUploadError = (error) => {
  console.error('上传失败:', error);
  errorMessage.value = error.message || '上传失败';
  message.error('上传失败: ' + (error.message || '未知错误'));
};

// 测试获取签名
const testGetSignature = async () => {
  testing.value = true;
  try {
    const response = await getOssSignature('image', 'test.jpg');
    console.log('获取签名成功:', response);
    signatureResult.value = response.data;
    message.success('获取签名成功！');
  } catch (error) {
    console.error('获取签名失败:', error);
    message.error('获取签名失败: ' + (error.message || '未知错误'));
  } finally {
    testing.value = false;
  }
};

// 清空结果
const clearResults = () => {
  imageResult.value = null;
  videoResult.value = null;
  errorMessage.value = '';
  signatureResult.value = null;
  message.info('结果已清空');
};
</script>

<style scoped>
.oss-upload-test {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

pre {
  background-color: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
}
</style>
