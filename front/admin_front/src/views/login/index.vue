<template>
  <div
    style="
      display: flex;
      width: 100%;
      height: 100vh;
      background: linear-gradient(135deg, #1a237e, #283593, #3949ab);
      position: relative;
      overflow: hidden;
    "
  >
    <!-- 背景装饰元素 -->
    <div
      style="
        position: absolute;
        top: -100px;
        right: -100px;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
      "
    ></div>
    <div
      style="
        position: absolute;
        bottom: -150px;
        left: -100px;
        width: 400px;
        height: 400px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.05);
      "
    ></div>

    <!-- 左侧介绍区域 -->
    <div
      style="
        flex: 1;
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 250px;
      "
    >
      <!-- Logo -->
      <div style="margin-bottom: 20px">
        <img src="@/assets/logo.png" alt="Logo" style="height: 80px" />
      </div>

      <!-- 介绍标题 -->
      <h1 style="font-size: 36px; font-weight: 600; margin-bottom: 20px">
        鼎峰课堂-后台管理系统
      </h1>

      <!-- 介绍内容 -->
      <p
        style="
          font-size: 18px;
          line-height: 1.8;
          margin-bottom: 10px;
          opacity: 0.9;
        "
      >
        打造全新的在线学习体验，助力教师高效管理课程内容和学生活动。
      </p>

      <!-- 特点列表 -->
      <div style="margin-top: 30px">
        <div style="display: flex; align-items: center; margin-bottom: 15px">
          <div
            style="
              width: 50px;
              height: 50px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.2);
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 15px;
            "
          >
            <n-icon size="30" color="#fff">
              <BookOutline />
            </n-icon>
          </div>
          <div>
            <h3 style="font-size: 18px; margin-bottom: 5px">丰富的课程管理</h3>
            <p style="font-size: 14px; opacity: 0.8">
              轻松创建和管理多样化课程内容
            </p>
          </div>
        </div>

        <div style="display: flex; align-items: center; margin-bottom: 15px">
          <div
            style="
              width: 50px;
              height: 50px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.2);
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 15px;
            "
          >
            <n-icon size="30" color="#fff">
              <PeopleOutline />
            </n-icon>
          </div>
          <div>
            <h3 style="font-size: 18px; margin-bottom: 5px">完善的用户体系</h3>
            <p style="font-size: 14px; opacity: 0.8">
              精细化的用户权限与角色管理
            </p>
          </div>
        </div>

        <div style="display: flex; align-items: center">
          <div
            style="
              width: 50px;
              height: 50px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.2);
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 15px;
            "
          >
            <n-icon size="30" color="#fff">
              <BarChartOutline />
            </n-icon>
          </div>
          <div>
            <h3 style="font-size: 18px; margin-bottom: 5px">数据分析可视化</h3>
            <p style="font-size: 14px; opacity: 0.8">
              全方位掌握学习数据和教学成效
            </p>
          </div>
        </div>
      </div>

      <!-- 页脚 -->
      <div style="margin-top: 40px; font-size: 14px; opacity: 0.7">
        &copy; {{ new Date().getFullYear() }} 鼎峰课堂教育
      </div>
    </div>

    <!-- 右侧 - 登录框悬浮 -->
    <div
      style="
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 170px;
      "
    >
      <!-- 登录框 -->
      <div
        style="
          width: 450px;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
          padding: 30px;
          position: relative;
        "
      >
        <!-- 登录标题 -->
        <div style="text-align: center; margin-bottom: 10px">
          <h2 style="font-size: 25px; color: #333; font-weight: 500">
            管理员登录
          </h2>
          <p style="color: #666; margin-top: 8px; font-size: 16px">
            输入账号密码进入系统
          </p>
        </div>

        <!-- 登录表单 -->
        <n-form ref="formRef" :model="formModel" :rules="rules" size="medium">
          <n-form-item path="username">
            <n-input
              v-model:value="formModel.username"
              placeholder="请输入用户名"
              style="height: 35px; font-size: 16px"
            >
              <template #prefix>
                <n-icon>
                  <PersonOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="password">
            <n-input
              v-model:value="formModel.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="click"
              style="height: 35px; font-size: 16px"
            >
              <template #prefix>
                <n-icon>
                  <LockClosedOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="captchaCode">
            <div style="display: flex; gap: 10px">
              <n-input
                v-model:value="formModel.captchaCode"
                placeholder="请输入验证码"
                :maxlength="5"
                style="height: 35px; flex: 1; font-size: 16px"
              >
                <template #prefix>
                  <n-icon>
                    <ShieldCheckmarkOutline />
                  </n-icon>
                </template>
              </n-input>
              <div
                style="
                  height: 35px;
                  width: 160px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 4px;
                  overflow: hidden;
                  border: 1px solid #eee;
                "
                @click="refreshCaptcha"
              >
                <img
                  v-if="captchaInfo.captchaImage"
                  :src="captchaInfo.captchaImage"
                  alt="验证码"
                  style="width: 100%; height: 100%; object-fit: cover"
                />
                <n-button
                  v-else
                  @click.stop="refreshCaptcha"
                  type="primary"
                  size="small"
                  >获取验证码</n-button
                >
              </div>
            </div>
          </n-form-item>

          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 20px;
            "
          >
            <!-- <n-checkbox v-model:checked="formModel.remember">记住我</n-checkbox> -->
            <!-- <a
              href="javascript:void(0)"
              style="color: #2080f0; text-decoration: none; font-size: 13px"
              >忘记密码？</a
            > -->
          </div>

          <n-button
            type="primary"
            block
            :loading="loading"
            @click="handleLogin"
            style="height: 42px; font-size: 15px"
          >
            登录系统
          </n-button>
        </n-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useMessage } from "naive-ui";
import {
  PersonOutline,
  LockClosedOutline,
  ShieldCheckmarkOutline,
  BookOutline,
  PeopleOutline,
  BarChartOutline,
} from "@vicons/ionicons5";
import CryptoJS from "crypto-js";
import { login } from "@/api/auth";
import { getCaptcha } from "@/api/auth";
import { setToken } from "@/utils/token";

const router = useRouter();
const message = useMessage();
const formRef = ref(null);
const loading = ref(false);

// 验证码信息
const captchaInfo = reactive({
  captchaId: "",
  captchaImage: "",
});

// 表单数据
const formModel = reactive({
  username: "",
  password: "",
  captchaCode: "",
  remember: false,
});

// 表单验证规则
const rules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度不能小于6位", trigger: "blur" },
  ],
  captchaCode: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { len: 5, message: "验证码长度为5位", trigger: "blur" },
  ],
};

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const res = await getCaptcha();
    if (res.code === 200) {
      captchaInfo.captchaId = res.data.captchaId;
      captchaInfo.captchaImage = res.data.captchaImage;
    } else {
      message.error("获取验证码失败");
    }
  } catch (error) {
    message.error("获取验证码失败");
  }
};

// 页面加载时获取验证码
onMounted(() => {
  refreshCaptcha();
});

// 登录处理
const handleLogin = (e) => {
  e.preventDefault();
  formRef.value.validate(async (errors) => {
    if (!errors) {
      loading.value = true;
      try {
        // 对密码进行MD5加密
        const encryptedPassword = CryptoJS.MD5(formModel.password).toString();

        // 调用登录API
        const res = await login({
          username: formModel.username,
          password: encryptedPassword,
          captchaCode: formModel.captchaCode,
          captchaId: captchaInfo.captchaId,
        });

        if (res.code === 200) {
          // 使用工具函数设置token（同时设置localStorage和cookie）
          setToken(res.data.token);

          // 存储用户信息
          localStorage.setItem(
            "userInfo",
            JSON.stringify({
              userId: res.data.userId,
              username: res.data.username,
              realName: res.data.realName,
              avatar: res.data.avatar,
              role: res.data.role,
            })
          );

          message.success("登录成功");
          router.push("/");
        } else {
          message.error(res.msg || "登录失败");
          // 刷新验证码
          refreshCaptcha();
        }
      } catch (error) {
        message.error(error.message || "登录失败");
        refreshCaptcha();
      } finally {
        loading.value = false;
      }
    } else {
      message.error("请输入正确的登录信息");
    }
  });
};
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  background: linear-gradient(to right, #3494e6, #ec6ead);

  .login-box {
    width: 400px;
    padding: 40px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .login-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30px;

      .login-logo {
        height: 64px;
        margin-bottom: 16px;
      }

      .login-title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }

    .login-form {
      .captcha-container {
        display: flex;
        align-items: center;
        gap: 10px;

        .captcha-image {
          height: 36px;
          min-width: 120px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .login-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .forgot-password {
          color: #2080f0;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .login-button {
        height: 44px;
        font-size: 16px;
      }
    }
  }

  .login-footer {
    margin-top: 40px;
    color: #fff;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .login-box {
    width: 90%;
    max-width: 400px;
  }
}
</style>
