<template>
  <div class="login-page">
    <!-- 背景装饰元素 -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>

    <!-- 左侧介绍区域 -->
    <div class="intro-section">
      <!-- Logo -->
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo" />
      </div>

      <!-- 介绍标题 -->
      <h1 class="intro-title">
        鼎峰课堂-后台管理系统
      </h1>

      <!-- 介绍内容 -->
      <p class="intro-description">
        打造全新的在线学习体验，助力教师高效管理课程内容和学生活动。
      </p>

      <!-- 特点列表 -->
      <div class="features-list">
        <div class="feature-item">
          <div class="feature-icon">
            <n-icon size="30" color="#fff">
              <BookOutline />
            </n-icon>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">丰富的课程管理</h3>
            <p class="feature-desc">
              轻松创建和管理多样化课程内容
            </p>
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <n-icon size="30" color="#fff">
              <PeopleOutline />
            </n-icon>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">完善的用户体系</h3>
            <p class="feature-desc">
              精细化的用户权限与角色管理
            </p>
          </div>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <n-icon size="30" color="#fff">
              <BarChartOutline />
            </n-icon>
          </div>
          <div class="feature-content">
            <h3 class="feature-title">数据分析可视化</h3>
            <p class="feature-desc">
              全方位掌握学习数据和教学成效
            </p>
          </div>
        </div>
      </div>

      <!-- 页脚 -->
      <div class="intro-footer">
        &copy; {{ new Date().getFullYear() }} 鼎峰课堂教育
      </div>
    </div>

    <!-- 右侧 - 登录框悬浮 -->
    <div class="login-section">
      <!-- 登录框 -->
      <div class="login-box">
        <!-- 登录标题 -->
        <div class="login-header">
          <h2 class="login-title">
            管理员登录
          </h2>
          <p class="login-subtitle">
            输入账号密码进入系统
          </p>
        </div>

        <!-- 登录表单 -->
        <n-form ref="formRef" :model="formModel" :rules="rules" size="medium" class="login-form">
          <n-form-item path="username">
            <n-input
              v-model:value="formModel.username"
              placeholder="请输入用户名"
              class="form-input"
            >
              <template #prefix>
                <n-icon>
                  <PersonOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="password">
            <n-input
              v-model:value="formModel.password"
              type="password"
              placeholder="请输入密码"
              show-password-on="click"
              class="form-input"
            >
              <template #prefix>
                <n-icon>
                  <LockClosedOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item path="captchaCode">
            <div class="captcha-container">
              <n-input
                v-model:value="formModel.captchaCode"
                placeholder="请输入验证码"
                :maxlength="5"
                class="form-input captcha-input"
              >
                <template #prefix>
                  <n-icon>
                    <ShieldCheckmarkOutline />
                  </n-icon>
                </template>
              </n-input>
              <div class="captcha-image" @click="refreshCaptcha">
                <img
                  v-if="captchaInfo.captchaImage"
                  :src="captchaInfo.captchaImage"
                  alt="验证码"
                />
                <n-button
                  v-else
                  @click.stop="refreshCaptcha"
                  type="primary"
                  size="small"
                  >获取验证码</n-button
                >
              </div>
            </div>
          </n-form-item>

          <div class="login-options">
            <!-- <n-checkbox v-model:checked="formModel.remember">记住我</n-checkbox> -->
            <!-- <a href="javascript:void(0)" class="forgot-password">忘记密码？</a> -->
          </div>

          <n-button
            type="primary"
            block
            :loading="loading"
            @click="handleLogin"
            class="login-button"
          >
            登录系统
          </n-button>
        </n-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useMessage } from "naive-ui";
import {
  PersonOutline,
  LockClosedOutline,
  ShieldCheckmarkOutline,
  BookOutline,
  PeopleOutline,
  BarChartOutline,
} from "@vicons/ionicons5";
import CryptoJS from "crypto-js";
import { login } from "@/api/auth";
import { getCaptcha } from "@/api/auth";
import { setToken } from "@/utils/token";

const router = useRouter();
const message = useMessage();
const formRef = ref(null);
const loading = ref(false);

// 验证码信息
const captchaInfo = reactive({
  captchaId: "",
  captchaImage: "",
});

// 表单数据
const formModel = reactive({
  username: "",
  password: "",
  captchaCode: "",
  remember: false,
});

// 表单验证规则
const rules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度不能小于6位", trigger: "blur" },
  ],
  captchaCode: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { len: 5, message: "验证码长度为5位", trigger: "blur" },
  ],
};

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const res = await getCaptcha();
    if (res.code === 200) {
      captchaInfo.captchaId = res.data.captchaId;
      captchaInfo.captchaImage = res.data.captchaImage;
    } else {
      message.error("获取验证码失败");
    }
  } catch (error) {
    message.error("获取验证码失败");
  }
};

// 页面加载时获取验证码
onMounted(() => {
  refreshCaptcha();
});

// 登录处理
const handleLogin = (e) => {
  e.preventDefault();
  formRef.value.validate(async (errors) => {
    if (!errors) {
      loading.value = true;
      try {
        // 对密码进行MD5加密
        const encryptedPassword = CryptoJS.MD5(formModel.password).toString();

        // 调用登录API
        const res = await login({
          username: formModel.username,
          password: encryptedPassword,
          captchaCode: formModel.captchaCode,
          captchaId: captchaInfo.captchaId,
        });

        if (res.code === 200) {
          // 使用工具函数设置token（同时设置localStorage和cookie）
          setToken(res.data.token);

          // 存储用户信息
          localStorage.setItem(
            "userInfo",
            JSON.stringify({
              userId: res.data.userId,
              username: res.data.username,
              realName: res.data.realName,
              avatar: res.data.avatar,
              role: res.data.role,
            })
          );

          message.success("登录成功");
          router.push("/");
        } else {
          // 刷新验证码
          refreshCaptcha();
        }
      } catch (error) {
        refreshCaptcha();
      } finally {
        loading.value = false;
      }
    } else {
      message.error("请输入正确的登录信息");
    }
  });
};
</script>

<style lang="scss" scoped>
// 基础样式
.login-page {
  display: flex;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #1a237e, #283593, #3949ab);
  position: relative;
  overflow: hidden;
}

// 背景装饰元素
.bg-decoration {
  position: absolute;
  border-radius: 50%;

  &.bg-decoration-1 {
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
  }

  &.bg-decoration-2 {
    bottom: -150px;
    left: -100px;
    width: 400px;
    height: 400px;
    background: rgba(255, 255, 255, 0.05);
  }
}

// 左侧介绍区域
.intro-section {
  flex: 1;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem 4rem;
  min-width: 0; // 防止flex子项溢出

  .logo-container {
    margin-bottom: 1.25rem;

    .logo {
      height: 80px;
      max-width: 100%;
    }
  }

  .intro-title {
    font-size: 2.25rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    line-height: 1.2;
  }

  .intro-description {
    font-size: 1.125rem;
    line-height: 1.8;
    margin-bottom: 0.625rem;
    opacity: 0.9;
  }

  .features-list {
    margin-top: 1.875rem;

    .feature-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.9375rem;

      &:last-child {
        margin-bottom: 0;
      }

      .feature-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 0.9375rem;
        flex-shrink: 0;
      }

      .feature-content {
        .feature-title {
          font-size: 1.125rem;
          margin-bottom: 0.3125rem;
          font-weight: 500;
        }

        .feature-desc {
          font-size: 0.875rem;
          opacity: 0.8;
          margin: 0;
        }
      }
    }
  }

  .intro-footer {
    margin-top: 2.5rem;
    font-size: 0.875rem;
    opacity: 0.7;
  }
}

// 右侧登录区域
.login-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;

  .login-box {
    width: 100%;
    max-width: 450px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 1.875rem;
    position: relative;

    .login-header {
      text-align: center;
      margin-bottom: 1.25rem;

      .login-title {
        font-size: 1.5625rem;
        color: #333;
        font-weight: 500;
        margin: 0 0 0.5rem 0;
      }

      .login-subtitle {
        color: #666;
        margin: 0;
        font-size: 1rem;
      }
    }

    .login-form {
      .form-input {
        height: 2.1875rem;
        font-size: 1rem;
      }

      .captcha-container {
        display: flex;
        gap: 0.625rem;

        .captcha-input {
          flex: 1;
        }

        .captcha-image {
          height: 2.1875rem;
          width: 10rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          overflow: hidden;
          border: 1px solid #eee;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .login-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.25rem;

        .forgot-password {
          color: #2080f0;
          text-decoration: none;
          font-size: 0.8125rem;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .login-button {
        height: 2.625rem;
        font-size: 0.9375rem;
      }
    }
  }
}

// 响应式设计
// 大屏幕 (≥1200px)
@media (min-width: 1200px) {
  .intro-section {
    padding: 2rem 6rem;

    .intro-title {
      font-size: 2.5rem;
    }

    .intro-description {
      font-size: 1.25rem;
    }
  }

  .login-section {
    padding: 3rem;
  }
}

// 中等屏幕 (768px - 1199px)
@media (min-width: 768px) and (max-width: 1199px) {
  .intro-section {
    padding: 1.5rem 2rem;

    .intro-title {
      font-size: 2rem;
    }

    .intro-description {
      font-size: 1rem;
    }

    .features-list {
      .feature-item {
        .feature-icon {
          width: 40px;
          height: 40px;
          margin-right: 0.75rem;

          :deep(.n-icon) {
            font-size: 24px !important;
          }
        }

        .feature-content {
          .feature-title {
            font-size: 1rem;
          }

          .feature-desc {
            font-size: 0.8125rem;
          }
        }
      }
    }
  }

  .login-section {
    padding: 1.5rem;

    .login-box {
      max-width: 400px;
      padding: 1.5rem;
    }
  }
}

// 小屏幕 (≤767px) - 移动端
@media (max-width: 767px) {
  .login-page {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  // 背景装饰元素在移动端调整
  .bg-decoration {
    &.bg-decoration-1 {
      width: 200px;
      height: 200px;
      top: -50px;
      right: -50px;
    }

    &.bg-decoration-2 {
      width: 250px;
      height: 250px;
      bottom: -100px;
      left: -50px;
    }
  }

  .intro-section {
    flex: none;
    padding: 2rem 1.5rem 1rem;
    text-align: center;

    .logo-container {
      margin-bottom: 1rem;

      .logo {
        height: 60px;
      }
    }

    .intro-title {
      font-size: 1.75rem;
      margin-bottom: 1rem;
    }

    .intro-description {
      font-size: 1rem;
      margin-bottom: 0.5rem;
    }

    .features-list {
      margin-top: 1.5rem;

      .feature-item {
        margin-bottom: 1rem;

        .feature-icon {
          width: 40px;
          height: 40px;
          margin-right: 0.75rem;

          :deep(.n-icon) {
            font-size: 20px !important;
          }
        }

        .feature-content {
          .feature-title {
            font-size: 1rem;
          }

          .feature-desc {
            font-size: 0.8125rem;
          }
        }
      }
    }

    .intro-footer {
      margin-top: 1.5rem;
      font-size: 0.8125rem;
    }
  }

  .login-section {
    flex: none;
    padding: 1rem 1.5rem 2rem;

    .login-box {
      max-width: none;
      padding: 1.5rem;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);

      .login-header {
        margin-bottom: 1.5rem;

        .login-title {
          font-size: 1.375rem;
        }

        .login-subtitle {
          font-size: 0.9375rem;
        }
      }

      .login-form {
        .form-input {
          height: 2.5rem;
          font-size: 1rem;
        }

        .captcha-container {
          flex-direction: column;
          gap: 0.75rem;

          .captcha-input {
            flex: none;
          }

          .captcha-image {
            width: 100%;
            height: 2.5rem;
            flex-shrink: 1;
          }
        }

        .login-button {
          height: 2.75rem;
          font-size: 1rem;
        }
      }
    }
  }
}

// 超小屏幕 (≤480px)
@media (max-width: 480px) {
  .intro-section {
    padding: 1.5rem 1rem 0.75rem;

    .intro-title {
      font-size: 1.5rem;
    }

    .intro-description {
      font-size: 0.9375rem;
    }

    .features-list {
      margin-top: 1rem;

      .feature-item {
        margin-bottom: 0.75rem;

        .feature-icon {
          width: 35px;
          height: 35px;
          margin-right: 0.625rem;

          :deep(.n-icon) {
            font-size: 18px !important;
          }
        }

        .feature-content {
          .feature-title {
            font-size: 0.9375rem;
          }

          .feature-desc {
            font-size: 0.75rem;
          }
        }
      }
    }
  }

  .login-section {
    padding: 0.75rem 1rem 1.5rem;

    .login-box {
      padding: 1.25rem;

      .login-header {
        .login-title {
          font-size: 1.25rem;
        }

        .login-subtitle {
          font-size: 0.875rem;
        }
      }
    }
  }
}
</style>
