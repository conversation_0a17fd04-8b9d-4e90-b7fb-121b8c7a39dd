<template>
  <div class="tag-config-container">
    <n-card title="标签配置管理" class="main-card">
      <div class="content-layout">
        <!-- 左侧筛选区域 -->
        <div class="filter-sidebar">
          <n-card title="标签类型" size="small" class="filter-card">
            <n-space vertical>
              <n-button
                v-for="category in categoryList"
                :key="category.value"
                :type="selectedCategory === category.value ? 'primary' : 'default'"
                :ghost="selectedCategory !== category.value"
                block
                @click="handleCategorySelect(category.value)"
              >
                {{ category.label }}
                <n-badge
                  v-if="category.count > 0"
                  :value="category.count"
                  :max="99"
                  class="ml-2"
                />
              </n-button>
              
              <n-divider />
              
              <n-button
                type="primary"
                dashed
                block
                @click="showAddCategoryModal = true"
              >
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                添加新类型
              </n-button>
            </n-space>
          </n-card>
        </div>

        <!-- 右侧主要内容区域 -->
        <div class="main-content">
          <!-- 操作栏 -->
          <div class="action-bar">
            <n-space justify="space-between">
              <n-space>
                <n-input
                  v-model:value="searchParams.keyword"
                  placeholder="搜索标签名称或值"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <n-icon><SearchOutline /></n-icon>
                  </template>
                </n-input>
                <n-button @click="handleSearch">搜索</n-button>
                <n-button @click="handleReset">重置</n-button>
              </n-space>
              
              <n-space>
                <n-button
                  type="primary"
                  @click="handleAdd"
                  :disabled="!selectedCategory"
                >
                  <template #icon>
                    <n-icon><AddOutline /></n-icon>
                  </template>
                  添加标签
                </n-button>
                <n-button
                  type="error"
                  :disabled="!selectedRows.length"
                  @click="handleBatchDelete"
                >
                  <template #icon>
                    <n-icon><TrashOutline /></n-icon>
                  </template>
                  批量删除
                </n-button>
              </n-space>
            </n-space>
          </div>

          <!-- 数据表格 -->
          <n-data-table
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :pagination="pagination"
            :row-key="(row) => row.id"
            @update:checked-row-keys="handleSelectionChange"
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
          />
        </div>
      </div>
    </n-card>

    <!-- 添加/编辑标签弹窗 -->
    <n-modal
      v-model:show="showEditModal"
      :title="editingItem.id ? '编辑标签' : '添加标签'"
      preset="dialog"
      :positive-text="editingItem.id ? '更新' : '创建'"
      negative-text="取消"
      :positive-button-props="{ loading: submitting }"
      @positive-click="handleSubmit"
      @negative-click="handleCancel"
    >
      <n-form
        ref="formRef"
        :model="editingItem"
        :rules="formRules"
        label-placement="left"
        label-width="80px"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="标签类型" path="category">
          <n-select
            v-model:value="editingItem.category"
            :options="categoryOptions"
            placeholder="请选择标签类型"
            :disabled="!!editingItem.id"
          />
        </n-form-item>
        
        <n-form-item label="标签名称" path="label">
          <n-input
            v-model:value="editingItem.label"
            placeholder="请输入标签显示名称"
          />
        </n-form-item>
        

        
        <n-form-item label="排序值" path="sortOrder">
          <n-input-number
            v-model:value="editingItem.sortOrder"
            :min="0"
            placeholder="数值越小越靠前"
          />
        </n-form-item>
        
        <n-form-item label="备注说明" path="remark">
          <n-input
            v-model:value="editingItem.remark"
            type="textarea"
            placeholder="可选：添加备注说明"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </n-form-item>
        
        <n-form-item label="状态" path="status">
          <n-switch
            v-model:value="editingItem.status"

          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
    </n-modal>

    <!-- 添加新类型弹窗 -->
    <n-modal
      v-model:show="showAddCategoryModal"
      title="添加新标签类型"
      preset="dialog"
      positive-text="确认"
      negative-text="取消"
      :positive-button-props="{ loading: addingCategory }"
      @positive-click="handleAddCategory"
      @negative-click="closeAddCategoryModal"
    >
      <n-form-item label="类型标识" :show-label="false">
        <n-input
          v-model:value="newCategoryKey"
          placeholder="请输入类型标识（如：course_level）"
        />
      </n-form-item>
      <n-form-item label="类型名称" :show-label="false">
        <n-input
          v-model:value="newCategoryName"
          placeholder="请输入类型显示名称（如：课程难度）"
        />
      </n-form-item>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, h } from "vue";
import { useMessage, useDialog, NTag } from "naive-ui";
import {
  AddOutline,
  SearchOutline,
  TrashOutline,
  PencilOutline,
  EyeOutline,
  CloseCircleOutline,
  CheckmarkCircleOutline,
} from "@vicons/ionicons5";
import {
  getTagConfigPage,
  createTagConfig,
  updateTagConfig,
  deleteTagConfig,
  batchDeleteTagConfigs,
  getTagConfigCategoryList,
} from "@/api/tagConfig";

const message = useMessage();
const dialog = useDialog();

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const addingCategory = ref(false);
const tableData = ref([]);
const selectedRows = ref([]);
const selectedCategory = ref("");
const showEditModal = ref(false);
const showAddCategoryModal = ref(false);
const newCategoryKey = ref("");
const newCategoryName = ref("");

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
});

// 搜索参数
const searchParams = reactive({
  keyword: "",
  category: "",
  status: null,
});

// 编辑项数据
const editingItem = reactive({
  id: null,
  category: "",
  label: "",

  sortOrder: 0,
  remark: "",
  status: 1,
  isSystem: 0,
});

// 标签类型列表（从后端动态获取）
const categoryList = ref([]);

// 类型选项（用于下拉选择）
const categoryOptions = computed(() => {
  return categoryList.value
    .filter(item => item.value !== "")
    .map(item => ({
      label: item.label,
      value: item.value,
    }));
});

// 表格列配置
const columns = [
  {
    type: "selection",
    disabled: (row) => row.isSystem === 1,
  },
  {
    title: "标签名称",
    key: "label",
    width: 150,
  },
  {
    title: "标签值",
    key: "value",
    width: 150,
  },
  {
    title: "标签类型",
    key: "category",
    width: 120,
    render: (row) => {
      const category = categoryList.value.find(c => c.value === row.category);
      return category ? category.label : row.category;
    },
  },
  {
    title: "排序值",
    key: "sortOrder",
    width: 80,
    align: "center",
  },
  {
    title: "状态",
    key: "status",
    width: 80,
    align: "center",
    render: (row) => {
      return h(
        NTag,
        {
          type: row.status === true ? "success" : "error",
          size: "small",
        },
        row.status === true ? "启用" : "禁用"
      );
    },
  },
  {
    title: "系统标签",
    key: "isSystem",
    width: 80,
    align: "center",
    render: (row) => {
      return h(
        "n-tag",
        {
          type: row.isSystem === 1 ? "warning" : "info",
          size: "small",
        },
        row.isSystem === 1 ? "系统" : "自定义"
      );
    },
  },
  {
    title: "备注",
    key: "remark",
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "创建时间",
    key: "createdAt",
    width: 160,
    render: (row) => {
      return row.createdAt ? new Date(row.createdAt).toLocaleString() : "-";
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 280,
    fixed: "right",
    align: "center",
    render: (row) => {
      return h(
        "div",
        { style: "display: flex; justify-content: center; gap: 10px;" },
        [
          h(
            "n-button",
            {
              size: "small",
              type: "primary",
              style:
                "padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
              onClick: () => handleView(row),
            },
            {
              default: () => "查看",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(EyeOutline),
                  }
                ),
            }
          ),
          h(
            "n-button",
            {
              size: "small",
              type: "info",
              style:
                "padding: 0 12px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
              onClick: () => handleEdit(row),
            },
            {
              default: () => "编辑",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(PencilOutline),
                  }
                ),
            }
          ),
          h(
            "n-button",
            {
              size: "small",
              type: row.status === true ? "warning" : "success",
              style:
                row.status === true
                  ? "padding: 0 12px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;"
                  : "padding: 0 12px; border-radius: 15px; background-color: #52c41a; border: none; color: white;",
              onClick: () => handleToggleStatus(row),
            },
            {
              default: () => (row.status === true ? "禁用" : "启用"),
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () =>
                      h(
                        row.status === true
                          ? CloseCircleOutline
                          : CheckmarkCircleOutline
                      ),
                  }
                ),
            }
          ),
          h(
            "n-button",
            {
              size: "small",
              type: "error",
              style:
                "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
              disabled: row.isSystem === 1,
              onClick: () => handleDelete(row),
            },
            {
              default: () => "删除",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(TrashOutline),
                  }
                ),
            }
          ),
        ]
      );
    },
  },
];

// 表单验证规则
const formRules = {
  category: [
    { required: true, message: "请选择标签类型", trigger: "change" },
  ],
  label: [
    { required: true, message: "请输入标签名称", trigger: "blur" },
    { min: 1, max: 50, message: "标签名称长度应在1-50字符之间", trigger: "blur" },
  ],

  sortOrder: [
    { type: "number", min: 0, message: "排序值不能为负数", trigger: "blur" },
  ],
};

// 加载标签数据
const loadData = async () => {
  try {
    loading.value = true;

    const params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      category: selectedCategory.value || undefined,
      keyword: searchParams.keyword || undefined,
      status: searchParams.status,
    };

    const res = await getTagConfigPage(params);
    if (res.code === 200) {
      tableData.value = res.data.records || [];
      pagination.itemCount = res.data.total || 0;
    } else {
      message.error(res.message || "加载数据失败");
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    message.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 加载标签类型列表和统计
const loadCategoryStats = async () => {
  try {
    const res = await getTagConfigCategoryList();
    if (res.code === 200) {
      categoryList.value = res.data || [];
    }
  } catch (error) {
    console.error("加载类型列表失败:", error);
    message.error("加载类型列表失败");
  }
};

// 处理类型选择
const handleCategorySelect = (category) => {
  selectedCategory.value = category;
  searchParams.category = category;
  pagination.page = 1;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 处理重置
const handleReset = () => {
  searchParams.keyword = "";
  searchParams.status = null;
  pagination.page = 1;
  loadData();
};

// 处理分页变化
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 处理页面大小变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = keys;
};

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editingItem, {
    id: null,
    category: selectedCategory.value || "",
    label: "",
    value: "",
    sortOrder: 0,
    remark: "",
    status: true,
    isSystem: 0,
  });
};

// 处理查看
const handleView = (row) => {
  Object.assign(editingItem, { ...row });
  showEditModal.value = true;
  // 可以添加只读模式的逻辑
};

// 处理添加
const handleAdd = () => {
  resetEditForm();
  showEditModal.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  Object.assign(editingItem, { ...row });
  showEditModal.value = true;
};

// 处理状态切换
const handleToggleStatus = (row) => {
  const newStatus = row.status === true ? false : true;
  const statusText = newStatus === true ? "启用" : "禁用";

  dialog.info({
    title: "确认操作",
    content: `确定要${statusText}标签"${row.label}"吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await updateTagConfig(row.id, { ...row, status: newStatus });
        if (res.code === 200) {
          message.success(`${statusText}成功`);
          loadData();
          loadCategoryStats();
        } else {
          message.error(res.message || `${statusText}失败`);
        }
      } catch (error) {
        console.error(`${statusText}失败:`, error);
        message.error(`${statusText}失败`);
      }
    },
  });
};

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除标签"${row.label}"吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await deleteTagConfig(row.id);
        if (res.code === 200) {
          message.success("删除成功");
          loadData();
          loadCategoryStats();
        } else {
          message.error(res.message || "删除失败");
        }
      } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败");
      }
    },
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  if (!selectedRows.value.length) {
    message.warning("请选择要删除的标签");
    return;
  }

  dialog.warning({
    title: "确认批量删除",
    content: `确定要删除选中的 ${selectedRows.value.length} 个标签吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await batchDeleteTagConfigs(selectedRows.value);
        if (res.code === 200) {
          message.success("批量删除成功");
          selectedRows.value = [];
          loadData();
          loadCategoryStats();
        } else {
          message.error(res.message || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除失败:", error);
        message.error("批量删除失败");
      }
    },
  });
};
const formRef = ref(null);
// 处理表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    submitting.value = true;

    const data = { ...editingItem };
    let res;

    if (data.id) {
      // 更新
      res = await updateTagConfig(data.id, data);
    } else {
      // 创建
      res = await createTagConfig(data);
    }

    if (res.code === 200) {
      message.success(data.id ? "更新成功" : "创建成功");
      showEditModal.value = false;
      loadData();
      loadCategoryStats();
    } else {
      message.error(res.message || (data.id ? "更新失败" : "创建失败"));
    }
  } catch (error) {
    console.error("提交失败:", error);
    if (error?.message) {
      message.error(error.message);
    }
  } finally {
    submitting.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  showEditModal.value = false;
  resetEditForm();
};

// 添加新类型
const handleAddCategory = async () => {
  if (!newCategoryKey.value.trim() || !newCategoryName.value.trim()) {
    message.error("请输入类型标识和名称");
    return false;
  }

  addingCategory.value = true;

  try {
    // 检查类型是否已存在
    const existingCategory = categoryList.value.find(
      c => c.value === newCategoryKey.value
    );
    if (existingCategory) {
      message.error("该类型标识已存在");
      return false;
    }

    // 添加到类型列表
    categoryList.value.push({
      label: newCategoryName.value,
      value: newCategoryKey.value,
      count: 0,
    });

    message.success("类型添加成功");
    closeAddCategoryModal();
    return true;
  } catch (error) {
    console.error("添加类型失败:", error);
    message.error("添加类型失败");
    return false;
  } finally {
    addingCategory.value = false;
  }
};

// 关闭添加类型弹窗
const closeAddCategoryModal = () => {
  newCategoryKey.value = "";
  newCategoryName.value = "";
  showAddCategoryModal.value = false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
  loadCategoryStats();
});
</script>

<style lang="scss" scoped>
.tag-config-container {
  padding: 20px;
}

.main-card {
  min-height: calc(100vh - 120px);
}

.content-layout {
  display: flex;
  gap: 20px;
  height: 100%;
}

.filter-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.filter-card {
  position: sticky;
  top: 20px;
}

.main-content {
  flex: 1;
  min-width: 0;
}

.action-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
