<template>
  <div class="teacher-detail-card">
    <!-- 顶部信息区 -->
    <div class="teacher-header">
      <div class="teacher-avatar-container" @click="handleAvatarClick">

        <n-avatar
          :src="formData.avatarFullUrl || defaultAvatar"
          :fallback-src="defaultAvatar"
          round
          :size="80"
          class="teacher-avatar"
        />
        <div class="upload-overlay">
          <n-icon size="22"><CloudUploadOutline /></n-icon>
        </div>
        <div
          class="teacher-status-badge"
          :class="{ active: localTeacherData.status === 1 }"
        >
          {{ localTeacherData.status === 1 ? "已启用" : "已禁用" }}
        </div>
      </div>
      <div class="teacher-basic-info">
        <h2>{{ localTeacherData.name }}</h2>
        <div class="teacher-id">ID: {{ localTeacherData.id }}</div>
        <div class="teacher-tags">
          <n-tag
            size="small"
            :color="{ color: '#1890ff', textColor: '#ffffff' }"
            class="info-tag"
          >
            <template #icon>
              <n-icon><PodiumOutline /></n-icon>
            </template>
            {{ localTeacherData.title || "未设置职称" }}
          </n-tag>
          <n-tag
            size="small"
            :color="{
              color: getRatingColor(localTeacherData.rating),
              textColor: '#ffffff',
            }"
            class="info-tag"
          >
            <template #icon>
              <n-icon><StarOutline /></n-icon>
            </template>
            评分 {{ localTeacherData.rating }}
          </n-tag>
          <n-tag
            size="small"
            :color="{ color: '#13c2c2', textColor: '#ffffff' }"
            class="info-tag"
          >
            <template #icon>
              <n-icon><PeopleOutline /></n-icon>
            </template>
            {{ localTeacherData.studentCount }} 学生
          </n-tag>
          <n-tag
            size="small"
            :color="{ color: '#fa8c16', textColor: '#ffffff' }"
            class="info-tag"
          >
            <template #icon>
              <n-icon><BookOutline /></n-icon>
            </template>
            {{ localTeacherData.courseCount }} 课程
          </n-tag>
        </div>
      </div>
    </div>

    <!-- 内容区 -->
    <n-divider />

    <!-- 查看模式 -->
    <div v-if="!editMode" class="teacher-detail-content">
      <!-- 第一行：基本信息 -->
      <div class="info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><PersonOutline /></n-icon
              >讲师姓名
            </div>
            <div class="info-value">{{ localTeacherData.name }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><PodiumOutline /></n-icon
              >职称/头衔
            </div>
            <div class="info-value">{{ localTeacherData.title || "-" }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><LocationOutline /></n-icon
              >所在地
            </div>
            <div class="info-value highlight-text">
              {{ localTeacherData.location || "-" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><StarOutline /></n-icon>评分
            </div>
            <div class="info-value">
              <n-rate
                :value="localTeacherData.rating"
                :count="5"
                readonly
                size="small"
              />
              <span style="margin-left: 8px">{{ localTeacherData.rating }}</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"
                ><CheckmarkCircleOutline /></n-icon
              >状态
            </div>
            <div class="info-value">
              <n-tag
                :type="localTeacherData.status === 1 ? 'success' : 'error'"
                size="small"
              >
                {{ localTeacherData.status === 1 ? "启用" : "禁用" }}
              </n-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><TimeOutline /></n-icon
              >创建时间
            </div>
            <div class="info-value">
              {{ formatDate(localTeacherData.createdAt) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行：统计信息 -->
      <div class="info-section">
        <h3 class="section-title">统计信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><PeopleOutline /></n-icon
              >学生总数
            </div>
            <div class="info-value highlight-text">
              {{ localTeacherData.studentCount || 0 }} 人
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><BookOutline /></n-icon
              >课程总数
            </div>
            <div class="info-value highlight-text">
              {{ localTeacherData.courseCount || 0 }} 门
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">
              <n-icon size="16" class="info-icon"><RibbonOutline /></n-icon
              >专业领域
            </div>
            <div class="info-value">
              {{ localTeacherData.expertise || "-" }}
            </div>
          </div>
        </div>
      </div>

      <!-- 第三行：讲师简介 -->
      <div class="info-section bio-section">
        <h3 class="section-title">讲师简介</h3>
        <div class="bio-content">
          {{ localTeacherData.introduction || "暂无简介" }}
        </div>
      </div>

      <!-- 第四行：教学经验 -->
      <div class="info-section bio-section">
        <h3 class="section-title">教学经验</h3>
        <div class="bio-content">
          {{ localTeacherData.experience || "暂无教学经验记录" }}
        </div>
      </div>
    </div>

    <!-- 编辑模式 -->
    <div v-else class="teacher-detail-content edit-mode">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="120px"
        require-mark-placement="right-hanging"
        :label-align="'left'"
      >
        <!-- 基本信息 -->
        <div class="edit-section">
          <h3 class="edit-section-title">基本信息</h3>
          <div class="edit-form-content">
            <div class="form-row">
              <n-form-item label="讲师姓名" path="name" class="form-item-half">
                <n-input
                  v-model:value="formData.name"
                  placeholder="请输入讲师姓名"
                />
              </n-form-item>
              <n-form-item label="职称/头衔" path="title" class="form-item-half">
                <n-input
                  v-model:value="formData.title"
                  placeholder="请输入职称，如：高级讲师、资深讲师等"
                />
              </n-form-item>
            </div>
            <div class="form-row">
              <n-form-item label="所在地" path="location" class="form-item-half">
                <n-input
                  v-model:value="formData.location"
                  placeholder="请输入所在地"
                />
              </n-form-item>
              <n-form-item label="评分" path="rating" class="form-item-half">
                <n-rate v-model:value="formData.rating" :count="5" />
              </n-form-item>
            </div>
            <div class="form-row">
              <n-form-item label="专业领域" path="expertise" class="form-item-full">
                <n-input
                  v-model:value="formData.expertise"
                  type="textarea"
                  placeholder="请输入专业领域，如：微积分、线性代数、数学建模、高等数学、概率论与数理统计等"
                  :autosize="{ minRows: 2, maxRows: 3 }"
                />
              </n-form-item>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="edit-section statistics-section" v-if="formData.id">
          <h3 class="edit-section-title">统计信息</h3>
          <div class="edit-form-content">
            <div class="statistics-grid">
              <div class="statistics-item">
                <div class="statistics-label">
                  <n-icon size="16" class="statistics-icon"><PeopleOutline /></n-icon>
                  学生总数
                </div>
                <n-input-number
                  v-model:value="formData.studentCount"
                  :min="0"
                  :show-button="false"
                  placeholder="0"
                  class="statistics-input"
                />
              </div>
              <div class="statistics-item">
                <div class="statistics-label">
                  <n-icon size="16" class="statistics-icon"><BookOutline /></n-icon>
                  课程总数
                </div>
                <n-input-number
                  v-model:value="formData.courseCount"
                  :min="0"
                  :show-button="false"
                  placeholder="0"
                  class="statistics-input"
                />
              </div>
              <div class="statistics-item">
                <div class="statistics-label">
                  <n-icon size="16" class="statistics-icon"><CheckmarkCircleOutline /></n-icon>
                  状态
                </div>
                <n-switch
                  v-model:value="formData.status"
                  :checked-value="1"
                  :unchecked-value="0"
                  size="large"
                  class="statistics-switch"
                >
                  <template #checked>启用</template>
                  <template #unchecked>禁用</template>
                </n-switch>
              </div>
            </div>
          </div>
        </div>

        <!-- 讲师简介 -->
        <div class="edit-section compact">
          <h3 class="edit-section-title">讲师简介</h3>
          <div class="edit-form-content">
            <n-form-item path="introduction" class="form-item-full">
              <n-input
                v-model:value="formData.introduction"
                type="textarea"
                placeholder="请输入讲师简介"
                :autosize="{ minRows: 2, maxRows: 3 }"
              />
            </n-form-item>
          </div>
        </div>

        <!-- 教学经验 -->
        <div class="edit-section compact">
          <h3 class="edit-section-title">教学经验</h3>
          <div class="edit-form-content">
            <n-form-item path="experience" class="form-item-full">
              <n-input
                v-model:value="formData.experience"
                type="textarea"
                placeholder="请输入教学经验"
                :autosize="{ minRows: 2, maxRows: 3 }"
              />
            </n-form-item>
          </div>
        </div>
      </n-form>
    </div>

    <!-- 底部操作区 -->
    <div class="action-footer">
      <n-space>
        <template v-if="editMode">
          <n-button type="primary" @click="handleSave" :loading="saveLoading">
            <template #icon
              ><n-icon><CheckmarkOutline /></n-icon
            ></template>
            保存
          </n-button>
          <!-- <n-button @click="cancelEdit">
            <template #icon
              ><n-icon><CloseOutline /></n-icon
            ></template>
            取消
          </n-button> -->
        </template>
        <template v-else>
          <n-button
            type="info"
            @click="handleToggleStatus"
            :disabled="saveLoading"
          >
            <template #icon
              ><n-icon><RefreshCircleOutline /></n-icon
            ></template>
            {{ localTeacherData.status === 1 ? "禁用" : "启用" }}
          </n-button>
          <n-button type="error" @click="handleDelete" :disabled="saveLoading">
            <template #icon
              ><n-icon><TrashOutline /></n-icon
            ></template>
            删除
          </n-button>
          <n-button @click="close">
            <template #icon
              ><n-icon><CloseOutline /></n-icon
            ></template>
            关闭
          </n-button>
        </template>
      </n-space>
    </div>

    <!-- 头像上传对话框 -->
    <n-modal
      v-model:show="showAvatarUploadModal"
      preset="card"
      title="修改头像"
      style="width: 300px"
    >
      <OssDirectUpload
        v-model="tempAvatar"
        category="image"
        :show-preview="true"
        @upload-success="handleAvatarUploadSuccess"
        @upload-error="handleAvatarUploadError"
      />

      <template #footer>
        <div style="text-align: right">
          <n-button
            style="margin-right: 8px"
            @click="showAvatarUploadModal = false"
            >取消</n-button
          >
          <n-button
            type="primary"
            :disabled="!tempAvatar"
            @click="confirmAvatarUpload"
            >确认使用</n-button
          >
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
  import {
    PersonOutline,
    LocationOutline,
    TimeOutline,
    BookOutline,
    PencilOutline,
    TrashOutline,
    CheckmarkCircleOutline,
    CloseCircleOutline,
    CheckmarkOutline,
    CloseOutline,
    CloudUploadOutline,
    StarOutline,
    PeopleOutline,
    PodiumOutline,
    RibbonOutline,
    RefreshCircleOutline,
  } from "@vicons/ionicons5";
  import { computed, ref, reactive, onMounted, nextTick, watch, toRef } from "vue";
  import { useMessage } from "naive-ui";
  import dayjs from "dayjs";
  import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";

  const props = defineProps({
    teacherData: {
      type: Object,
      required: true,
    },
    initialEditMode: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits([
    "edit",
    "toggle-status",
    "delete",
    "close",
    "save",
  ]);

  const defaultAvatar =
    "https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg";

  // 表单相关
  const editMode = ref(false);
  const formRef = ref(null);
  const saveLoading = ref(false);

  // 头像上传相关
  const showAvatarUploadModal = ref(false);
  const tempAvatar = ref("");
  const message = useMessage();

  // 创建本地响应式讲师数据副本
  const localTeacherData = reactive({ ...props.teacherData });

  // 表单数据
  const formData = reactive({
    id: null,
    name: "",
    avatar: "",
    avatarFullUrl: "", // 添加头像完整URL字段
    title: "",
    introduction: "",
    experience: "",
    expertise: "",
    location: "",
    rating: 5.0,
    studentCount: 0,
    courseCount: 0,
    status: 1,
  });

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: "请输入讲师姓名", trigger: "blur" }],
    title: [{ required: true, message: "请输入职称", trigger: "blur" }],
    introduction: [
      { required: true, message: "请输入讲师简介", trigger: "blur" },
    ],
    location: [{ required: true, message: "请输入所在地", trigger: "blur" }],
    expertise: [{ required: true, message: "请输入专业领域", trigger: "blur" }],
  };

  // 获取评分颜色
  const getRatingColor = (rating) => {
    if (rating >= 4.8) return "#f5222d"; // 红色 - 优秀
    if (rating >= 4.5) return "#fa8c16"; // 橙色 - 良好
    if (rating >= 4.0) return "#52c41a"; // 绿色 - 中等
    return "#1890ff"; // 蓝色 - 一般
  };

  // 开始编辑
  const startEdit = () => {
    // 使用nextTick确保DOM更新完成后再切换编辑模式
    nextTick(() => {
      editMode.value = true;
    });
  };

  // 取消编辑
  const cancelEdit = () => {
    editMode.value = false;
  };

  // 关闭弹窗
  const close = () => {
    emit("close");
  };

  // 处理状态切换
  const handleToggleStatus = () => {
    emit("toggle-status");
  };

  // 处理删除
  const handleDelete = () => {
    emit("delete");
  };

  // 保存编辑
  const handleSave = () => {
    formRef.value?.validate((errors) => {
      if (!errors) {
        saveLoading.value = true;
        // 触发保存事件
        emit("save", { ...formData });
        // 实际应用中，这里会有异步保存操作，完成后再关闭编辑模式
        // 这里简化处理
        setTimeout(() => {
          saveLoading.value = false;
          editMode.value = false;
        }, 500);
      } else {
        message.error("表单验证失败，请检查必填项");
      }
    });
  };

  // 日期格式化
  const formatDate = (date) => {
    return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
  };

  // 头像上传相关方法
  const handleAvatarClick = () => {
    showAvatarUploadModal.value = true;
    // 初始化临时头像
    tempAvatar.value = formData.avatar || "";
  };

  // 处理头像上传成功
  const handleAvatarUploadSuccess = (fileData) => {
    console.log("头像上传成功:", fileData);
    tempAvatar.value = fileData.objectKey;
    formData.avatarFullUrl = fileData.baseUrl + fileData.objectKey;
  };

  // 处理头像上传失败
  const handleAvatarUploadError = (error) => {
    console.error("头像上传失败:", error);
  };

  const beforeAvatarUpload = (data) => {
    // 验证图片格式
    if (!data.file.type.startsWith("image/")) {
      message.error("只能上传图片文件");
      return false;
    }
    // 验证图片大小，限制为2MB
    if (data.file.size > 2 * 1024 * 1024) {
      message.error("图片大小不能超过2MB");
      return false;
    }
    return true;
  };

  const handleAvatarUploadChange = ({ file }) => {
    // 如果文件已上传，生成预览
    if (file.status === "finished" || file.status === undefined) {
      // 创建本地预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        previewAvatar.value = e.target.result;
      };
      reader.readAsDataURL(file.file);
    }
  };

  const confirmAvatarUpload = () => {
    if (tempAvatar.value) {
      // 更新表单数据中的头像URL
      formData.avatar = tempAvatar.value;
      // 如果不是编辑模式，自动开启编辑模式
      if (!editMode.value) {
        startEdit();
      }
      showAvatarUploadModal.value = false;
      tempAvatar.value = "";
      message.success("头像已更新，请点击保存以应用更改");
    }
  };

  // 监听teacherData变化，同步更新localTeacherData和formData
  watch(() => props.teacherData, (newTeacherData) => {
    // 更新本地讲师数据副本
    Object.assign(localTeacherData, newTeacherData);
    
    // 更新表单数据
    Object.keys(formData).forEach((key) => {
      if (key in newTeacherData) {
        formData[key] = newTeacherData[key];
      }
    });
  }, { deep: true, immediate: true });

  // 如果initialEditMode为true，初始化时自动进入编辑模式
  onMounted(() => {
    if (props.initialEditMode) {
      startEdit();
    }
  });
</script>

<style lang="scss" scoped>
  .teacher-detail-card {
    padding: 0;
    background-color: #ffffff;
    border-radius: 18px;
    width: 100%;

    .teacher-header {
      display: flex;
      padding: 16px 24px;
      border-radius: 8px 8px 0 0;
      background: #f5f7fa;

      .teacher-avatar-container {
        position: relative;
        margin-right: 20px;
        cursor: pointer;

        &:hover .upload-overlay {
          opacity: 1;
        }

        .teacher-avatar {
          border: 3px solid #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .upload-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
          color: white;
        }

        .teacher-status-badge {
          position: absolute;
          bottom: 1px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 12px;
          padding: 1px 8px;
          border-radius: 10px;
          background-color: #d03050;
          color: white;
          font-weight: 500;
          white-space: nowrap;

          &.active {
            background-color: #18a058;
          }
        }
      }

      .teacher-basic-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        h2 {
          margin: 0 0 4px 0;
          font-size: 20px;
          color: #333;
          font-weight: 600;
        }

        .teacher-id {
          color: #8c8c8c;
          font-size: 15px;
          margin-bottom: 8px;
        }

        .teacher-tags {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .info-tag {
            border-radius: 12px;
            padding: 0 10px;
          }
        }
      }
    }

    .teacher-detail-content {
      padding: 16px 2px 2px;

      &.edit-mode {
        padding: 16px 12px 0;
        background-color: #fafbfc;
        border-radius: 8px;
        margin: 4px;
      }

      .info-section {
        margin-bottom: 16px;

        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #333;
          margin-bottom: 12px;
          border-left: 3px solid #1890ff;
          padding-left: 8px;
          display: flex;
          align-items: center;

          &::before {
            content: "";
            margin-right: 8px;
          }
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
          }

          @media (max-width: 480px) {
            grid-template-columns: 1fr;
          }

          .info-item {
            .info-label {
              color: #666;
              font-size: 15px;
              margin-bottom: 4px;
              display: flex;
              align-items: center;

              .info-icon {
                margin-right: 5px;
                color: #909399;
              }
            }

            .info-value {
              color: #333;
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;

              &.highlight-text {
                color: #1890ff;
              }
            }
          }
        }

        .form-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;

          @media (max-width: 768px) {
            grid-template-columns: repeat(2, 1fr);
          }

          @media (max-width: 480px) {
            grid-template-columns: 1fr;
          }

          :deep(.n-form-item) {
            margin-bottom: 0;

            &.n-form-item--top-labelled {
              grid-column: span 3;
            }
          }
        }
      }

      // 编辑模式专用样式
      .edit-section {
        margin-bottom: 16px;
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #f0f2f5;

        &:last-child {
          margin-bottom: 0;
        }

        &.compact {
          margin-bottom: 12px;
          padding: 12px 16px;
        }

        .edit-section-title {
          font-size: 15px;
          font-weight: 600;
          color: #333;
          margin-bottom: 16px;
          border-left: 4px solid #1890ff;
          padding-left: 12px;
          display: flex;
          align-items: center;
        }

        &.compact .edit-section-title {
          margin-bottom: 12px;
          font-size: 14px;
        }

        // 统计信息专用样式
        &.statistics-section {
          // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          // color: white;

          .edit-section-title {
            // color: white;
            // border-left-color: #ffffff;
          }

          .statistics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;

            @media (max-width: 768px) {
              grid-template-columns: 1fr;
              gap: 16px;
            }
          }

          .statistics-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.25);
              transform: translateY(-2px);
            }

            .statistics-label {
              display: flex;
              align-items: center;
              font-size: 14px;
              font-weight: 500;
              // color: rgba(255, 255, 255, 0.9);
              margin-bottom: 12px;
              white-space: nowrap;

              .statistics-icon {
                margin-right: 6px;
                // color: rgba(255, 255, 255, 0.8);
              }
            }

            .statistics-input {
              width: 100%;

              :deep(.n-input-number) {
                .n-input-number-input {
                  .n-input__input-el {
                    background: rgba(255, 255, 255, 0.9);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: #333;
                    text-align: center;
                    font-weight: 600;
                    font-size: 16px;
                    border-radius: 8px;

                    &::placeholder {
                      color: #999;
                    }

                    &:focus {
                      background: white;
                      border-color: #667eea;
                      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
                    }
                  }
                }
              }
            }

            .statistics-switch {
              :deep(.n-switch) {
                --n-rail-color-active: rgba(255, 255, 255, 0.9);
                --n-rail-color: rgba(255, 255, 255, 0.3);

                .n-switch__checked-text,
                .n-switch__unchecked-text {
                  color: #333;
                  font-weight: 500;
                }
              }
            }
          }
        }

        .edit-form-content {
          .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            @media (max-width: 768px) {
              flex-direction: column;
              gap: 12px;
            }
          }

          .form-item-half {
            flex: 1;
            min-width: 0;
          }

          .form-item-third {
            flex: 1;
            min-width: 0;
          }

          .form-item-full {
            width: 100%;
          }

          :deep(.n-form-item) {
            margin-bottom: 0;

            .n-form-item-label {
              font-weight: 500;
              color: #555;
              font-size: 14px;
              padding-bottom: 8px;
            }

            .n-form-item-feedback-wrapper {
              min-height: 24px;
            }
          }

          :deep(.n-input),
          :deep(.n-input-number),
          :deep(.n-rate),
          :deep(.n-switch) {
            border-radius: 8px;
          }

          :deep(.n-input) {
            .n-input__input-el {
              padding: 8px 12px;
              font-size: 14px;
            }
          }

          :deep(.n-input--textarea) {
            .n-input__textarea-el {
              padding: 8px 12px;
              font-size: 14px;
              line-height: 1.6;
            }
          }

          :deep(.n-input-number) {
            .n-input-number-input {
              .n-input__input-el {
                padding: 8px 12px;
              }
            }
          }
        }
      }

      .bio-section {
        margin-bottom: 0;

        .bio-content {
          background-color: #f9f9f9;
          border-radius: 6px;
          padding: 12px;
          color: #555;
          line-height: 1.6;
          min-height: 80px;
        }
      }
    }

    .action-footer {
      display: flex;
      justify-content: center;
      padding: 16px;
      border-top: 1px solid #f0f0f0;
      margin-top: 12px;
    }
  }

  .n-divider {
    margin: 0;
  }

  .preview-avatar {
    border: 2px solid #eee;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
</style>
