<template>
  <div class="page-container">
    <n-card title="讲师管理" class="content-card">
      <!-- 搜索区域 -->
      <div class="search-bar">
        <n-form inline :label-width="80">
          <n-form-item label="讲师姓名">
            <n-input
              v-model:value="searchParams.name"
              placeholder="请输入讲师姓名"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>
          <n-form-item label="职称">
            <n-input
              v-model:value="searchParams.title"
              placeholder="请输入职称"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>
          <n-form-item label="专业领域">
            <n-input
              v-model:value="searchParams.expertise"
              placeholder="请输入专业领域"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>

          <n-form-item>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button class="m-l-10" @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-form-item>
        </n-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button type="primary" @click="handleAdd">
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加讲师
        </n-button>
        <n-button
          class="m-l-10"
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        remote
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:sorter="handleSorterChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />
    </n-card>
  </div>
</template>

<script setup>
  import { h, ref, reactive, computed, onMounted } from "vue";
  import {
    AddOutline,
    RefreshOutline,
    SearchOutline,
    PencilOutline,
    TrashOutline,
    CloseCircleOutline,
    CheckmarkCircleOutline,
    PersonOutline,
    BookOutline,
    SchoolOutline,
    StarOutline,
    PeopleOutline,
  } from "@vicons/ionicons5";
  import {
    getTeacherList,
    createTeacher,
    updateTeacher,
    deleteTeacher,
    batchDeleteTeachers,
  } from "@/api/teacher";
  import TeacherDetailCard from "./TeacherDetailCard.vue";

  const message = useMessage();
  const dialog = useDialog();

  // 表格数据
  const loading = ref(false);
  const tableData = ref([]);
  const selectedRowKeys = ref([]);

  // 搜索参数
  const searchParams = reactive({
    name: "",
    title: "",
    expertise: "",
    location: "",
    status: null,
  });

  // 状态选项
  const statusOptions = [
    { label: "启用", value: 1 },
    { label: "禁用", value: 0 },
  ];

  // 分页
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [10, 20, 30, 50],
    sortField: null,
    sortOrder: null,
    onChange: (page) => {
      pagination.page = page;
    },
    onUpdatePageSize: (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
    },
  });

  // 获取讲师评分展示颜色
  const getRatingColor = (rating) => {
    if (rating >= 4.8) return "#f5222d"; // 红色 - 优秀
    if (rating >= 4.5) return "#fa8c16"; // 橙色 - 良好
    if (rating >= 4.0) return "#52c41a"; // 绿色 - 中等
    return "#1890ff"; // 蓝色 - 一般
  };

  // 表格列
  const createColumns = ({ handleEdit, handleDelete }) => {
    return [
      {
        type: "selection",
        align: "center",
      },
      {
        title: "ID",
        key: "id",
        width: 80,
        align: "center",
      },
      {
        title: "讲师姓名",
        key: "name",
        width: 120,
        align: "center",
        render(row) {
          return h(
            "div",
            {
              style:
                "display: flex; align-items: center; justify-content: center; flex-direction: column;",
            },
            [
              row.avatar
                ? h("n-avatar", {
                    size: "small",
                    src: row.avatar,
                    round: true,
                    style: "margin-bottom: 4px;",
                  })
                : null,
              h("span", { style: "font-weight: 500;" }, row.name),
            ]
          );
        },
      },
      {
        title: "职称",
        key: "title",
        width: 150,
        align: "center",
        render(row) {
          return h(
            "n-tag",
            {
              type: "success",
              size: "small",
              style:
                "background-color: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff; padding: 0 10px; font-weight: 500; border-radius: 12px;",
            },
            { default: () => row.title }
          );
        },
      },
      {
        title: "专业领域",
        key: "expertise",
        width: 180,
        align: "center",
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: "评分",
        key: "rating",
        width: 100,
        align: "center",
        sorter: true,
        sortOrder: computed(() => {
          if (pagination.sortField === "rating") {
            return pagination.sortOrder;
          }
          return false;
        }),
        render(row) {
          return h(
            "n-tag",
            {
              type: "warning",
              size: "small",
              round: true,
              style: `background-color: ${getRatingColor(
                row.rating
              )}; color: white; border: none; padding: 2px 10px; border-radius: 12px; font-weight: 500;`,
            },
            {
              default: () => row.rating,
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                    style: "margin-right: 4px;",
                  },
                  {
                    default: () => h(StarOutline),
                  }
                ),
            }
          );
        },
      },
      {
        title: "学生数",
        key: "studentCount",
        width: 100,
        align: "center",
        sorter: true,
        sortOrder: computed(() => {
          if (pagination.sortField === "studentCount") {
            return pagination.sortOrder;
          }
          return false;
        }),
        render(row) {
          return h(
            "n-tag",
            {
              type: "info",
              size: "small",
              style:
                "background-color: #e6fffb; color: #13c2c2; border: 1px solid #87e8de; padding: 0 10px; font-weight: 500;",
            },
            {
              default: () => row.studentCount,
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                    style: "margin-right: 4px;",
                  },
                  {
                    default: () => h(PeopleOutline),
                  }
                ),
            }
          );
        },
      },
      {
        title: "课程数",
        key: "courseCount",
        width: 100,
        align: "center",
        render(row) {
          return h(
            "n-tag",
            {
              type: "info",
              size: "small",
              style:
                "background-color: #fff7e6; color: #fa8c16; border: 1px solid #ffd591; padding: 0 10px; font-weight: 500;",
            },
            {
              default: () => row.courseCount,
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                    style: "margin-right: 4px;",
                  },
                  {
                    default: () => h(BookOutline),
                  }
                ),
            }
          );
        },
      },
      {
        title: "状态",
        key: "status",
        width: 100,
        align: "center",
        render(row) {
          return h(
            "n-tag",
            {
              type: row.status === 1 ? "success" : "error",
              size: "medium",
              style:
                row.status === 1
                  ? "background-color: #18a058; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;"
                  : "background-color: #d03050; color: white; border: none; font-weight: 500; padding: 2px 12px; border-radius: 15px;",
            },
            {
              default: () => (row.status === 1 ? "启用" : "禁用"),
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 16,
                    style: "margin-right: 4px;",
                  },
                  {
                    default: () =>
                      h(
                        row.status === 1
                          ? CheckmarkCircleOutline
                          : CloseCircleOutline
                      ),
                  }
                ),
            }
          );
        },
      },
      {
        title: "操作",
        key: "actions",
        width: 250,
        fixed: "right",
        align: "center",
        render(row) {
          return h(
            "div",
            { style: "display: flex; justify-content: center; gap: 10px;" },
            [
              h(
                "n-button",
                {
                  size: "small",
                  type: "primary",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
                  onClick: () => handleView(row),
                },
                {
                  default: () => "查看",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(PersonOutline),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: "info",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #13c2c2; border: none; color: white;",
                  onClick: () => handleEdit(row),
                },
                {
                  default: () => "编辑",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(PencilOutline),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: row.status === 1 ? "warning" : "success",
                  style:
                    row.status === 1
                      ? "padding: 0 12px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;"
                      : "padding: 0 12px; border-radius: 15px; background-color: #52c41a; border: none; color: white;",
                  onClick: () => handleToggleStatus(row),
                },
                {
                  default: () => (row.status === 1 ? "禁用" : "启用"),
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () =>
                          h(
                            row.status === 1
                              ? CloseCircleOutline
                              : CheckmarkCircleOutline
                          ),
                      }
                    ),
                }
              ),
              h(
                "n-button",
                {
                  size: "small",
                  type: "error",
                  style:
                    "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
                  onClick: () => handleDelete(row),
                },
                {
                  default: () => "删除",
                  icon: () =>
                    h(
                      "n-icon",
                      {
                        size: 14,
                      },
                      {
                        default: () => h(TrashOutline),
                      }
                    ),
                }
              ),
            ]
          );
        },
      },
    ];
  };

  const columns = createColumns({
    handleEdit,
    handleDelete,
  });

  // 加载数据
  const loadData = async () => {
    loading.value = true;
    try {
      const params = {
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
        ...searchParams,
      };

      // 添加排序参数
      if (pagination.sortField && pagination.sortOrder) {
        params.orderField = pagination.sortField;
        params.orderType = pagination.sortOrder === "ascend" ? "asc" : "desc";
      }

      // 调用API
      const res = await getTeacherList(params);

      // 更新数据
      tableData.value = res.data.records;
      pagination.pageCount = res.data.pages;
      pagination.page = res.data.current;
      pagination.pageSize = res.data.size;
      pagination.itemCount = res.data.total;
    } catch (error) {
      console.error("加载讲师数据失败:", error);
      message.error("加载讲师数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 处理选中行变化
  const handleCheckedRowKeysChange = (keys) => {
    selectedRowKeys.value = keys;
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page;
    loadData();
  };

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadData();
  };

  // 处理排序变化
  const handleSorterChange = (sorter) => {
    if (sorter) {
      pagination.sortField = sorter.columnKey;
      pagination.sortOrder = sorter.order;
    } else {
      pagination.sortField = null;
      pagination.sortOrder = null;
    }
    loadData();
  };

  // 处理搜索
  const handleSearch = () => {
    pagination.page = 1;
    loadData();
  };

  // 重置搜索
  const resetSearch = () => {
    Object.keys(searchParams).forEach((key) => {
      searchParams[key] = key === "status" ? null : "";
    });
    pagination.page = 1;
    pagination.sortField = null;
    pagination.sortOrder = null;
    loadData();
  };

  // 修改handleAdd函数，创建空的讲师数据并打开TeacherDetailCard编辑模式
  const handleAdd = () => {
    const newTeacher = {
      id: null,
      name: "",
      avatar: "",
      title: "",
      introduction: "",
      experience: "",
      expertise: "",
      location: "",
      rating: 5.0,
      studentCount: 0,
      courseCount: 0,
      status: 1,
    };

    // 打开编辑对话框
    dialogInstRef.value = dialog.info({
      title: "添加讲师",
      content: () => {
        return h(TeacherDetailCard, {
          teacherData: newTeacher,
          initialEditMode: true,
          onSave: (teacherData) => {
            handleSaveTeacher(teacherData);
          },
          onClose: () => {
            dialogInstRef.value?.destroy();
          },
        });
      },
      closable: true,
      maskClosable: false,
      positiveText: null,
      negativeText: null,
      style: { width: "700px", maxWidth: "90vw" },
    });
  };

  // 添加dialogInstRef变量用于保存对话框实例
  const dialogInstRef = ref(null);

  // 修改handleView函数以显示TeacherDetailCard组件
  function handleView(row) {
    // 保存对话框实例以便后续关闭
    console.log(row);
    dialogInstRef.value = dialog.info({
      title: "讲师详情",
      content: () => {
        return h(TeacherDetailCard, {
          teacherData: row,
          onEdit: () => handleEdit(row),
          onToggleStatus: () => handleToggleStatus(row),
          onDelete: () => handleDelete(row),
          onSave: (updatedData) => {
            // 处理保存逻辑
            handleSaveTeacher(updatedData);
          },
          onClose: () => {
            dialogInstRef.value?.destroy();
          },
        });
      },
      closable: true,
      maskClosable: true,
      positiveText: null,
      negativeText: null,
      style: { width: "700px", maxWidth: "90vw" },
    });
  }

  // 添加保存讲师信息的处理函数
  const handleSaveTeacher = async (teacherData) => {
    try {
      const saveData = { ...teacherData };
      const res = await updateTeacher(saveData);

      if (res.code === 200) {
        message.success("更新成功");
        // 关闭弹窗
        dialogInstRef.value?.destroy();
        loadData(); // 刷新数据
      } else {
        message.error(res.message || "更新失败");
      }
    } catch (error) {
      console.error("更新讲师失败:", error);
      message.error("更新失败");
    }
  };

  // 修改handleEdit函数，使用对话框直接打开TeacherDetailCard组件的编辑模式
  function handleEdit(row) {
    // 保存对话框实例以便后续关闭
    console.log(row);
    dialogInstRef.value = dialog.info({
      title: "编辑讲师",
      content: () => {
        return h(TeacherDetailCard, {
          teacherData: row,
          initialEditMode: true, // 初始化为编辑模式
          onToggleStatus: () => handleToggleStatus(row),
          onDelete: () => handleDelete(row),
          onSave: (updatedData) => {
            // 处理保存逻辑
            handleSaveTeacher(updatedData);
          },
          onClose: () => {
            dialogInstRef.value?.destroy();
          },
        });
      },
      closable: true,
      maskClosable: false, // 编辑模式下禁止点击遮罩关闭
      positiveText: null,
      negativeText: null,
      style: { width: "700px", maxWidth: "90vw" },
    });
  }

  // 处理状态切换
  const handleToggleStatus = (row) => {
    const action = row.status === 1 ? "禁用" : "启用";
    dialog.warning({
      title: `确认${action}`,
      content: `确定要${action}讲师 "${row.name}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          // 模拟API调用，实际项目中需要实现
          const newStatus = row.status === 1 ? 0 : 1;
          const res = await updateTeacher({
            ...row,
            status: newStatus,
          });

          if (res.code === 200) {
            message.success(`${action}成功`);
            // 更新本地数据状态
            row.status = newStatus;
            // 刷新数据以确保同步
            loadData();
            // 如果弹窗打开，关闭弹窗重新打开以更新状态
            if (dialogInstRef.value) {
              dialogInstRef.value.destroy();
            }
          } else {
            message.error(res.message || `${action}失败`);
          }
        } catch (error) {
          message.error(`${action}失败`);
        }
      },
    });
  };

  // 处理删除
  function handleDelete(row) {
    dialog.warning({
      title: "确认删除",
      content: `确定要删除讲师 "${row.name}" 吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const res = await deleteTeacher(row.id);
          if (res.code === 200) {
            message.success("删除成功");
            loadData();
          } else {
            message.error(res.message || "删除失败");
          }
        } catch (error) {
          message.error("删除失败");
        }
      },
    });
  }

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning("请至少选择一条记录");
      return;
    }

    dialog.warning({
      title: "确认批量删除",
      content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
      positiveText: "确定",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          const res = await batchDeleteTeachers(selectedRowKeys.value);
          if (res.code === 200) {
            message.success("批量删除成功");
            selectedRowKeys.value = [];
            loadData();
          } else {
            message.error(res.message || "批量删除失败");
          }
        } catch (error) {
          message.error("批量删除失败");
        }
      },
    });
  };

  onMounted(() => {
    loadData();
  });
</script>

<style lang="scss" scoped>
  .content-card {
    margin-bottom: 20px;
  }

  .action-bar {
    margin-bottom: 20px;
  }

  .search-bar {
    margin-bottom: 0px;
  }

  .m-l-10 {
    margin-left: 10px;
  }
</style>
