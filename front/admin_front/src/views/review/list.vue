<template>
  <div class="page-container">
    <n-card title="课程评价管理" class="content-card">
      <!-- 搜索区域 -->
      <n-grid :cols="4" :x-gap="16">
        <n-gi>
          <n-form-item label="课程名称">
            <n-input
              v-model:value="searchParams.courseName"
              placeholder="请输入课程名称"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="用户名称">
            <n-input
              v-model:value="searchParams.username"
              placeholder="请输入用户名称"
              clearable
              @keydown.enter="handleSearch"
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="评分">
            <n-select
              v-model:value="searchParams.rating"
              :options="ratingOptions"
              placeholder="请选择评分"
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-space>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-space>
        </n-gi>
      </n-grid>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
        <n-button class="m-l-10" type="warning" @click="loadData">
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />

      <!-- 评价详情对话框 -->
      <n-modal
        v-model:show="showDetailModal"
        title="评价详情"
        preset="card"
        :style="{ width: '600px' }"
      >
        <n-descriptions bordered>
          <n-descriptions-item label="用户名称">
            {{ detailData.username }}
          </n-descriptions-item>
          <n-descriptions-item label="课程名称">
            {{ detailData.courseName }}
          </n-descriptions-item>
          <n-descriptions-item label="评分">
            <n-rate readonly :value="detailData.rating" />
          </n-descriptions-item>
          <n-descriptions-item label="评价时间">
            {{ detailData.createTime }}
          </n-descriptions-item>
          <n-descriptions-item label="评价内容" :span="3">
            {{ detailData.content }}
          </n-descriptions-item>
        </n-descriptions>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showDetailModal = false">关闭</n-button>
            <n-button type="error" @click="handleDelete(detailData)">
              删除评价
            </n-button>
          </n-space>
        </template>
      </n-modal>
    </n-card>
  </div>
</template>

<script setup>
import { h, ref, reactive, onMounted } from "vue";
import {
  SearchOutline,
  RefreshOutline,
  TrashOutline,
  EyeOutline,
  StarOutline,
} from "@vicons/ionicons5";
import {
  getCourseReviews,
  getCourseReviewDetail,
  deleteCourseReview,
  batchDeleteCourseReviews,
} from "@/api/userInteraction";

const message = useMessage();
const dialog = useDialog();

// 表格数据
const loading = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 搜索参数
const searchParams = reactive({
  courseName: "",
  username: "",
  rating: null,
});

// 评分选项
const ratingOptions = [
  { label: "5星", value: 5 },
  { label: "4星", value: 4 },
  { label: "3星", value: 3 },
  { label: "2星", value: 2 },
  { label: "1星", value: 1 },
];

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// 评价详情
const showDetailModal = ref(false);
const detailData = reactive({
  id: null,
  username: "",
  userId: null,
  courseName: "",
  courseId: null,
  rating: 0,
  content: "",
  createTime: "",
});

// 表格列
const createColumns = () => {
  return [
    {
      type: "selection",
    },
    {
      title: "ID",
      key: "id",
      width: 80,
    },
    {
      title: "用户名称",
      key: "username",
      width: 150,
    },
    {
      title: "课程名称",
      key: "courseName",
      width: 200,
    },
    {
      title: "评分",
      key: "rating",
      width: 150,
      render(row) {
        return h(
          "div",
          {
            class: "flex-start",
          },
          [
            h(
              "n-rate",
              {
                readonly: true,
                value: row.rating,
                size: "small",
              },
              {}
            ),
          ]
        );
      },
    },
    {
      title: "评价内容",
      key: "content",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "评价时间",
      key: "createTime",
      width: 180,
      sorter: "default",
    },
    {
      title: "操作",
      key: "actions",
      width: 150,
      fixed: "right",
      render(row) {
        return h("div", { class: "flex-start" }, [
          h(
            "n-button",
            {
              quaternary: true,
              size: "small",
              type: "info",
              onClick: () => handleViewDetail(row),
            },
            {
              default: () => "查看",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(EyeOutline),
                  }
                ),
            }
          ),
          h(
            "n-button",
            {
              quaternary: true,
              size: "small",
              type: "error",
              onClick: () => handleDelete(row),
            },
            {
              default: () => "删除",
              icon: () =>
                h(
                  "n-icon",
                  {
                    size: 14,
                  },
                  {
                    default: () => h(TrashOutline),
                  }
                ),
            }
          ),
        ]);
      },
    },
  ];
};

const columns = createColumns();

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      ...searchParams,
    };

    // 调用API
    const res = await getCourseReviews(params);

    // 更新数据
    tableData.value = res.data.records;
    pagination.pageCount = res.data.pages;
    pagination.page = res.data.current;
    pagination.pageSize = res.data.size;
    pagination.itemCount = res.data.total;
  } catch (error) {
    console.error("加载评价数据失败:", error);
    message.error("加载评价数据失败");
  } finally {
    loading.value = false;
  }
};

// 处理选中行变化
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach((key) => {
    searchParams[key] = key === "rating" ? null : "";
  });
  pagination.page = 1;
  loadData();
};

// 处理查看详情
const handleViewDetail = async (row) => {
  try {
    const res = await getCourseReviewDetail(row.id);
    if (res.code === 200) {
      Object.keys(detailData).forEach((key) => {
        detailData[key] = res.data[key] || row[key] || "";
      });
      showDetailModal.value = true;
    } else {
      message.error(res.message || "获取评价详情失败");
    }
  } catch (error) {
    message.error("获取评价详情失败");
  }
};

// 处理删除
const handleDelete = (row) => {
  dialog.warning({
    title: "确认删除",
    content: "确定要删除该评价吗？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await deleteCourseReview(row.id);
        if (res.code === 200) {
          message.success("删除成功");
          showDetailModal.value = false;
          loadData();
        } else {
          message.error(res.message || "删除失败");
        }
      } catch (error) {
        message.error("删除失败");
      }
    },
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请至少选择一条记录");
    return;
  }

  dialog.warning({
    title: "确认批量删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const res = await batchDeleteCourseReviews(selectedRowKeys.value);
        if (res.code === 200) {
          message.success("批量删除成功");
          selectedRowKeys.value = [];
          loadData();
        } else {
          message.error(res.message || "批量删除失败");
        }
      } catch (error) {
        message.error("批量删除失败");
      }
    },
  });
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.content-card {
  margin-bottom: 20px;
}

.action-bar {
  margin: 20px 0;
}

.flex-start {
  display: flex;
  align-items: center;
  gap: 8px;
}

.m-l-10 {
  margin-left: 10px;
}
</style>
