<template>
  <div class="page-container">
    <n-grid :cols="24" :x-gap="16" :y-gap="16">
      <!-- 统计卡片 -->
      <n-gi :span="6">
        <n-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <n-icon size="30"><PersonOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statData.totalUsers }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
        </n-card>
      </n-gi>
      <n-gi :span="6">
        <n-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon course-icon">
              <n-icon size="30"><BookOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statData.totalCourses }}</div>
              <div class="stat-label">总课程数</div>
            </div>
          </div>
        </n-card>
      </n-gi>
      <n-gi :span="6">
        <n-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon user-add-icon">
              <n-icon size="30"><PersonAddOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statData.todayNewUsers }}</div>
              <div class="stat-label">今日新增用户</div>
            </div>
          </div>
        </n-card>
      </n-gi>
      <n-gi :span="6">
        <n-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon sales-icon">
              <n-icon size="30"><CashOutline /></n-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ formatNumber(statData.todaySales) }}</div>
              <div class="stat-label">今日销售额</div>
            </div>
          </div>
        </n-card>
      </n-gi>

      <!-- 用户增长趋势图 -->
      <n-gi :span="12">
        <n-card title="用户增长趋势" class="chart-card">
          <n-spin :show="loadingUserTrend">
            <div ref="userChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <!-- 销售额趋势图 -->
      <n-gi :span="12">
        <n-card title="销售额趋势" class="chart-card">
          <n-spin :show="loadingSalesTrend">
            <div ref="salesChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <!-- 课程分类分布 -->
      <n-gi :span="12">
        <n-card title="课程分类分布" class="chart-card">
          <n-spin :show="loadingCategoryDistribution">
            <div ref="categoryChartRef" class="chart-container"></div>
          </n-spin>
        </n-card>
      </n-gi>

      <!-- 热门课程 -->
      <n-gi :span="12">
        <n-card title="热门课程排行" class="table-card">
          <n-spin :show="loadingHotCourses">
            <n-list hoverable clickable>
              <n-list-item v-for="(course, index) in hotCourses" :key="course.id">
                <n-thing :title="course.title" :description="course.description">
                  <template #avatar>
                    <n-avatar
                      :style="{
                        backgroundColor: getRandomColor(index),
                        color: '#fff',
                      }"
                      >{{ index + 1 }}</n-avatar
                    >
                  </template>
                  <template #header-extra>
                    <n-tag type="info">{{ course.sales }}人购买</n-tag>
                  </template>
                  <template #footer>
                    <n-space>
                      <n-tag type="success">
                        {{ course.categoryName }}
                      </n-tag>
                      <n-tag>
                        {{ course.teacherName }}
                      </n-tag>
                      <span class="price">¥{{ course.price }}</span>
                    </n-space>
                  </template>
                </n-thing>
              </n-list-item>
            </n-list>
          </n-spin>
        </n-card>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { useMessage } from "naive-ui";
import * as echarts from "echarts";
import {
  PersonOutline,
  BookOutline,
  PersonAddOutline,
  CashOutline,
} from "@vicons/ionicons5";
import {
  getTotalUsers,
  getTotalCourses,
  getTodayNewUsers,
  getTodaySales,
  getUserGrowthTrend,
  getSalesTrend,
  getCoursesCategoryDistribution,
  getHotCourses,
} from "@/api/statistics";

const message = useMessage();

// 图表DOM引用
const userChartRef = ref(null);
const salesChartRef = ref(null);
const categoryChartRef = ref(null);

// 图表实例
let userChart = null;
let salesChart = null;
let categoryChart = null;

// 加载状态
const loadingUserTrend = ref(false);
const loadingSalesTrend = ref(false);
const loadingCategoryDistribution = ref(false);
const loadingHotCourses = ref(false);

// 统计数据
const statData = reactive({
  totalUsers: 0,
  totalCourses: 0,
  todayNewUsers: 0,
  todaySales: 0,
});

// 热门课程数据
const hotCourses = ref([]);

// 格式化数字，添加千位分隔符
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 随机颜色
const getRandomColor = (index) => {
  const colors = [
    "#1890ff",
    "#36cbcb",
    "#4ecb73",
    "#fbd437",
    "#f2637b",
    "#975fe4",
    "#36cbcb",
    "#fd7e14",
  ];
  return colors[index % colors.length];
};

// 初始化用户增长趋势图
const initUserChart = () => {
  if (userChart) {
    userChart.dispose();
  }
  userChart = echarts.init(userChartRef.value);
  userChart.setOption({
    tooltip: {
      trigger: "axis",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "新增用户",
        type: "line",
        smooth: true,
        data: [],
        itemStyle: {
          color: "#1890ff",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(24,144,255,0.3)",
            },
            {
              offset: 1,
              color: "rgba(24,144,255,0.1)",
            },
          ]),
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    userChart && userChart.resize();
  });
};

// 初始化销售额趋势图
const initSalesChart = () => {
  if (salesChart) {
    salesChart.dispose();
  }
  salesChart = echarts.init(salesChartRef.value);
  salesChart.setOption({
    tooltip: {
      trigger: "axis",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "¥{value}",
      },
    },
    series: [
      {
        name: "销售额",
        type: "line",
        smooth: true,
        data: [],
        itemStyle: {
          color: "#36cbcb",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(54,203,203,0.3)",
            },
            {
              offset: 1,
              color: "rgba(54,203,203,0.1)",
            },
          ]),
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    salesChart && salesChart.resize();
  });
};

// 初始化课程分类分布图
const initCategoryChart = () => {
  if (categoryChart) {
    categoryChart.dispose();
  }
  categoryChart = echarts.init(categoryChartRef.value);
  categoryChart.setOption({
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    series: [
      {
        name: "课程分布",
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  });

  window.addEventListener("resize", () => {
    categoryChart && categoryChart.resize();
  });
};

// 加载统计数据
const loadStatData = async () => {
  try {
    const [
      totalUsersRes,
      totalCoursesRes,
      todayNewUsersRes,
      todaySalesRes,
    ] = await Promise.all([
      getTotalUsers(),
      getTotalCourses(),
      getTodayNewUsers(),
      getTodaySales(),
    ]);

    if (
      totalUsersRes.code === 200 &&
      totalCoursesRes.code === 200 &&
      todayNewUsersRes.code === 200 &&
      todaySalesRes.code === 200
    ) {
      statData.totalUsers = totalUsersRes.data;
      statData.totalCourses = totalCoursesRes.data;
      statData.todayNewUsers = todayNewUsersRes.data;
      statData.todaySales = todaySalesRes.data;
    }
  } catch (error) {
    console.error("加载统计数据失败:", error);
    message.error("加载统计数据失败");
  }
};

// 加载用户增长趋势数据
const loadUserTrendData = async () => {
  loadingUserTrend.value = true;
  try {
    const params = {
      days: 30, // 最近30天
    };
    const res = await getUserGrowthTrend(params);
    if (res.code === 200) {
      const data = res.data || [];
      const dates = data.map((item) => item.date);
      const values = data.map((item) => item.count);

      userChart.setOption({
        xAxis: {
          data: dates,
        },
        series: [
          {
            data: values,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载用户增长趋势数据失败:", error);
    message.error("加载用户增长趋势数据失败");
  } finally {
    loadingUserTrend.value = false;
  }
};

// 加载销售额趋势数据
const loadSalesTrendData = async () => {
  loadingSalesTrend.value = true;
  try {
    const params = {
      days: 30, // 最近30天
    };
    const res = await getSalesTrend(params);
    if (res.code === 200) {
      const data = res.data || [];
      const dates = data.map((item) => item.date);
      const values = data.map((item) => item.amount);

      salesChart.setOption({
        xAxis: {
          data: dates,
        },
        series: [
          {
            data: values,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载销售额趋势数据失败:", error);
    message.error("加载销售额趋势数据失败");
  } finally {
    loadingSalesTrend.value = false;
  }
};

// 加载课程分类分布数据
const loadCategoryDistributionData = async () => {
  loadingCategoryDistribution.value = true;
  try {
    const res = await getCoursesCategoryDistribution();
    if (res.code === 200) {
      const data =
        res.data?.map((item) => ({
          value: item.count,
          name: item.categoryName,
        })) || [];

      categoryChart.setOption({
        series: [
          {
            data: data,
          },
        ],
      });
    }
  } catch (error) {
    console.error("加载课程分类分布数据失败:", error);
    message.error("加载课程分类分布数据失败");
  } finally {
    loadingCategoryDistribution.value = false;
  }
};

// 加载热门课程数据
const loadHotCoursesData = async () => {
  loadingHotCourses.value = true;
  try {
    const params = {
      limit: 5, // 获取前5条
    };
    const res = await getHotCourses(params);
    if (res.code === 200) {
      hotCourses.value = res.data || [];
    }
  } catch (error) {
    console.error("加载热门课程数据失败:", error);
    message.error("加载热门课程数据失败");
  } finally {
    loadingHotCourses.value = false;
  }
};

onMounted(() => {
  // 初始化图表
  initUserChart();
  initSalesChart();
  initCategoryChart();

  // 加载数据
  loadStatData();
  loadUserTrendData();
  loadSalesTrendData();
  loadCategoryDistributionData();
  loadHotCoursesData();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 16px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 16px;
  color: #fff;
}

.user-icon {
  background-color: #1890ff;
}

.course-icon {
  background-color: #36cbcb;
}

.user-add-icon {
  background-color: #4ecb73;
}

.sales-icon {
  background-color: #fbd437;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.2;
}

.stat-label {
  color: #999;
  font-size: 14px;
}

.chart-card,
.table-card {
  height: 360px;
}

.chart-container {
  height: 300px;
}

.price {
  color: #f5222d;
  font-weight: bold;
}
</style>
