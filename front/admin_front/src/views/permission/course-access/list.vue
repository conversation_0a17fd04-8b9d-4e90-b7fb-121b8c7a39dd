<template>
  <div class="course-access-list">
    <!-- 页面标题 -->
    <n-page-header >
      <template #extra>
        <n-space>
          <n-button type="primary" @click="showGrantModal = true">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            授予权限
          </n-button>
          <n-button type="info" @click="showBatchGrantModal = true">
            <template #icon>
              <n-icon><PeopleOutline /></n-icon>
            </template>
            批量授权
          </n-button>
          <n-button @click="$router.push('/permission/course-access/statistics')">
            <template #icon>
              <n-icon><BarChartOutline /></n-icon>
            </template>
            权限统计
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <!-- 搜索筛选 -->
    <n-card class="search-card">
      <n-form
        ref="searchFormRef"
        :model="searchForm"
        label-placement="left"
        label-width="auto"
        class="search-form"
      >
        <n-grid :cols="24" :x-gap="16">
          <n-form-item-gi :span="6" label="选择用户" path="userId">
            <UserSelector
              v-model:value="searchForm.userId"
              placeholder="请搜索用户"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="选择课程" path="courseId">
            <CourseSelector
              v-model:value="searchForm.courseId"
              placeholder="请搜索课程"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="权限类型" path="accessType">
            <n-select
              v-model:value="searchForm.accessType"
              :options="accessTypeOptions"
              placeholder="请选择权限类型"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="获取方式" path="acquireMethod">
            <n-select
              v-model:value="searchForm.acquireMethod"
              :options="acquireMethodOptions"
              placeholder="请选择获取方式"
              clearable
            />
          </n-form-item-gi>
        </n-grid>
        <n-grid :cols="24" :x-gap="16">
          <n-form-item-gi :span="6" label="权限状态" path="status">
            <n-select
              v-model:value="searchForm.status"
              :options="statusOptions"
              placeholder="请选择权限状态"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="是否买断" path="isBuyout">
            <n-select
              v-model:value="searchForm.isBuyout"
              :options="buyoutOptions"
              placeholder="请选择是否买断"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="是否激活" path="isActive">
            <n-select
              v-model:value="searchForm.isActive"
              :options="activeOptions"
              placeholder="请选择是否激活"
              clearable
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6">
            <n-space>
              <n-button type="primary" @click="handleSearch">
                <template #icon>
                  <n-icon><SearchOutline /></n-icon>
                </template>
                搜索
              </n-button>
              <n-button @click="handleReset">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
                重置
              </n-button>
            </n-space>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </n-card>

    <!-- 数据表格 -->
    <n-card>
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        flex-height
        style="height: 600px"
      />
    </n-card>

    <!-- 授予权限弹窗 -->
    <GrantAccessModal
      v-model:show="showGrantModal"
      @success="handleSearch"
    />

    <!-- 批量授权弹窗 -->
    <BatchGrantModal
      v-model:show="showBatchGrantModal"
      @success="handleSearch"
    />

    <!-- 权限详情弹窗 -->
    <AccessDetailModal
      v-model:show="showDetailModal"
      :access-data="selectedAccess"
    />

    <!-- 退款处理弹窗 -->
    <RefundModal
      v-model:show="showRefundModal"
      :access-data="selectedAccess"
      @success="handleSearch"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import {
  AddOutline,
  PeopleOutline,
  BarChartOutline,
  SearchOutline,
  RefreshOutline,
  EyeOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  CashOutline
} from '@vicons/ionicons5'
import {
  getUserAccessPageList,
  activateAccess,
  deactivateAccess,
  getAccessTypeOptions,
  getAcquireMethodOptions,
  getStatusOptions,
  formatAccessType,
  formatAcquireMethod,
  formatPaymentMethod,
  formatPrice,
  formatDateTime,
  isAccessExpiringSoon,
  isAccessExpired
} from '@/api/permission'
import GrantAccessModal from './components/GrantAccessModal.vue'
import BatchGrantModal from './components/BatchGrantModal.vue'
import AccessDetailModal from './components/AccessDetailModal.vue'
import RefundModal from './components/RefundModal.vue'
import UserSelector from '@/components/Selector/UserSelector.vue'
import CourseSelector from '@/components/Selector/CourseSelector.vue'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const showGrantModal = ref(false)
const showBatchGrantModal = ref(false)
const showDetailModal = ref(false)
const showRefundModal = ref(false)
const selectedAccess = ref(null)

// 搜索表单
const searchForm = reactive({
  userId: null,
  courseId: null,
  accessType: null,
  acquireMethod: null,
  status: null,
  isBuyout: null,
  isActive: null
})

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page) => {
    pagination.page = page
    handleSearch()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    handleSearch()
  }
})

// 选项数据
const accessTypeOptions = getAccessTypeOptions()
const acquireMethodOptions = getAcquireMethodOptions()
const statusOptions = getStatusOptions()
const buyoutOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
]
const activeOptions = [
  { label: '已激活', value: true },
  { label: '未激活', value: false }
]

// 表格列配置
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '用户',
    key: 'userId',
    width: 150,
    render(row) {
      return h('div', [
        h('div', { style: 'font-weight: 500;' }, row.userName || `用户${row.userId}`),
        h('div', { style: 'font-size: 12px; color: #999;' }, `ID: ${row.userId}`)
      ])
    }
  },
  {
    title: '课程',
    key: 'courseId',
    width: 200,
    render(row) {
      return h('div', [
        h('div', {
          style: 'font-weight: 500; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;',
          title: row.courseName || `课程${row.courseId}`
        }, row.courseName || `课程${row.courseId}`),
        h('div', { style: 'font-size: 12px; color: #999;' }, `ID: ${row.courseId}`)
      ])
    }
  },
  {
    title: '权限类型',
    key: 'accessType',
    width: 120,
    render(row) {
      return formatAccessType(row.accessType)
    }
  },
  {
    title: '获取方式',
    key: 'acquireMethod',
    width: 120,
    render(row) {
      return formatAcquireMethod(row.acquireMethod)
    }
  },
  {
    title: '是否买断',
    key: 'isBuyout',
    width: 100,
    render(row) {
      return h(
        'n-tag',
        {
          type: row.isBuyout ? 'success' : 'default',
          size: 'small'
        },
        row.isBuyout ? '买断' : '普通'
      )
    }
  },
  {
    title: '支付金额',
    key: 'pricePaid',
    width: 120,
    render(row) {
      return formatPrice(row.pricePaid)
    }
  },
  {
    title: '原价',
    key: 'originalPrice',
    width: 120,
    render(row) {
      return formatPrice(row.originalPrice)
    }
  },
  {
    title: '支付方式',
    key: 'paymentMethod',
    width: 120,
    render(row) {
      return formatPaymentMethod(row.paymentMethod)
    }
  },
  {
    title: '过期时间',
    key: 'expireTime',
    width: 180,
    render(row) {
      if (!row.expireTime) {
        return h('n-tag', { type: 'success', size: 'small' }, '永久有效')
      }
      
      const isExpired = isAccessExpired(row.expireTime)
      const isExpiring = isAccessExpiringSoon(row.expireTime)
      
      return h('div', [
        h('div', formatDateTime(row.expireTime)),
        isExpired
          ? h('n-tag', { type: 'error', size: 'small' }, '已过期')
          : isExpiring
          ? h('n-tag', { type: 'warning', size: 'small' }, '即将过期')
          : null
      ])
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        0: { type: 'error', text: '已失效' },
        1: { type: 'success', text: '有效' },
        2: { type: 'warning', text: '已退款' }
      }
      const status = statusMap[row.status] || { type: 'default', text: '未知' }
      return h('n-tag', { type: status.type, size: 'small' }, status.text)
    }
  },
  {
    title: '激活状态',
    key: 'isActive',
    width: 100,
    render(row) {
      return h(
        'n-tag',
        {
          type: row.isActive ? 'success' : 'error',
          size: 'small'
        },
        row.isActive ? '已激活' : '未激活'
      )
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 180,
    render(row) {
      return formatDateTime(row.createdAt)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 280,
    fixed: 'right',
    align: 'center',
    render(row) {
      return h(
        'div',
        { style: 'display: flex; justify-content: center; gap: 8px;' },
        [
          h(
            'n-button',
            {
              size: 'small',
              type: 'primary',
              style: 'padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;',
              onClick: () => handleViewDetail(row)
            },
            {
              default: () => '查看',
              icon: () => h('n-icon', { size: 14 }, h(EyeOutline))
            }
          ),
          row.isActive
            ? h(
                'n-button',
                {
                  size: 'small',
                  type: 'warning',
                  style: 'padding: 0 12px; border-radius: 15px; background-color: #fa8c16; border: none; color: white;',
                  onClick: () => handleDeactivate(row)
                },
                {
                  default: () => '停用',
                  icon: () => h('n-icon', { size: 14 }, h(CloseCircleOutline))
                }
              )
            : h(
                'n-button',
                {
                  size: 'small',
                  type: 'success',
                  style: 'padding: 0 12px; border-radius: 15px; background-color: #52c41a; border: none; color: white;',
                  onClick: () => handleActivate(row)
                },
                {
                  default: () => '激活',
                  icon: () => h('n-icon', { size: 14 }, h(CheckmarkCircleOutline))
                }
              ),
          row.status === 1 && row.acquireMethod === 1
            ? h(
                'n-button',
                {
                  size: 'small',
                  type: 'error',
                  style: 'padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;',
                  onClick: () => handleRefund(row)
                },
                {
                  default: () => '退款',
                  icon: () => h('n-icon', { size: 14 }, h(CashOutline))
                }
              )
            : null
        ].filter(Boolean)
      )
    }
  }
]

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const { data } = await getUserAccessPageList(params)
    tableData.value = data.records || []
    pagination.itemCount = data.total || 0
  } catch (error) {
    message.error('获取数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = null
  })
  pagination.page = 1
  handleSearch()
}

// 查看详情
const handleViewDetail = (row) => {
  selectedAccess.value = row
  showDetailModal.value = true
}

// 激活权限
const handleActivate = async (row) => {
  try {
    await activateAccess(row.id)
    message.success('权限激活成功')
    handleSearch()
  } catch (error) {
    message.error('权限激活失败：' + error.message)
  }
}

// 停用权限
const handleDeactivate = async (row) => {
  try {
    await deactivateAccess({
      accessId: row.id,
      reason: '管理员手动停用'
    })
    message.success('权限停用成功')
    handleSearch()
  } catch (error) {
    message.error('权限停用失败：' + error.message)
  }
}

// 处理退款
const handleRefund = (row) => {
  selectedAccess.value = row
  showRefundModal.value = true
}

// 初始化
onMounted(() => {
  handleSearch()
})
</script>

<style lang="scss" scoped>
.course-access-list {
  padding: 24px;

  .search-card {
    margin-bottom: 16px;
  }

  .search-form {
    .n-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
