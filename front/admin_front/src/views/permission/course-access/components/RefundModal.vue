<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="处理退款"
    style="width: 500px"
    :mask-closable="false"
  >
    <div v-if="accessData" class="refund-info">
      <!-- 权限信息 -->
      <n-alert type="info" style="margin-bottom: 16px">
        <template #header>权限信息</template>
        <div>用户ID: {{ accessData.userId }}</div>
        <div>课程ID: {{ accessData.courseId }}</div>
        <div>支付金额: {{ formatPrice(accessData.pricePaid) }}</div>
        <div>支付方式: {{ formatPaymentMethod(accessData.paymentMethod) }}</div>
        <div>订单ID: {{ accessData.orderId || '-' }}</div>
      </n-alert>

      <!-- 退款表单 -->
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="退款金额" path="refundAmount">
          <n-input-number
            v-model:value="formData.refundAmount"
            placeholder="请输入退款金额"
            :precision="2"
            :min="0"
            :max="maxRefundAmount"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </n-input-number>
          <template #feedback>
            <span style="color: #999; font-size: 12px;">
              最大可退款金额: {{ formatPrice(maxRefundAmount) }}
            </span>
          </template>
        </n-form-item>

        <n-form-item label="退款原因" path="refundReason">
          <n-select
            v-model:value="formData.refundReason"
            :options="refundReasonOptions"
            placeholder="请选择退款原因"
            clearable
          />
        </n-form-item>

        <n-form-item v-if="formData.refundReason === '其他'" label="详细原因" path="customReason">
          <n-input
            v-model:value="formData.customReason"
            type="textarea"
            placeholder="请输入详细的退款原因"
            :rows="3"
          />
        </n-form-item>

        <n-form-item label="备注" path="remark">
          <n-input
            v-model:value="formData.remark"
            type="textarea"
            placeholder="请输入备注信息（可选）"
            :rows="2"
          />
        </n-form-item>
      </n-form>

      <!-- 退款确认 -->
      <n-alert type="warning" style="margin-top: 16px">
        <template #header>退款确认</template>
        <div>退款后该权限将被标记为已退款状态，用户将无法继续访问相关内容。</div>
        <div>此操作不可撤销，请谨慎操作。</div>
      </n-alert>
    </div>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="error" :loading="loading" @click="handleSubmit">
          确认退款
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import {
  processRefund,
  formatPrice,
  formatPaymentMethod
} from '@/api/permission'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  accessData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const dialog = useDialog()
const formRef = ref()
const loading = ref(false)

// 控制弹窗显示
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = reactive({
  refundAmount: null,
  refundReason: '',
  customReason: '',
  remark: ''
})

// 最大退款金额
const maxRefundAmount = computed(() => {
  return props.accessData?.pricePaid || 0
})

// 退款原因选项
const refundReasonOptions = [
  { label: '用户申请退款', value: '用户申请退款' },
  { label: '课程质量问题', value: '课程质量问题' },
  { label: '技术故障', value: '技术故障' },
  { label: '误操作', value: '误操作' },
  { label: '重复购买', value: '重复购买' },
  { label: '违规内容', value: '违规内容' },
  { label: '系统错误', value: '系统错误' },
  { label: '其他', value: '其他' }
]

// 表单验证规则
const rules = {
  refundAmount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value <= 0) {
          return new Error('退款金额必须大于0')
        }
        if (value > maxRefundAmount.value) {
          return new Error(`退款金额不能超过${formatPrice(maxRefundAmount.value)}`)
        }
        return true
      },
      trigger: 'blur'
    }
  ],
  refundReason: [
    { required: true, message: '请选择退款原因', trigger: 'change' }
  ],
  customReason: [
    { required: true, message: '请输入详细的退款原因', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  formData.refundAmount = props.accessData?.pricePaid || null
  formData.refundReason = ''
  formData.customReason = ''
  formData.remark = ''
}

// 取消
const handleCancel = () => {
  showModal.value = false
  resetForm()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 二次确认
    const confirmed = await new Promise((resolve) => {
      dialog.warning({
        title: '确认退款',
        content: `确定要为用户 ${props.accessData.userId} 退款 ${formatPrice(formData.refundAmount)} 吗？`,
        positiveText: '确认退款',
        negativeText: '取消',
        onPositiveClick: () => resolve(true),
        onNegativeClick: () => resolve(false)
      })
    })
    
    if (!confirmed) return
    
    loading.value = true
    
    const submitData = {
      accessId: props.accessData.id,
      refundAmount: formData.refundAmount,
      refundReason: formData.refundReason === '其他' 
        ? formData.customReason 
        : formData.refundReason
    }
    
    if (formData.remark) {
      submitData.remark = formData.remark
    }
    
    await processRefund(submitData)
    
    message.success('退款处理成功')
    emit('success')
    handleCancel()
  } catch (error) {
    message.error('退款处理失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (newVal) => {
    if (newVal && props.accessData) {
      resetForm()
    }
  }
)

// 监听权限数据变化
watch(
  () => props.accessData,
  (newVal) => {
    if (newVal && props.show) {
      resetForm()
    }
  }
)
</script>

<style lang="scss" scoped>
.refund-info {
  :deep(.n-form-item-feedback) {
    margin-top: 4px;
  }
}
</style>
