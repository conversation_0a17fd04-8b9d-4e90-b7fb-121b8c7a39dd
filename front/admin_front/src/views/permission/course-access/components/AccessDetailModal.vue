<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="权限详情"
    style="width: 800px"
  >
    <div v-if="accessData" class="access-detail">
      <!-- 基本信息 -->
      <n-card title="基本信息" size="small" style="margin-bottom: 16px">
        <n-descriptions :column="3" bordered size="small">
          <n-descriptions-item label="权限ID">
            {{ accessData.id }}
          </n-descriptions-item>
          <n-descriptions-item label="用户">
            <div>
              <div style="font-weight: 500;">{{ accessData.userName || `用户${accessData.userId}` }}</div>
              <div style="font-size: 12px; color: #999;">ID: {{ accessData.userId }}</div>
            </div>
          </n-descriptions-item>
          <n-descriptions-item label="课程">
            <div>
              <div style="font-weight: 500;">{{ accessData.courseName || `课程${accessData.courseId}` }}</div>
              <div style="font-size: 12px; color: #999;">ID: {{ accessData.courseId }}</div>
            </div>
          </n-descriptions-item>
          <n-descriptions-item label="权限类型">
            <n-tag :type="getAccessTypeTagType(accessData.accessType)" size="small">
              {{ formatAccessType(accessData.accessType) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="获取方式">
            <n-tag :type="getAcquireMethodTagType(accessData.acquireMethod)" size="small">
              {{ formatAcquireMethod(accessData.acquireMethod) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="是否买断">
            <n-tag :type="accessData.isBuyout ? 'success' : 'default'" size="small">
              {{ accessData.isBuyout ? '买断' : '普通' }}
            </n-tag>
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 权限范围 -->
      <n-card
        v-if="accessData.chapterId || accessData.lessonId"
        title="权限范围"
        size="small"
        style="margin-bottom: 16px"
      >
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item v-if="accessData.chapterId" label="章节ID">
            {{ accessData.chapterId }}
          </n-descriptions-item>
          <n-descriptions-item v-if="accessData.lessonId" label="课时ID">
            {{ accessData.lessonId }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 支付信息 -->
      <n-card
        v-if="accessData.acquireMethod === 1"
        title="支付信息"
        size="small"
        style="margin-bottom: 16px"
      >
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item label="支付金额">
            <n-text :type="accessData.pricePaid > 0 ? 'success' : 'default'" strong>
              {{ formatPrice(accessData.pricePaid) }}
            </n-text>
          </n-descriptions-item>
          <n-descriptions-item label="原价">
            {{ formatPrice(accessData.originalPrice) }}
          </n-descriptions-item>
          <n-descriptions-item label="支付方式">
            {{ formatPaymentMethod(accessData.paymentMethod) }}
          </n-descriptions-item>
          <n-descriptions-item label="订单ID">
            {{ accessData.orderId || '-' }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 其他信息 -->
      <n-card
        v-if="accessData.pointsUsed || accessData.couponId"
        title="其他信息"
        size="small"
        style="margin-bottom: 16px"
      >
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item v-if="accessData.pointsUsed" label="使用积分">
            {{ accessData.pointsUsed }}
          </n-descriptions-item>
          <n-descriptions-item v-if="accessData.couponId" label="优惠券ID">
            {{ accessData.couponId }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 状态信息 -->
      <n-card title="状态信息" size="small" style="margin-bottom: 16px">
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item label="过期时间">
            <div v-if="accessData.expireTime">
              <div>{{ formatDateTime(accessData.expireTime) }}</div>
              <n-tag
                v-if="isAccessExpired(accessData.expireTime)"
                type="error"
                size="small"
                style="margin-top: 4px"
              >
                已过期
              </n-tag>
              <n-tag
                v-else-if="isAccessExpiringSoon(accessData.expireTime)"
                type="warning"
                size="small"
                style="margin-top: 4px"
              >
                即将过期
              </n-tag>
            </div>
            <n-tag v-else type="success" size="small">永久有效</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="激活状态">
            <n-tag :type="accessData.isActive ? 'success' : 'error'" size="small">
              {{ accessData.isActive ? '已激活' : '未激活' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="权限状态">
            <n-tag :type="getStatusTagType(accessData.status)" size="small">
              {{ formatStatus(accessData.status) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="管理员ID">
            {{ accessData.adminId || '-' }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 退款信息 -->
      <n-card
        v-if="accessData.refundTime || accessData.refundAmount || accessData.refundReason"
        title="退款信息"
        size="small"
        style="margin-bottom: 16px"
      >
        <n-descriptions :column="1" bordered size="small">
          <n-descriptions-item v-if="accessData.refundTime" label="退款时间">
            {{ formatDateTime(accessData.refundTime) }}
          </n-descriptions-item>
          <n-descriptions-item v-if="accessData.refundAmount" label="退款金额">
            <n-text type="error" strong>{{ formatPrice(accessData.refundAmount) }}</n-text>
          </n-descriptions-item>
          <n-descriptions-item v-if="accessData.refundReason" label="退款原因">
            {{ accessData.refundReason }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 时间信息 -->
      <n-card title="时间信息" size="small" style="margin-bottom: 16px">
        <n-descriptions :column="2" bordered size="small">
          <n-descriptions-item label="创建时间">
            {{ formatDateTime(accessData.createdAt) }}
          </n-descriptions-item>
          <n-descriptions-item label="更新时间">
            {{ formatDateTime(accessData.updatedAt) }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 备注信息 -->
      <n-card v-if="accessData.remark" title="备注信息" size="small">
        <n-text>{{ accessData.remark }}</n-text>
      </n-card>
    </div>

    <template #action>
      <n-space>
        <n-button @click="showModal = false">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { computed } from 'vue'
import {
  formatAccessType,
  formatAcquireMethod,
  formatPaymentMethod,
  formatStatus,
  formatPrice,
  formatDateTime,
  isAccessExpired,
  isAccessExpiringSoon
} from '@/api/permission'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  accessData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show'])

// 控制弹窗显示
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 获取权限类型标签类型
const getAccessTypeTagType = (type) => {
  switch (type) {
    case 1: return 'success'  // 课程权限
    case 2: return 'info'     // 章节权限
    case 3: return 'warning'  // 课时权限
    default: return 'default'
  }
}

// 获取获取方式标签类型
const getAcquireMethodTagType = (method) => {
  switch (method) {
    case 1: return 'error'    // 购买
    case 2: return 'success'  // 免费
    case 3: return 'info'     // 积分兑换
    case 4: return 'warning'  // 优惠券兑换
    case 5: return 'primary'  // 管理员赠送
    case 6: return 'default'  // 推广活动
    default: return 'default'
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 0: return 'error'    // 已失效
    case 1: return 'success'  // 有效
    case 2: return 'warning'  // 已退款
    default: return 'default'
  }
}
</script>

<style lang="scss" scoped>
.access-detail {
  max-height: 70vh;
  overflow-y: auto;

  :deep(.n-descriptions-item-label) {
    font-weight: 500;
  }

  :deep(.n-card) {
    .n-card__header {
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 600;
    }

    .n-card__content {
      padding: 12px 16px;
    }
  }

  :deep(.n-descriptions) {
    .n-descriptions-item-label {
      width: 100px;
      font-size: 13px;
    }

    .n-descriptions-item-content {
      font-size: 13px;
    }
  }
}
</style>
