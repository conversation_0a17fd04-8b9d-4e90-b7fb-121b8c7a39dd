<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="批量授权"
    style="width: 600px"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="120px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="选择用户" path="userIds">
        <!-- {{formData}} -->
        <UserSelector
          v-model:value="formData.userIds"
          placeholder="请搜索并选择用户"
          :multiple="true"
          @change="handleUserChange"
        />
        <template #feedback>
          <span style="color: #999; font-size: 12px;">
            支持多选用户进行批量授权
          </span>
        </template>
      </n-form-item>

      <n-form-item label="选择课程" path="courseId">
        <CourseSelector
          v-model:value="formData.courseId"
          placeholder="请搜索并选择课程"
          :status="1"
          @change="handleCourseChange"
        />
      </n-form-item>

      <n-form-item label="权限类型" path="accessType">
        <n-select
          v-model:value="formData.accessType"
          :options="accessTypeOptions"
          placeholder="请选择权限类型"
          @update:value="handleAccessTypeChange"
        />
      </n-form-item>

      <n-form-item
        v-if="formData.accessType === 2 || formData.accessType === 3"
        :label="formData.accessType === 2 ? '选择章节' : '选择课时'"
        :path="formData.accessType === 2 ? 'chapterId' : 'lessonId'"
      >
        <ChapterLessonCascader
          v-model:value="formData.cascaderValue"
          :course-id="formData.courseId"
          :access-type="formData.accessType"
          :placeholder="formData.accessType === 2 ? '请选择章节' : '请选择课时'"
          @change="handleCascaderChange"
        />
      </n-form-item>

      <n-form-item label="获取方式" path="acquireMethod">
        <n-select
          v-model:value="formData.acquireMethod"
          :options="acquireMethodOptions"
          placeholder="请选择获取方式"
          @update:value="handleAcquireMethodChange"
        />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 5"
        label="管理员ID"
        path="adminId"
      >
        <n-input
          v-model:value="formData.adminId"
          placeholder="请输入管理员ID"
          clearable
        />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod !== 1"
        label="过期时间"
        path="expireTime"
      >
        <n-date-picker
          v-model:value="formData.expireTime"
          type="datetime"
          placeholder="请选择过期时间，不选择则永久有效"
          style="width: 100%"
          clearable
        />
      </n-form-item>

      <n-form-item label="备注" path="remark">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="2"
        />
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          确认批量授权
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  batchGrantAccess,
  getAccessTypeOptions,
  getAcquireMethodOptions
} from '@/api/permission'
import CourseSelector from '@/components/Selector/CourseSelector.vue'
import UserSelector from '@/components/Selector/UserSelector.vue'
import ChapterLessonCascader from '@/components/Selector/ChapterLessonCascader.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref()
const loading = ref(false)

// 控制弹窗显示
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = reactive({
  userIds: [],
  courseId: null,
  chapterId: '',
  lessonId: '',
  cascaderValue: null, // 级联选择器的值
  accessType: null,
  acquireMethod: null,
  adminId: '',
  expireTime: null,
  remark: ''
})

// 选中的用户和课程信息
const selectedUsers = ref([])
const selectedCourse = ref(null)

// 选项数据
const accessTypeOptions = getAccessTypeOptions()
const acquireMethodOptions = getAcquireMethodOptions()

// 表单验证规则
const rules = {
  userIds: [
    { required: true, message: '请选择用户', trigger: 'change' ,type:'array'},
    {
      validator: (rule, value) => {
        if (!value || value.length === 0) {
          return new Error('请至少选择一个用户')
        }
        return true
      },
      trigger: 'change'
    }
  ],
  courseId: [
    { required: true, message: '请选择课程', trigger: 'change' ,type:'number'}
  ],
  accessType: [
    { required: true, message: '请选择权限类型', trigger: 'change' ,type:'number'}
  ],
  acquireMethod: [
    { required: true, message: '请选择获取方式', trigger: 'change' ,type:'number'}
  ],
  adminId: [
    { required: true, message: '请输入管理员ID', trigger: 'blur' ,type:'number'}
  ]
}

// 用户选择变化处理
const handleUserChange = (value, users) => {
  selectedUsers.value = users || []
}

// 课程选择变化处理
const handleCourseChange = (value, courses) => {
  selectedCourse.value = courses?.[0] || null

  // 清空级联选择器的值
  formData.cascaderValue = null
  formData.chapterId = ''
  formData.lessonId = ''
}

// 级联选择器变化处理
const handleCascaderChange = (value, selectedData) => {
  if (!selectedData) {
    formData.chapterId = ''
    formData.lessonId = ''
    return
  }

  if (formData.accessType === 2) {
    // 章节权限
    formData.chapterId = selectedData.id
    formData.lessonId = ''
  } else if (formData.accessType === 3) {
    // 课时权限
    formData.chapterId = selectedData.chapterId
    formData.lessonId = selectedData.id
  }
}

// 权限类型变化处理
const handleAccessTypeChange = (value) => {
  // 清空级联选择器相关数据
  formData.cascaderValue = null
  formData.chapterId = ''
  formData.lessonId = ''
}

// 获取方式变化处理
const handleAcquireMethodChange = (value) => {
  // 重置相关字段
  formData.adminId = ''

  // 根据获取方式设置默认值
  if (value === 2) {
    // 免费获取
    formData.remark = '批量免费授权'
  } else if (value === 5) {
    // 管理员赠送
    formData.remark = '管理员批量赠送'
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'userIds') {
      formData[key] = []
    } else if (key === 'adminId' || key === 'remark' || key === 'chapterId' || key === 'lessonId') {
      formData[key] = ''
    } else {
      formData[key] = null
    }
  })
  selectedUsers.value = []
  selectedCourse.value = null
}

// 取消
const handleCancel = () => {
  showModal.value = false
  resetForm()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 获取用户ID列表
    const userIds = formData.userIds

    if (!userIds || userIds.length === 0) {
      message.error('请选择用户')
      return
    }
    
    const submitData = {
      userIds,
      courseId: formData.courseId,
      accessType: formData.accessType,
      acquireMethod: formData.acquireMethod,
      remark: formData.remark || '批量授权'
    }
    
    // 添加管理员ID（如果是管理员赠送）
    if (formData.acquireMethod === 5 && formData.adminId) {
      submitData.adminId = parseInt(formData.adminId)
    }
    
    // 处理过期时间
    if (formData.expireTime) {
      submitData.expireTime = new Date(formData.expireTime).toISOString()
    }
    
    await batchGrantAccess(submitData)
    
    message.success(`成功为 ${userIds.length} 个用户批量授权`)
    emit('success')
    handleCancel()
  } catch (error) {
    message.error('批量授权失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)
</script>

<style lang="scss" scoped>
:deep(.n-form-item-feedback) {
  margin-top: 4px;
}
</style>
