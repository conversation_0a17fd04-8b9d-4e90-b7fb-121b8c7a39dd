<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="授予权限"
    style="width: 600px"
    :mask-closable="false"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="120px"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="选择用户" path="userId">

        <UserSelector
          v-model:value="formData.userId"
          placeholder="请搜索并选择用户"
          @change="handleUserChange"
        />
      </n-form-item>

      <n-form-item label="选择课程" path="courseId">
        <CourseSelector
          v-model:value="formData.courseId"
          placeholder="请搜索并选择课程"
          :status="1"
          @change="handleCourseChange"
        />
      </n-form-item>

      <n-form-item label="权限类型" path="accessType">
        <n-select
          v-model:value="formData.accessType"
          :options="accessTypeOptions"
          placeholder="请选择权限类型"
          @update:value="handleAccessTypeChange"
        />
      </n-form-item>

      <n-form-item
        v-if="formData.accessType === 2 || formData.accessType === 3"
        :label="formData.accessType === 2 ? '选择章节' : '选择课时'"
        :path="formData.accessType === 2 ? 'chapterId' : 'lessonId'"
      >
        <ChapterLessonCascader
          v-model:value="formData.cascaderValue"
          :course-id="formData.courseId"
          :access-type="formData.accessType"
          :placeholder="formData.accessType === 2 ? '请选择章节' : '请选择课时'"
          @change="handleCascaderChange"
        />
      </n-form-item>

      <n-form-item label="获取方式" path="acquireMethod">
        <n-select
          v-model:value="formData.acquireMethod"
          :options="acquireMethodOptions"
          placeholder="请选择获取方式"
          @update:value="handleAcquireMethodChange"
        />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 1"
        label="是否买断"
        path="isBuyout"
      >
        <n-switch v-model:value="formData.isBuyout" />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 1"
        label="支付金额"
        path="pricePaid"
      >
        <n-input-number
          v-model:value="formData.pricePaid"
          placeholder="请输入支付金额"
          :precision="2"
          :min="0"
          style="width: 100%"
        >
          <template #prefix>¥</template>
        </n-input-number>
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 1"
        label="原价"
        path="originalPrice"
      >
        <n-input-number
          v-model:value="formData.originalPrice"
          placeholder="课程原价（自动加载）"
          :precision="2"
          :min="0"
          :disabled="!!selectedCourse"
          style="width: 100%"
        >
          <template #prefix>¥</template>
        </n-input-number>
        <template #feedback>
          <span v-if="selectedCourse" style="color: #999; font-size: 12px;">
            已自动加载课程原价，如需修改请重新选择课程
          </span>
        </template>
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 1"
        label="支付方式"
        path="paymentMethod"
      >
        <n-select
          v-model:value="formData.paymentMethod"
          :options="paymentMethodOptions"
          placeholder="请选择支付方式"
        />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 1"
        label="订单号"
        path="orderId"
      >
        <n-input
          v-model:value="formData.orderId"
          placeholder="请输入订单号"
          clearable
        />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 3"
        label="使用积分"
        path="pointsUsed"
      >
        <n-input-number
          v-model:value="formData.pointsUsed"
          placeholder="请输入使用的积分数量"
          :min="0"
          style="width: 100%"
        />
      </n-form-item>

      <n-form-item
        v-if="formData.acquireMethod === 4"
        label="优惠券码"
        path="couponId"
      >
        <n-input
          v-model:value="formData.couponId"
          placeholder="请输入优惠券码"
          clearable
        />
      </n-form-item>

      <!-- <n-form-item
        v-if="formData.acquireMethod === 5"
        label="管理员"
        path="adminId"  
      >
        <n-input
          v-model:value="formData.adminId"
          placeholder="请输入管理员ID"
          clearable
        />
      </n-form-item> -->

      <n-form-item
        v-if="!formData.isBuyout"
        label="过期时间"
        path="expireTime"
      >
        <n-date-picker
          v-model:value="formData.expireTime"
          type="datetime"
          placeholder="请选择过期时间，不选择则永久有效"
          style="width: 100%"
          clearable
        />
      </n-form-item>

      <n-form-item label="备注" path="remark">
        <n-input
          v-model:value="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="3"
        />
      </n-form-item>
    </n-form>

    <template #action>
      <n-space>
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" :loading="loading" @click="handleSubmit">
          确认授予
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import {
  grantPurchaseAccess,
  grantFreeAccess,
  grantPointsAccess,
  grantCouponAccess,
  grantAdminGift,
  getAccessTypeOptions,
  getAcquireMethodOptions,
  getPaymentMethodOptions
} from '@/api/permission'
import UserSelector from '@/components/Selector/UserSelector.vue'
import CourseSelector from '@/components/Selector/CourseSelector.vue'
import ChapterLessonCascader from '@/components/Selector/ChapterLessonCascader.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'success'])

const message = useMessage()
const formRef = ref()
const loading = ref(false)

// 控制弹窗显示
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = reactive({
  userId: null,
  courseId: null,
  chapterId: '',
  lessonId: '',
  cascaderValue: null, // 级联选择器的值
  accessType: null,
  acquireMethod: null,
  isBuyout: false,
  pricePaid: null,
  originalPrice: null,
  paymentMethod: null,
  orderId: '',
  pointsUsed: null,
  couponId: '',
  adminId: 'admin',
  expireTime: null,
  remark: ''
})

// 选中的用户和课程信息
const selectedUser = ref(null)
const selectedCourse = ref(null)

// 选项数据
const accessTypeOptions = getAccessTypeOptions()
const acquireMethodOptions = getAcquireMethodOptions()
const paymentMethodOptions = getPaymentMethodOptions()

// 用户选择变化处理
const handleUserChange = (_, users) => {
  selectedUser.value = users?.[0] || null
  if (selectedUser.value) {
    formData.remark = `为用户 ${selectedUser.value.username} 授予权限`
  }
}

// 课程选择变化处理
const handleCourseChange = (_, courses) => {
  selectedCourse.value = courses?.[0] || null

  // 清空级联选择器的值
  formData.cascaderValue = null
  formData.chapterId = ''
  formData.lessonId = ''

  // 自动加载课程原价
  if (selectedCourse.value) {
    // 如果是购买方式，自动设置原价和支付金额
    if (formData.acquireMethod === 1) {
      if (selectedCourse.value.price !== undefined && selectedCourse.value.price !== null) {
        formData.originalPrice = selectedCourse.value.price
        formData.pricePaid = selectedCourse.value.price
      }
    }
  } else {
    // 清空价格信息
    if (formData.acquireMethod === 1) {
      formData.originalPrice = null
      formData.pricePaid = null
    }
  }
}

// 级联选择器变化处理
const handleCascaderChange = (_, selectedData) => {
  if (!selectedData) {
    formData.chapterId = ''
    formData.lessonId = ''
    return
  }

  if (formData.accessType === 2) {
    // 章节权限
    formData.chapterId = selectedData.id
    formData.lessonId = ''
  } else if (formData.accessType === 3) {
    // 课时权限
    formData.chapterId = selectedData.chapterId
    formData.lessonId = selectedData.id
  }
}



// 表单验证规则
const rules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change',type:'number' }
  ],
  courseId: [
    { required: true, message: '请选择课程', trigger: 'change' ,type:'number' }
  ],
  accessType: [
    { required: true, message: '请选择权限类型', trigger: 'change' ,type:'number' }
  ],
  acquireMethod: [
    { required: true, message: '请选择获取方式', trigger: 'change' ,type:'number'}
  ],
  chapterId: [
    { required: true, message: '请输入章节', trigger: 'blur' }
  ],
  lessonId: [
    { required: true, message: '请输入课时', trigger: 'blur' }
  ],
  pricePaid: [
    { required: true, message: '请输入支付金额', trigger: 'blur' ,type:'number'}
  ],
  originalPrice: [
    { required: true, message: '请输入原价', trigger: 'blur' ,type:'number'}
  ],
  paymentMethod: [
    { required: true, message: '请选择支付方式', trigger: 'change' ,type:'string'}
  ],
  orderId: [
    { required: true, message: '请输入订单号', trigger: 'blur' ,type:'string'}
  ],
  pointsUsed: [
    { required: true, message: '请输入使用的积分数量', trigger: 'blur' ,type:'number'}
  ],
  couponId: [
    { required: true, message: '请输入优惠券码', trigger: 'blur' ,type:'number'}
  ],
  adminId: [
    { required: true, message: '请输入管理员', trigger: 'blur' ,type:'number'}
  ]
}

// 权限类型变化处理
const handleAccessTypeChange = () => {
  // 清空级联选择器相关数据
  formData.cascaderValue = null
  formData.chapterId = ''
  formData.lessonId = ''
}

// 获取方式变化处理
const handleAcquireMethodChange = (acquireMethod) => {
  // 重置相关字段
  formData.isBuyout = false
  formData.pricePaid = null
  formData.originalPrice = null
  formData.paymentMethod = null
  formData.orderId = ''
  formData.pointsUsed = null
  formData.couponId = ''
  formData.adminId = ''

  // 根据获取方式设置默认值
  if (acquireMethod === 1) {
    // 购买方式 - 自动加载课程原价
    if (selectedCourse.value && selectedCourse.value.price !== undefined && selectedCourse.value.price !== null) {
      formData.originalPrice = selectedCourse.value.price
      formData.pricePaid = selectedCourse.value.price
    }
  } else if (acquireMethod === 2) {
    // 免费获取
    formData.pricePaid = 0
    formData.originalPrice = 0
    formData.paymentMethod = 'gift'
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'chapterId' || key === 'lessonId' || key === 'orderId' ||
        key === 'couponId' || key === 'adminId' || key === 'remark') {
      formData[key] = ''
    } else if (key === 'isBuyout') {
      formData[key] = false
    } else {
      formData[key] = null
    }
  })
  selectedUser.value = null
  selectedCourse.value = null
}

// 取消
const handleCancel = () => {
  showModal.value = false
  resetForm()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    const submitData = { ...formData }

    // 删除前端UI组件相关字段，避免后端反序列化错误
    delete submitData.cascaderValue

    // 处理数字类型字段
    if (submitData.userId) submitData.userId = parseInt(submitData.userId)
    if (submitData.courseId) submitData.courseId = parseInt(submitData.courseId)
    if (submitData.chapterId) submitData.chapterId = parseInt(submitData.chapterId)
    if (submitData.lessonId) submitData.lessonId = parseInt(submitData.lessonId)
    if (submitData.adminId) submitData.adminId = parseInt(submitData.adminId)
    if (submitData.couponId) submitData.couponId = parseInt(submitData.couponId)

    // 处理时间字段
    if (submitData.expireTime) {
      submitData.expireTime = new Date(submitData.expireTime).toISOString()
    }
    
    // 根据获取方式调用不同的API
    switch (submitData.acquireMethod) {
      case 1: // 购买
        await grantPurchaseAccess(submitData)
        break
      case 2: // 免费
        await grantFreeAccess(submitData)
        break
      case 3: // 积分兑换
        await grantPointsAccess(submitData)
        break
      case 4: // 优惠券兑换
        await grantCouponAccess(submitData)
        break
      case 5: // 管理员赠送
        await grantAdminGift(submitData)
        break
      default:
        throw new Error('未知的获取方式')
    }
    
    message.success('权限授予成功')
    emit('success')
    handleCancel()
  } catch (error) {
    message.error('权限授予失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      resetForm()
    }
  }
)
</script>
