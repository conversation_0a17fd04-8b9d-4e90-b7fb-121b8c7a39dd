import request from "./request";

// 讲师分页查询
export function getTeacherList(params) {
  return request({
    url: "/teachers/page",
    method: "get",
    params,
  });
}

// 获取所有讲师（分页）
export function getAllTeachers(params) {
  return request({
    url: "/teachers/page",
    method: "get",
    params,
  });
}

// 获取讲师详情
export function getTeacherDetail(id) {
  return request({
    url: `/teachers/${id}`,
    method: "get",
  });
}

// 创建讲师
export function createTeacher(data) {
  return request({
    url: "/teachers",
    method: "post",
    data,
  });
}

// 更新讲师
export function updateTeacher(data) {
  return request({
    url: "/teachers",
    method: "put",
    data,
  });
}

// 删除讲师
export function deleteTeacher(id) {
  return request({
    url: `/teachers/${id}`,
    method: "delete",
  });
}

// 批量删除讲师
export function batchDeleteTeachers(ids) {
  return request({
    url: "/teachers/batch",
    method: "delete",
    data: ids,
  });
}
