import request from "./request";

// 课程分页查询
export function getCourseList(params) {
  return request({
    url: "/courses-manage/page",
    method: "get",
    params,
  });
}

// 课程增强分页查询（支持多种筛选条件）
export function getCourseListEnhanced(params) {
  return request({
    url: "/courses-manage/page/enhanced",
    method: "get",
    params,
  });
}

// 获取课程详情（包含关联信息）
export function getCourseDetail(id) {
  return request({
    url: `/courses-manage/detail/${id}`,
    method: "get",
  });
}

// 创建课程
export function createCourse(data) {
  return request({
    url: "/courses-manage",
    method: "post",
    data,
  });
}

// 更新课程
export function updateCourse(data) {
  return request({
    url: "/courses-manage",
    method: "put",
    data,
  });
}

// 删除课程
export function deleteCourse(id) {
  return request({
    url: `/courses-manage/${id}`,
    method: "delete",
  });
}

// 批量删除课程
export function batchDeleteCourses(ids) {
  return request({
    url: "/courses-manage/batch",
    method: "delete",
    data: ids,
  });
}

// 获取分类列表（分页）
export function getCategoryList(params) {
  return request({
    url: "/categories/page",
    method: "get",
    params,
  });
}

// 获取分类分页列表
export function getCategoryPage(params) {
  return request({
    url: "/categories/page",
    method: "get",
    params,
  });
}

// 创建分类
export function createCategory(data) {
  return request({
    url: "/categories",
    method: "post",
    data,
  });
}

// 更新分类
export function updateCategory(data) {
  return request({
    url: "/categories",
    method: "put",
    data,
  });
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: `/categories/${id}`,
    method: "delete",
  });
}

// 获取课程章节（分页）
export function getChapterList(params) {
  return request({
    url: "/course-chapters/page",
    method: "get",
    params,
  });
}

// 创建章节
export function createChapter(data) {
  return request({
    url: "/course-chapters",
    method: "post",
    data,
  });
}

// 更新章节
export function updateChapter(data) {
  return request({
    url: "/course-chapters",
    method: "put",
    data,
  });
}

// 删除章节
export function deleteChapter(id) {
  return request({
    url: `/course-chapters/${id}`,
    method: "delete",
  });
}

// 获取课时列表（分页）
export function getLessonList(params) {
  return request({
    url: "/course-lessons/page",
    method: "get",
    params,
  });
}

// 创建课时
export function createLesson(data) {
  return request({
    url: "/course-lessons",
    method: "post",
    data,
  });
}

// 更新课时
export function updateLesson(data) {
  return request({
    url: "/course-lessons",
    method: "put",
    data,
  });
}

// 删除课时
export function deleteLesson(id) {
  return request({
    url: `/course-lessons/${id}`,
    method: "delete",
  });
}

// 获取课程树形结构（章节+课时）
export function getCourseTree(courseId) {
  return request({
    url: `/courses-manage/${courseId}/tree`,
    method: "get",
  });
}

// 获取课程章节树形结构
export function getCourseChapters(courseId) {
  return request({
    url: `/course-chapters/tree/${courseId}`,
    method: "get",
  });
}
