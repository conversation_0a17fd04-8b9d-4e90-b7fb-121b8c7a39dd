import request from "./request";

// 用户登录
export function login(data) {
  return request({
    url: "/user/login",
    method: "post",
    data,
  });
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: "/user/info",
    method: "get",
  });
}

// 用户分页查询
export function getUserList(params) {
  return request({
    url: "/users-manage/page",
    method: "get",
    params,
  });
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/users-manage/${id}`,
    method: "get",
  });
}

// 创建用户
export function createUser(data) {
  return request({
    url: "/users-manage",
    method: "post",
    data,
  });
}

// 更新用户
export function updateUser(data) {
  return request({
    url: "/users-manage",
    method: "put",
    data,
  });
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/users-manage/${id}`,
    method: "delete",
  });
}

// 批量删除用户
export function batchDeleteUsers(ids) {
  return request({
    url: "/users-manage/batch",
    method: "delete",
    data: ids,
  });
}

// 启用用户
export function enableUser(id) {
  return request({
    url: `/users-manage/enable/${id}`,
    method: "put",
  });
}

// 禁用用户
export function disableUser(id) {
  return request({
    url: `/users-manage/disable/${id}`,
    method: "put",
  });
}
