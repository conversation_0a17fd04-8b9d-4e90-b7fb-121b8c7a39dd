import request from "./request";

// 直播课程分页查询
export function getLiveCourseList(params) {
  return request({
    url: "/live-courses/page",
    method: "get",
    params,
  });
}

// 获取正在直播的课程
export function getLivingCourses(params) {
  return request({
    url: "/live-courses/getLivingCourses",
    method: "get",
    params,
  });
}

// 获取即将开始的直播课程
export function getUpcomingCourses(params) {
  return request({
    url: "/live-courses/getUpcomingCourses",
    method: "get",
    params,
  });
}

// 获取已结束的直播课程
export function getEndedCourses(params) {
  return request({
    url: "/live-courses/getEndedCourses",
    method: "get",
    params,
  });
}

// 获取直播课程详情
export function getLiveCourseDetail(id) {
  return request({
    url: `/live-courses/${id}`,
    method: "get",
  });
}

// 创建直播课程
export function createLiveCourse(data) {
  return request({
    url: "/live-courses",
    method: "post",
    data,
  });
}

// 更新直播课程
export function updateLiveCourse(data) {
  return request({
    url: "/live-courses",
    method: "put",
    data,
  });
}

// 删除直播课程
export function deleteLiveCourse(id) {
  return request({
    url: `/live-courses/${id}`,
    method: "delete",
  });
}

// 批量删除直播课程
export function batchDeleteLiveCourses(ids) {
  return request({
    url: "/live-courses/batch",
    method: "delete",
    data: ids,
  });
}

// 开始直播
export function startLive(id) {
  return request({
    url: `/live-courses/start/${id}`,
    method: "put",
  });
}

// 结束直播
export function endLive(id) {
  return request({
    url: `/live-courses/end/${id}`,
    method: "put",
  });
}
