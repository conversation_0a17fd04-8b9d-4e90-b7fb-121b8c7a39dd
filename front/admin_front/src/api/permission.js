import request from './request'

// 权限检查
export function checkAccess(params) {
  return request({
    url: '/user-course-access/check',
    method: 'get',
    params
  })
}
// 获取用户权限列表 page
export function getUserAccessPageList(params) {
  return request({
    url: '/user-course-access/page',
    method: 'get',
    params
  })
}

// 获取用户权限列表
export function getUserAccessList(params) {
  return request({
    url: '/user-course-access/list',
    method: 'get',
    params
  })
}

// 授予购买权限
export function grantPurchaseAccess(data) {
  return request({
    url: '/user-course-access/grant/purchase',
    method: 'post',
    data
  })
}

// 授予免费权限
export function grantFreeAccess(data) {
  return request({
    url: '/user-course-access/grant/free',
    method: 'post',
    data
  })
}

// 授予积分兑换权限
export function grantPointsAccess(data) {
  return request({
    url: '/user-course-access/grant/points',
    method: 'post',
    data
  })
}

// 授予优惠券兑换权限
export function grantCouponAccess(data) {
  return request({
    url: '/user-course-access/grant/coupon',
    method: 'post',
    data
  })
}

// 管理员赠送权限
export function grantAdminGift(data) {
  return request({
    url: '/user-course-access/grant/admin-gift',
    method: 'post',
    data
  })
}

// 批量授权
export function batchGrantAccess(data) {
  return request({
    url: '/user-course-access/grant/batch',
    method: 'post',
    data
  })
}

// 快速购买课程
export function quickPurchaseCourse(params) {
  return request({
    url: '/user-course-access/quick-purchase',
    method: 'post',
    params
  })
}

// 处理退款
export function processRefund(data) {
  return request({
    url: '/user-course-access/refund',
    method: 'post',
    data
  })
}

// 激活权限
export function activateAccess(accessId) {
  return request({
    url: `/user-course-access/activate/${accessId}`,
    method: 'post'
  })
}

// 停用权限
export function deactivateAccess(data) {
  return request({
    url: '/user-course-access/deactivate',
    method: 'post',
    data
  })
}

// 获取用户有效权限
export function getUserValidAccess(userId) {
  return request({
    url: `/user-course-access/user/${userId}/valid`,
    method: 'get'
  })
}

// 获取用户课程权限
export function getUserCourseAccess(userId, courseId) {
  return request({
    url: `/user-course-access/user/${userId}/course/${courseId}`,
    method: 'get'
  })
}

// 获取即将过期的权限
export function getExpiringAccess(userId) {
  return request({
    url: `/user-course-access/user/${userId}/expiring`,
    method: 'get'
  })
}

// 获取用户权限统计
export function getUserAccessStatistics(userId) {
  return request({
    url: `/user-course-access/user/${userId}/statistics`,
    method: 'get'
  })
}

// 获取全局权限统计
export function getGlobalAccessStatistics() {
  return request({
    url: '/user-course-access/statistics/global',
    method: 'get'
  })
}

// 获取权限类型选项
export function getAccessTypeOptions() {
  return [
    { label: '课程权限', value: 1 },
    { label: '章节权限', value: 2 },
    { label: '课时权限', value: 3 }
  ]
}

// 获取获取方式选项
export function getAcquireMethodOptions() {
  return [
    { label: '购买', value: 1 },
    { label: '免费', value: 2 },
    { label: '积分兑换', value: 3 },
    { label: '优惠券兑换', value: 4 },
    { label: '管理员赠送', value: 5 },
    { label: '推广活动', value: 6 }
  ]
}

// 获取支付方式选项
export function getPaymentMethodOptions() {
  return [
    { label: '微信支付', value: 'wechat' },
    { label: '支付宝', value: 'alipay' },
    { label: '积分', value: 'points' },
    { label: '优惠券', value: 'coupon' },
    { label: '赠送', value: 'gift' }
  ]
}

// 获取权限状态选项
export function getStatusOptions() {
  return [
    { label: '已失效', value: 0 },
    { label: '有效', value: 1 },
    { label: '已退款', value: 2 }
  ]
}

// 格式化权限类型
export function formatAccessType(type) {
  const options = getAccessTypeOptions()
  const option = options.find(item => item.value === type)
  return option ? option.label : '未知'
}

// 格式化获取方式
export function formatAcquireMethod(method) {
  const options = getAcquireMethodOptions()
  const option = options.find(item => item.value === method)
  return option ? option.label : '未知'
}

// 格式化支付方式
export function formatPaymentMethod(method) {
  const options = getPaymentMethodOptions()
  const option = options.find(item => item.value === method)
  return option ? option.label : '未知'
}

// 格式化权限状态
export function formatStatus(status) {
  const options = getStatusOptions()
  const option = options.find(item => item.value === status)
  return option ? option.label : '未知'
}

// 格式化价格
export function formatPrice(price) {
  if (price === null || price === undefined) return '免费'
  return `¥${parseFloat(price).toFixed(2)}`
}

// 格式化时间
export function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 检查权限是否即将过期
export function isAccessExpiringSoon(expireTime, days = 7) {
  if (!expireTime) return false
  const now = new Date()
  const expire = new Date(expireTime)
  const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))
  return diffDays > 0 && diffDays <= days
}

// 检查权限是否已过期
export function isAccessExpired(expireTime) {
  if (!expireTime) return false
  return new Date(expireTime) < new Date()
}
