import request from '@/utils/request'

// 分页查询标签配置
export function getTagConfigPage(params) {
  return request({
    url: '/tag-configs/page',
    method: 'get',
    params
  })
}

// 获取标签配置列表
export function getTagConfigList(params) {
  return request({
    url: '/tag-configs/list',
    method: 'get',
    params
  })
}

// 根据ID查询标签配置
export function getTagConfigById(id) {
  return request({
    url: `/tag-configs/${id}`,
    method: 'get'
  })
}

// 新增标签配置
export function createTagConfig(data) {
  return request({
    url: '/tag-configs',
    method: 'post',
    data
  })
}

// 修改标签配置
export function updateTagConfig(data) {
  return request({
    url: '/tag-configs',
    method: 'put',
    data
  })
}

// 删除标签配置
export function deleteTagConfig(id) {
  return request({
    url: `/tag-configs/${id}`,
    method: 'delete'
  })
}

// 批量删除标签配置
export function batchDeleteTagConfigs(ids) {
  return request({
    url: '/tag-configs/batch',
    method: 'delete',
    data: ids
  })
}

// 根据分类获取标签配置列表
export function getTagConfigByCategory(category) {
  return request({
    url: `/tag-configs/getByCategory/${category}`,
    method: 'get'
  })
}

// 获取可用的标签配置列表
export function getAvailableTagConfigByCategory(category) {
  return request({
    url: `/tag-configs/getAvailableByCategory/${category}`,
    method: 'get'
  })
}

// 检查标签值是否存在
export function checkValueExists(category, value, excludeId) {
  return request({
    url: '/tag-configs/checkValueExists',
    method: 'get',
    params: { category, value, excludeId }
  })
}

// 获取标签类型统计
export function getTagConfigCategories() {
  return request({
    url: '/tag-configs/categories',
    method: 'get'
  })
}

// 获取标签类型列表（包含中文名称和统计）
export function getTagConfigCategoryList() {
  return request({
    url: '/tag-configs/category-list',
    method: 'get'
  })
}