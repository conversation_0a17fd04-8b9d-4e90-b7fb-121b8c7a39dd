import { uploadService } from "./request";

/**
 * 获取文件上传配置信息
 * @returns {Promise} 配置信息
 */
export function getFileUploadInfo() {
  return uploadService({
    url: "/file/info",
    method: "get",
  });
}

/**
 * 上传文件
 * @param {File} file 文件对象
 * @param {string} category 文件分类 (image/video/audio/document)
 * @param {Function} onProgress 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadFile(file, category, onProgress) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("category", category);

  return uploadService({
    url: "/file/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        onProgress(percentCompleted);
      }
    },
  });
}

/**
 * 批量上传文件
 * @param {Array} files 文件数组
 * @param {string} category 文件分类
 * @param {Function} onProgress 总体进度回调
 * @param {Function} onFileProgress 单个文件进度回调
 * @returns {Promise} 上传结果数组
 */
export function uploadFiles(files, category, onProgress, onFileProgress) {
  const uploadPromises = files.map((file, index) => {
    return uploadFile(file, category, (progress) => {
      if (onFileProgress) {
        onFileProgress(index, progress);
      }
    });
  });

  // 监听总体进度
  if (onProgress) {
    let completedCount = 0;
    uploadPromises.forEach((promise) => {
      promise.finally(() => {
        completedCount++;
        const totalProgress = Math.round((completedCount / files.length) * 100);
        onProgress(totalProgress);
      });
    });
  }

  return Promise.all(uploadPromises);
}
