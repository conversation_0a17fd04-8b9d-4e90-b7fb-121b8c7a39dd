import request from "./request";

// ==================== 课程评价 ====================

// 获取课程评价列表
export function getCourseReviews(params) {
  return request({
    url: "/course-reviews/page",
    method: "get",
    params,
  });
}

// 获取课程评价详情
export function getCourseReviewDetail(id) {
  return request({
    url: `/course-reviews/${id}`,
    method: "get",
  });
}

// 添加课程评价
export function addCourseReview(data) {
  return request({
    url: "/course-reviews",
    method: "post",
    data,
  });
}

// 更新课程评价
export function updateCourseReview(data) {
  return request({
    url: "/course-reviews",
    method: "put",
    data,
  });
}

// 删除课程评价
export function deleteCourseReview(id) {
  return request({
    url: `/course-reviews/${id}`,
    method: "delete",
  });
}

// 批量删除课程评价
export function batchDeleteCourseReviews(ids) {
  return request({
    url: "/course-reviews/batch",
    method: "delete",
    data: ids,
  });
}

// 根据课程ID获取评价
export function getReviewsByCourseId(courseId, params) {
  return request({
    url: `/course-reviews/course/${courseId}`,
    method: "get",
    params,
  });
}

// ==================== 用户收藏 ====================

// 获取用户收藏列表
export function getUserFavorites(params) {
  return request({
    url: "/user-favorites/page",
    method: "get",
    params,
  });
}

// 根据用户ID获取收藏列表
export function getFavoritesByUserId(userId, params) {
  return request({
    url: `/user-favorites/getByUserId/${userId}`,
    method: "get",
    params,
  });
}

// 检查用户是否收藏了课程
export function checkFavorite(params) {
  return request({
    url: `/user-favorites/checkFavorite`,
    method: "get",
    params,
  });
}

// 添加收藏
export function addFavorite(data) {
  return request({
    url: "/user-favorites/add",
    method: "post",
    data,
  });
}

// 取消收藏
export function cancelFavorite(data) {
  return request({
    url: "/user-favorites/cancel",
    method: "delete",
    data,
  });
}

// ==================== 用户打卡 ====================

// 获取用户打卡记录
export function getUserCheckins(params) {
  return request({
    url: "/user-checkins/page",
    method: "get",
    params,
  });
}

// 获取用户打卡记录（按用户ID）
export function getCheckinsByUserId(userId, params) {
  return request({
    url: `/user-checkins/getByUserId/${userId}`,
    method: "get",
    params,
  });
}

// 获取用户当天打卡记录
export function getTodayCheckin(userId) {
  return request({
    url: `/user-checkins/getTodayCheckin/${userId}`,
    method: "get",
  });
}

// 用户打卡
export function userCheckin(data) {
  return request({
    url: "/user-checkins/checkin",
    method: "post",
    data,
  });
}

// 获取用户连续打卡天数
export function getConsecutiveDays(userId) {
  return request({
    url: `/user-checkins/getConsecutiveDays/${userId}`,
    method: "get",
  });
}

// 删除打卡记录
export function deleteCheckin(id) {
  return request({
    url: `/user-checkins/${id}`,
    method: "delete",
  });
}

// 批量删除打卡记录
export function batchDeleteCheckins(ids) {
  return request({
    url: "/user-checkins/batch",
    method: "delete",
    data: ids,
  });
}
