import request from "./request";

// 获取学习记录列表
export function getLearningRecords(params) {
  return request({
    url: "/user-learning-records/page",
    method: "get",
    params,
  });
}

// 获取用户学习记录
export function getUserLearningRecords(userId, params) {
  return request({
    url: `/user-learning-records/user/${userId}`,
    method: "get",
    params,
  });
}

// 获取课程学习记录
export function getCourseLearningRecords(courseId, params) {
  return request({
    url: `/user-learning-records/course/${courseId}`,
    method: "get",
    params,
  });
}

// 获取用户特定课程的学习记录
export function getUserCourseRecord(userId, courseId) {
  return request({
    url: `/user-learning-records/user/${userId}/course/${courseId}`,
    method: "get",
  });
}

// 创建/更新学习记录
export function saveUserLearningRecord(data) {
  return request({
    url: "/user-learning-records",
    method: "post",
    data,
  });
}

// 删除学习记录
export function deleteLearningRecord(id) {
  return request({
    url: `/user-learning-records/${id}`,
    method: "delete",
  });
}

// 批量删除学习记录
export function batchDeleteLearningRecords(ids) {
  return request({
    url: "/user-learning-records/batch",
    method: "delete",
    data: ids,
  });
}

// 获取用户总学习时长
export function getUserTotalLearningTime(userId) {
  return request({
    url: `/user-learning-records/total-time/${userId}`,
    method: "get",
  });
}

// 获取用户课程完成率
export function getUserCourseCompletionRate(userId, courseId) {
  return request({
    url: `/user-learning-records/completion-rate`,
    method: "get",
    params: { userId, courseId },
  });
}

// 获取用户最近学习的课程
export function getUserRecentLearning(userId, limit = 5) {
  return request({
    url: `/user-learning-records/recent`,
    method: "get",
    params: { userId, limit },
  });
}

// 获取用户学习统计数据
export function getUserLearningStats(userId) {
  return request({
    url: `/user-learning-records/stats/${userId}`,
    method: "get",
  });
}
