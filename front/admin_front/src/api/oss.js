import service from "./request";

/**
 * 获取OSS PostObject签名
 * @param {string} category 文件分类 (image/video/audio/document)
 * @param {string} fileName 文件名（可选）
 * @returns {Promise} PostObject签名信息
 */
export function getOssSignature(category, fileName = null) {
  const params = { category };
  if (fileName) {
    params.fileName = fileName;
  }

  return service({
    url: "/oss/signature",
    method: "get",
    params,
  });
}

/**
 * 获取自定义OSS PostObject签名
 * @param {Object} params 参数对象
 * @param {string} params.pathPrefix 路径前缀
 * @param {string} params.fileName 文件名
 * @param {string} params.contentType 文件类型（可选）
 * @param {number} params.maxFileSize 最大文件大小（可选）
 * @returns {Promise} PostObject签名信息
 */
export function getCustomOssSignature(params) {
  return service({
    url: "/oss/signature/custom",
    method: "get",
    params,
  });
}

/**
 * 使用PostObject方式上传文件到阿里云OSS
 * @param {File} file 文件对象
 * @param {Object} signature PostObject签名信息
 * @param {Function} onProgress 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadToOssWithPostObject(file, signature, onProgress) {
  return new Promise((resolve, reject) => {
    try {
      console.log('开始使用PostObject方式上传文件到OSS:', {
        objectKey: signature.objectKey,
        uploadUrl: signature.uploadUrl,
        bucketName: signature.bucketName
      });

      // 创建FormData
      const formData = new FormData();

      // 添加签名相关字段（必须在file字段之前）
      Object.entries(signature.formFields).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // 添加文件（必须最后添加）
      formData.append('file', file);

      // 创建XMLHttpRequest
      const xhr = new XMLHttpRequest();

      // 设置上传进度监听
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentage = Math.round((event.loaded / event.total) * 100);
            onProgress(percentage);
          }
        });
      }

      // 设置完成监听
      xhr.addEventListener('load', () => {

        if (xhr.status === 200 || xhr.status === 204) {
          // 正确的文件URL = baseUrl + objectKey
          const baseUrl = 'https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/';
          const fileUrl = baseUrl + signature.objectKey;

          const result = {
            fileName: signature.objectKey,
            originalFileName: file.name,
            fileSize: file.size,
            contentType: file.type,
            bucketName: signature.bucketName,
            objectKey: signature.objectKey,
            baseUrl,
            uploadTime: Date.now(),
            category: extractCategoryFromPath(signature.objectKey),
          };

          console.log('OSS PostObject上传成功，文件URL:', fileUrl);
          resolve(result);
        } else {
          console.error('OSS PostObject上传失败:', xhr.status, xhr.statusText, xhr.responseText);
          reject(new Error(`上传失败: HTTP ${xhr.status} ${xhr.statusText}`));
        }
      });

      // 设置错误监听
      xhr.addEventListener('error', () => {
        console.error('OSS PostObject上传网络错误');
        reject(new Error('网络连接失败，请检查网络连接'));
      });

      // 设置超时监听
      xhr.addEventListener('timeout', () => {
        console.error('OSS PostObject上传超时');
        reject(new Error('上传超时，请重试'));
      });

      // 配置请求
      xhr.open('POST', signature.uploadUrl);
      xhr.timeout = 300000; // 5分钟超时

      // 发送请求
      xhr.send(formData);

    } catch (error) {
      console.error('PostObject上传初始化失败:', error);
      reject(new Error('上传初始化失败: ' + error.message));
    }
  });
}

/**
 * 便捷的文件上传函数（获取签名+上传）
 * @param {File} file 文件对象
 * @param {string} category 文件分类
 * @param {Function} onProgress 上传进度回调
 * @returns {Promise} 上传结果
 */
export function uploadFile(file, category, onProgress) {
  return new Promise(async (resolve, reject) => {
    try {
      // 1. 获取PostObject签名
      console.log('获取PostObject签名...');
      const signatureResponse = await getOssSignature(category, file.name);

      if (signatureResponse.code !== 200) {
        throw new Error('获取签名失败: ' + signatureResponse.msg);
      }

      const signature = signatureResponse.data;
      console.log('签名获取成功:', signature.objectKey);

      // 2. 使用PostObject上传文件
      console.log('开始上传文件...');
      const result = await uploadToOssWithPostObject(file, signature, onProgress);

      console.log('文件上传成功:', result);
      resolve(result);

    } catch (error) {
      console.error('文件上传失败:', error);
      reject(error);
    }
  });
}

/**
 * 生成唯一文件名
 * @param {string} originalName 原始文件名
 * @param {string} pathPrefix 路径前缀
 * @returns {string} 生成的文件名
 */
function generateFileName(originalName, pathPrefix) {
  const extension = originalName.split('.').pop().toLowerCase();
  const uuid = generateUUID();
  return `${pathPrefix}${uuid}.${extension}`;
}

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 从文件路径中提取分类
 * @param {string} fileName 文件名
 * @returns {string} 文件分类
 */
function extractCategoryFromPath(fileName) {
  const parts = fileName.split('/');
  return parts[0] || 'unknown';
}

/**
 * 验证文件类型
 * @param {File} file 文件对象
 * @param {string[]} allowedTypes 允许的文件类型（支持MIME类型或扩展名）
 * @returns {boolean} 是否有效
 */
export function validateFileType(file, allowedTypes) {
  // 获取文件扩展名
  const extension = file.name.split('.').pop().toLowerCase();

  // 获取文件MIME类型
  const mimeType = file.type.toLowerCase();

  // 检查是否匹配MIME类型或扩展名
  return allowedTypes.some(type => {
    if (type.includes('/')) {
      // 如果是MIME类型格式（包含/）
      return mimeType === type.toLowerCase();
    } else {
      // 如果是扩展名格式
      return extension === type.toLowerCase();
    }
  });
}

/**
 * 验证文件大小
 * @param {File} file 文件对象
 * @param {number} maxSize 最大大小（字节）
 * @returns {boolean} 是否有效
 */
export function validateFileSize(file, maxSize) {
  return file.size <= maxSize;
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
