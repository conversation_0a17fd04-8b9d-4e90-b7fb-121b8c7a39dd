import request from "./request";

// 获取客服联系信息列表
export function getCustomerServiceList(params) {
  return request({
    url: "/customer-service-contacts/page",
    method: "get",
    params,
  });
}

// 获取所有可用客服联系信息
export function getAllAvailableCustomerService() {
  return request({
    url: "/customer-service-contacts/getAllAvailable",
    method: "get",
  });
}

// 获取随机一个客服联系信息
export function getRandomCustomerService() {
  return request({
    url: "/customer-service-contacts/getRandomOne",
    method: "get",
  });
}

// 获取客服联系信息详情
export function getCustomerServiceDetail(id) {
  return request({
    url: `/customer-service-contacts/${id}`,
    method: "get",
  });
}

// 创建客服联系信息
export function createCustomerService(data) {
  return request({
    url: "/customer-service-contacts",
    method: "post",
    data,
  });
}

// 更新客服联系信息
export function updateCustomerService(data) {
  return request({
    url: "/customer-service-contacts",
    method: "put",
    data,
  });
}

// 删除客服联系信息
export function deleteCustomerService(id) {
  return request({
    url: `/customer-service-contacts/${id}`,
    method: "delete",
  });
}

// 批量删除客服联系信息
export function batchDeleteCustomerServices(ids) {
  return request({
    url: "/customer-service-contacts/batch",
    method: "delete",
    data: ids,
  });
}

// 启用客服联系信息
export function enableCustomerService(id) {
  return request({
    url: `/customer-service-contacts/enable/${id}`,
    method: "put",
  });
}

// 禁用客服联系信息
export function disableCustomerService(id) {
  return request({
    url: `/customer-service-contacts/disable/${id}`,
    method: "put",
  });
}
