<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    :title="title"
    size="huge"
    :bordered="false"
    :segmented="false"
    :closable="true"
    :mask-closable="true"
    class="video-preview-tree-modal"
    @after-leave="handleModalClose"
  >
    <div class="video-preview-container">
      <n-grid :cols="24" :x-gap="16">
        <!-- 左侧视频播放器 -->
        <n-gi :span="16">
          <div class="video-player-section">
            <!-- 视频播放器容器 -->
            <div
              ref="playerContainer"
              class="xgplayer-container"
              v-show="currentVideoUrl"
            ></div>
            
            <!-- 无视频提示 -->
            <div v-if="!currentVideoUrl" class="no-video-hint">
              <n-icon size="64" color="#ccc">
                <VideocamOutline />
              </n-icon>
              <div class="hint-text">请从右侧选择视频进行播放</div>
            </div>
            
            <!-- 当前视频信息 -->
            <div v-if="currentVideoInfo" class="current-video-info">
              <n-descriptions :column="2" bordered size="small">
                <n-descriptions-item label="课时标题">
                  {{ currentVideoInfo.lessonTitle }}
                </n-descriptions-item>
                <n-descriptions-item label="所属章节">
                  {{ currentVideoInfo.chapterTitle }}
                </n-descriptions-item>
                <n-descriptions-item label="视频时长">
                  {{ formatDuration(currentVideoInfo.duration) }}
                </n-descriptions-item>
                <n-descriptions-item label="是否免费">
                  <n-tag :type="currentVideoInfo.isFree ? 'success' : 'warning'" size="small">
                    {{ currentVideoInfo.isFree ? '免费观看' : '付费内容' }}
                  </n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </div>
          </div>
        </n-gi>
        
        <!-- 右侧视频树结构 -->
        <n-gi :span="8">
          <div class="video-tree-section">
            <n-card title="课程目录" size="small" :bordered="false">
              <n-collapse v-model:expanded-names="expandedChapters">
                <n-collapse-item
                  v-for="(chapter, chapterIndex) in videoTree"
                  :key="chapter.id || chapterIndex"
                  :name="chapterIndex"
                  :title="`第${chapterIndex + 1}章：${chapter.title || '未命名章节'}`"
                >
                  <div class="chapter-lessons">
                    <div
                      v-for="(lesson, lessonIndex) in chapter.lessons"
                      :key="lesson.id || lessonIndex"
                      class="lesson-item"
                      :class="{ 
                        'lesson-active': currentLessonId === lesson.id,
                        'lesson-no-video': !lesson.videoUrl 
                      }"
                      @click="selectVideo(lesson, chapter)"
                    >
                      <div class="lesson-content">
                        <div class="lesson-title">
                          <n-icon class="lesson-icon">
                            <PlayCircleOutline v-if="lesson.videoUrl" />
                            <VideocamOutline v-else />
                          </n-icon>
                          <span>{{ lessonIndex + 1 }}. {{ lesson.title }}</span>
                        </div>
                        <div class="lesson-meta">
                          <n-space size="small">
                            <n-tag v-if="lesson.isFree" type="success" size="tiny">免费</n-tag>
                            <n-tag v-else type="warning" size="tiny">付费</n-tag>
                            <n-tag type="info" size="tiny">{{ formatDuration(lesson.duration) }}</n-tag>
                          </n-space>
                        </div>
                      </div>
                      <div v-if="lesson.videoUrl" class="lesson-play-icon">
                        <n-icon size="16" color="#18a058">
                          <PlayCircleOutline />
                        </n-icon>
                      </div>
                    </div>
                  </div>
                </n-collapse-item>
              </n-collapse>
              
              <!-- 无章节提示 -->
              <div v-if="!videoTree || videoTree.length === 0" class="no-chapters-hint">
                <n-empty description="暂无课程内容" />
              </div>
            </n-card>
          </div>
        </n-gi>
      </n-grid>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, watch, nextTick, onUnmounted, computed } from "vue";
import { useMessage } from "naive-ui";
import { VideocamOutline, PlayCircleOutline } from "@vicons/ionicons5";
import Player from "xgplayer";

// Props
const props = defineProps({
  // 是否显示模态框
  show: {
    type: Boolean,
    default: false,
  },
  // 模态框标题
  title: {
    type: String,
    default: "视频预览",
  },
  // 视频树结构数据
  videoTree: {
    type: Array,
    default: () => [],
  },
  // 默认选中的视频ID
  defaultVideoId: {
    type: [String, Number],
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:show", "video-change"]);

// Refs
const visible = ref(false);
const playerContainer = ref(null);
const message = useMessage();

// 播放器实例
let playerInstance = null;

// 响应式数据
const expandedChapters = ref([]);
const currentVideoUrl = ref("");
const currentVideoInfo = ref(null);
const currentLessonId = ref(null);

// 计算总视频数量
const totalVideoCount = computed(() => {
  return props.videoTree.reduce((count, chapter) => {
    return count + (chapter.lessons?.filter(lesson => lesson.videoUrl)?.length || 0);
  }, 0);
});

// 监听show属性变化
watch(
  () => props.show,
  (newValue) => {
    visible.value = newValue;
    if (newValue) {
      initializeModal();
    }
  },
  { immediate: true }
);

// 监听visible变化，同步到父组件
watch(visible, (newValue) => {
  emit("update:show", newValue);
});

// 监听videoTree变化
watch(
  () => props.videoTree,
  (newTree) => {
    if (newTree && newTree.length > 0) {
      initializeExpandedChapters();
      if (props.defaultVideoId) {
        selectVideoById(props.defaultVideoId);
      }
    }
  },
  { deep: true, immediate: true }
);

// 初始化模态框
const initializeModal = () => {
  initializeExpandedChapters();
  if (props.defaultVideoId) {
    selectVideoById(props.defaultVideoId);
  } else if (props.videoTree && props.videoTree.length > 0) {
    // 如果没有默认视频，选择第一个有视频的课时
    selectFirstAvailableVideo();
  }
};

// 初始化展开的章节
const initializeExpandedChapters = () => {
  if (!props.videoTree) return;
  
  // 如果视频总数小于12，默认展开所有章节
  if (totalVideoCount.value < 12) {
    expandedChapters.value = props.videoTree.map((_, index) => index);
  } else {
    // 否则只展开第一个章节
    expandedChapters.value = [0];
  }
};

// 根据ID选择视频
const selectVideoById = (videoId) => {
  for (let chapterIndex = 0; chapterIndex < props.videoTree.length; chapterIndex++) {
    const chapter = props.videoTree[chapterIndex];
    const lesson = chapter.lessons?.find(l => l.id === videoId);
    if (lesson) {
      selectVideo(lesson, chapter);
      // 确保该章节展开
      if (!expandedChapters.value.includes(chapterIndex)) {
        expandedChapters.value.push(chapterIndex);
      }
      break;
    }
  }
};

// 选择第一个可用的视频
const selectFirstAvailableVideo = () => {
  for (const chapter of props.videoTree) {
    const firstVideoLesson = chapter.lessons?.find(lesson => lesson.videoUrl);
    if (firstVideoLesson) {
      selectVideo(firstVideoLesson, chapter);
      break;
    }
  }
};

// 选择视频
const selectVideo = async (lesson, chapter) => {
  if (!lesson.videoUrl) {
    message.warning("该课时暂无视频内容");
    return;
  }
  
  // 构建完整的视频URL
  let videoUrl = lesson.videoUrl;
  if (!videoUrl.startsWith('http')) {
    videoUrl = `https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/${lesson.videoUrl}`;
  }
  
  currentVideoUrl.value = videoUrl;
  currentLessonId.value = lesson.id;
  currentVideoInfo.value = {
    lessonTitle: lesson.title,
    chapterTitle: chapter.title,
    duration: lesson.duration || 0,
    isFree: lesson.isFree === 1 || lesson.isFree === true,
  };
  
  // 初始化播放器
  await nextTick();
  setTimeout(() => {
    initPlayer(videoUrl);
  }, 100);
  
  // 发送视频变化事件
  emit("video-change", { lesson, chapter, videoUrl });
};

// 播放器初始化
const initPlayer = (url) => {
  if (!playerContainer.value || !url) {
    console.warn('🚨 播放器容器或URL为空:', { container: playerContainer.value, url });
    return;
  }
  
  // 销毁现有播放器
  destroyPlayer();
  
  try {
    console.log('🎬 开始初始化视频树预览播放器:', { container: playerContainer.value, url });
    
    // 确保容器有唯一ID
    if (!playerContainer.value.id) {
      playerContainer.value.id = `xgplayer-tree-${Date.now()}`;
    }
    
    // 创建新的播放器实例
    playerInstance = new Player({
      el: playerContainer.value,
      url: url,
      width: '100%',
      height: 'auto',
      autoplay: false,
      poster: '',
      playsinline: true,
      fluid: true,
      videoInit: true,
      controls: {
        mode: 'normal'
      },
      cssFullscreen: true,
      rotateFullscreen: false,
      onReady: () => {
        console.log('🎬 视频树预览播放器初始化完成');
      },
      onError: (error) => {
        console.error('🚨 视频树预览播放器错误:', error);
        message.error("视频播放失败，请检查视频文件");
      }
    });
    
    console.log('🎬 视频树预览播放器创建成功:', playerInstance);
    
  } catch (error) {
    console.error('🚨 视频树预览播放器初始化失败:', error);
    message.error("视频播放器初始化失败");
  }
};

// 播放器销毁
const destroyPlayer = () => {
  if (playerInstance) {
    try {
      playerInstance.destroy();
      playerInstance = null;
    } catch (error) {
      console.error('🚨 销毁视频树预览播放器失败:', error);
    }
  }
};

// 模态框关闭处理
const handleModalClose = () => {
  destroyPlayer();
  currentVideoUrl.value = "";
  currentVideoInfo.value = null;
  currentLessonId.value = null;
};

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return "0分钟";
  const minutes = Math.floor(duration / 60);
  const seconds = duration % 60;
  if (minutes > 0) {
    return seconds > 0 ? `${minutes}分${seconds}秒` : `${minutes}分钟`;
  }
  return `${seconds}秒`;
};

// 组件卸载时清理
onUnmounted(() => {
  destroyPlayer();
});
</script>

<style lang="scss" scoped>
.video-preview-tree-modal {
  .n-card {
    max-width: 95vw;
    max-height: 90vh;
  }
}

.video-preview-container {
  .video-player-section {
    .xgplayer-container {
      width: 100%;
      min-height: 400px;
      max-height: 60vh;
      border-radius: 8px;
      overflow: hidden;
      background-color: #000;
      margin-bottom: 16px;
      
      // XGPlayer 样式覆盖
      :deep(.xgplayer) {
        border-radius: 8px;
        
        .xgplayer-controls {
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        }
        
        .xgplayer-progress {
          .xgplayer-progress-played {
            background: #18a058;
          }
          
          .xgplayer-progress-btn {
            background: #18a058;
          }
        }
        
        .xgplayer-play,
        .xgplayer-pause {
          color: #fff;
        }
        
        .xgplayer-time {
          color: #fff;
          font-size: 12px;
        }
        
        .xgplayer-volume {
          color: #fff;
        }
        
        .xgplayer-fullscreen {
          color: #fff;
        }
      }
    }
    
    .no-video-hint {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      color: #999;
      background-color: #f5f5f5;
      border-radius: 8px;
      
      .hint-text {
        margin-top: 16px;
        font-size: 16px;
      }
    }
    
    .current-video-info {
      margin-top: 16px;
    }
  }
  
  .video-tree-section {
    height: 60vh;
    overflow-y: auto;
    
    .chapter-lessons {
      .lesson-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        
        &:hover {
          background-color: #f0f9ff;
          border-color: #e1f5fe;
        }
        
        &.lesson-active {
          background-color: #e8f5e8;
          border-color: #18a058;
          
          .lesson-title {
            color: #18a058;
            font-weight: 500;
          }
        }
        
        &.lesson-no-video {
          cursor: not-allowed;
          opacity: 0.6;
          
          &:hover {
            background-color: transparent;
            border-color: transparent;
          }
        }
        
        .lesson-content {
          flex: 1;
          
          .lesson-title {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 14px;
            
            .lesson-icon {
              margin-right: 6px;
              font-size: 14px;
            }
          }
          
          .lesson-meta {
            font-size: 12px;
          }
        }
        
        .lesson-play-icon {
          margin-left: 8px;
        }
      }
    }
    
    .no-chapters-hint {
      padding: 40px 20px;
      text-align: center;
    }
  }
}
</style>
