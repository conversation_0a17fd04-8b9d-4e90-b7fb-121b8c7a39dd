<template>
  <n-modal
    v-model:show="visible"
    size="large"
    :bordered="false"
    :closable="true"
    :mask-closable="true"
    class="video-preview-modal"
    @after-leave="handleModalClose"
  >
    <n-card
      class="modal-content"
      :closable="true"
      :title="title"
      :mask-closable="true"
      @close="
        () => {
          visible = false;
        }
      "
    >
      <!-- 背景装饰 -->


      <div class="modal-header">

        <div class="header-actions" v-if="hasCatalog">
          <n-button quaternary circle size="small" @click="toggleCatalog">
            <n-icon>
              <MenuOutline />
            </n-icon>
          </n-button>
        </div>
      </div>

      <div class="content-wrapper">
        <!-- 左侧视频播放区域 -->
        <div class="video-section" :class="{ 'with-catalog': showCatalog }">
          <div class="video-preview-container">
            <!-- 视频播放器容器 -->
            <div
              ref="playerContainer"
              class="xgplayer-container"
              v-show="videoUrl"
            ></div>

            <!-- 无视频提示 -->
            <div v-if="!videoUrl" class="no-video-hint">
              <n-icon size="48" color="#ccc">
                <VideocamOutline />
              </n-icon>
              <div class="hint-text">暂无视频内容</div>
            </div>

            <!-- 视频信息 -->
            <div v-if="videoInfo" class="video-info">
              <n-descriptions :column="2" bordered size="small">
                <n-descriptions-item label="课时标题">
                  {{ videoInfo.lessonTitle }}
                </n-descriptions-item>
                <n-descriptions-item label="所属章节">
                  {{ videoInfo.chapterTitle }}
                </n-descriptions-item>
                <n-descriptions-item label="视频时长">
                  {{ formatDuration(videoInfo.duration) }}
                </n-descriptions-item>
                <n-descriptions-item label="是否免费">
                  <n-tag
                    :type="videoInfo.isFree ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ videoInfo.isFree ? "免费观看" : "付费内容" }}
                  </n-tag>
                </n-descriptions-item>
              </n-descriptions>
            </div>
          </div>
        </div>

        <!-- 右侧目录树 -->
        <div v-show="showCatalog" class="catalog-section">
          <div class="catalog-header">
            <h4>课程目录</h4>
            <n-button
              quaternary
              circle
              size="tiny"
              @click="showCatalog = false"
            >
              <n-icon>
                <CloseOutline />
              </n-icon>
            </n-button>
          </div>

          <div class="catalog-content">
            <n-tree
              v-if="processedCatalogTree && processedCatalogTree.length"
              :data="processedCatalogTree"
              :selectable="true"
              :show-irrelevant-nodes="false"
              :selected-keys="selectedKeys"
              :node-props="getNodeProps"
              @update:selected-keys="handleNodeSelect"
            />

            <div v-else class="empty-catalog">
              <n-icon size="32" color="#ccc">
                <FolderOpenOutline />
              </n-icon>
              <div class="empty-text">暂无课程目录</div>
            </div>
          </div>
        </div>
      </div>
    </n-card>
  </n-modal>
</template>

<script setup>
  import { ref, watch, nextTick, onUnmounted, computed } from "vue";
  import {
    VideocamOutline,
    MenuOutline,
    CloseOutline,
    FolderOpenOutline,
  } from "@vicons/ionicons5";
  import Player from "xgplayer";

  // Props
  const props = defineProps({
    // 是否显示模态框
    show: {
      type: Boolean,
      default: false,
    },
    // 视频URL
    videoUrl: {
      type: String,
      default: "",
    },
    // 模态框标题
    title: {
      type: String,
      default: "视频预览",
    },
    // 视频信息
    videoInfo: {
      type: Object,
      default: () => ({}),
    },
    // 目录树数据
    catalogTree: {
      type: Array,
      default: () => [],
    },
  });

  // Emits
  const emit = defineEmits(["update:show", "video-select"]);

  // Computed
  const hasCatalog = computed(() => {
    return Array.isArray(props.catalogTree) && props.catalogTree.length > 0;
  });

  // Refs
  const visible = ref(false);
  const playerContainer = ref(null);
  const showCatalog = ref(false);
  const selectedKeys = ref([]);

  // 播放器实例
  let playerInstance = null;

  // 处理后的目录树数据
  const processedCatalogTree = computed(() => {
    let tree = [...(props.catalogTree || [])];
    
    // 如果有videoUrl但目录中没有找到匹配项，添加到第一个节点
    if (props.videoUrl && tree.length > 0) {
      const foundUrl = findUrlInTree(tree, props.videoUrl);
      if (!foundUrl && props.title) {
        // 添加到第一个节点的children中
        if (!tree[0].children) {
          tree[0].children = [];
        }
        const newNode = {
          key: `added_${Date.now()}`,
          label: props.title,
          videoUrl: props.videoUrl,
          isNew: true
        };
        tree[0].children.unshift(newNode);
      }
    }
    
    return tree;
  });

  // 在目录树中查找URL
  const findUrlInTree = (tree, url) => {
    for (const node of tree) {
      if (node.videoUrl === url) {
        return node;
      }
      if (node.children) {
        const found = findUrlInTree(node.children, url);
        if (found) return found;
      }
    }
    return null;
  };

  // 查找URL对应的key
  const findKeyByUrl = (tree, url) => {
    for (const node of tree) {
      if (node.videoUrl === url) {
        return node.key;
      }
      if (node.children) {
        const foundKey = findKeyByUrl(node.children, url);
        if (foundKey) return foundKey;
      }
    }
    return null;
  };

  // 监听show属性变化
  watch(
    () => props.show,
    (newValue) => {
      visible.value = newValue;
    },
    { immediate: true }
  );

  // 监听visible变化，同步到父组件
  watch(visible, (newValue) => {
    emit("update:show", newValue);
  });

  // 监听videoUrl变化，初始化播放器和更新选中状态
  watch(
    () => props.videoUrl,
    async (newUrl) => {
      if (newUrl && visible.value && playerContainer.value) {
        await nextTick();
        // 添加延迟确保DOM完全准备好
        setTimeout(() => {
          console.log("🎬 视频URL变化，重新初始化播放器:", newUrl);
          initPlayer(newUrl);
        }, 100);
      } else {
        destroyPlayer();
      }
      
      // 更新目录选中状态
      updateSelectedKeys(newUrl);
    }
  );

  // 监听目录树变化，更新选中状态
  watch(
    () => processedCatalogTree.value,
    () => {
      updateSelectedKeys(props.videoUrl);
    },
    { deep: true }
  );

  // 更新选中的keys
  const updateSelectedKeys = (url) => {
    if (url && processedCatalogTree.value) {
      const key = findKeyByUrl(processedCatalogTree.value, url);
      selectedKeys.value = key ? [key] : [];
    } else {
      selectedKeys.value = [];
    }
  };

  // 监听模态框显示状态
  watch(visible, async (newValue) => {
    if (newValue) {
      // 模态框打开时，等待DOM渲染完成
      await nextTick();
      // 再次等待确保模态框完全渲染
      setTimeout(() => {
        if (props.videoUrl && playerContainer.value) {
          console.log("🎬 模态框打开，准备初始化播放器");
          initPlayer(props.videoUrl);
        }
      }, 100);
    } else {
      destroyPlayer();
    }
  });

  // 播放器初始化
  const initPlayer = (url) => {
    if (!playerContainer.value || !url) {
      console.warn("🚨 播放器容器或URL为空:", {
        container: playerContainer.value,
        url,
      });
      return;
    }

    // 销毁现有播放器
    destroyPlayer();

    try {
      console.log("🎬 开始初始化视频预览播放器:", {
        container: playerContainer.value,
        url,
      });

      // 确保容器有唯一ID
      if (!playerContainer.value.id) {
        playerContainer.value.id = `xgplayer-${Date.now()}`;
      }

      // 创建新的播放器实例
      playerInstance = new Player({
        el: playerContainer.value, // 使用el而不是id
        url: url,
        width: "100%",
        height: 350, // 固定高度为350px
        autoplay: false,
        poster: "", // 可以设置封面图
        playsinline: true,
        fluid: false, // 关闭自适应，使用固定尺寸
        videoInit: true,
        // 播放器配置
        controls: {
          mode: "normal",
        },
        // 样式配置
        cssFullscreen: true,
        rotateFullscreen: false,
        // 事件监听
        onReady: () => {
          console.log("🎬 视频预览播放器初始化完成");
        },
        onError: (error) => {
          console.error("🚨 视频预览播放器错误:", error);
        },
      });

      console.log("🎬 视频预览播放器创建成功:", playerInstance);
    } catch (error) {
      console.error("🚨 视频预览播放器初始化失败:", error);
    }
  };

  // 播放器销毁
  const destroyPlayer = () => {
    if (playerInstance) {
      try {
        playerInstance.destroy();
        playerInstance = null;
      } catch (error) {
        console.error("🚨 销毁视频预览播放器失败:", error);
      }
    }
  };

  // 模态框关闭处理
  const handleModalClose = () => {
    destroyPlayer();
  };

  // 格式化时长
  const formatDuration = (duration) => {
    if (!duration) return "0分钟";
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    if (minutes > 0) {
      return seconds > 0 ? `${minutes}分${seconds}秒` : `${minutes}分钟`;
    }
    return `${seconds}秒`;
  };

  // 切换目录显示
  const toggleCatalog = () => {
    showCatalog.value = !showCatalog.value;
  };

  // 获取树节点属性
  const getNodeProps = ({ option }) => {
    return {
      onClick() {
        if (option.videoUrl) {
          handleVideoSelect(option);
        }
      },
    };
  };

  // 处理节点选择
  const handleNodeSelect = (selectedKeys, selectedNodes) => {
    if (selectedNodes.length > 0) {
      const node = selectedNodes[0];
      if (node.videoUrl) {
        handleVideoSelect(node);
      }
    }
  };

  // 处理视频选择
  const handleVideoSelect = (videoItem) => {
    console.log("🎬 选择视频:", videoItem);
    emit("video-select", videoItem);
  };

  // 组件卸载时清理
  onUnmounted(() => {
    destroyPlayer();
  });
</script>

<style lang="scss" scoped>
  .video-preview-modal {
    max-width: 1000px !important;
    width: 95% !important;
    max-height: 85vh !important;
  }

  .modal-content {
    position: relative;
    padding: 20px;
    min-height: 500px;

    // 背景装饰
    .background-decoration {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      opacity: 0.02;
      border-radius: 12px;
      pointer-events: none;
      z-index: 0;

      &::before {
        content: "";
        position: absolute;
        top: -30%;
        left: -30%;
        width: 160%;
        height: 160%;
        background: radial-gradient(
            circle at 30% 30%,
            rgba(102, 126, 234, 0.05) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 70% 70%,
            rgba(118, 75, 162, 0.05) 0%,
            transparent 50%
          );
        animation: backgroundFloat 40s ease-in-out infinite;
      }
    }

    .modal-header {
      position: relative;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .content-wrapper {
      position: relative;
      z-index: 1;
      display: flex;
      gap: 20px;
      height: calc(100% - 80px);
    }

    .video-section {
      flex: 1;
      transition: all 0.3s ease;

      &.with-catalog {
        flex: 0 0 65%;
      }
    }

    .catalog-section {
      flex: 0 0 300px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;
      border: 1px solid #e8e8e8;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .catalog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .catalog-content {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;

        .empty-catalog {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #999;

          .empty-text {
            margin-top: 12px;
            font-size: 14px;
          }
        }
      }
    }
  }

  .video-preview-container {
    .xgplayer-container {
      width: 100%;
      height: 350px !important;
      border-radius: 12px;
      overflow: hidden;
      background-color: #000;
      margin-bottom: 16px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .no-video-hint {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 350px;
      color: #999;
      border: 2px dashed #d9d9d9;
      border-radius: 12px;
      margin-bottom: 16px;
      background: rgba(249, 249, 249, 0.5);
      backdrop-filter: blur(5px);

      .hint-text {
        margin-top: 12px;
        font-size: 14px;
      }
    }

    .video-info {
      margin-top: 16px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      padding: 16px;
      backdrop-filter: blur(10px);
      border: 1px solid #e8e8e8;
    }
  }

  // 背景动画
  @keyframes backgroundFloat {
    0%,
    100% {
      transform: translate(0, 0) rotate(0deg);
    }
    33% {
      transform: translate(30px, -30px) rotate(120deg);
    }
    66% {
      transform: translate(-20px, 20px) rotate(240deg);
    }
  }

  // 目录树样式
  :deep(.n-tree) {
    .n-tree-node {
      margin: 4px 0;

      .n-tree-node-content {
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        &.n-tree-node-content--selected {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }
      }

      .n-tree-node-content__text {
        font-size: 14px;
      }
    }
  }
</style>
