<template>
  <div class="profile-card">
    <!-- 顶部区域 - 头像和基本信息 -->
    <div class="profile-header">
      <div class="avatar-container">
        <n-avatar
          :size="80"
          :src="getAvatarUrl()"
          round
          fallback-src="https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg"
        />
      </div>
      <div class="basic-info">
        <h2 class="name">{{ getDisplayName() }}</h2>
        <div class="tags">
          <span class="id-tag">ID: {{ data.id }}</span>
          <n-tag
            v-if="getTagInfo().text"
            :type="getTagInfo().type"
            :style="getTagInfo().style"
          >
            {{ getTagInfo().text }}
          </n-tag>
        </div>
      </div>
    </div>

    <!-- 信息内容区域 -->
    <div class="profile-content">
      <div class="info-grid">
        <div
          v-for="(field, index) in displayFields"
          :key="index"
          class="info-item"
        >
          <span class="info-label">{{ field.label }}:</span>
          <template v-if="field.type === 'tag'">
            <n-tag
              size="small"
              :type="field.tagType || 'default'"
              :style="field.tagStyle || {}"
            >
              {{ getFieldValue(field) }}
            </n-tag>
          </template>
          <template v-else-if="field.type === 'date'">
            <span class="info-value date">
              {{ formatDate(getFieldValue(field)) }}
            </span>
          </template>
          <template v-else>
            <span class="info-value">{{ getFieldValue(field) }}</span>
          </template>
        </div>
      </div>

      <!-- 详细介绍区域 -->
      <div v-if="description.field" class="description-section">
        <div class="description-label">{{ description.label }}:</div>
        <div class="description-content">
          {{ data[description.field] || description.default || "暂无内容" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import dayjs from "dayjs";

const props = defineProps({
  // 要展示的数据对象
  data: {
    type: Object,
    required: true,
  },
  // 配置展示的字段及其标签
  displayFields: {
    type: Array,
    default: () => [],
  },
  // 主标签配置
  tagConfig: {
    type: Object,
    default: () => ({
      field: "",
      options: [],
    }),
  },
  // 长文本描述配置
  description: {
    type: Object,
    default: () => ({
      field: "",
      label: "介绍",
      default: "暂无介绍",
    }),
  },
  // 头像字段
  avatarField: {
    type: String,
    default: "avatar",
  },
  // 名称显示配置
  nameConfig: {
    type: Object,
    default: () => ({
      mainField: "nickname",
      fallbackField: "username",
    }),
  },
});

// 获取显示名称
const getDisplayName = () => {
  if (props.data[props.nameConfig.mainField]) {
    return props.data[props.nameConfig.mainField];
  }
  return props.data[props.nameConfig.fallbackField] || "未命名";
};

// 获取头像URL
const getAvatarUrl = () => {
  const avatar = props.data[props.avatarField];
  return avatar ? avatar.trim() : "";
};

// 获取标签信息
const getTagInfo = () => {
  if (!props.tagConfig.field || !props.data[props.tagConfig.field]) {
    return { text: "", type: "default", style: "" };
  }

  const value = props.data[props.tagConfig.field];
  const option = props.tagConfig.options.find((opt) => opt.value === value);

  if (option) {
    return {
      text: option.label,
      type: option.type || "default",
      style: option.style || "",
    };
  }

  return { text: "未知", type: "default", style: "" };
};

// 获取字段值
const getFieldValue = (field) => {
  if (field.valueFormatter) {
    return field.valueFormatter(props.data[field.key], props.data);
  }

  if (field.options && props.data[field.key] !== undefined) {
    const option = field.options.find(
      (opt) => opt.value === props.data[field.key]
    );
    return option
      ? option.label
      : props.data[field.key] || field.default || "-";
  }

  return props.data[field.key] || field.default || "-";
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return "-";
  return dayjs(date).format("YYYY-MM-DD HH:mm");
};
</script>

<style lang="scss" scoped>
.profile-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  font-family: "Helvetica Neue", Arial, sans-serif;

  .profile-header {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
    border-bottom: 1px solid #eaecef;

    .avatar-container {
      margin-right: 20px;
      .n-avatar {
        border: 3px solid white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .basic-info {
      flex: 1;

      .name {
        margin: 0 0 8px;
        color: #303133;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .tags {
        display: flex;
        align-items: center;
        gap: 10px;

        .id-tag {
          color: #606266;
          font-size: 0.85rem;
          background: rgba(0, 0, 0, 0.04);
          padding: 2px 8px;
          border-radius: 10px;
        }
      }
    }
  }

  .profile-content {
    padding: 20px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .info-item {
        display: flex;
        align-items: center;

        .info-label {
          font-weight: 500;
          width: 80px;
          color: #606266;
          flex-shrink: 0;
        }

        .info-value {
          color: #303133;
          &.date {
            font-size: 0.85rem;
          }
        }
      }
    }

    .description-section {
      margin-top: 20px;

      .description-label {
        font-weight: 500;
        margin-bottom: 8px;
        color: #606266;
      }

      .description-content {
        background: #f9f9f9;
        padding: 16px;
        border-radius: 8px;
        min-height: 60px;
        color: #303133;
        line-height: 1.6;
        font-size: 0.95rem;
      }
    }
  }
}
</style>
