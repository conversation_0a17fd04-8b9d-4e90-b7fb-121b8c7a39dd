<template>
  <div class="video-upload-container">
    <!-- 视频上传区域 -->
    <div
      class="upload-area"
      :class="{
        'upload-area--dragover': isDragOver,
        'upload-area--disabled': disabled || uploading,
        'upload-area--has-video': videoUrl,
      }"
      @click="handleClick"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
    >
      <!-- 视频预览 -->
      <div v-if="videoUrl" class="video-preview">
        <div class="video-container">
          <video
            ref="videoRef"
            :src="videoUrl"
            controls
            preload="metadata"
            @loadedmetadata="handleVideoLoaded"
            @error="handleVideoError"
          >
            您的浏览器不支持视频播放
          </video>
          
          <!-- 视频信息 -->
          <div v-if="videoInfo" class="video-info">
            <div class="info-item">
              <span class="label">文件名：</span>
              <span class="value">{{ videoInfo.fileName }}</span>
            </div>
            <div class="info-item">
              <span class="label">大小：</span>
              <span class="value">{{ formatFileSize(videoInfo.fileSize) }}</span>
            </div>
            <div v-if="videoDuration" class="info-item">
              <span class="label">时长：</span>
              <span class="value">{{ formatDuration(videoDuration) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="video-actions">
          <n-button size="small" @click.stop="handleReplace">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            重新上传
          </n-button>
          <n-button size="small" type="error" @click.stop="handleRemove">
            <template #icon>
              <n-icon><TrashOutline /></n-icon>
            </template>
            删除
          </n-button>
        </div>
      </div>

      <!-- 上传提示 -->
      <div v-else class="upload-hint">
        <div class="upload-icon">
          <n-icon size="64" :color="disabled ? '#ccc' : '#18a058'">
            <VideocamOutline />
          </n-icon>
        </div>
        <div class="upload-text">
          <div class="primary-text">
            {{ disabled ? "视频上传已禁用" : "点击或拖拽视频文件到此处上传" }}
          </div>
          <div class="secondary-text">
            {{ acceptText }}
          </div>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress-container">
          <n-progress
            type="line"
            :percentage="uploadProgress"
            :show-indicator="true"
            indicator-placement="inside"
          />
          <div class="progress-text">
            正在上传视频... {{ uploadProgress }}%
          </div>
          <div v-if="uploadSpeed" class="upload-speed">
            上传速度: {{ uploadSpeed }}
          </div>
        </div>
      </div>
    </div>

    <!-- 文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="video/*"
      style="display: none"
      @change="handleFileChange"
    />

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <n-alert type="error" :show-icon="true" closable @close="clearError">
        {{ errorMessage }}
      </n-alert>
    </div>

    <!-- URL输入框 -->
    <!-- <div v-if="showUrlInput" class="url-input-container">
      <n-input
        v-model:value="urlInput"
        placeholder="或直接输入视频URL"
        clearable
        @blur="handleUrlInput"
        @keyup.enter="handleUrlInput"
      >
        <template #prefix>
          <n-icon><LinkOutline /></n-icon>
        </template>
      </n-input>
    </div> -->
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { useMessage } from "naive-ui";
import {
  VideocamOutline,
  RefreshOutline,
  TrashOutline,
  LinkOutline,
} from "@vicons/ionicons5";
import { uploadFile, getFileUploadInfo } from "@/api/file";

// Props
const props = defineProps({
  // 初始视频URL
  modelValue: {
    type: String,
    default: "",
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否显示URL输入框
  showUrlInput: {
    type: Boolean,
    default: true,
  },
  // 最大文件大小（MB）
  maxSize: {
    type: Number,
    default: null,
  },
});
const {modelValue,disabled,showUrlInput,maxSize} = props

// Emits
const emit = defineEmits([
  "update:modelValue",
  "upload-success",
  "upload-error",
  "upload-progress",
  "video-loaded",
  "video-remove",
]);

// Refs
const fileInputRef = ref(null);
const videoRef = ref(null);
const message = useMessage();

// 响应式数据
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadSpeed = ref("");
const isDragOver = ref(false);
const errorMessage = ref("");
const videoUrl = ref("");
const urlInput = ref("");
const videoInfo = ref(null);
const videoDuration = ref(0);
const uploadConfig = ref(null);
const uploadStartTime = ref(0);
const uploadedBytes = ref(0);

// 计算属性
const acceptText = computed(() => {
  if (!uploadConfig.value) return "";
  
  const config = uploadConfig.value;
  return `支持格式：${config.supportedVideoTypes}，最大${config.maxVideoSize}`;
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    videoUrl.value = newValue || "";
    urlInput.value = newValue || "";
  },
  { immediate: true }
);

// 方法
const loadUploadConfig = async () => {
  try {
    const res = await getFileUploadInfo();
    if (res.code === 200) {
      uploadConfig.value = res.data;
    }
  } catch (error) {
    console.error("加载上传配置失败:", error);
  }
};

const handleClick = () => {
  if (disabled.value || uploading.value) return;
  if (!videoUrl.value) {
    fileInputRef.value?.click();
  }
};

const handleReplace = () => {
  fileInputRef.value?.click();
};

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    uploadVideo(file);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  if (disabled.value || uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files);
  const videoFile = files.find(file => file.type.startsWith('video/'));
  
  if (videoFile) {
    uploadVideo(videoFile);
  } else {
    message.warning("请拖拽视频文件");
  }
};

const handleDragOver = (event) => {
  event.preventDefault();
  if (!disabled.value && !uploading.value) {
    isDragOver.value = true;
  }
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const uploadVideo = async (file) => {
  try {
    // 验证文件
    const validation = validateFile(file);
    if (!validation.valid) {
      errorMessage.value = validation.message;
      return;
    }
    
    clearError();
    uploading.value = true;
    uploadProgress.value = 0;
    uploadStartTime.value = Date.now();
    uploadedBytes.value = 0;
    
    // 设置临时预览
    const tempUrl = URL.createObjectURL(file);
    videoUrl.value = tempUrl;
    videoInfo.value = {
      fileName: file.name,
      fileSize: file.size,
    };
    
    // 上传文件
    const res = await uploadFile(file, "video", (progress) => {
      uploadProgress.value = progress;
      
      // 计算上传速度
      const now = Date.now();
      const timeElapsed = (now - uploadStartTime.value) / 1000; // 秒
      const bytesUploaded = (file.size * progress) / 100;
      const speed = bytesUploaded / timeElapsed; // bytes/second
      
      if (speed > 0) {
        uploadSpeed.value = formatSpeed(speed);
      }
      
      emit("upload-progress", progress);
    });
    
    if (res.code === 200) {
      // 释放临时URL
      URL.revokeObjectURL(tempUrl);
      
      // 设置最终URL
      videoUrl.value = res.data.fileUrl;
      urlInput.value = res.data.fileUrl;
      videoInfo.value = {
        fileName: res.data.originalFileName,
        fileSize: res.data.fileSize,
      };
      
      emit("update:modelValue", res.data.fileUrl);
      emit("upload-success", res.data);
      message.success("视频上传成功");
    }
  } catch (error) {
    console.error("上传失败:", error);
    errorMessage.value = error.message || "视频上传失败";
    emit("upload-error", error);
    message.error("视频上传失败");
    
    // 清除临时预览
    if (videoUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(videoUrl.value);
      videoUrl.value = "";
      videoInfo.value = null;
    }
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
    uploadSpeed.value = "";
  }
};

const validateFile = (file) => {
  if (!uploadConfig.value) {
    return { valid: false, message: "上传配置未加载" };
  }

  const config = uploadConfig.value;
  const maxSize = parseSize(config.maxVideoSize);
  const supportedTypes = config.supportedVideoTypes.split(",");

  // 检查文件大小
  if (file.size > maxSize) {
    return {
      valid: false,
      message: `视频文件大小超过限制，最大允许 ${formatFileSize(maxSize)}`,
    };
  }

  // 检查文件类型
  const fileExtension = file.name.split(".").pop().toLowerCase();
  if (!supportedTypes.includes(fileExtension)) {
    return {
      valid: false,
      message: `不支持的视频格式，支持格式：${supportedTypes.join(", ")}`,
    };
  }

  return { valid: true };
};

const handleUrlInput = () => {
  if (urlInput.value && urlInput.value !== videoUrl.value) {
    videoUrl.value = urlInput.value;
    videoInfo.value = {
      fileName: "外部视频",
      fileSize: 0,
    };
    emit("update:modelValue", urlInput.value);
  }
};

const handleRemove = () => {
  if (videoUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(videoUrl.value);
  }

  videoUrl.value = "";
  urlInput.value = "";
  videoInfo.value = null;
  videoDuration.value = 0;

  emit("update:modelValue", "");
  emit("video-remove");
  message.info("视频已删除");
};

const handleVideoLoaded = () => {
  if (videoRef.value) {
    videoDuration.value = videoRef.value.duration;
    emit("video-loaded", {
      duration: videoDuration.value,
      videoElement: videoRef.value,
    });
  }
};

const handleVideoError = (error) => {
  console.error("视频加载错误:", error);
  errorMessage.value = "视频加载失败，请检查视频文件或URL";
};

const clearError = () => {
  errorMessage.value = "";
};

const parseSize = (sizeStr) => {
  const match = sizeStr.match(/^(\d+)(MB|KB|GB)$/i);
  if (!match) return 0;

  const value = parseInt(match[1]);
  const unit = match[2].toUpperCase();

  switch (unit) {
    case "KB":
      return value * 1024;
    case "MB":
      return value * 1024 * 1024;
    case "GB":
      return value * 1024 * 1024 * 1024;
    default:
      return value;
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatSpeed = (bytesPerSecond) => {
  const k = 1024;
  const sizes = ["B/s", "KB/s", "MB/s", "GB/s"];
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));

  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
};

// 初始化
onMounted(() => {
  loadUploadConfig();
});
</script>

<style lang="scss" scoped>
.video-upload-container {
  width: 100%;
}

.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 300px;
  overflow: hidden;

  &:hover {
    border-color: #18a058;
    background-color: #f0f9ff;
  }

  &--dragover {
    border-color: #18a058;
    background-color: #f0f9ff;
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(24, 160, 88, 0.15);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      border-color: #d9d9d9;
      background-color: #fafafa;
      transform: none;
    }
  }

  &--has-video {
    border-color: #18a058;
    background-color: #fff;
    cursor: default;

    &:hover {
      background-color: #fff;
    }
  }
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;

  video {
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
    background-color: #000;
  }
}

.video-info {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;

  .info-item {
    display: flex;
    margin-bottom: 4px;
    font-size: 13px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #666;
      min-width: 60px;
    }

    .value {
      color: #333;
      font-weight: 500;
    }
  }
}

.video-actions {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  height: 100%;

  .upload-icon {
    margin-bottom: 20px;
  }

  .upload-text {
    text-align: center;

    .primary-text {
      font-size: 18px;
      color: #333;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .secondary-text {
      font-size: 14px;
      color: #999;
      line-height: 1.5;
    }
  }
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);

  .progress-container {
    padding: 20px;

    .progress-text {
      text-align: center;
      font-size: 14px;
      color: #333;
      margin-top: 12px;
      font-weight: 500;
    }

    .upload-speed {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }
}

.error-message {
  margin-top: 16px;
}

.url-input-container {
  margin-top: 16px;
}
</style>
