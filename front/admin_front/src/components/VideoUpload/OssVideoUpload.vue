<template>
  <div class="oss-video-upload-container">
    <!-- 视频上传区域 -->
    <div
      class="upload-area"
      :class="{
        'upload-area--dragover': isDragOver,
        'upload-area--disabled': disabled || uploading,
        'upload-area--has-video': videoUrl,
      }"
      @click="handleClick"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
    >
      <!-- 视频预览 -->
      <div v-if="videoUrl" class="video-preview">
        <div class="video-container">
          <!-- 使用原生HTML5 video标签进行预览 -->
          <video
            ref="videoRef"
            :src="videoUrl"
            controls
            :preload="isLocalPreview ? 'metadata' : 'none'"
            class="video-player"
            @loadedmetadata="handleVideoLoaded"
            @error="handleVideoError"
          >
            您的浏览器不支持视频播放
          </video>
          
          <!-- 线上视频流量提示 -->
          <div v-if="!isLocalPreview && videoUrl" class="online-video-tip">
            <n-icon size="16"><CloudDownloadOutline /></n-icon>
            <span>线上视频，点击播放时才加载以节省流量</span>
          </div>
          
          <!-- 视频信息 -->
          <div v-if="videoInfo" class="video-info">
            <div class="info-item">
              <span class="label">文件名：</span>
              <span class="value">{{ videoInfo.fileName }}</span>
            </div>
            <div class="info-item">
              <span class="label">大小：</span>
              <span class="value">{{ formatFileSize(videoInfo.fileSize) }}</span>
            </div>
            <div v-if="videoDuration" class="info-item">
              <span class="label">时长：</span>
              <span class="value">{{ formatDuration(videoDuration) }}</span>
            </div>
            <div class="info-item">
              <span class="label">上传方式：</span>
              <span class="value tech-badge">
                <n-icon size="14"><FlashOutline /></n-icon>
                OSS直传
              </span>
            </div>
            <div v-if="manualUpload" class="info-item">
              <span class="label">上传状态：</span>
              <span class="value" :class="{ 'pending-upload': isFileReady, 'uploaded': !isFileReady && !isLocalPreview }">
                {{ isFileReady ? '待上传' : (isLocalPreview ? '本地文件' : '已上传') }}
              </span>
            </div>
            <div class="info-item">
              <span class="label">预览模式：</span>
              <span class="value tech-badge" :class="{ 'local-preview': isLocalPreview, 'online-preview': !isLocalPreview }">
                <n-icon size="14">
                  <EyeOutline v-if="isLocalPreview" />
                  <CloudDownloadOutline v-else />
                </n-icon>
                {{ isLocalPreview ? '本地预览' : '线上视频（按需加载）' }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="video-actions">
          <!-- 手动上传模式的按钮 -->
          <template v-if="manualUpload">
            <n-button 
              v-if="isFileReady && !uploading && !hideUploadButton" 
              size="small" 
              type="primary" 
              @click.stop="manualUploadFile"
            >
              <template #icon>
                <n-icon><CloudUploadOutline /></n-icon>
              </template>
              上传到云端
            </n-button>
            <n-button size="small" @click.stop="handleReplace">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重新选择
            </n-button>
          </template>
          <!-- 自动上传模式的按钮 -->
          <template v-else>
            <n-button size="small" @click.stop="handleReplace">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重新上传
            </n-button>
          </template>
          <n-button size="small" type="error" @click.stop="handleRemove">
            <template #icon>
              <n-icon><TrashOutline /></n-icon>
            </template>
            删除
          </n-button>
        </div>
      </div>

      <!-- 上传提示 -->
      <div v-else class="upload-hint">
        <div class="upload-icon">
          <n-icon size="64" :color="disabled ? '#ccc' : '#18a058'">
            <VideocamOutline />
          </n-icon>
        </div>
        <div class="upload-text">
          <div class="primary-text">
            {{ disabled ? "视频上传已禁用" : (manualUpload ? "点击或拖拽视频文件到此处" : "点击或拖拽视频文件到此处上传") }}
          </div>
          <div class="secondary-text">
            {{ acceptText }}
          </div>
          <div v-if="manualUpload" class="manual-tip">
            手动上传模式：选择文件后需要手动点击上传
          </div>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <div class="progress-container">
          <n-progress
            type="line"
            :percentage="uploadProgress"
            :show-indicator="true"
            indicator-placement="inside"
            :color="progressColor"
          />
          <div class="progress-text">
            {{ uploadStatus }} {{ uploadProgress }}%
          </div>
          <div v-if="uploadSpeed" class="upload-speed">
            上传速度: {{ uploadSpeed }}
          </div>
          <div class="tech-info">
            <n-icon size="12"><FlashOutline /></n-icon>
            直传阿里云OSS
          </div>
        </div>
      </div>
    </div>

    <!-- 文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="video/*"
      style="display: none"
      @change="handleFileChange"
    />

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <n-alert type="error" :show-icon="true" closable @close="clearError">
        {{ errorMessage }}
      </n-alert>
    </div>

    <!-- URL输入框 -->
    <!-- <div v-if="showUrlInput" class="url-input-container">
      <n-input
        v-model:value="urlInput"
        placeholder="或直接输入视频URL"
        clearable
        @blur="handleUrlInput"
        @keyup.enter="handleUrlInput"
      >
        <template #prefix>
          <n-icon><LinkOutline /></n-icon>
        </template>
      </n-input>
    </div> -->
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from "vue";
import { useMessage } from "naive-ui";
import {
  VideocamOutline,
  RefreshOutline,
  TrashOutline,
  LinkOutline,
  FlashOutline,
  EyeOutline,
  CloudDownloadOutline,
  CloudUploadOutline,
} from "@vicons/ionicons5";
import { uploadFile, validateFileType, validateFileSize, formatFileSize } from "@/api/oss";

// Props
const props = defineProps({
  // 初始视频URL
  modelValue: {
    type: String,
    default: "",
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否显示URL输入框
  showUrlInput: {
    type: Boolean,
    default: true,
  },
  // 是否为手动上传模式（受控上传）
  manualUpload: {
    type: Boolean,
    default: false,
  },
  // 是否隐藏手动上传按钮（通过ref调用上传）
  hideUploadButton: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits([
  "update:modelValue",
  "upload-success",
  "upload-error",
  "upload-progress",
  "video-loaded",
  "video-remove",
  "duration-change",
]);

// Refs
const fileInputRef = ref(null);
const videoRef = ref(null);
const message = useMessage();

// 响应式数据
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref("准备上传...");
const uploadSpeed = ref("");
const isDragOver = ref(false);
const errorMessage = ref("");
const videoUrl = ref("");
const urlInput = ref("");
const videoInfo = ref(null);
const videoDuration = ref(0);
const isLocalPreview = ref(false); // 标记是否为本地预览
const uploadStartTime = ref(0);
const pendingFile = ref(null); // 待上传的文件
const isFileReady = ref(false); // 文件是否准备就绪

// 计算属性
const acceptText = computed(() => {
  const types = ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"];
  const maxSize = formatFileSize(500 * 1024 * 1024); // 500MB
  return `支持格式：${types.join(", ")}，最大${maxSize}`;
});

const progressColor = computed(() => {
  if (uploadProgress.value < 30) return "#ff6b6b";
  if (uploadProgress.value < 70) return "#ffa726";
  return "#18a058";
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    // 只有在非本地预览状态下才更新videoUrl
    // 这样可以避免外部传入的OSS URL覆盖本地预览
    if (!isLocalPreview.value) {
      videoUrl.value = newValue || "";
    }
    urlInput.value = newValue || "";
  },
  { immediate: true }
);

// 组件生命周期
onMounted(() => {
  // 如果已有视频URL，设置为非本地预览
  if (videoUrl.value && !isLocalPreview.value) {
    videoInfo.value = {
      fileName: "在线视频",
      fileSize: 0,
    };
  }
});

onUnmounted(() => {
  // 清理本地预览资源
  if (isLocalPreview.value && videoUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(videoUrl.value);
  }
});

// 方法
const handleClick = () => {
  if (props.disabled || uploading.value) return;
  if (!videoUrl.value) {
    fileInputRef.value?.click();
  }
};

const handleReplace = () => {
  fileInputRef.value?.click();
};

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    if (props.manualUpload) {
      prepareFile(file);
    } else {
      uploadVideo(file);
    }
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  if (props.disabled || uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files);
  const videoFile = files.find(file => file.type.startsWith('video/'));
  
  if (videoFile) {
    if (props.manualUpload) {
      prepareFile(videoFile);
    } else {
      uploadVideo(videoFile);
    }
  } else {
    message.warning("请拖拽视频文件");
  }
};

const handleDragOver = (event) => {
  event.preventDefault();
  if (!props.disabled && !uploading.value) {
    isDragOver.value = true;
  }
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

// 准备文件（手动上传模式）
const prepareFile = (file) => {
  // 验证文件
  const validation = validateFile(file);
  if (!validation.valid) {
    errorMessage.value = validation.message;
    return;
  }

  clearError();
  
  // 保存待上传的文件
  pendingFile.value = file;
  isFileReady.value = true;

  // 设置本地预览
  const tempUrl = URL.createObjectURL(file);
  videoUrl.value = tempUrl;
  isLocalPreview.value = true;
  videoInfo.value = {
    fileName: file.name,
    fileSize: file.size,
  };

  message.info("文件已准备就绪，可以手动上传");
};

// 手动触发上传
const manualUploadFile = async () => {
  if (!pendingFile.value) {
    const errorMsg = "没有待上传的文件";
    message.warning(errorMsg);
    throw new Error(errorMsg);
  }

  try {
    await uploadVideo(pendingFile.value);
    // 上传成功后清除待上传状态
    pendingFile.value = null;
    isFileReady.value = false;
    
    // 返回上传结果
    return {
      success: true,
      message: "上传成功",
      fileName: videoInfo.value?.fileName,
      duration: videoDuration.value,
    };
  } catch (error) {
    console.error("手动上传失败:", error);
    throw error;
  }
};

const uploadVideo = async (file) => {
  try {
    // 如果不是手动上传模式，需要验证文件和设置预览
    if (!props.manualUpload) {
      // 验证文件
      const validation = validateFile(file);
      if (!validation.valid) {
        errorMessage.value = validation.message;
        return;
      }

      clearError();

      // 设置本地预览 - 优先预览本地视频
      const tempUrl = URL.createObjectURL(file);
      videoUrl.value = tempUrl;
      isLocalPreview.value = true; // 标记为本地预览
      videoInfo.value = {
        fileName: file.name,
        fileSize: file.size,
      };
    }

    uploading.value = true;
    uploadProgress.value = 0;
    uploadStatus.value = "准备上传...";
    uploadStartTime.value = Date.now();

    uploadStatus.value = "正在上传到OSS...";

    // 使用PostObject方式上传到OSS
    const result = await uploadFile(file, "video", (progress) => {
      uploadProgress.value = progress;

      // 计算上传速度
      const now = Date.now();
      const timeElapsed = (now - uploadStartTime.value) / 1000; // 秒
      const bytesUploaded = (file.size * progress) / 100;
      const speed = bytesUploaded / timeElapsed; // bytes/second

      if (speed > 0) {
        uploadSpeed.value = formatSpeed(speed);
      }

      emit("upload-progress", progress);
    });

    uploadStatus.value = "上传完成";

    // 保持本地预览，不释放临时URL，不替换为OSS URL
    // videoUrl.value 保持为本地blob URL，用于预览
    // URL.revokeObjectURL(tempUrl); // 注释掉，保持本地预览

    // 更新文件信息，但保持本地视频预览
    urlInput.value = result.fileUrl; // URL输入框显示OSS地址，供用户查看
    videoInfo.value = {
      fileName: result.originalFileName,
      fileSize: result.fileSize,
    };

    // 发送OSS相对路径给父组件，但视频预览仍使用本地文件
    emit("update:modelValue", result.objectKey);

    // 发送上传成功事件，包含文件信息
    const uploadResult = {
      ...result,
      duration: videoDuration.value,
      durationFormatted: formatDuration(videoDuration.value),
      isLocalPreview: isLocalPreview.value,
    };

    emit("upload-success", uploadResult);
    message.success("视频上传成功，使用本地预览节省带宽");

  } catch (error) {
    console.error("上传失败:", error);
    errorMessage.value = error.message || "视频上传失败";
    emit("upload-error", error);
    message.error("视频上传失败");

    // 清除本地预览
    if (isLocalPreview.value && videoUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(videoUrl.value);
      videoUrl.value = "";
      videoInfo.value = null;
      isLocalPreview.value = false;
    }
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
    uploadSpeed.value = "";
    uploadStatus.value = "";
  }
};

const validateFile = (file) => {
  const allowedTypes = [
    "video/mp4", "video/avi", "video/mov", "video/wmv",
    "video/flv", "video/mkv", "video/webm", "video/quicktime",
    // 添加扩展名支持以增强兼容性
    "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"
  ];
  const maxFileSize = 500 * 1024 * 1024; // 500MB

  // 检查文件类型
  if (!validateFileType(file, allowedTypes)) {
    // 提取扩展名用于错误提示
    const typeNames = ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"];
    return {
      valid: false,
      message: `不支持的视频格式，支持格式：${typeNames.join(", ")}`,
    };
  }

  // 检查文件大小
  if (!validateFileSize(file, maxFileSize)) {
    return {
      valid: false,
      message: `视频文件大小超过限制，最大允许 ${formatFileSize(maxFileSize)}`,
    };
  }

  return { valid: true };
};

const handleUrlInput = async () => {
  if (urlInput.value && urlInput.value !== videoUrl.value) {
    // 如果当前是本地预览，先清理本地资源
    if (isLocalPreview.value && videoUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(videoUrl.value);
    }

    // 设置为外部URL
    videoUrl.value = urlInput.value;
    isLocalPreview.value = false; // 标记为非本地预览
    videoInfo.value = {
      fileName: "外部视频",
      fileSize: 0,
    };

    emit("update:modelValue", urlInput.value);
  }
};

const handleRemove = () => {
  // 清理本地预览资源
  if (isLocalPreview.value && videoUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // 清理手动上传模式的状态
  if (props.manualUpload) {
    pendingFile.value = null;
    isFileReady.value = false;
  }

  videoUrl.value = "";
  urlInput.value = "";
  videoInfo.value = null;
  videoDuration.value = 0;
  isLocalPreview.value = false;

  emit("update:modelValue", "");
  emit("video-remove");
  message.info("视频已删除");
};

const handleVideoLoaded = () => {
  if (videoRef.value) {
    videoDuration.value = videoRef.value.duration;

    // 发送视频加载完成事件，包含时长信息
    const videoData = {
      duration: videoDuration.value,
      durationFormatted: formatDuration(videoDuration.value),
      videoElement: videoRef.value,
      fileName: videoInfo.value?.fileName || '',
      fileSize: videoInfo.value?.fileSize || 0,
      isLocalPreview: isLocalPreview.value,
    };

    emit("video-loaded", videoData);

    // 同时发送时长变化事件
    emit("duration-change", videoDuration.value);
  }
};

const handleVideoError = (error) => {
  console.error("视频加载错误:", error);
  errorMessage.value = "视频加载失败，请检查视频文件或URL";
};

const clearError = () => {
  errorMessage.value = "";
};

const formatSpeed = (bytesPerSecond) => {
  const k = 1024;
  const sizes = ["B/s", "KB/s", "MB/s", "GB/s"];
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));

  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
};

// 暴露方法给父组件
defineExpose({
  // 手动触发上传
  upload: manualUploadFile,
  // 获取当前状态
  getStatus: () => ({
    isFileReady: isFileReady.value,
    isUploading: uploading.value,
    isLocalPreview: isLocalPreview.value,
    fileName: videoInfo.value?.fileName,
    fileSize: videoInfo.value?.fileSize,
    duration: videoDuration.value,
  }),
  // 检查是否有待上传文件
  hasFileReady: () => isFileReady.value,
  // 清除文件
  clear: handleRemove,
  // 重新选择文件
  reselect: handleReplace,
});
</script>

<style lang="scss" scoped>
.oss-video-upload-container {
  width: 100%;
}

.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 300px;
  overflow: hidden;

  &:hover {
    border-color: #18a058;
    background-color: #f0f9ff;
  }

  &--dragover {
    border-color: #18a058;
    background-color: #f0f9ff;
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(24, 160, 88, 0.15);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      border-color: #d9d9d9;
      background-color: #fafafa;
      transform: none;
    }
  }

  &--has-video {
    border-color: #18a058;
    background-color: #fff;
    cursor: default;

    &:hover {
      background-color: #fff;
    }
  }
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;

  .video-player {
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
    background-color: #000;
    object-fit: contain;
  }

  .online-video-tip {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #e8f4fd;
    border: 1px solid #91caff;
    border-radius: 4px;
    color: #1677ff;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.video-info {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 13px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #666;
      min-width: 60px;
    }

    .value {
      color: #333;
      font-weight: 500;

      &.tech-badge {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #18a058;
        font-size: 12px;

        &.local-preview {
          color: #2080f0;
        }

        &.online-preview {
          color: #fa8c16;
        }
      }

      &.pending-upload {
        color: #fa8c16;
        font-weight: 600;
      }

      &.uploaded {
        color: #52c41a;
        font-weight: 600;
      }
    }
  }
}

.video-actions {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  height: 100%;

  .upload-icon {
    margin-bottom: 20px;
  }

  .upload-text {
    text-align: center;

    .primary-text {
      font-size: 18px;
      color: #333;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .secondary-text {
      font-size: 14px;
      color: #999;
      line-height: 1.5;
      margin-bottom: 12px;
    }

    .manual-tip {
      font-size: 12px;
      color: #fa8c16;
      margin-top: 8px;
      font-weight: 500;
    }
  }
}

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);

  .progress-container {
    padding: 20px;

    .progress-text {
      text-align: center;
      font-size: 14px;
      color: #333;
      margin-top: 12px;
      font-weight: 500;
    }

    .upload-speed {
      text-align: center;
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }

    .tech-info {
      text-align: center;
      font-size: 11px;
      color: #18a058;
      margin-top: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
    }
  }
}

.error-message {
  margin-top: 16px;
}

.url-input-container {
  margin-top: 16px;
}
</style>
