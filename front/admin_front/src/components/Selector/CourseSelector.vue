<template>
  <n-select
    v-model:value="selectedValue"
    :options="options"
    :loading="loading"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="true"
    :remote="true"
    :clear-filter-after-select="false"
    :render-label="renderLabel"
    @search="handleSearch"
    @update:value="handleUpdate"
    @clear="handleClear"
  >
    <template #empty>
      <div style="text-align: center; padding: 12px;">
        {{ searchQuery ? '未找到匹配的课程' : '请输入课程名称搜索' }}
      </div>
    </template>
    
    <template #action>
      <div v-if="searchQuery && !loading && options.length === 0" style="padding: 8px; text-align: center; color: #999;">
        未找到课程 "{{ searchQuery }}"
      </div>
    </template>
  </n-select>
</template>

<script setup>
import { ref, watch, computed, h, onMounted } from 'vue'
import { getCourseListEnhanced } from '@/api/course'
import { useMessage } from 'naive-ui'

const props = defineProps({
  value: {
    type: [String, Number, Array],
    default: null
  },
  placeholder: {
    type: String,
    default: '请选择课程'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  // 是否显示课程ID
  showCourseId: {
    type: Boolean,
    default: true
  },
  // 课程状态筛选
  status: {
    type: [String, Number],
    default: null
  },
  // 防抖延迟时间（毫秒）
  debounceDelay: {
    type: Number,
    default: 300
  }
})

const emit = defineEmits(['update:value', 'change'])

const message = useMessage()
const loading = ref(false)
const options = ref([])
const searchQuery = ref('')
let debounceTimer = null

// 双向绑定
const selectedValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 渲染标签 - 显示封面和课程标题
const renderLabel = (option) => {
  const course = option.course
  if (!course) return option.label

  return h('div', {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    }
  }, [
    // 课程封面
    h('img', {
      src: course.fullCoverImageUrl || course.coverImageUrl || '/default-course-cover.svg',
      style: {
        width: '32px',
        height: '24px',
        borderRadius: '4px',
        objectFit: 'cover',
        flexShrink: 0
      },
      onError: (e) => {
        e.target.src = '/default-course-cover.svg'
      }
    }),
    // 课程标题和ID
    h('span', {
      style: {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }
    }, option.label)
  ])
}

onMounted(() => {
  handleSearch('')
})

// 实际搜索课程的函数
const performSearch = async (query) => {
  loading.value = true
  try {
    const params = {
      page: 1,
      pageSize: 10,
      title: query, // 按课程标题搜索
    }
    
    // 如果指定了状态，添加状态筛选
    if (props.status !== null) {
      params.status = props.status
    }
    
    const { data } = await getCourseListEnhanced(params)
    
    options.value = (data.records || []).map(course => ({
      label: props.showCourseId 
        ? `${course.title} (ID: ${course.id})`
        : course.title,
      value: course.id,
      course: course // 保存完整课程信息
    }))
  } catch (error) {
    message.error('搜索课程失败：' + error.message)
    options.value = []
  } finally {
    loading.value = false
  }
}

// 带防抖的搜索课程函数
const handleSearch = (query) => {
  searchQuery.value = query
  
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 如果是空查询，立即执行搜索
  if (!query) {
    performSearch(query)
    return
  }
  
  // 设置新的防抖定时器
  debounceTimer = setTimeout(() => {
    performSearch(query)
  }, props.debounceDelay)
}

// 值更新处理
const handleUpdate = (value) => {
  emit('update:value', value)
  emit('change', value, getSelectedCourses(value))
}

// 清空处理
const handleClear = () => {
  searchQuery.value = ''
  options.value = []
  emit('update:value', props.multiple ? [] : null)
  emit('change', props.multiple ? [] : null, [])
}

// 获取选中的课程信息
const getSelectedCourses = (value) => {
  if (!value) return []
  
  const values = Array.isArray(value) ? value : [value]
  return options.value
    .filter(option => values.includes(option.value))
    .map(option => option.course)
}

// 初始化时如果有值，需要加载对应的课程信息
const loadInitialCourses = async () => {
  if (!props.value) return
  
  const values = Array.isArray(props.value) ? props.value : [props.value]
  if (values.length === 0) return
  
  loading.value = true
  try {
    // 批量获取课程信息
    const coursePromises = values.map(async (courseId) => {
      try {
        const { data } = await getCourseListEnhanced({
          page: 1,
          pageSize: 1,
          id: courseId
        })
        return data.records?.[0]
      } catch (error) {
        console.warn(`获取课程 ${courseId} 信息失败:`, error)
        return null
      }
    })
    
    const courses = await Promise.all(coursePromises)
    options.value = courses
      .filter(course => course)
      .map(course => ({
        label: props.showCourseId 
          ? `${course.title} (ID: ${course.id})`
          : course.title,
        value: course.id,
        course: course
      }))
  } catch (error) {
    console.error('加载初始课程信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听value变化，加载初始数据
watch(
  () => props.value,
  (newValue) => {
    if (newValue && options.value.length === 0) {
      loadInitialCourses()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  search: handleSearch,
  clear: handleClear,
  getSelectedCourses
})
</script>

<style lang="scss" scoped>
// 可以添加自定义样式
</style>
