<template>
  <n-cascader
    v-model:value="selectedValue"
    :options="options"
    :loading="loading"
    :placeholder="placeholder"
    :clearable="clearable"
    :check-strategy="checkStrategy"
    :multiple="multiple"
    :show-path="true"
    separator=" / "
    expand-trigger="click"
    @update:value="handleUpdate"
    @clear="handleClear"
  >
    <template #empty>
      <div style="text-align: center; padding: 12px;">
        {{ courseId ? '该课程暂无章节课时' : '请先选择课程' }}
      </div>
    </template>
  </n-cascader>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { getCourseTree } from '@/api/course'
import { useMessage } from 'naive-ui'

const props = defineProps({
  value: {
    type: [String, Number, Array],
    default: null
  },
  courseId: {
    type: [String, Number],
    default: null
  },
  placeholder: {
    type: String,
    default: '请选择章节或课时'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: false
  },
  // 权限类型：2-章节权限，3-课时权限
  accessType: {
    type: Number,
    default: 3
  },
  showPath: {
    type: Boolean,
    default: true
  },
  separator: {
    type: String,
    default: ' / '
  }
})

const emit = defineEmits(['update:value', 'change'])

const message = useMessage()
const loading = ref(false)
const options = ref([])

// 双向绑定
const selectedValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 检查策略：章节权限时选择父节点，课时权限时选择子节点
const checkStrategy = computed(() => {
  return props.accessType === 2 ? 'parent' : 'child'
})

// 加载课程树形数据
const loadCourseTree = async () => {
  if (!props.courseId) {
    options.value = []
    return
  }

  loading.value = true
  try {
    const { data } = await getCourseTree(props.courseId)

    // 转换数据格式为级联选择器需要的格式
    options.value = transformTreeData(data || [])
  } catch (error) {
    message.error('获取课程结构失败：' + error.message)
    options.value = []
  } finally {
    loading.value = false
  }
}

// 转换树形数据格式
const transformTreeData = (chapters) => {
  if (!chapters || chapters.length === 0) {
    return []
  }

  return chapters.map(chapter => {
    const chapterNode = {
      label: chapter.title,
      value: `chapter_${chapter.id}`,
      key: `chapter_${chapter.id}`,
      type: 'chapter',
      id: chapter.id,
      disabled: props.accessType === 3 // 课时权限时禁用章节选择
    }

    // 添加课时子节点
    if (chapter.lessons && chapter.lessons.length > 0) {
      chapterNode.children = chapter.lessons.map(lesson => ({
        label: lesson.title,
        value: `lesson_${lesson.id}`,
        key: `lesson_${lesson.id}`,
        type: 'lesson',
        id: lesson.id,
        chapterId: chapter.id,
        disabled: props.accessType === 2, // 章节权限时禁用课时选择
        isLeaf: true
      }))
    } else {
      // 没有课时的章节
      chapterNode.children = []
      if (props.accessType === 2) {
        chapterNode.isLeaf = true
      }
    }

    return chapterNode
  })
}



// 值更新处理
const handleUpdate = (value, option, pathValues) => {
  emit('update:value', value)

  // 解析选中的数据
  const selectedData = parseSelectedData(value, option, pathValues)
  emit('change', value, selectedData, pathValues)
}

// 解析选中的数据
const parseSelectedData = (value, option, pathValues) => {
  if (!value) return null

  if (Array.isArray(value)) {
    // 多选情况
    return value.map((val, index) => parseSelectedValue(val, option?.[index], pathValues?.[index]))
  } else {
    // 单选情况
    return parseSelectedValue(value, option, pathValues)
  }
}

// 解析单个选中值
const parseSelectedValue = (value, option, pathValues) => {
  if (!value) return null

  const [type, id] = value.split('_')

  // 从路径值中获取章节ID（对于课时权限）
  let chapterId = null
  if (type === 'lesson' && pathValues && pathValues.length >= 2) {
    const chapterValue = pathValues[pathValues.length - 2]
    if (chapterValue && chapterValue.startsWith('chapter_')) {
      chapterId = parseInt(chapterValue.split('_')[1])
    }
  } else if (type === 'chapter') {
    chapterId = parseInt(id)
  }

  return {
    type: type, // 'chapter' 或 'lesson'
    id: parseInt(id),
    chapterId: chapterId,
    lessonId: type === 'lesson' ? parseInt(id) : null,
    label: option?.label || '',
    value: value
  }
}

// 清空处理
const handleClear = () => {
  emit('update:value', props.multiple ? [] : null)
  emit('change', props.multiple ? [] : null, null)
}

// 监听课程ID变化
watch(
  () => props.courseId,
  (newCourseId) => {
    if (newCourseId) {
      loadCourseTree()
    } else {
      options.value = []
      handleClear()
    }
  },
  { immediate: true }
)

// 监听权限类型变化
watch(
  () => props.accessType,
  () => {
    handleClear()
    if (props.courseId) {
      loadCourseTree()
    }
  }
)

// 暴露方法给父组件
defineExpose({
  loadCourseTree,
  clear: handleClear
})
</script>

<style lang="scss" scoped>
:deep(.n-cascader) {
  width: 100%;
}

:deep(.n-cascader-menu) {
  min-width: 200px;
}

:deep(.n-cascader-option) {
  &.n-cascader-option--disabled {
    opacity: 0.5;
  }
}
</style>
