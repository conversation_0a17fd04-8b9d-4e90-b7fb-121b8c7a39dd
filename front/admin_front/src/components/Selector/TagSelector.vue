<template>
  <div class="tag-selector">
    <n-select
      :value="value"
      :options="options"
      :placeholder="placeholder"
      :filterable="filterable"
      :clearable="clearable"
      :loading="loading"
      @update:value="handleValueChange"
    >
      <template #action v-if="allowAdd">
        <n-button
          text
          type="primary"
          @click="showAddModal = true"
        >
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          添加新{{ tagLabel }}
        </n-button>
      </template>
    </n-select>

    <!-- 添加新分类弹窗 -->
    <n-modal
      v-model:show="showAddModal"
      preset="card"
              :title="`添加新${tagLabel}`"
      style="width: 400px"
    >
      <n-form
        ref="formRef"
        :model="formModel"
        :rules="rules"
        label-placement="left"
        label-width="80px"
      >
        <n-form-item :label="`${tagLabel}名称`" path="name">
          <n-input
            v-model:value="formModel.name"
            :placeholder="`请输入${tagLabel}名称`"
          />
        </n-form-item>
        <n-form-item :label="`${tagLabel}描述`" path="description">
          <n-input
            v-model:value="formModel.description"
            type="textarea"
            :placeholder="`请输入${tagLabel}描述（可选）`"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button
            type="primary"
            :loading="addLoading"
            @click="handleAddTag"
          >
            确定
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { AddOutline } from "@vicons/ionicons5";
import { getTagConfigByCategory, createTagConfig } from "@/api/tagConfig";
import { useMessage } from "naive-ui";

const message = useMessage();

// Props定义
const props = defineProps({
  value: {
    type: [String, Number],
    default: null,
  },
  category: {
    type: String,
    required: true,
    default: "course_category",
  },
  tagLabel: {
    type: String,
    default: "标签",
  },
  placeholder: {
    type: String,
    default: "请选择标签",
  },
  filterable: {
    type: Boolean,
    default: true,
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  allowAdd: {
    type: Boolean,
    default: true,
  },
  autoLoad: {
    type: Boolean,
    default: true,
  },
});

// Emits定义
const emit = defineEmits(['update:value', 'change', 'add-success']);

// 响应式数据
const loading = ref(false);
const options = ref([]);
const showAddModal = ref(false);
const addLoading = ref(false);
const formRef = ref(null);

// 表单数据
const formModel = reactive({
  name: "",
  description: "",
});

// 表单验证规则
const rules = computed(() => ({
  name: [
    { required: true, message: `请输入${props.tagLabel}名称`, trigger: "blur" },
    { min: 2, max: 20, message: `${props.tagLabel}名称长度在2到20个字符`, trigger: "blur" },
  ],
}));

// 加载标签选项
const loadOptions = async () => {
  if (!props.category) return;
  
  loading.value = true;
  try {
    const res = await getTagConfigByCategory(props.category);
    if (res.code === 200) {
      options.value = res.data.map((item) => ({
        label: item.label,
        value: item.value,
      }));
    }
  } catch (error) {
    console.error(`加载${props.tagLabel}选项失败:`, error);
    message.error(`加载${props.tagLabel}选项失败`);
  } finally {
    loading.value = false;
  }
};

// 处理值变化
const handleValueChange = (value) => {
  emit('update:value', value);
  emit('change', value);
};

// 处理添加新标签
const handleAddTag = async () => {
  try {
    await formRef.value?.validate();
    addLoading.value = true;

    // 创建新的标签配置
    const newTagConfig = {
      category: props.category,
      label: formModel.name,
      remark: formModel.description,
      status: 1,
      isSystem: 0,
      sortOrder: options.value.length + 1,
    };

    const res = await createTagConfig(newTagConfig);
    if (res.code === 200) {
      message.success(`${props.tagLabel}添加成功`);

      // 重新加载选项
      await loadOptions();

      // 自动选择新添加的标签
      const newValue = res.data.value;
      emit('update:value', newValue);
      emit('change', newValue);
      emit('add-success', res.data);

      // 关闭弹窗并重置表单
      showAddModal.value = false;
      formModel.name = "";
      formModel.description = "";
    } else {
      message.error(res.message || `${props.tagLabel}添加失败`);
    }
  } catch (error) {
    console.error(`添加${props.tagLabel}失败:`, error);
    if (error?.message) {
      message.error(error.message);
    } else {
      message.error(`${props.tagLabel}添加失败`);
    }
  } finally {
    addLoading.value = false;
  }
};

// 刷新选项的方法（供外部调用）
const refresh = () => {
  loadOptions();
};

// 监听category变化，重新加载选项
watch(() => props.category, () => {
  if (props.autoLoad) {
    loadOptions();
  }
}, { immediate: false });

// 组件挂载时加载数据
onMounted(() => {
  if (props.autoLoad) {
    loadOptions();
  }
});

// 暴露方法给父组件
defineExpose({
  loadOptions,
  refresh,
});
</script>

<style lang="scss" scoped>
.tag-selector {
  width: 100%;
}
</style> 