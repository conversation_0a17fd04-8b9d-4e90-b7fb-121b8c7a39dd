<template>
  <n-select
    v-model:value="selectedValue"
    :options="options"
    :loading="loading"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="true"
    :remote="true"
    :clear-filter-after-select="false"
    :render-label="renderLabel"
    @search="handleSearch"
    @update:value="handleUpdate"
    @clear="handleClear"
  >
    <template #empty>
      <div style="text-align: center; padding: 12px;">
        {{ searchQuery ? '未找到匹配的老师' : '请输入老师姓名搜索' }}
      </div>
    </template>
    
    <template #action>
      <div v-if="searchQuery && !loading && options.length === 0" style="padding: 8px; text-align: center; color: #999;">
        未找到老师 "{{ searchQuery }}"
      </div>
    </template>
  </n-select>
</template>

<script setup>
import { ref, watch, computed, h, onMounted } from 'vue'
import { getTeacherList } from '@/api/teacher'
import { useMessage } from 'naive-ui'

const props = defineProps({
  value: {
    type: [String, Number, Array],
    default: null
  },
  placeholder: {
    type: String,
    default: '请选择老师'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  // 是否显示老师ID
  showTeacher : {
    type: Boolean,
    default: false
  },
  // 防抖延迟时间（毫秒）
  debounceDelay: {
    type: Number,
    default: 300
  }
})

const emit = defineEmits(['update:value', 'change'])

const message = useMessage()
const loading = ref(false)
const options = ref([])
const searchQuery = ref('')
let debounceTimer = null

// 双向绑定
const selectedValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 渲染标签 - 显示头像和姓名
const renderLabel = (option) => {
  const teacher = option.teacher
  if (!teacher) return option.label

  return h('div', {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    }
  }, [
    // 头像
    h('img', {
      src: teacher.fullAvatarUrl || teacher.avatarUrl || '/default-avatar.svg',
      style: {
        width: '24px',
        height: '24px',
        borderRadius: '50%',
        objectFit: 'cover',
        flexShrink: 0
      },
      onError: (e) => {
        e.target.src = '/default-avatar.svg'
      }
    }),
    // 姓名和ID
    h('span', {
      style: {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }
    }, option.label)
  ])
}

onMounted(() => {
  handleSearch('')
})

// 实际搜索老师的函数
const performSearch = async (query) => {
  loading.value = true
  try {
    const { data } = await getTeacherList({
      page: 1,
      pageSize: 10,
      name: query, // 按老师姓名搜索
      status: 1 // 只搜索有效老师
    })
    
    options.value = (data.records || []).map(teacher => ({
      label: props.showTeacherId 
        ? `${teacher.name} (ID: ${teacher.id})`
        : teacher.name,
      value: teacher.id,
      teacher: teacher // 保存完整老师信息
    }))
  } catch (error) {
    message.error('搜索老师失败：' + error.message)
    options.value = []
  } finally {
    loading.value = false
  }
}

// 带防抖的搜索老师函数
const handleSearch = (query) => {
  searchQuery.value = query
  
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 如果是空查询，立即执行搜索
  if (!query) {
    performSearch(query)
    return
  }
  
  // 设置新的防抖定时器
  debounceTimer = setTimeout(() => {
    performSearch(query)
  }, props.debounceDelay)
}

// 值更新处理
const handleUpdate = (value) => {
  emit('update:value', value)
  emit('change', value, getSelectedTeachers(value))
}

// 清空处理
const handleClear = () => {
  searchQuery.value = ''
  options.value = []
  emit('update:value', props.multiple ? [] : null)
  emit('change', props.multiple ? [] : null, [])
}

// 获取选中的老师信息
const getSelectedTeachers = (value) => {
  if (!value) return []
  
  const values = Array.isArray(value) ? value : [value]
  return options.value
    .filter(option => values.includes(option.value))
    .map(option => option.teacher)
}

// 初始化时如果有值，需要加载对应的老师信息
const loadInitialTeachers = async () => {
  if (!props.value) return
  
  const values = Array.isArray(props.value) ? props.value : [props.value]
  if (values.length === 0) return
  
  loading.value = true
  try {
    // 批量获取老师信息
    const teacherPromises = values.map(async (teacherId) => {
      try {
        const { data } = await getTeacherList({
          page: 1,
          pageSize: 1,
          id: teacherId
        })
        return data.records?.[0]
      } catch (error) {
        console.warn(`获取老师 ${teacherId} 信息失败:`, error)
        return null
      }
    })
    
    const teachers = await Promise.all(teacherPromises)
    options.value = teachers
      .filter(teacher => teacher)
      .map(teacher => ({
        label: props.showTeacherId 
          ? `${teacher.name} (ID: ${teacher.id})`
          : teacher.name,
        value: teacher.id,
        teacher: teacher
      }))
  } catch (error) {
    console.error('加载初始老师信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听value变化，加载初始数据
watch(
  () => props.value,
  (newValue) => {
    if (newValue && options.value.length === 0) {
      loadInitialTeachers()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  search: handleSearch,
  clear: handleClear,
  getSelectedTeachers
})
</script>

<style lang="scss" scoped>
// 可以添加自定义样式
</style>
