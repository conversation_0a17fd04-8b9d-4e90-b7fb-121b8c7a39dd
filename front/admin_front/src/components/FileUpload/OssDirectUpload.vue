<template>

  <div class="oss-direct-upload-container">
    <!-- 上传区域 -->
    <div
      class="upload-area"
      :class="{
        'upload-area--dragover': isDragOver,
        'upload-area--disabled': disabled || uploading,
      }"
      @click="handleClick"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
    >
      <!-- 预览区域 -->
      <div v-if="previewUrl && showPreview" class="preview-container">
        <!-- 图片预览 -->

        <div v-if="isImage" class="image-preview">
          <img
            :src="previewUrl"
            :alt="fileName"
            @load="handleImageLoad"
            @error="handleImageError"
          />
          <div class="preview-overlay">
            <n-button
              quaternary
              circle
              size="small"
              @click.stop="handleRemove"
            >
              <template #icon>
                <n-icon><CloseOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideo" class="video-preview">
          <video
            ref="videoRef"
            :src="previewUrl"
            controls
            preload="metadata"
            @loadedmetadata="handleVideoLoaded"
          />
          <div class="preview-overlay">
            <n-button
              quaternary
              circle
              size="small"
              @click.stop="handleRemove"
            >
              <template #icon>
                <n-icon><CloseOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>

        <!-- 其他文件预览 -->
        <div v-else class="file-preview">
          <div class="file-icon">
            <n-icon size="48">
              <DocumentOutline />
            </n-icon>
          </div>
          <div class="file-info">
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
          </div>
          <div class="preview-overlay">
            <n-button
              quaternary
              circle
              size="small"
              @click.stop="handleRemove"
            >
              <template #icon>
                <n-icon><CloseOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>



      </div>

      <!-- 上传提示 -->
      <div v-else class="upload-hint">
        <div class="upload-icon">
          <n-icon size="48" :color="disabled ? '#ccc' : '#18a058'">
            <CloudUploadOutline />
          </n-icon>
        </div>
        <div class="upload-text">
          <div class="primary-text">
            {{ disabled ? "上传已禁用" : "点击或拖拽文件到此处上传" }}
          </div>
          <div class="secondary-text">
            {{ acceptText }}
          </div>
          <!-- <div class="tech-text">
            <n-icon size="16"><FlashOutline /></n-icon>
            直传OSS，无文件大小限制
          </div> -->
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <n-progress
          type="line"
          :percentage="uploadProgress"
          :show-indicator="false"
        />
        <div class="progress-text">
          {{ uploadStatus }} {{ uploadProgress }}%
        </div>
      </div>
    </div>

    <!-- 文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="handleFileChange"
    />

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <n-alert type="error" :show-icon="false">
        {{ errorMessage }}
      </n-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { useMessage } from "naive-ui";
import {
  CloudUploadOutline,
  CloseOutline,
  DocumentOutline,
  FlashOutline,
} from "@vicons/ionicons5";
import { uploadFile, validateFileType, validateFileSize, formatFileSize } from "@/api/oss";
import { getFileUrl } from "@/utils/fileUtils";

// Props
const props = defineProps({
  // 文件分类
  category: {
    type: String,
    required: true,
    validator: (value) => ["image", "video", "audio", "document"].includes(value),
  },
  // 是否支持多文件上传
  multiple: {
    type: Boolean,
    default: false,
  },
  // 是否显示预览
  showPreview: {
    type: Boolean,
    default: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: "",
  },
  // 初始文件URL
  modelValue: {
    type: [String, Array],
    default: "",
  },
});
const {disabled,showPreview} = props

// Emits
const emit = defineEmits([
  "update:modelValue",
  "upload-success",
  "upload-error",
  "upload-progress",
  "file-remove",
]);

// Refs
const fileInputRef = ref(null);
const videoRef = ref(null);
const message = useMessage();

// 响应式数据
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref("准备上传...");
const isDragOver = ref(false);
const errorMessage = ref("");
const previewUrl = ref("");
const fileName = ref("");
const fileSize = ref(0);
// PostObject方式不需要STS Token

// 计算属性
const isImage = computed(() => {
  return props.category === "image" && previewUrl.value;
});

const isVideo = computed(() => {
  return props.category === "video" && previewUrl.value;
});

const acceptText = computed(() => {
  // 根据文件分类返回支持的格式和大小限制（仅用于显示）
  const categoryConfig = {
    image: {
      types: ["jpg", "jpeg", "png", "gif", "webp", "bmp"],
      maxSize: 10 * 1024 * 1024 // 10MB
    },
    video: {
      types: ["mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"],
      maxSize: 500 * 1024 * 1024 // 500MB
    },
    audio: {
      types: ["mp3", "wav", "aac", "flac", "ogg", "m4a"],
      maxSize: 50 * 1024 * 1024 // 50MB
    },
    document: {
      types: ["pdf", "doc", "docx"],
      maxSize: 20 * 1024 * 1024 // 20MB
    }
  };

  const config = categoryConfig[props.category] || categoryConfig.document;
  const types = config.types.join(", ");
  const maxSize = formatFileSize(config.maxSize);
  return `支持格式：${types}，最大${maxSize}`;
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && typeof newValue === "string") {
      // 如果是相对路径，转换为完整URL用于预览
      previewUrl.value = getFileUrl(newValue);
      // 从URL中提取文件名
      const urlParts = newValue.split("/");
      fileName.value = urlParts[urlParts.length - 1];
    } else {
      previewUrl.value = "";
      fileName.value = "";
      fileSize.value = 0;
    }
  },
  { immediate: true }
);

// 方法

const handleClick = () => {
  if (disabled || uploading.value) return;
  fileInputRef.value?.click();
};

const handleFileChange = (event) => {
  const files = Array.from(event.target.files);
  if (files.length > 0) {
    handleFiles(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  if (disabled || uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files);
  if (files.length > 0) {
    handleFiles(files);
  }
};

const handleDragOver = (event) => {
  event.preventDefault();
  if (!disabled && !uploading.value) {
    isDragOver.value = true;
  }
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleFiles = async (files) => {
  if (files.length === 0) return;
  
  // 如果不支持多文件，只取第一个
  const filesToUpload = props.multiple ? files : [files[0]];
  
  for (const file of filesToUpload) {
    await uploadSingleFile(file);
  }
};

const uploadSingleFile = async (file) => {
  try {
    // 验证文件
    const validation = validateFile(file);
    if (!validation.valid) {
      errorMessage.value = validation.message;
      return;
    }
    
    errorMessage.value = "";
    uploading.value = true;
    uploadProgress.value = 0;
    uploadStatus.value = "准备上传...";
    
    // 设置预览
    if (props.showPreview) {
      setFilePreview(file);
    }
    
    uploadStatus.value = "正在上传...";
    
    // 使用PostObject方式上传到OSS
    const result = await uploadFile(file, props.category, (progress) => {
      uploadProgress.value = progress;
      emit("upload-progress", progress);
    });
    
    uploadStatus.value = "上传完成";

    console.log('文件上传成功，相对路径:', result.fileUrl);
    console.log('文件预览URL:', result.previewUrl);

    // 更新文件信息，保持本地预览
    fileName.value = result.originalFileName;
    fileSize.value = result.fileSize;

    // 设置预览URL为完整URL，但存储的值为相对路径
    if (result.previewUrl) {
      previewUrl.value = result.previewUrl;
    }

    // 向父组件传递相对路径（用于存储到数据库）
    emit("update:modelValue", result.fileUrl);
    emit("upload-success", result);
    message.success("文件上传成功");
    
  } catch (error) {
    console.error("上传失败:", error);
    errorMessage.value = error.message || "上传失败";
    emit("upload-error", error);

    // 显示错误信息
    message.error("上传失败: " + (error.message || "未知错误"));
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
    uploadStatus.value = "";
  }
};

const validateFile = (file) => {
  // 根据文件分类获取配置
  const categoryConfig = {
    image: {
      types: [
        "image/jpeg", "image/jpg", "image/png", "image/gif",
        "image/webp", "image/bmp", "image/tiff", "image/tif",
        // 添加扩展名支持以增强兼容性
        "jpg", "jpeg", "png", "gif", "webp", "bmp", "tiff", "tif"
      ],
      maxSize: 10 * 1024 * 1024 // 10MB
    },
    video: {
      types: [
        "video/mp4", "video/avi", "video/mov", "video/wmv",
        "video/flv", "video/mkv", "video/webm", "video/quicktime",
        // 添加扩展名支持以增强兼容性
        "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"
      ],
      maxSize: 500 * 1024 * 1024 // 500MB
    },
    audio: {
      types: [
        "audio/mp3", "audio/wav", "audio/aac", "audio/flac",
        "audio/ogg", "audio/m4a", "audio/mpeg",
        // 添加扩展名支持以增强兼容性
        "mp3", "wav", "aac", "flac", "ogg", "m4a"
      ],
      maxSize: 50 * 1024 * 1024 // 50MB
    },
    document: {
      types: [
        "application/pdf", "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        // 添加扩展名支持以增强兼容性
        "pdf", "doc", "docx"
      ],
      maxSize: 20 * 1024 * 1024 // 20MB
    }
  };

  const config = categoryConfig[props.category] || categoryConfig.document;

  // 检查文件类型
  if (!validateFileType(file, config.types)) {
    const typeNames = config.types.map(type => type.split('/')[1]).join(", ");
    return {
      valid: false,
      message: `不支持的文件格式，支持格式：${typeNames}`,
    };
  }

  // 检查文件大小
  if (!validateFileSize(file, config.maxSize)) {
    return {
      valid: false,
      message: `文件大小超过限制，最大允许 ${formatFileSize(config.maxSize)}`,
    };
  }

  return { valid: true };
};

const setFilePreview = (file) => {
  if (props.category === "image" || props.category === "video") {
    // 清理之前的blob URL
    if (previewUrl.value && previewUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl.value);
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      previewUrl.value = e.target.result;
    };
    reader.readAsDataURL(file);
  }

  fileName.value = file.name;
  fileSize.value = file.size;
};

const handleRemove = () => {
  previewUrl.value = "";
  fileName.value = "";
  fileSize.value = 0;
  emit("update:modelValue", "");
  emit("file-remove");
};

const handleImageLoad = () => {
  // 图片加载成功
};

const handleImageError = (event) => {
  console.error('图片预览加载失败:', event);
  errorMessage.value = "图片预览加载失败";
};

const handleVideoLoaded = () => {
  // 视频加载完成后的处理
  if (videoRef.value) {
    console.log("视频时长:", videoRef.value.duration);
  }
};

// PostObject方式无需初始化
</script>

<style lang="scss" scoped>
.oss-direct-upload-container {
  width: 100%;
}

.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #18a058;
    background-color: #f0f9ff;
  }

  &--dragover {
    border-color: #18a058;
    background-color: #f0f9ff;
    transform: scale(1.02);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      border-color: #d9d9d9;
      background-color: #fafafa;
      transform: none;
    }
  }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  position: relative;
  max-width: 100%;
  max-height: 300px;

  img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
    border-radius: 4px;
  }
}

.video-preview {
  position: relative;
  max-width: 100%;
  max-height: 300px;

  video {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
  }
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;

  .file-icon {
    margin-bottom: 12px;
    color: #666;
  }

  .file-info {
    text-align: center;

    .file-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      word-break: break-all;
    }

    .file-size {
      font-size: 12px;
      color: #999;
    }
  }
}

.preview-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;

  .n-button {
    color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;

  .upload-icon {
    margin-bottom: 16px;
  }

  .upload-text {
    text-align: center;

    .primary-text {
      font-size: 16px;
      color: #333;
      margin-bottom: 8px;
    }

    .secondary-text {
      font-size: 12px;
      color: #999;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .tech-text {
      font-size: 11px;
      color: #18a058;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      font-weight: 500;
    }
  }
}

.upload-progress {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;

  .progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}

.error-message {
  margin-top: 12px;
}
</style>

<style lang="scss" scoped>
.oss-direct-upload-container {
  width: 100%;
}

.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #18a058;
    background-color: #f0f9ff;
  }

  &--dragover {
    border-color: #18a058;
    background-color: #f0f9ff;
    transform: scale(1.02);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      border-color: #d9d9d9;
      background-color: #fafafa;
      transform: none;
    }
  }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  position: relative;
  max-width: 100%;
  max-height: 300px;

  img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
    border-radius: 4px;
  }
}

.video-preview {
  position: relative;
  max-width: 100%;
  max-height: 300px;

  video {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
  }
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;

  .file-icon {
    margin-bottom: 12px;
    color: #666;
  }

  .file-info {
    text-align: center;

    .file-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      word-break: break-all;
    }

    .file-size {
      font-size: 12px;
      color: #999;
    }
  }
}

.preview-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;

  .n-button {
    color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;

  .upload-icon {
    margin-bottom: 16px;
  }

  .upload-text {
    text-align: center;

    .primary-text {
      font-size: 16px;
      color: #333;
      margin-bottom: 8px;
    }

    .secondary-text {
      font-size: 12px;
      color: #999;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .tech-text {
      font-size: 11px;
      color: #18a058;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      font-weight: 500;
    }
  }
}

.upload-progress {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;

  .progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}

.error-message {
  margin-top: 12px;
}
</style>
