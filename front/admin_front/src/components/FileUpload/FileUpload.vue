<template>
  <div class="file-upload-container">
    <!-- 上传区域 -->
    <div
      class="upload-area"
      :class="{
        'upload-area--dragover': isDragOver,
        'upload-area--disabled': disabled || uploading,
      }"
      @click="handleClick"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
    >
      <!-- 预览区域 -->
      <div v-if="previewUrl && showPreview" class="preview-container">
        <!-- 图片预览 -->
        <div v-if="isImage" class="image-preview">
          <img :src="previewUrl" :alt="fileName" />
          <div class="preview-overlay">
            <n-button
              quaternary
              circle
              size="small"
              @click.stop="handleRemove"
            >
              <template #icon>
                <n-icon><CloseOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideo" class="video-preview">
          <video
            ref="videoRef"
            :src="previewUrl"
            controls
            preload="metadata"
            @loadedmetadata="handleVideoLoaded"
          />
          <div class="preview-overlay">
            <n-button
              quaternary
              circle
              size="small"
              @click.stop="handleRemove"
            >
              <template #icon>
                <n-icon><CloseOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>

        <!-- 其他文件预览 -->
        <div v-else class="file-preview">
          <div class="file-icon">
            <n-icon size="48">
              <DocumentOutline />
            </n-icon>
          </div>
          <div class="file-info">
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
          </div>
          <div class="preview-overlay">
            <n-button
              quaternary
              circle
              size="small"
              @click.stop="handleRemove"
            >
              <template #icon>
                <n-icon><CloseOutline /></n-icon>
              </template>
            </n-button>
          </div>
        </div>
      </div>

      <!-- 上传提示 -->
      <div v-else class="upload-hint">
        <div class="upload-icon">
          <n-icon size="48" :color="disabled ? '#ccc' : '#18a058'">
            <CloudUploadOutline />
          </n-icon>
        </div>
        <div class="upload-text">
          <div class="primary-text">
            {{ disabled ? "上传已禁用" : "点击或拖拽文件到此处上传" }}
          </div>
          <div class="secondary-text">
            {{ acceptText }}
          </div>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <n-progress
          type="line"
          :percentage="uploadProgress"
          :show-indicator="false"
        />
        <div class="progress-text">
          上传中... {{ uploadProgress }}%
        </div>
      </div>
    </div>

    <!-- 文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="handleFileChange"
    />

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      <n-alert type="error" :show-icon="false">
        {{ errorMessage }}
      </n-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { useMessage } from "naive-ui";
import {
  CloudUploadOutline,
  CloseOutline,
  DocumentOutline,
} from "@vicons/ionicons5";
import { uploadFile, getFileUploadInfo } from "@/api/file";

// Props
const props = defineProps({
  // 文件分类
  category: {
    type: String,
    required: true,
    validator: (value) => ["image", "video", "audio", "document"].includes(value),
  },
  // 是否支持多文件上传
  multiple: {
    type: Boolean,
    default: false,
  },
  // 是否显示预览
  showPreview: {
    type: Boolean,
    default: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 最大文件大小（MB）
  maxSize: {
    type: Number,
    default: null,
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: "",
  },
  // 初始文件URL
  modelValue: {
    type: [String, Array],
    default: "",
  },
});
const {category,modelValue,accept,disabled,maxSize,multiple,showPreview} = props

// Emits
const emit = defineEmits([
  "update:modelValue",
  "upload-success",
  "upload-error",
  "upload-progress",
  "file-remove",
]);

// Refs
const fileInputRef = ref(null);
const videoRef = ref(null);
const message = useMessage();

// 响应式数据
const uploading = ref(false);
const uploadProgress = ref(0);
const isDragOver = ref(false);
const errorMessage = ref("");
const previewUrl = ref("");
const fileName = ref("");
const fileSize = ref(0);
const uploadConfig = ref(null);

// 计算属性
const isImage = computed(() => {
  return props.category === "image" && previewUrl.value;
});

const isVideo = computed(() => {
  return props.category === "video" && previewUrl.value;
});

const acceptText = computed(() => {
  if (!uploadConfig.value) return "";
  
  const config = uploadConfig.value;
  switch (props.category) {
    case "image":
      return `支持格式：${config.supportedImageTypes}，最大${config.maxImageSize}`;
    case "video":
      return `支持格式：${config.supportedVideoTypes}，最大${config.maxVideoSize}`;
    case "audio":
      return `支持格式：${config.supportedAudioTypes}，最大${config.maxAudioSize}`;
    case "document":
      return `支持格式：${config.supportedDocumentTypes}，最大${config.maxDocumentSize}`;
    default:
      return "";
  }
});

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && typeof newValue === "string") {
      previewUrl.value = newValue;
      // 从URL中提取文件名
      const urlParts = newValue.split("/");
      fileName.value = urlParts[urlParts.length - 1];
    } else {
      previewUrl.value = "";
      fileName.value = "";
      fileSize.value = 0;
    }
  },
  { immediate: true }
);

// 方法
const loadUploadConfig = async () => {
  try {
    const res = await getFileUploadInfo();
    if (res.code === 200) {
      uploadConfig.value = res.data;
    }
  } catch (error) {
    console.error("加载上传配置失败:", error);
  }
};

const handleClick = () => {
  if (disabled.value || uploading.value) return;
  fileInputRef.value?.click();
};

const handleFileChange = (event) => {
  const files = Array.from(event.target.files);
  if (files.length > 0) {
    handleFiles(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  if (disabled.value || uploading.value) return;
  
  const files = Array.from(event.dataTransfer.files);
  if (files.length > 0) {
    handleFiles(files);
  }
};

const handleDragOver = (event) => {
  event.preventDefault();
  if (!disabled.value && !uploading.value) {
    isDragOver.value = true;
  }
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleFiles = async (files) => {
  if (files.length === 0) return;

  // 如果不支持多文件，只取第一个
  const filesToUpload = props.multiple ? files : [files[0]];

  for (const file of filesToUpload) {
    await uploadSingleFile(file);
  }
};

const uploadSingleFile = async (file) => {
  try {
    // 验证文件
    const validation = validateFile(file);
    if (!validation.valid) {
      errorMessage.value = validation.message;
      return;
    }

    errorMessage.value = "";
    uploading.value = true;
    uploadProgress.value = 0;

    // 设置预览
    if (props.showPreview) {
      setFilePreview(file);
    }

    // 上传文件
    const res = await uploadFile(file, props.category, (progress) => {
      uploadProgress.value = progress;
      emit("upload-progress", progress);
    });

    if (res.code === 200) {
      const fileUrl = res.data.fileUrl;
      previewUrl.value = fileUrl;
      fileName.value = res.data.originalFileName;
      fileSize.value = res.data.fileSize;

      emit("update:modelValue", fileUrl);
      emit("upload-success", res.data);
      message.success("上传成功");
    }
  } catch (error) {
    console.error("上传失败:", error);
    errorMessage.value = error.message || "上传失败";
    emit("upload-error", error);
    message.error("上传失败");
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
  }
};

const validateFile = (file) => {
  if (!uploadConfig.value) {
    return { valid: false, message: "上传配置未加载" };
  }

  const config = uploadConfig.value;
  let maxSize, supportedTypes;

  switch (props.category) {
    case "image":
      maxSize = parseSize(config.maxImageSize);
      supportedTypes = config.supportedImageTypes.split(",");
      break;
    case "video":
      maxSize = parseSize(config.maxVideoSize);
      supportedTypes = config.supportedVideoTypes.split(",");
      break;
    case "audio":
      maxSize = parseSize(config.maxAudioSize);
      supportedTypes = config.supportedAudioTypes.split(",");
      break;
    case "document":
      maxSize = parseSize(config.maxDocumentSize);
      supportedTypes = config.supportedDocumentTypes.split(",");
      break;
    default:
      return { valid: false, message: "不支持的文件类型" };
  }

  // 检查文件大小
  if (file.size > maxSize) {
    return {
      valid: false,
      message: `文件大小超过限制，最大允许 ${formatFileSize(maxSize)}`,
    };
  }

  // 检查文件类型
  const fileExtension = file.name.split(".").pop().toLowerCase();
  if (!supportedTypes.includes(fileExtension)) {
    return {
      valid: false,
      message: `不支持的文件格式，支持格式：${supportedTypes.join(", ")}`,
    };
  }

  return { valid: true };
};

const setFilePreview = (file) => {
  if (props.category === "image" || props.category === "video") {
    const reader = new FileReader();
    reader.onload = (e) => {
      previewUrl.value = e.target.result;
    };
    reader.readAsDataURL(file);
  }

  fileName.value = file.name;
  fileSize.value = file.size;
};

const handleRemove = () => {
  previewUrl.value = "";
  fileName.value = "";
  fileSize.value = 0;
  emit("update:modelValue", "");
  emit("file-remove");
};

const handleVideoLoaded = () => {
  // 视频加载完成后的处理
  if (videoRef.value) {
    console.log("视频时长:", videoRef.value.duration);
  }
};

const parseSize = (sizeStr) => {
  const match = sizeStr.match(/^(\d+)(MB|KB|GB)$/i);
  if (!match) return 0;

  const value = parseInt(match[1]);
  const unit = match[2].toUpperCase();

  switch (unit) {
    case "KB":
      return value * 1024;
    case "MB":
      return value * 1024 * 1024;
    case "GB":
      return value * 1024 * 1024 * 1024;
    default:
      return value;
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 初始化
loadUploadConfig();
</script>

<style lang="scss" scoped>
.file-upload-container {
  width: 100%;
}

.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #18a058;
    background-color: #f0f9ff;
  }

  &--dragover {
    border-color: #18a058;
    background-color: #f0f9ff;
    transform: scale(1.02);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      border-color: #d9d9d9;
      background-color: #fafafa;
      transform: none;
    }
  }
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview {
  position: relative;
  max-width: 100%;
  max-height: 300px;

  img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
    border-radius: 4px;
  }
}

.video-preview {
  position: relative;
  max-width: 100%;
  max-height: 300px;

  video {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
  }
}

.file-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;

  .file-icon {
    margin-bottom: 12px;
    color: #666;
  }

  .file-info {
    text-align: center;

    .file-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      word-break: break-all;
    }

    .file-size {
      font-size: 12px;
      color: #999;
    }
  }
}

.preview-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;

  .n-button {
    color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }
}

.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;

  .upload-icon {
    margin-bottom: 16px;
  }

  .upload-text {
    text-align: center;

    .primary-text {
      font-size: 16px;
      color: #333;
      margin-bottom: 8px;
    }

    .secondary-text {
      font-size: 12px;
      color: #999;
      line-height: 1.4;
    }
  }
}

.upload-progress {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;

  .progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}

.error-message {
  margin-top: 12px;
}
</style>
