<template>
  <n-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-placement="left"
    label-width="100px"
    require-mark-placement="right-hanging"
  >
    <n-grid :cols="24" :x-gap="24">
      <!-- 左侧表单区域 -->
      <n-gi :span="16">
        <n-form-item label="课程标题" path="title">
          <n-input
            v-model:value="form.title"
            placeholder="请输入课程标题"
          />
        </n-form-item>

        <n-form-item label="课程副标题" path="subtitle">
          <n-input
            v-model:value="form.subtitle"
            placeholder="请输入课程副标题"
          />
        </n-form-item>

        <n-form-item label="讲师" path="teacherId">
          <TeacherSelector v-model:value="form.teacherId" />
        </n-form-item>

        <n-form-item label="分类" path="categoryId">
          <TagSelector
            v-model:value="form.categoryId"
            category="course_category"
            tag-label="课程分类"
            placeholder="请选择课程分类"
            :allow-add="true"
            @change="handleCategoryChange"
            @add-success="handleCategoryAddSuccess"
          />
        </n-form-item>

        <n-grid :cols="2" :x-gap="24">
          <n-gi>
            <n-form-item label="价格" path="price">
              <n-input-number
                v-model:value="form.price"
                :min="0"
                :show-button="false"
                :precision="2"
                placeholder="请输入价格"
              >
                <template #prefix>¥</template>
              </n-input-number>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="原价" path="originalPrice">
              <n-input-number
                v-model:value="form.originalPrice"
                :min="0"
                :show-button="false"
                :precision="2"
                placeholder="请输入原价"
              >
                <template #prefix>¥</template>
              </n-input-number>
            </n-form-item>
          </n-gi>
        </n-grid>

        <n-grid :cols="2" :x-gap="24">
          <n-gi>
            <n-form-item label="难度级别" path="level">
              <n-select
                v-model:value="form.level"
                :options="levelOptions"
                filterable
                placeholder="请选择难度级别"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="适合年龄段" path="ageGroup">
              <TagSelector
                v-model:value="form.ageGroup"
                category="age_group"
                tag-label="年龄段"
                placeholder="请选择适合年龄段"
                :allow-add="true"
                @change="handleAgeGroupChange"
                @add-success="handleAgeGroupAddSuccess"
              />
            </n-form-item>
          </n-gi>
        </n-grid>

        <n-form-item label="联系电话" path="contactInfoPhone">
          <n-input
            v-model:value="form.contactInfoPhone"
            placeholder="请输入联系电话"
          />
        </n-form-item>

        <n-form-item label="联系微信" path="contactInfoWechat">
          <n-input
            v-model:value="form.contactInfoWechat"
            placeholder="请输入联系微信"
          />
        </n-form-item>

        <n-form-item label="课程描述" path="description">
          <n-input
            v-model:value="form.description"
            type="textarea"
            placeholder="请输入课程描述"
            :autosize="{
              minRows: 4,
              maxRows: 8,
            }"
          />
        </n-form-item>

        <n-form-item label="联系备注" path="contactInfoRemark">
          <n-input
            v-model:value="form.contactInfoRemark"
            type="textarea"
            placeholder="请输入联系备注"
            :autosize="{
              minRows: 3,
              maxRows: 5,
            }"
          />
        </n-form-item>
      </n-gi>

      <!-- 右侧上传区域和选项 -->
      <n-gi :span="8">
        <n-card title="课程封面" size="small">
          <OssDirectUpload
            v-model="coverImageModel"
            category="image"
            :show-preview="true"
            @upload-success="handleCoverUploadSuccess"
            @upload-error="handleCoverUploadError"
          />
        </n-card>

        <n-card title="课程设置" size="small" class="m-t-20">
          <n-space vertical>
            <n-form-item label="状态" path="status">
              <n-switch
                v-model:value="form.status"
                :checked-value="1"
                :unchecked-value="0"
              >
                <template #checked>上架</template>
                <template #unchecked>下架</template>
              </n-switch>
            </n-form-item>

            <n-form-item label="课程类型">
              <n-space vertical>
                <n-checkbox v-model:checked="form.isLive">
                  是否直播课
                </n-checkbox>
                <n-checkbox v-model:checked="form.isFeatured">
                  是否推荐课程
                </n-checkbox>
                <n-checkbox v-model:checked="form.isSpecialTraining">
                  是否特训营
                </n-checkbox>
                <n-checkbox v-model:checked="form.isOneOnOne">
                  是否一对一
                </n-checkbox>
              </n-space>
            </n-form-item>
          </n-space>
        </n-card>
      </n-gi>
    </n-grid>
  </n-form>


</template>

<script setup>
import { ref, computed } from "vue";
import OssDirectUpload from "@/components/FileUpload/OssDirectUpload.vue";
import TeacherSelector from "@/components/Selector/TeacherSelector.vue";
import TagSelector from "@/components/Selector/TagSelector.vue";
import { useCourseOptions } from "@/composables/useCourseOptions";
import { useCourseFormRules } from "@/composables/useCourseFormRules";

const props = defineProps({
  form: {
    type: Object,
    required: true,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "update:form",
  "cover-upload-success",
  "cover-upload-error",
]);

const formRef = ref(null);

// 使用组合式函数
const {
  levelOptions
} = useCourseOptions();

const { rules } = useCourseFormRules();

// 封面图片双向绑定
const coverImageModel = computed({
  get: () => props.isEdit ? props.form.coverImageFullUrl : props.form.coverImage,
  set: (value) => {
    const updateData = { ...props.form };
    if (props.isEdit) {
      updateData.coverImageFullUrl = value;
      updateData.coverImage = value; // 同时更新相对路径
    } else {
      updateData.coverImage = value;
    }
    emit("update:form", updateData);
  },
});

// TagSelector 相关处理

// 处理年龄段选择变化
const handleAgeGroupChange = (value) => {
  console.log("年龄段选择变化:", value);
};

// 处理年龄段添加成功
const handleAgeGroupAddSuccess = (newTag) => {
  console.log("年龄段添加成功:", newTag);
};

// 处理分类选择变化
const handleCategoryChange = (value) => {
  console.log("分类选择变化:", value);
};

// 处理分类添加成功
const handleCategoryAddSuccess = (newTag) => {
  console.log("分类添加成功:", newTag);
};

// 封面上传处理
const handleCoverUploadSuccess = (fileData) => {
  emit("cover-upload-success", fileData);
};

const handleCoverUploadError = (error) => {
  emit("cover-upload-error", error);
};





// 表单验证方法
const validate = () => {
  return formRef.value?.validate();
};

// 暴露方法给父组件
defineExpose({
  validate,
});
</script>

<style lang="scss" scoped>
.m-t-20 {
  margin-top: 20px;
}
</style>
