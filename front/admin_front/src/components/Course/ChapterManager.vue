<template>
  <n-card title="章节与课时" class="chapter-card">
    <!-- 添加章节按钮 -->
    <div class="chapter-action-bar">
      <n-button type="primary" @click="addChapter">
        <template #icon>
          <n-icon><AddOutline /></n-icon>
        </template>
        添加章节
      </n-button>
    </div>

    <!-- 章节列表 -->
    <div class="chapter-list">
      <n-collapse v-model:expanded-names="expandedChapters">
        <n-collapse-item
          v-for="(chapter, chapterIndex) in chapters"
          :key="chapter.id || chapterIndex"
          :name="chapterIndex"
          :title="`第${chapterIndex + 1}章：${
            chapter.title || '未命名章节'
          }`"
        >
          <template #header-extra>
            <n-space>
              <n-tag size="small"
                >{{ chapter.lessons?.length || 0 }}课时</n-tag
              >
              <n-tag
                v-if="getChapterVideoStats(chapter).videoCount > 0"
                type="info"
                size="small"
              >
                {{ getChapterVideoStats(chapter).videoCount }}个视频
              </n-tag>
              <n-tag
                v-if="getChapterVideoStats(chapter).totalDuration > 0"
                type="success"
                size="small"
              >
                {{
                  getChapterVideoStats(chapter).totalDurationFormatted
                }}
              </n-tag>
              <n-button
                quaternary
                circle
                size="small"
                @click.stop="editChapter(chapterIndex)"
              >
                <template #icon>
                  <n-icon><PencilOutline /></n-icon>
                </template>
              </n-button>
              <n-button
                quaternary
                circle
                size="small"
                @click.stop="removeChapter(chapterIndex)"
              >
                <template #icon>
                  <n-icon><TrashOutline /></n-icon>
                </template>
              </n-button>
            </n-space>
          </template>

          <!-- 章节信息表单 -->
          <n-form
            v-if="chapter.editing"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
          >
            <n-form-item label="章节标题">
              <n-input
                v-model:value="chapter.title"
                placeholder="请输入章节标题"
              />
            </n-form-item>
            <n-form-item label="章节描述">
              <n-input
                v-model:value="chapter.description"
                type="textarea"
                placeholder="请输入章节描述"
                :autosize="{
                  minRows: 2,
                  maxRows: 4,
                }"
              />
            </n-form-item>
            <n-form-item label="排序值">
              <n-input-number
                v-model:value="chapter.sortOrder"
                :min="0"
                placeholder="数值越小越靠前"
              />
            </n-form-item>
            <n-space justify="end">
              <n-button @click="cancelEditChapter(chapterIndex)"
                >取消</n-button
              >
              <n-button
                type="primary"
                @click="saveChapter(chapterIndex)"
              >
                保存章节
              </n-button>
            </n-space>
          </n-form>

          <!-- 课时列表 -->
          <div v-else>
            <div class="lesson-header">
              <div class="lesson-title">课时列表</div>
              <n-button size="small" @click="addLesson(chapterIndex)">
                <template #icon>
                  <n-icon><AddOutline /></n-icon>
                </template>
                添加课时
              </n-button>
            </div>

            <n-list>
              <n-list-item
                v-for="(lesson, lessonIndex) in chapter.lessons"
                :key="lesson.id || `${chapterIndex}-${lessonIndex}`"
              >
                <n-thing
                  :title="`${lessonIndex + 1}. ${
                    lesson.title || '未命名课时'
                  }`"
                  :description="lesson.isFree ? '免费' : '付费'"
                >
                  <template #header-extra>
                    <n-space>
                      <n-button
                        quaternary
                        circle
                        size="small"
                        @click="editLesson(chapterIndex, lessonIndex)"
                      >
                        <template #icon>
                          <n-icon><PencilOutline /></n-icon>
                        </template>
                      </n-button>
                      <n-button
                        quaternary
                        circle
                        size="small"
                        @click="removeLesson(chapterIndex, lessonIndex)"
                      >
                        <template #icon>
                          <n-icon><TrashOutline /></n-icon>
                        </template>
                      </n-button>
                    </n-space>
                  </template>

                  <!-- 如果课时正在编辑 -->
                  <div v-if="lesson.editing" class="lesson-edit-form">
                    <n-form
                      label-placement="left"
                      label-width="80px"
                      require-mark-placement="right-hanging"
                    >
                      <n-form-item label="课时标题">
                        <n-input
                          v-model:value="lesson.title"
                          placeholder="请输入课时标题"
                        />
                      </n-form-item>
                      <n-form-item label="课时描述">
                        <n-input
                          v-model:value="lesson.description"
                          type="textarea"
                          placeholder="请输入课时描述"
                          :autosize="{
                            minRows: 2,
                            maxRows: 4,
                          }"
                        />
                      </n-form-item>
                      <n-grid :cols="2" :x-gap="12">
                        <n-gi>
                          <n-form-item label="视频时长">
                            <n-input-number
                              v-model:value="lesson.duration"
                              :min="0"
                              placeholder="单位：秒"
                            />
                          </n-form-item>
                        </n-gi>
                        <n-gi>
                          <n-form-item label="排序值">
                            <n-input-number
                              v-model:value="lesson.sortOrder"
                              :min="0"
                              placeholder="数值越小越靠前"
                            />
                          </n-form-item>
                        </n-gi>
                      </n-grid>
                      <!-- 视频上传 -->
                      <n-form-item label="课时视频">
                        <OssVideoUpload
                          v-model="lesson.videoUrl"
                          :show-url-input="true"
                          @upload-success="
                            (data) =>
                              handleVideoUploadSuccess(
                                data,
                                chapterIndex,
                                lessonIndex
                              )
                          "
                          @upload-error="handleVideoUploadError"
                          @video-loaded="
                            (data) =>
                              handleVideoLoaded(
                                data,
                                chapterIndex,
                                lessonIndex
                              )
                          "
                          @duration-change="
                            (duration) =>
                              handleDurationChange(
                                duration,
                                chapterIndex,
                                lessonIndex
                              )
                          "
                        />
                      </n-form-item>
                      <n-form-item label="是否免费">
                        <n-switch
                          v-model:value="lesson.isFree"
                          :checked-value="1"
                          :unchecked-value="0"
                        >
                          <template #checked>是</template>
                          <template #unchecked>否</template>
                        </n-switch>
                      </n-form-item>
                      <n-space justify="end">
                        <n-button
                          @click="
                            cancelEditLesson(chapterIndex, lessonIndex)
                          "
                        >
                          取消
                        </n-button>
                        <n-button
                          type="primary"
                          @click="saveLesson(chapterIndex, lessonIndex)"
                        >
                          保存课时
                        </n-button>
                      </n-space>
                    </n-form>
                  </div>
                  <div v-else>
                    <p v-if="lesson.description">
                      {{ lesson.description }}
                    </p>
                    <p v-if="lesson.videoUrl">
                      视频链接：{{ lesson.videoUrl }}
                    </p>
                    <p>时长：{{ formatDuration(lesson.duration) }}</p>
                  </div>
                </n-thing>
              </n-list-item>
              <n-empty
                v-if="!chapter.lessons || !chapter.lessons.length"
                description="暂无课时"
              >
                <template #extra>
                  <n-button
                    size="small"
                    @click="addLesson(chapterIndex)"
                  >
                    添加课时
                  </n-button>
                </template>
              </n-empty>
            </n-list>
          </div>
        </n-collapse-item>
      </n-collapse>

      <n-empty
        v-if="!chapters.length"
        description="暂无章节"
      >
        <template #extra>
          <n-button @click="addChapter">添加章节</n-button>
        </template>
      </n-empty>
    </div>
  </n-card>
</template>

<script setup>
import { computed } from "vue";
import { useMessage, useDialog } from "naive-ui";
import { AddOutline, PencilOutline, TrashOutline } from "@vicons/ionicons5";
import OssVideoUpload from "@/components/VideoUpload/OssVideoUpload.vue";
import { calculateChapterVideoStats } from "@/utils/timeFormat";

const props = defineProps({
  chapters: {
    type: Array,
    required: true,
  },
  expanded: {
    type: Array,
    default: () => [],
  },
  courseId: {
    type: [Number, String],
    default: null,
  },
});

const emit = defineEmits([
  "update:chapters",
  "update:expanded",
  "video-upload-success",
  "video-upload-error",
  "video-loaded",
  "duration-change",
]);

const message = useMessage();
const dialog = useDialog();

// 双向绑定章节展开状态
const expandedChapters = computed({
  get: () => props.expanded,
  set: (value) => emit("update:expanded", value),
});

// 格式化视频时长
const formatDuration = (seconds) => {
  if (!seconds) return "0秒";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  let result = "";
  if (hours > 0) result += `${hours}小时`;
  if (minutes > 0) result += `${minutes}分钟`;
  if (remainingSeconds > 0) result += `${remainingSeconds}秒`;

  return result;
};

// 获取章节视频统计信息
const getChapterVideoStats = (chapter) => {
  return calculateChapterVideoStats(chapter.lessons || []);
};

// 添加章节
const addChapter = () => {
  const newChapterIndex = props.chapters.length;
  const newChapters = [...props.chapters];

  newChapters.push({
    title: "",
    description: "",
    sortOrder: newChapterIndex,
    lessons: [],
    editing: true,
  });

  emit("update:chapters", newChapters);

  // 自动展开新添加的章节
  if (!expandedChapters.value.includes(newChapterIndex)) {
    const newExpanded = [...expandedChapters.value, newChapterIndex];
    emit("update:expanded", newExpanded);
  }
};

// 编辑章节
const editChapter = (index) => {
  const newChapters = [...props.chapters];
  newChapters[index].editing = true;
  emit("update:chapters", newChapters);
};

// 取消编辑章节
const cancelEditChapter = (index) => {
  const newChapters = [...props.chapters];

  if (!newChapters[index].id) {
    // 如果是新添加的章节，则直接移除
    newChapters.splice(index, 1);
  } else {
    newChapters[index].editing = false;
  }

  emit("update:chapters", newChapters);
};

// 保存章节
const saveChapter = (index) => {
  const newChapters = [...props.chapters];
  newChapters[index].editing = false;
  emit("update:chapters", newChapters);
};

// 删除章节
const removeChapter = (index) => {
  dialog.warning({
    title: "确认删除",
    content: "确定要删除此章节吗？包含的所有课时也将被删除。",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      const newChapters = [...props.chapters];
      newChapters.splice(index, 1);
      emit("update:chapters", newChapters);
      message.success("删除成功");
    },
  });
};

// 添加课时
const addLesson = (chapterIndex) => {
  const newChapters = [...props.chapters];

  if (!newChapters[chapterIndex].lessons) {
    newChapters[chapterIndex].lessons = [];
  }

  newChapters[chapterIndex].lessons.push({
    title: "",
    description: "",
    videoUrl: "",
    duration: 0,
    isFree: 0,
    sortOrder: newChapters[chapterIndex].lessons.length,
    editing: true,
  });

  emit("update:chapters", newChapters);
};

// 编辑课时
const editLesson = (chapterIndex, lessonIndex) => {
  const newChapters = [...props.chapters];
  newChapters[chapterIndex].lessons[lessonIndex].editing = true;
  emit("update:chapters", newChapters);
};

// 取消编辑课时
const cancelEditLesson = (chapterIndex, lessonIndex) => {
  const newChapters = [...props.chapters];

  if (!newChapters[chapterIndex].lessons[lessonIndex].id) {
    // 如果是新添加的课时，则直接移除
    newChapters[chapterIndex].lessons.splice(lessonIndex, 1);
  } else {
    newChapters[chapterIndex].lessons[lessonIndex].editing = false;
  }

  emit("update:chapters", newChapters);
};

// 保存课时
const saveLesson = (chapterIndex, lessonIndex) => {
  const newChapters = [...props.chapters];
  newChapters[chapterIndex].lessons[lessonIndex].editing = false;
  emit("update:chapters", newChapters);
};

// 删除课时
const removeLesson = (chapterIndex, lessonIndex) => {
  dialog.warning({
    title: "确认删除",
    content: "确定要删除此课时吗？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      const newChapters = [...props.chapters];
      newChapters[chapterIndex].lessons.splice(lessonIndex, 1);
      emit("update:chapters", newChapters);
      message.success("删除成功");
    },
  });
};

// 处理视频上传成功
const handleVideoUploadSuccess = (fileData, chapterIndex, lessonIndex) => {
  emit("video-upload-success", fileData, chapterIndex, lessonIndex);

  // 自动设置视频时长
  if (fileData.duration && chapterIndex !== undefined && lessonIndex !== undefined) {
    const newChapters = [...props.chapters];
    newChapters[chapterIndex].lessons[lessonIndex].duration = Math.round(fileData.duration);
    emit("update:chapters", newChapters);
  }
};

// 处理视频上传失败
const handleVideoUploadError = (error) => {
  emit("video-upload-error", error);
};

// 处理视频加载完成
const handleVideoLoaded = (videoData, chapterIndex, lessonIndex) => {
  emit("video-loaded", videoData, chapterIndex, lessonIndex);

  // 自动设置视频时长
  if (videoData.duration && chapterIndex !== undefined && lessonIndex !== undefined) {
    const newChapters = [...props.chapters];
    newChapters[chapterIndex].lessons[lessonIndex].duration = Math.round(videoData.duration);
    emit("update:chapters", newChapters);
  }
};

// 处理视频时长变化
const handleDurationChange = (duration, chapterIndex, lessonIndex) => {
  emit("duration-change", duration, chapterIndex, lessonIndex);

  if (duration && chapterIndex !== undefined && lessonIndex !== undefined) {
    const newChapters = [...props.chapters];
    newChapters[chapterIndex].lessons[lessonIndex].duration = Math.round(duration);
    emit("update:chapters", newChapters);
  }
};
</script>

<style lang="scss" scoped>
.chapter-card {
  margin-top: 20px;
}

.chapter-action-bar {
  margin-bottom: 20px;
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.lesson-title {
  font-weight: 500;
  font-size: 16px;
}

.lesson-edit-form {
  margin-top: 10px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}
</style>
