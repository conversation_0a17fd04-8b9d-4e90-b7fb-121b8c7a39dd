<template>
  <n-layout has-sider class="layout-container">
    <!-- 侧边栏 -->
    <n-layout-sider
      collapse-mode="width"
      :collapsed="collapsed"
      :collapsed-width="80"
      :width="260"
      :native-scrollbar="false"
      show-trigger
      @update:collapsed="collapsed = $event"
      class="layout-sider"
    >
      <div class="logo-container">
        <img
          v-if="collapsed"
          src="@/assets/logo-mini.png"
          alt="Logo"
          class="logo-mini"
        />
        <img v-else src="@/assets/logo.png" alt="Logo" class="logo-full" />
      </div>

      <!-- 侧边栏菜单 -->
      <n-menu
        :collapsed="collapsed"
        :collapsed-width="80"
        :collapsed-icon-size="30"
        :value="activeMenu"
        :options="menuOptions"
        :default-expanded-keys="defaultExpandedKeys"
        @update:value="handleMenuChange"
      />
    </n-layout-sider>

    <n-layout>
      <!-- 头部 -->
      <n-layout-header class="layout-header">
        <div class="header-left">
          <n-button quaternary circle @click="collapsed = !collapsed">
            <template #icon>
              <n-icon size="30">
                <MenuOutline />
              </n-icon>
            </template>
          </n-button>
          <n-breadcrumb>
            <n-breadcrumb-item v-for="item in breadcrumbList" :key="item.path">
              {{ item.title }}
            </n-breadcrumb-item>
          </n-breadcrumb>
        </div>
        <div class="header-right">
          <!-- 个人信息下拉菜单 -->
          <n-dropdown :options="userOptions" @select="handleUserSelect">
            <div class="user-info">
              <n-avatar :src="userAvatar" round size="small" />
              <span v-if="!collapsed" class="m-l-10">{{ userName }}</span>
            </div>
          </n-dropdown>
        </div>
      </n-layout-header>

      <!-- 内容区域 -->
      <n-layout-content class="layout-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </n-layout-content>

      <!-- 页脚 -->
      <n-layout-footer class="layout-footer">
        <div class="footer-content">
          鼎峰课堂管理系统 &copy; {{ new Date().getFullYear() }}
        </div>
      </n-layout-footer>
    </n-layout>
  </n-layout>
</template>

<script setup>
import { ref, computed, onMounted, h } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  PersonOutline,
  LogOutOutline,
  MenuOutline,
  HomeOutline,
  PeopleOutline,
  BookOutline,
  SchoolOutline,
  VideocamOutline,
  ChatbubbleEllipsesOutline,
  CallOutline,
  BarChartOutline,
  SettingsOutline,
  ShieldOutline,
} from "@vicons/ionicons5";
import { NIcon, useMessage, useDialog } from "naive-ui";
import { removeToken } from "@/utils/token";
import { logout } from "@/api/auth";

// 路由实例
const router = useRouter();
const route = useRoute();

// 侧边栏状态
const collapsed = ref(false);

// 用户信息
const userName = ref("管理员");
const userAvatar = ref(
  "https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg"
);

// 激活的菜单
const activeMenu = computed(() => route.path);

// 面包屑
const breadcrumbList = computed(() => {
  const list = [];
  let matched = route.matched;

  matched.forEach((item) => {
    if (item.meta && item.meta.title) {
      list.push({
        path: item.path,
        title: item.meta.title,
      });
    }
  });

  return list;
});

// 渲染图标
function renderIcon(icon) {
  return () => h(NIcon, null, { default: () => h(icon) });
}

// 菜单选项
const menuOptions = [
  {
    label: "控制台",
    key: "/dashboard",
    icon: renderIcon(HomeOutline),
  },
  {
    label: "用户管理",
    key: "/user",
    icon: renderIcon(PeopleOutline),
    children: [
      {
        label: "用户列表",
        key: "/user/list",
      },
    ],
  },
  {
    label: "讲师管理",
    key: "/teacher",
    icon: renderIcon(SchoolOutline),
    children: [
      {
        label: "讲师列表",
        key: "/teacher/list",
      },
    ],
  },
  {
    label: "课程管理",
    key: "/course",
    icon: renderIcon(BookOutline),
    children: [
      {
        label: "课程列表",
        key: "/course/list",
      },
    ],
  },
  {
    label: "课程授权",
    key: "/permission",
    icon: renderIcon(ShieldOutline),
    children: [
      {
        label: "课程权限管理",
        key: "/permission/course-access",
      },
      {
        label: "课程权限统计",
        key: "/permission/course-access/statistics",
      },
    ],
  },
  {
    label: "直播推荐",
    key: "/live",
    icon: renderIcon(VideocamOutline),
    children: [
      {
        label: "直播列表",
        key: "/live/list",
      },
      {
        label: "首页推荐",
        key: "/live/homepage",
      },
    ],
  },
  {
    label: "评价管理",
    key: "/review",
    icon: renderIcon(ChatbubbleEllipsesOutline),
    children: [
      {
        label: "评价列表",
        key: "/review/list",
      },
    ],
  },
  {
    label: "客服管理",
    key: "/customer",
    icon: renderIcon(CallOutline),
    children: [
      {
        label: "客服列表",
        key: "/customer/list",
      },
    ],
  },
  {
    label: "统计分析",
    key: "/statistics",
    icon: renderIcon(BarChartOutline),
    children: [
      {
        label: "学习记录",
        key: "/statistics/learn",
      },
      {
        label: "打卡记录",
        key: "/statistics/checkin",
      },
    ],
  },

  {
    label: "系统管理",
    key: "/system",
    icon: renderIcon(SettingsOutline),
    children: [
      {
        label: "标签配置",
        key: "/system/tag-config",
      },
    ],
  },
];

// 计算默认展开的菜单项（只有一个子项的菜单）
const defaultExpandedKeys = computed(() => {
  const keys = [];
  menuOptions.forEach((item) => {
    if (item.children && item.children.length === 1) {
      keys.push(item.key);
    }
  });
  return keys;
});

// 用户下拉菜单选项
const userOptions = [
  {
    label: "个人信息",
    key: "profile",
    icon: renderIcon(PersonOutline),
  },
  {
    label: "退出登录",
    key: "logout",
    icon: renderIcon(LogOutOutline),
  },
];

// 菜单切换
function handleMenuChange(key) {
  router.push(key);
}

// 用户菜单选择
async function handleUserSelect(key) {
  if (key === "logout") {
    try {
      // 调用后端退出登录API
      await logout();
      
      // 使用工具函数清除token（同时清除localStorage和cookie）
      removeToken();
      
      window.$message.success("退出登录成功");
      router.push("/login");
    } catch (error) {
      // 即使后端API失败，也要清除前端token
      removeToken();
      window.$message.warning("退出登录");
      router.push("/login");
    }
  } else if (key === "profile") {
    // 打开个人信息页面
  }
}

onMounted(() => {
  // 可以在这里获取用户信息

  // 设置全局的message和dialog
  window.$message = useMessage();
  window.$dialog = useDialog();
});
</script>

<style lang="scss" scoped>
.layout-container {
  .layout-sider {
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .logo-container {
      height: 80px;
      padding: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-full {
        height: 60px;
      }

      .logo-mini {
        height: 50px;
        width: 50px;
      }
    }
  }

  .layout-header {
    height: 64px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    margin-bottom: -20px;

    .header-left {
      display: flex;
      align-items: center;
    }

    .header-right {
      display: flex;
      align-items: center;

      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 0 12px;
      }
    }
  }

  .layout-content {
    overflow-y: auto;
  }

  .layout-footer {
    padding: 16px;
    text-align: center;
    border-top: 1px solid #f0f0f0;

    .footer-content {
      color: #999;
      font-size: 14px;
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
