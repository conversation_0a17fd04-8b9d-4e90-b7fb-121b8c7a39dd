/**
 * 时间格式化工具函数
 */

/**
 * 将秒数转换为可读的时间格式
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export function formatDuration(seconds) {
  if (!seconds || isNaN(seconds) || seconds <= 0) {
    return "0分钟";
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    if (minutes > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${hours}小时`;
    }
  } else if (minutes > 0) {
    if (remainingSeconds > 0) {
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      return `${minutes}分钟`;
    }
  } else {
    return `${remainingSeconds}秒`;
  }
}

/**
 * 将秒数转换为简洁的时间格式（用于显示）
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export function formatDurationSimple(seconds) {
  if (!seconds || isNaN(seconds) || seconds <= 0) {
    return "0分钟";
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    if (minutes > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${hours}小时`;
    }
  } else {
    return `${minutes}分钟`;
  }
}

/**
 * 将分钟数转换为可读的时间格式
 * @param {number} minutes - 分钟数
 * @returns {string} 格式化后的时间字符串
 */
export function formatMinutes(minutes) {
  if (!minutes || isNaN(minutes) || minutes <= 0) {
    return "0分钟";
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours > 0) {
    if (remainingMinutes > 0) {
      return `${hours}小时${remainingMinutes}分钟`;
    } else {
      return `${hours}小时`;
    }
  } else {
    return `${minutes}分钟`;
  }
}

/**
 * 将时间字符串转换为秒数
 * @param {string} timeStr - 时间字符串，格式如 "1:30:45" 或 "30:45"
 * @returns {number} 秒数
 */
export function parseTimeToSeconds(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return 0;
  }

  const parts = timeStr.split(':').map(part => parseInt(part, 10));
  
  if (parts.length === 3) {
    // HH:MM:SS 格式
    const [hours, minutes, seconds] = parts;
    return hours * 3600 + minutes * 60 + seconds;
  } else if (parts.length === 2) {
    // MM:SS 格式
    const [minutes, seconds] = parts;
    return minutes * 60 + seconds;
  } else if (parts.length === 1) {
    // SS 格式
    return parts[0];
  }
  
  return 0;
}

/**
 * 将秒数转换为 HH:MM:SS 格式
 * @param {number} seconds - 秒数
 * @returns {string} HH:MM:SS 格式的时间字符串
 */
export function formatSecondsToTime(seconds) {
  if (!seconds || isNaN(seconds) || seconds <= 0) {
    return "00:00:00";
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 将秒数转换为 MM:SS 格式
 * @param {number} seconds - 秒数
 * @returns {string} MM:SS 格式的时间字符串
 */
export function formatSecondsToMinutes(seconds) {
  if (!seconds || isNaN(seconds) || seconds <= 0) {
    return "00:00";
  }

  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;

  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 计算章节的视频统计信息
 * @param {Array} lessons - 课时列表
 * @returns {Object} 包含视频数量和总时长的对象
 */
export function calculateChapterVideoStats(lessons) {
  if (!lessons || !Array.isArray(lessons)) {
    return {
      videoCount: 0,
      totalDuration: 0,
      totalDurationFormatted: "0分钟"
    };
  }

  const videoLessons = lessons.filter(lesson => lesson.videoUrl);
  const totalDuration = videoLessons.reduce((sum, lesson) => {
    return sum + (lesson.duration || 0);
  }, 0);

  return {
    videoCount: videoLessons.length,
    totalDuration: totalDuration,
    totalDurationFormatted: formatDurationSimple(totalDuration)
  };
}

/**
 * 计算课程的视频统计信息
 * @param {Array} chapters - 章节列表
 * @returns {Object} 包含视频数量和总时长的对象
 */
export function calculateCourseVideoStats(chapters) {
  if (!chapters || !Array.isArray(chapters)) {
    return {
      videoCount: 0,
      totalDuration: 0,
      totalDurationFormatted: "0分钟"
    };
  }

  let totalVideoCount = 0;
  let totalDuration = 0;

  chapters.forEach(chapter => {
    if (chapter.lessons && Array.isArray(chapter.lessons)) {
      const stats = calculateChapterVideoStats(chapter.lessons);
      totalVideoCount += stats.videoCount;
      totalDuration += stats.totalDuration;
    }
  });

  return {
    videoCount: totalVideoCount,
    totalDuration: totalDuration,
    totalDurationFormatted: formatDurationSimple(totalDuration)
  };
}
