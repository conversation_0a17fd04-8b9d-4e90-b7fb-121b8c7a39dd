/**
 * 文件处理工具类
 * <AUTHOR>
 * @since 2025-01-15
 */

// OSS配置
const OSS_CONFIG = {
  baseUrl: 'https://dianfeng-class.oss-cn-chengdu.aliyuncs.com',
  bucketName: 'dianfeng-class'
};

/**
 * 获取完整的文件访问URL
 * @param {string} filePath - 文件相对路径
 * @returns {string} 完整的文件访问URL
 */
export function getFileUrl(filePath) {
  if (!filePath) {
    return '';
  }
  
  // 如果已经是完整URL，直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }
  
  // 拼接完整URL
  return `${OSS_CONFIG.baseUrl}/${filePath}`;
}

/**
 * 获取文件预览URL（带过期时间的临时URL）
 * @param {string} filePath - 文件相对路径
 * @param {number} expireMinutes - 过期时间（分钟），默认60分钟
 * @returns {Promise<string>} 预览URL
 */
export async function getPreviewUrl(filePath, expireMinutes = 60) {
  if (!filePath) {
    return '';
  }
  
  try {
    // 调用后端API获取预签名URL
    const response = await fetch(`/api/file/presigned-url?fileName=${encodeURIComponent(filePath)}&bucketName=${OSS_CONFIG.bucketName}&expireMinutes=${expireMinutes}`);
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      console.warn('获取预览URL失败，使用默认URL:', result.message);
      return getFileUrl(filePath);
    }
  } catch (error) {
    console.warn('获取预览URL失败，使用默认URL:', error);
    return getFileUrl(filePath);
  }
}

/**
 * 从完整URL中提取文件路径
 * @param {string} fullUrl - 完整的文件URL
 * @returns {string} 文件相对路径
 */
export function extractFilePath(fullUrl) {
  if (!fullUrl) {
    return '';
  }
  
  // 如果不是完整URL，直接返回
  if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
    return fullUrl;
  }
  
  try {
    const url = new URL(fullUrl);
    // 移除开头的斜杠
    return url.pathname.substring(1);
  } catch (error) {
    console.warn('解析URL失败:', error);
    return fullUrl;
  }
}

/**
 * 验证文件类型
 * @param {string} fileName - 文件名
 * @param {string} category - 文件分类
 * @returns {boolean} 是否为有效的文件类型
 */
export function isValidFileType(fileName, category) {
  if (!fileName) {
    return false;
  }
  
  const extension = getFileExtension(fileName).toLowerCase();
  
  const typeMap = {
    image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
    video: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'],
    audio: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'],
    document: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
  };
  
  const allowedTypes = typeMap[category] || [];
  return allowedTypes.includes(extension);
}

/**
 * 获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 文件扩展名
 */
export function getFileExtension(fileName) {
  if (!fileName) {
    return '';
  }
  
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) {
    return '';
  }
  
  return fileName.substring(lastDotIndex + 1);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) {
    return '0 B';
  }
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${units[i]}`;
}

/**
 * 检查文件大小是否符合限制
 * @param {number} fileSize - 文件大小（字节）
 * @param {string} category - 文件分类
 * @returns {boolean} 是否符合大小限制
 */
export function isValidFileSize(fileSize, category) {
  const sizeMap = {
    image: 10 * 1024 * 1024,      // 10MB
    video: 500 * 1024 * 1024,     // 500MB
    audio: 50 * 1024 * 1024,      // 50MB
    document: 20 * 1024 * 1024    // 20MB
  };
  
  const maxSize = sizeMap[category] || 100 * 1024 * 1024; // 默认100MB
  return fileSize <= maxSize;
}

/**
 * 获取文件类型图标
 * @param {string} fileName - 文件名
 * @returns {string} 图标名称
 */
export function getFileIcon(fileName) {
  const extension = getFileExtension(fileName).toLowerCase();
  
  const iconMap = {
    // 图片
    jpg: 'image', jpeg: 'image', png: 'image', gif: 'image', 
    bmp: 'image', webp: 'image', svg: 'image',
    
    // 视频
    mp4: 'video', avi: 'video', mov: 'video', wmv: 'video',
    flv: 'video', webm: 'video', mkv: 'video', m4v: 'video',
    
    // 音频
    mp3: 'audio', wav: 'audio', flac: 'audio', aac: 'audio',
    ogg: 'audio', wma: 'audio', m4a: 'audio',
    
    // 文档
    pdf: 'pdf', doc: 'word', docx: 'word',
    xls: 'excel', xlsx: 'excel',
    ppt: 'powerpoint', pptx: 'powerpoint',
    txt: 'text'
  };
  
  return iconMap[extension] || 'file';
}

export default {
  getFileUrl,
  getPreviewUrl,
  extractFilePath,
  isValidFileType,
  getFileExtension,
  formatFileSize,
  isValidFileSize,
  getFileIcon
};
