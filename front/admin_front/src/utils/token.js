/**
 * Token管理工具
 * 统一处理localStorage和cookie的token读写
 */

const TOKEN_KEY = 'token';
const COOKIE_TOKEN_KEY = 'satoken';

/**
 * 从cookie中获取token
 */
function getCookieToken() {
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === COOKIE_TOKEN_KEY) {
      return value;
    }
  }
  return null;
}

/**
 * 设置cookie
 */
function setCookie(name, value, days = 30) {
  const expires = new Date();
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
  document.cookie = `${name}=${value}; path=/; expires=${expires.toUTCString()}`;
}

/**
 * 删除cookie
 */
function deleteCookie(name) {
  document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
}

/**
 * 获取token（优先从cookie，其次从localStorage）
 */
export function getToken() {
  return getCookieToken() || localStorage.getItem(TOKEN_KEY);
}

/**
 * 设置token（同时设置到localStorage和cookie）
 */
export function setToken(token) {
  // 设置到localStorage
  localStorage.setItem(TOKEN_KEY, token);
  
  // 设置到cookie
  setCookie(COOKIE_TOKEN_KEY, token, 30);
}

/**
 * 清除token（同时清除localStorage和cookie）
 */
export function removeToken() {
  // 清除localStorage
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem('userInfo');

  // 清除cookie
  deleteCookie(COOKIE_TOKEN_KEY);
}

/**
 * 处理登录失效，清除token并跳转到登录页
 */
export function handleLoginExpired(message = '登录已过期，请重新登录') {
  // 清除所有认证信息
  removeToken();

  // 显示提示信息
  if (window.$message) {
    window.$message.warning(message);
  }

  // 使用Vue Router进行跳转（如果可用）
  if (window.$router) {
    window.$router.push('/login');
  } else {
    // 降级使用window.location
    window.location.href = '/#/login';
  }
}

/**
 * 检查是否已登录
 */
export function isLoggedIn() {
  return !!getToken();
}