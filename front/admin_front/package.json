{"name": "dianfeng_admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve --mode development", "dev:test": "vue-cli-service serve --mode test", "dev:staging": "vue-cli-service serve --mode staging", "build": "vue-cli-service build --mode production", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:staging": "vue-cli-service build --mode staging", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"@vicons/ionicons5": "^0.12.0", "@videojs/themes": "^1.0.1", "ali-oss": "^6.23.0", "axios": "^1.4.0", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "echarts": "^5.4.3", "naive-ui": "^2.34.4", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "video.js": "^8.23.3", "vue": "^3.2.13", "vue-router": "^4.2.4", "xgplayer": "^3.0.22"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "code-inspector-plugin": "^0.20.12", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.66.1", "sass-loader": "^13.3.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "core-js", "vue-demi", "yorkie"]}}