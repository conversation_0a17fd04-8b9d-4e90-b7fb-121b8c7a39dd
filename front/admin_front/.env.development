# 开发环境配置
NODE_ENV=development
VUE_APP_ENV=development

# API配置
VUE_APP_API_BASE_URL=http://localhost:8082/api
VUE_APP_API_TIMEOUT=10000

# OSS配置
VUE_APP_OSS_ENDPOINT=https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com
VUE_APP_OSS_BUCKET=dianfeng-class
VUE_APP_OSS_BASE_URL=https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/

# 调试配置
VUE_APP_DEBUG=true
VUE_APP_CONSOLE_LOG=true

# 功能开关
VUE_APP_ENABLE_MOCK=false
VUE_APP_ENABLE_VCONSOLE=true

# 其他配置
VUE_APP_TITLE=鼎峰课堂管理系统 - 开发环境
VUE_APP_VERSION=1.0.0-dev
