import React, {useState} from 'react';
import {Image, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {useRouter} from 'expo-router';
import {LinearGradient} from 'expo-linear-gradient';
import {ArrowLeft, Calendar, CheckCircle, Clock, Play,} from 'lucide-react-native';
import Card from '@/components/ui/Card';

// 模拟学习记录数据
const learningRecords = [
  {
    id: 1,
    date: '2023-10-20',
    dayOfWeek: '周五',
    totalTime: 120, // 分钟
    completedLessons: 3,
    records: [
      {
        id: 101,
        courseTitle: '高中数学核心知识精讲',
        lessonTitle: '第4课：函数与导数',
        thumbnail:
          'https://images.pexels.com/photos/6238297/pexels-photo-6238297.jpeg',
        duration: 45,
        progress: 100,
        completed: true,
        timeSpent: 45,
      },
      {
        id: 102,
        courseTitle: 'JavaScript全栈开发实战',
        lessonTitle: '第2课：ES6核心语法',
        thumbnail:
          'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg',
        duration: 60,
        progress: 100,
        completed: true,
        timeSpent: 65,
      },
      {
        id: 103,
        courseTitle: '英语口语进阶训练营',
        lessonTitle: '第3课：日常对话实践',
        thumbnail:
          'https://images.pexels.com/photos/4778611/pexels-photo-4778611.jpeg',
        duration: 30,
        progress: 35,
        completed: false,
        timeSpent: 10,
      },
    ],
  },
  {
    id: 2,
    date: '2023-10-19',
    dayOfWeek: '周四',
    totalTime: 90,
    completedLessons: 2,
    records: [
      {
        id: 201,
        courseTitle: '高中数学核心知识精讲',
        lessonTitle: '第3课：三角函数',
        thumbnail:
          'https://images.pexels.com/photos/6238297/pexels-photo-6238297.jpeg',
        duration: 45,
        progress: 100,
        completed: true,
        timeSpent: 45,
      },
      {
        id: 202,
        courseTitle: '物理竞赛提高班',
        lessonTitle: '第1课：力学基础',
        thumbnail:
          'https://images.pexels.com/photos/714699/pexels-photo-714699.jpeg',
        duration: 60,
        progress: 100,
        completed: true,
        timeSpent: 55,
      },
    ],
  },
  {
    id: 3,
    date: '2023-10-18',
    dayOfWeek: '周三',
    totalTime: 75,
    completedLessons: 1,
    records: [
      {
        id: 301,
        courseTitle: 'JavaScript全栈开发实战',
        lessonTitle: '第1课：开发环境配置',
        thumbnail:
          'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg',
        duration: 60,
        progress: 100,
        completed: true,
        timeSpent: 65,
      },
      {
        id: 302,
        courseTitle: '设计思维与创新实践',
        lessonTitle: '第1课：设计思维导论',
        thumbnail:
          'https://images.pexels.com/photos/3861943/pexels-photo-3861943.jpeg',
        duration: 40,
        progress: 25,
        completed: false,
        timeSpent: 10,
      },
    ],
  },
];

// 标签类型
type TabType = 'all' | 'completed' | 'inProgress';

export default function LearningRecordsScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<TabType>('all');

  // 渲染日期分组标题
  const renderDateHeader = (item) => (
    <View style={styles.dateHeader}>
      <View style={styles.dateContainer}>
        <Calendar size={16} color="#4080FF" />
        <Text style={styles.dateText}>
          {item.date} {item.dayOfWeek}
        </Text>
      </View>
      <View style={styles.dateStats}>
        <View style={styles.dateStatItem}>
          <Clock size={14} color="#666666" />
          <Text style={styles.dateStatText}>{item.totalTime}分钟</Text>
        </View>
        <View style={styles.dateStatItem}>
          <CheckCircle size={14} color="#52C41A" />
          <Text style={styles.dateStatText}>
            完成{item.completedLessons}课时
          </Text>
        </View>
      </View>
    </View>
  );

  // 渲染学习记录项
  const renderLessonItem = (record) => (
    <Card style={styles.lessonCard} key={record.id}>
      <View style={styles.lessonContainer}>
        <Image
          source={{ uri: record.thumbnail }}
          style={styles.lessonThumbnail}
        />
        <View style={styles.lessonContent}>
          <Text style={styles.courseTitle} numberOfLines={1}>
            {record.courseTitle}
          </Text>
          <Text style={styles.lessonTitle} numberOfLines={1}>
            {record.lessonTitle}
          </Text>

          <View style={styles.lessonStats}>
            <View style={styles.lessonStatItem}>
              <Clock size={12} color="#666666" />
              <Text style={styles.lessonStatText}>
                {record.timeSpent}/{record.duration}分钟
              </Text>
            </View>
            {record.completed ? (
              <View style={styles.completedBadge}>
                <CheckCircle size={12} color="#FFFFFF" />
                <Text style={styles.completedText}>已完成</Text>
              </View>
            ) : (
              <View style={styles.progressBadge}>
                <Text style={styles.progressText}>{record.progress}%</Text>
              </View>
            )}
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressBarBackground}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${record.progress}%` },
                  record.completed && styles.completedProgressBar,
                ]}
              />
            </View>
          </View>
        </View>
        <TouchableOpacity style={styles.playButton}>
          <Play size={18} color="#4080FF" fill="#4080FF" />
        </TouchableOpacity>
      </View>
    </Card>
  );

  // 渲染每天的记录组
  const renderDayGroup = (item) => (
    <View key={item.id} style={styles.dayGroup}>
      {renderDateHeader(item)}
      {item.records.map((record) => renderLessonItem(record))}
    </View>
  );

  // 根据标签过滤记录
  const getFilteredRecords = () => {
    if (activeTab === 'all') {
      return learningRecords;
    }

    // 深拷贝数据以避免修改原始数据
    return learningRecords
      .map((day) => {
        const filteredRecords = day.records.filter((record) =>
          activeTab === 'completed' ? record.completed : !record.completed
        );

        if (filteredRecords.length === 0) {
          return null;
        }

        return {
          ...day,
          records: filteredRecords,
          totalTime: filteredRecords.reduce((sum, r) => sum + r.timeSpent, 0),
          completedLessons: filteredRecords.filter((r) => r.completed).length,
        };
      })
      .filter(Boolean);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <LinearGradient
        colors={['#4080FF', '#6E9CFF']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>学习记录</Text>
          <View style={styles.placeholder}></View>
        </View>

        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>36</Text>
            <Text style={styles.statLabel}>累计课时</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>285</Text>
            <Text style={styles.statLabel}>学习分钟</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>8</Text>
            <Text style={styles.statLabel}>完成课程</Text>
          </View>
        </View>
      </LinearGradient>

      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'all' && styles.activeTabText,
            ]}
          >
            全部
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
          onPress={() => setActiveTab('completed')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'completed' && styles.activeTabText,
            ]}
          >
            已完成
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'inProgress' && styles.activeTab]}
          onPress={() => setActiveTab('inProgress')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'inProgress' && styles.activeTabText,
            ]}
          >
            进行中
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {getFilteredRecords().map(renderDayGroup)}

        {getFilteredRecords().length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无学习记录</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  headerGradient: {
    paddingTop: 45,
    paddingBottom: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
  },
  placeholder: {
    width: 24,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Bold',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: 'NotoSansSC-Regular',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    marginTop: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  activeTab: {
    backgroundColor: '#F0F5FF',
  },
  tabText: {
    fontSize: 14,
    color: '#666666',
    fontFamily: 'NotoSansSC-Medium',
  },
  activeTabText: {
    color: '#4080FF',
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
    marginTop: 10,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 30,
  },
  dayGroup: {
    marginBottom: 20,
  },
  dateHeader: {
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  dateStats: {
    flexDirection: 'row',
    paddingLeft: 24,
  },
  dateStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  dateStatText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
    fontFamily: 'NotoSansSC-Regular',
  },
  lessonCard: {
    marginBottom: 12,
    padding: 0,
    overflow: 'hidden',
    borderRadius: 12,
  },
  lessonContainer: {
    flexDirection: 'row',
    padding: 12,
  },
  lessonThumbnail: {
    width: 75,
    height: 75,
    borderRadius: 8,
  },
  lessonContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
  },
  courseTitle: {
    fontSize: 14,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
    marginBottom: 4,
  },
  lessonTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'NotoSansSC-Medium',
    marginBottom: 8,
  },
  lessonStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  lessonStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lessonStatText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
    fontFamily: 'NotoSansSC-Regular',
  },
  completedBadge: {
    backgroundColor: '#52C41A',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  completedText: {
    fontSize: 11,
    color: '#FFFFFF',
    marginLeft: 4,
    fontFamily: 'NotoSansSC-Medium',
  },
  progressBadge: {
    backgroundColor: '#F0F5FF',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  progressText: {
    fontSize: 11,
    color: '#4080FF',
    fontFamily: 'NotoSansSC-Medium',
  },
  progressContainer: {
    height: 4,
  },
  progressBarBackground: {
    flex: 1,
    height: 4,
    backgroundColor: '#EEEEEE',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4080FF',
    borderRadius: 2,
  },
  completedProgressBar: {
    backgroundColor: '#52C41A',
  },
  playButton: {
    padding: 5,
    alignSelf: 'center',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 14,
    color: '#999999',
    fontFamily: 'NotoSansSC-Regular',
  },
});
