import React from 'react';
import {FlatList, Image, SafeAreaView, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {useRouter} from 'expo-router';
import {LinearGradient} from 'expo-linear-gradient';
import {ArrowLeft, Bookmark, Calendar, Star,} from 'lucide-react-native';
import Card from '@/components/ui/Card';

// 模拟收藏课程数据
const favoriteCourses = [
  {
    id: 1,
    title: '高中数学核心知识精讲',
    teacher: '李明教授',
    thumbnail:
      'https://images.pexels.com/photos/6238297/pexels-photo-6238297.jpeg',
    rating: 4.9,
    reviewCount: 328,
    lastViewTime: '2023-10-15',
    progress: 60,
    tags: ['数学', '高中', '精品课'],
  },
  {
    id: 2,
    title: 'JavaScript全栈开发实战',
    teacher: '张涛',
    thumbnail:
      'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg',
    rating: 4.8,
    reviewCount: 215,
    lastViewTime: '2023-10-18',
    progress: 30,
    tags: ['编程', '前端', '全栈'],
  },
  {
    id: 3,
    title: '英语口语进阶训练营',
    teacher: '<PERSON> <PERSON>',
    thumbnail:
      'https://images.pexels.com/photos/4778611/pexels-photo-4778611.jpeg',
    rating: 4.7,
    reviewCount: 189,
    lastViewTime: '2023-10-20',
    progress: 75,
    tags: ['英语', '口语', '外教'],
  },
  {
    id: 4,
    title: '物理竞赛提高班',
    teacher: '王教授',
    thumbnail:
      'https://images.pexels.com/photos/714699/pexels-photo-714699.jpeg',
    rating: 4.9,
    reviewCount: 156,
    lastViewTime: '2023-10-12',
    progress: 40,
    tags: ['物理', '竞赛', '高阶'],
  },
  {
    id: 5,
    title: '设计思维与创新实践',
    teacher: '刘铭',
    thumbnail:
      'https://images.pexels.com/photos/3861943/pexels-photo-3861943.jpeg',
    rating: 4.6,
    reviewCount: 204,
    lastViewTime: '2023-10-05',
    progress: 90,
    tags: ['设计', '创新', '实践'],
  },
];

export default function FavoritesScreen() {
  const router = useRouter();

  // 渲染收藏课程项
  const renderCourseItem = ({ item }) => (
    <Card style={styles.courseCard}>
      <View style={styles.courseItemContainer}>
        <Image
          source={{ uri: item.thumbnail }}
          style={styles.courseThumbnail}
        />
        <View style={styles.courseContent}>
          <Text style={styles.courseTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.courseTeacher}>{item.teacher}</Text>

          <View style={styles.tagsContainer}>
            {item.tags.map((tag, index) => (
              <View key={index} style={styles.tagBadge}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>

          <View style={styles.courseStats}>
            <View style={styles.ratingStar}>
              <Star size={12} fill="#FFB800" color="#FFB800" />
              <Text style={styles.ratingText}>{item.rating}</Text>
              <Text style={styles.reviewCount}>({item.reviewCount})</Text>
            </View>

            <View style={styles.lastViewed}>
              <Calendar size={12} color="#666666" />
              <Text style={styles.lastViewedText}>{item.lastViewTime}</Text>
            </View>
          </View>

          <View style={styles.progressContainer}>
            <View style={styles.progressBarBackground}>
              <View
                style={[styles.progressBar, { width: `${item.progress}%` }]}
              />
            </View>
            <Text style={styles.progressText}>{item.progress}%</Text>
          </View>
        </View>
        <TouchableOpacity style={styles.bookmarkButton}>
          <Bookmark size={18} color="#FF6B00" fill="#FF6B00" />
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <LinearGradient
        colors={['#4080FF', '#6E9CFF']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>我的收藏</Text>
          <View style={styles.placeholder}></View>
        </View>

        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{favoriteCourses.length}</Text>
            <Text style={styles.statLabel}>收藏课程</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>3</Text>
            <Text style={styles.statLabel}>特训营</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>2</Text>
            <Text style={styles.statLabel}>直播课</Text>
          </View>
        </View>
      </LinearGradient>

      <View style={styles.contentContainer}>
        <FlatList
          data={favoriteCourses}
          renderItem={renderCourseItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  headerGradient: {
    paddingTop: 45,
    paddingBottom: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
  },
  placeholder: {
    width: 24,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Bold',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: 'NotoSansSC-Regular',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  contentContainer: {
    flex: 1,
    marginTop: -20,
  },
  listContainer: {
    padding: 15,
    paddingTop: 25,
  },
  courseCard: {
    marginBottom: 16,
    padding: 0,
    overflow: 'hidden',
    borderRadius: 12,
  },
  courseItemContainer: {
    flexDirection: 'row',
    padding: 12,
  },
  courseThumbnail: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  courseContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
  },
  courseTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'NotoSansSC-Medium',
    marginBottom: 5,
  },
  courseTeacher: {
    fontSize: 13,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
    marginBottom: 6,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 6,
  },
  tagBadge: {
    backgroundColor: '#F0F5FF',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 11,
    color: '#4080FF',
    fontFamily: 'NotoSansSC-Regular',
  },
  courseStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  ratingStar: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    color: '#333333',
    marginLeft: 4,
    fontFamily: 'NotoSansSC-Medium',
  },
  reviewCount: {
    fontSize: 11,
    color: '#999999',
    marginLeft: 2,
    fontFamily: 'NotoSansSC-Regular',
  },
  lastViewed: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lastViewedText: {
    fontSize: 11,
    color: '#666666',
    marginLeft: 4,
    fontFamily: 'NotoSansSC-Regular',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBarBackground: {
    flex: 1,
    height: 4,
    backgroundColor: '#EEEEEE',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4080FF',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 11,
    color: '#666666',
    marginLeft: 8,
    fontFamily: 'NotoSansSC-Regular',
  },
  bookmarkButton: {
    padding: 5,
    alignSelf: 'flex-start',
  },
});
