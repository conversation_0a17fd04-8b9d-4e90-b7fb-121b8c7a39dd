import React, {useEffect} from 'react';
import {SplashScreen, Stack} from 'expo-router';
import {StatusBar} from 'expo-status-bar';
import {useFrameworkReady} from '@/hooks/useFrameworkReady';
import {useFonts} from 'expo-font';
import {NotoSansSC_400Regular, NotoSansSC_500Medium, NotoSansSC_700Bold,} from '@expo-google-fonts/noto-sans-sc';
import ToastProvider from '@/components/common/toast/ToastProvider';
import {PortalProvider} from '@gorhom/portal';

// 防止开屏页自动隐藏
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useFrameworkReady();

  const [fontsLoaded, fontError] = useFonts({
    'NotoSansSC-Regular': NotoSansSC_400Regular,
    'NotoSansSC-Medium': NotoSansSC_500Medium,
    'NotoSansSC-Bold': NotoSansSC_700Bold,
  });

  // 字体加载完成后隐藏开屏页
  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // 如果字体未加载且没有错误，继续显示开屏页
  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <PortalProvider>
      <ToastProvider>
        <Stack
          screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: '#FFFFFF' },
            animation: 'slide_from_right',
          }}
        >
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="dark" />
      </ToastProvider>
    </PortalProvider>
  );
}
