import React, {useState} from 'react';
import {Dimensions, Image, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {Stack, useLocalSearchParams, useRouter} from 'expo-router';
import {
  ArrowLeft,
  CheckCircle,
  ChevronRight,
  Clock,
  Heart,
  Play,
  Share2,
  Star,
  User,
  Users,
} from 'lucide-react-native';
import {LinearGradient} from 'expo-linear-gradient';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import ContactModal from '@/components/common/ContactModal';

// 模拟的课程数据
const courseData = {
  id: '1',
  title: '设计思维与创新方法论',
  description:
    '本课程将教授您如何运用设计思维解决复杂问题，从用户需求出发，进行创新设计和解决方案开发。通过实际案例和项目实践，您将掌握创新设计的核心方法和工具。',
  imageUrl:
    'https://images.pexels.com/photos/3184306/pexels-photo-3184306.jpeg',
  teacher: '李教授',
  teacherTitle: '清华大学设计学院教授',
  teacherAvatar:
    'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
  teacherDescription:
    '李教授拥有20年设计教学经验，曾在多家知名企业担任设计顾问，著有《设计思维》等多部专著。',
  price: '499.00',
  originalPrice: '599.00',
  studentCount: 328,
  rating: 4.8,
  reviewCount: 126,
  duration: '12课时',
  totalTime: '24小时',
  level: '中级',
  tag: '设计',
  publishDate: '2023-06-01',
  updateDate: '2024-03-15',
  outline: [
    {
      id: '1',
      title: '第一章：设计思维概述',
      duration: '2课时',
      lessons: [
        { id: '1-1', title: '什么是设计思维', duration: '45分钟' },
        { id: '1-2', title: '设计思维的五个阶段', duration: '55分钟' },
      ],
    },
    {
      id: '2',
      title: '第二章：用户研究与洞察',
      duration: '3课时',
      lessons: [
        { id: '2-1', title: '用户研究方法', duration: '60分钟' },
        { id: '2-2', title: '用户痛点分析', duration: '50分钟' },
        { id: '2-3', title: '用户画像创建', duration: '45分钟' },
      ],
    },
    {
      id: '3',
      title: '第三章：创意发散与收敛',
      duration: '2课时',
      lessons: [
        { id: '3-1', title: '头脑风暴技巧', duration: '55分钟' },
        { id: '3-2', title: '创意筛选与评估', duration: '50分钟' },
      ],
    },
    {
      id: '4',
      title: '第四章：原型设计与测试',
      duration: '3课时',
      lessons: [
        { id: '4-1', title: '低保真原型设计', duration: '45分钟' },
        { id: '4-2', title: '高保真原型制作', duration: '60分钟' },
        { id: '4-3', title: '用户测试与反馈', duration: '50分钟' },
      ],
    },
    {
      id: '5',
      title: '第五章：设计方案实施',
      duration: '2课时',
      lessons: [
        { id: '5-1', title: '设计方案落地策略', duration: '55分钟' },
        { id: '5-2', title: '设计实施监控与调整', duration: '50分钟' },
      ],
    },
  ],
  reviews: [
    {
      id: '1',
      user: '张同学',
      avatar:
        'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg',
      rating: 5,
      content: '老师讲解非常清晰，案例也很有实用性，学到了很多设计思维的方法！',
      date: '2024-02-15',
    },
    {
      id: '2',
      user: '王同学',
      avatar:
        'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg',
      rating: 4,
      content: '课程内容丰富，但有些章节节奏稍快，需要多看几遍才能完全理解。',
      date: '2024-01-20',
    },
  ],
};

export default function CourseDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [activeTab, setActiveTab] = useState('outline');
  const [expandedChapters, setExpandedChapters] = useState<string[]>([]);
  const [contactModalVisible, setContactModalVisible] = useState(false);

  // 处理章节展开/折叠
  const toggleChapter = (chapterId: string) => {
    if (expandedChapters.includes(chapterId)) {
      setExpandedChapters(expandedChapters.filter((id) => id !== chapterId));
    } else {
      setExpandedChapters([...expandedChapters, chapterId]);
    }
  };

  // 联系方式信息
  const contactInfo = {
    phone: '18888888888',
    wechat: 'dfkt2024',
    qrcode:
      'https://images.pexels.com/photos/4126684/pexels-photo-4126684.jpeg', // 示例二维码图片
  };

  // 渲染课程大纲
  const renderOutline = () => {
    return (
      <View style={styles.tabContent}>
        <Text style={styles.sectionTitle}>课程大纲</Text>
        {courseData.outline.map((chapter) => (
          <Card key={chapter.id} style={styles.chapterCard}>
            <TouchableOpacity
              style={styles.chapterHeader}
              onPress={() => toggleChapter(chapter.id)}
            >
              <View style={styles.chapterInfo}>
                <Text style={styles.chapterTitle}>{chapter.title}</Text>
                <Text style={styles.chapterDuration}>{chapter.duration}</Text>
              </View>
              <ChevronRight
                size={20}
                color="#999"
                style={[
                  styles.chapterArrow,
                  expandedChapters.includes(chapter.id) &&
                    styles.chapterArrowExpanded,
                ]}
              />
            </TouchableOpacity>
            {expandedChapters.includes(chapter.id) && (
              <View style={styles.lessonsList}>
                {chapter.lessons.map((lesson) => (
                  <View key={lesson.id} style={styles.lessonItem}>
                    <View style={styles.lessonIcon}>
                      <Play size={14} color="#4080FF" />
                    </View>
                    <Text style={styles.lessonTitle}>{lesson.title}</Text>
                    <Text style={styles.lessonDuration}>{lesson.duration}</Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        ))}
      </View>
    );
  };

  // 渲染教师信息
  const renderTeacher = () => {
    return (
      <View style={styles.tabContent}>
        <Text style={styles.sectionTitle}>教师介绍</Text>
        <Card style={styles.teacherCard}>
          <View style={styles.teacherHeader}>
            <Image
              source={{ uri: courseData.teacherAvatar }}
              style={styles.teacherAvatar}
            />
            <View style={styles.teacherInfo}>
              <Text style={styles.teacherName}>{courseData.teacher}</Text>
              <Text style={styles.teacherTitle}>{courseData.teacherTitle}</Text>
            </View>
          </View>
          <Text style={styles.teacherDescription}>
            {courseData.teacherDescription}
          </Text>
        </Card>
      </View>
    );
  };

  // 渲染学员评价
  const renderReviews = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.reviewsHeader}>
          <Text style={styles.sectionTitle}>学员评价</Text>
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingScore}>{courseData.rating}</Text>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  color={
                    star <= Math.floor(courseData.rating)
                      ? '#FFAB00'
                      : '#E0E0E0'
                  }
                  fill={
                    star <= Math.floor(courseData.rating) ? '#FFAB00' : 'none'
                  }
                />
              ))}
            </View>
          </View>
        </View>
        {courseData.reviews.map((review) => (
          <Card key={review.id} style={styles.reviewCard}>
            <View style={styles.reviewHeader}>
              <Image
                source={{ uri: review.avatar }}
                style={styles.reviewAvatar}
              />
              <View style={styles.reviewUserInfo}>
                <Text style={styles.reviewUser}>{review.user}</Text>
                <View style={styles.reviewRating}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      size={12}
                      color={star <= review.rating ? '#FFAB00' : '#E0E0E0'}
                      fill={star <= review.rating ? '#FFAB00' : 'none'}
                    />
                  ))}
                </View>
              </View>
              <Text style={styles.reviewDate}>{review.date}</Text>
            </View>
            <Text style={styles.reviewContent}>{review.content}</Text>
          </Card>
        ))}
      </View>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <ContactModal
        visible={contactModalVisible}
        onClose={() => setContactModalVisible(false)}
        contactInfo={contactInfo}
        title="联系购买"
        subtitle="欢迎咨询课程相关信息，我们将为您提供专业解答"
      />
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
        >
          {/* 课程头部信息 */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#FFF" />
            </TouchableOpacity>
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.actionButton}>
                <Heart size={24} color="#FFF" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Share2 size={24} color="#FFF" />
              </TouchableOpacity>
            </View>
            <Image
              source={{ uri: courseData.imageUrl }}
              style={styles.courseImage}
            />
            <LinearGradient
              colors={['rgba(0,0,0,0.7)', 'transparent']}
              style={styles.imageOverlay}
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
            />
          </View>

          <View style={styles.courseInfo}>
            <View style={styles.tagContainer}>
              <Text style={styles.tag}>{courseData.tag}</Text>
              <Text style={styles.level}>{courseData.level}</Text>
            </View>

            <Text style={styles.courseTitle}>{courseData.title}</Text>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <User size={16} color="#666" />
                <Text style={styles.statText}>{courseData.teacher}</Text>
              </View>
              <View style={styles.statItem}>
                <Users size={16} color="#666" />
                <Text style={styles.statText}>
                  {courseData.studentCount}人已学
                </Text>
              </View>
              <View style={styles.statItem}>
                <Clock size={16} color="#666" />
                <Text style={styles.statText}>{courseData.duration}</Text>
              </View>
            </View>

            <View style={styles.priceContainer}>
              <Text style={styles.price}>¥{courseData.price}</Text>
              {courseData.originalPrice && (
                <Text style={styles.originalPrice}>
                  ¥{courseData.originalPrice}
                </Text>
              )}
            </View>

            <Card style={styles.descriptionCard}>
              <Text style={styles.descriptionTitle}>课程介绍</Text>
              <Text style={styles.description}>{courseData.description}</Text>
              <View style={styles.courseFeatures}>
                <View style={styles.featureItem}>
                  <CheckCircle size={16} color="#4080FF" />
                  <Text style={styles.featureText}>随时学习，永久观看</Text>
                </View>
                <View style={styles.featureItem}>
                  <CheckCircle size={16} color="#4080FF" />
                  <Text style={styles.featureText}>含课程资料下载</Text>
                </View>
                <View style={styles.featureItem}>
                  <CheckCircle size={16} color="#4080FF" />
                  <Text style={styles.featureText}>完课后获得证书</Text>
                </View>
              </View>
            </Card>

            <View style={styles.tabsContainer}>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === 'outline' && styles.activeTab,
                ]}
                onPress={() => setActiveTab('outline')}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === 'outline' && styles.activeTabText,
                  ]}
                >
                  课程大纲
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === 'teacher' && styles.activeTab,
                ]}
                onPress={() => setActiveTab('teacher')}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === 'teacher' && styles.activeTabText,
                  ]}
                >
                  教师介绍
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tab,
                  activeTab === 'reviews' && styles.activeTab,
                ]}
                onPress={() => setActiveTab('reviews')}
              >
                <Text
                  style={[
                    styles.tabText,
                    activeTab === 'reviews' && styles.activeTabText,
                  ]}
                >
                  学员评价
                </Text>
              </TouchableOpacity>
            </View>

            {activeTab === 'outline' && renderOutline()}
            {activeTab === 'teacher' && renderTeacher()}
            {activeTab === 'reviews' && renderReviews()}
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <Button
            title="立即购买"
            onPress={() => setContactModalVisible(true)}
            style={styles.buyButton}
          />
        </View>
      </SafeAreaView>
    </>
  );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  container: {
    flex: 1,
  },
  header: {
    height: 220,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtons: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
    flexDirection: 'row',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  courseImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  courseInfo: {
    flex: 1,
    padding: 16,
  },
  tagContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  tag: {
    backgroundColor: '#4080FF',
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 8,
    overflow: 'hidden',
  },
  level: {
    backgroundColor: '#FFAB00',
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  courseTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
    fontFamily: 'NotoSansSC-Bold',
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  statText: {
    fontSize: 13,
    color: '#666',
    marginLeft: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B00',
    fontFamily: 'NotoSansSC-Bold',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  descriptionCard: {
    padding: 16,
    marginBottom: 20,
  },
  descriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
    marginBottom: 16,
  },
  courseFeatures: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  tab: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#4080FF',
  },
  tabText: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#4080FF',
    fontWeight: '600',
  },
  tabContent: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    fontFamily: 'NotoSansSC-Medium',
  },
  chapterCard: {
    marginBottom: 12,
    padding: 0,
    overflow: 'hidden',
  },
  chapterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
  },
  chapterInfo: {
    flex: 1,
  },
  chapterTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  chapterDuration: {
    fontSize: 12,
    color: '#999',
  },
  chapterArrow: {
    transform: [{ rotate: '0deg' }],
  },
  chapterArrowExpanded: {
    transform: [{ rotate: '90deg' }],
  },
  lessonsList: {
    backgroundColor: '#F9FAFC',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  lessonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  lessonIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#EBF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  lessonTitle: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  lessonDuration: {
    fontSize: 12,
    color: '#999',
  },
  teacherCard: {
    padding: 16,
  },
  teacherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  teacherAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  teacherInfo: {
    flex: 1,
  },
  teacherName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
    fontFamily: 'NotoSansSC-Medium',
  },
  teacherTitle: {
    fontSize: 13,
    color: '#666',
  },
  teacherDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingScore: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFAB00',
    marginRight: 8,
  },
  starsContainer: {
    flexDirection: 'row',
  },
  reviewCard: {
    padding: 16,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  reviewAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
  },
  reviewUserInfo: {
    flex: 1,
  },
  reviewUser: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  reviewRating: {
    flexDirection: 'row',
  },
  reviewDate: {
    fontSize: 12,
    color: '#999',
  },
  reviewContent: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  footer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  buyButton: {
    backgroundColor: '#FF6B00',
  },
});
