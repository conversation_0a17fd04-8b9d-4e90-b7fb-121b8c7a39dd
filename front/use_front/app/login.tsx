import React, {useState} from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {useRouter} from 'expo-router';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {ArrowLeft, Eye, EyeOff} from 'lucide-react-native';
import {showGlobalToast} from '@/components/common/toast/ToastProvider';

export default function LoginScreen() {
  const router = useRouter();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = () => {
    if (!username.trim() || !password.trim()) {
      setError('请输入账号和密码');
      return;
    }

    // 开始加载状态
    setLoading(true);

    // 模拟网络请求延迟
    setTimeout(() => {
      // 清除错误
      setError('');

      // 在本地存储中保存用户信息
      const userInfo = {
        name: '张小鹿',
        avatarUrl:
          'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
        level: '青少年',
        id: '12345678',
        role: '高中一年级',
        school: '未来科技中学',
        isLoggedIn: true,
      };

      // 保存用户信息到全局变量
      global.userInfo = userInfo;

      // 登录成功提示
      showGlobalToast({
        message: '登录成功',
        type: 'success',
        duration: 2000,
        position: 'top',
      });

      // 结束加载状态
      setLoading(false);

      // 登录成功后返回个人页面
      setTimeout(() => {
        router.back();
      }, 500);
    }, 1000); // 模拟1秒的网络延迟
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <View style={styles.logoContainer}>
            {/* <Image
              source={{ uri: 'https://reactnative.dev/img/tiny_logo.png' }}
              style={styles.logo}
            /> */}
            <Text style={styles.logoText}>鼎峰课堂</Text>
          </View>

          <Card style={styles.formCard}>
            <Text style={styles.title}>账号登录</Text>

            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            <View style={styles.inputContainer}>
              <Text style={styles.label}>账号</Text>
              <TextInput
                style={styles.input}
                placeholder="请输入您的账号"
                placeholderTextColor="#999"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>密码</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="请输入您的密码"
                  placeholderTextColor="#999"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOff size={20} color="#666" />
                  ) : (
                    <Eye size={20} color="#666" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity style={styles.forgotPassword}>
              <Text style={styles.forgotPasswordText}>忘记密码?</Text>
            </TouchableOpacity>

            <Button
              title="登录"
              onPress={handleLogin}
              style={styles.loginButton}
              size="lg"
              loading={loading}
            />

            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>还没有账号? </Text>
              <TouchableOpacity onPress={() => router.push('/register')}>
                <Text style={styles.registerLink}>立即注册</Text>
              </TouchableOpacity>
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 15,
  },
  logoText: {
    marginTop: 12,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4080FF',
    fontFamily: 'NotoSansSC-Bold',
  },
  formCard: {
    padding: 24,
    borderRadius: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
    fontFamily: 'NotoSansSC-Bold',
  },
  errorText: {
    color: '#FF3B30',
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 14,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    color: '#333',
    marginBottom: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  input: {
    backgroundColor: '#F5F7FA',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333',
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333',
  },
  eyeIcon: {
    padding: 10,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 25,
  },
  forgotPasswordText: {
    color: '#4080FF',
    fontSize: 14,
  },
  loginButton: {
    marginBottom: 25,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  registerText: {
    color: '#666',
    fontSize: 14,
  },
  registerLink: {
    color: '#4080FF',
    fontSize: 14,
    fontWeight: '500',
  },
});
