import React, {useEffect, useState} from 'react';
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useRouter} from 'expo-router';
import {LinearGradient} from 'expo-linear-gradient';
import {
  AlertCircle,
  ArrowLeft,
  Award,
  BadgeCheck,
  BookOpen,
  Brain,
  Calendar,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Clock,
  FileText,
  Medal,
  Star,
  Trophy,
  Zap,
} from 'lucide-react-native';
import Card from '@/components/ui/Card';

const { width } = Dimensions.get('window');

// 自定义进度条组件
const ProgressBar = ({ progress, color, label, value, maxValue }) => {
  const progressAnim = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 1000,
      useNativeDriver: false,
      easing: Easing.out(Easing.cubic),
    }).start();
  }, [progress]);

  return (
    <View style={styles.progressBarContainer}>
      <View style={styles.progressInfo}>
        <Text style={styles.progressLabel}>{label}</Text>
        <Text style={styles.progressValue}>
          {value}/{maxValue}
        </Text>
      </View>
      <View style={styles.progressTrack}>
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: progressAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%'],
              }),
              backgroundColor: color,
            },
          ]}
        />
      </View>
    </View>
  );
};

// 拟人化学习助手气泡组件
const LearningAssistantBubble = ({ message, position = 'right' }) => (
  <View
    style={[
      styles.bubbleContainer,
      position === 'left' ? styles.bubbleLeft : styles.bubbleRight,
    ]}
  >
    {position === 'left' && (
      <Image
        source={{
          uri: 'https://images.pexels.com/photos/3760778/pexels-photo-3760778.jpeg',
        }}
        style={styles.assistantAvatar}
      />
    )}
    <View
      style={[
        styles.bubble,
        position === 'left' ? styles.bubbleLeftArrow : styles.bubbleRightArrow,
      ]}
    >
      <Text style={styles.bubbleText}>{message}</Text>
    </View>
    {position === 'right' && (
      <Image
        source={{
          uri: 'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
        }}
        style={styles.assistantAvatar}
      />
    )}
  </View>
);

// 成就卡片组件
const AchievementCard = ({ title, description, icon, color, unlocked }) => {
  const IconComponent = icon;
  return (
    <Card
      style={[styles.achievementCard, !unlocked && styles.lockedAchievement]}
    >
      <View
        style={[
          styles.achievementIconContainer,
          { backgroundColor: color + '20' },
        ]}
      >
        <IconComponent size={24} color={color} />
        {!unlocked && (
          <View style={styles.lockOverlay}>
            <AlertCircle size={16} color="#999999" />
          </View>
        )}
      </View>
      <View style={styles.achievementContent}>
        <Text style={styles.achievementTitle}>{title}</Text>
        <Text style={styles.achievementDescription} numberOfLines={2}>
          {description}
        </Text>
      </View>
      {unlocked && (
        <View style={styles.achievementBadge}>
          <BadgeCheck size={16} color="#52C41A" fill="#FFFFFF" />
        </View>
      )}
    </Card>
  );
};

// 学习图表组件
const LearningChart = ({ data, title }) => {
  const maxValue = Math.max(...data.values);

  return (
    <View style={styles.chartContainer}>
      <Text style={styles.chartTitle}>{title}</Text>
      <View style={styles.chartContent}>
        {data.values.map((value, index) => {
          const barHeight = (value / maxValue) * 120;
          return (
            <View key={index} style={styles.chartBarContainer}>
              <Animated.View
                style={[
                  styles.chartBar,
                  {
                    height: barHeight,
                    backgroundColor:
                      index === data.highlightIndex ? '#FF6B00' : '#4080FF',
                  },
                ]}
              />
              <Text style={styles.chartBarLabel}>{data.labels[index]}</Text>
              {index === data.highlightIndex && (
                <View style={styles.chartBarHighlight}>
                  <Text style={styles.chartBarHighlightText}>{value}分钟</Text>
                </View>
              )}
            </View>
          );
        })}
      </View>
    </View>
  );
};

// 推荐课程卡片组件
const RecommendedCourseCard = ({ course }) => (
  <Card style={styles.recommendedCourseCard}>
    <Image source={{ uri: course.thumbnail }} style={styles.courseImage} />
    <View style={styles.courseInfoContainer}>
      <View style={styles.courseTagContainer}>
        <Text style={styles.courseTag}>{course.category}</Text>
      </View>
      <Text style={styles.courseTitle} numberOfLines={2}>
        {course.title}
      </Text>
      <View style={styles.courseStats}>
        <View style={styles.courseRating}>
          <Star size={14} color="#FFB800" fill="#FFB800" />
          <Text style={styles.courseRatingText}>{course.rating}</Text>
        </View>
        <Text style={styles.courseStudents}>{course.students}人学习</Text>
      </View>
    </View>
  </Card>
);

// 模拟数据 - 学习报告
const studyReportData = {
  totalLearningTime: 3280, // 总学习时长（分钟）
  weeklyLearningTime: 420, // 本周学习时长（分钟）
  dailyAverage: 60, // 日均学习时长（分钟）
  continuousCheckinDays: 16, // 连续打卡天数
  totalCheckinDays: 45, // 累计打卡天数
  completedCourses: 8, // 已完成课程数
  completedLessons: 56, // 已完成课时数
  totalCourses: 12, // 总课程数
  inProgressCourses: 4, // 进行中课程数
  starPoints: 380, // 学习星点
  weekdayLearningTime: [45, 60, 90, 75, 70, 40, 40], // 一周各天学习时长
  monthlyLearningTime: [240, 310, 280, 420], // 最近四周学习时长

  // 学习强度评级: 1-5 (1:较弱, 5:很强)
  learningIntensity: 4,

  // 学习专注度评级: 1-5 (1:较差, 5:很好)
  learningFocus: 3,

  // 学习持续性评级: 1-5 (1:较差, 5:很好)
  learningConsistency: 5,

  // 当前学习课程进度
  currentCourses: [
    {
      id: 1,
      title: '高中数学核心知识精讲',
      progress: 0.75,
      completedLessons: 9,
      totalLessons: 12,
      color: '#4080FF',
    },
    {
      id: 2,
      title: 'JavaScript全栈开发实战',
      progress: 0.45,
      completedLessons: 5,
      totalLessons: 11,
      color: '#FF6B00',
    },
    {
      id: 3,
      title: '英语口语进阶训练营',
      progress: 0.62,
      completedLessons: 5,
      totalLessons: 8,
      color: '#52C41A',
    },
    {
      id: 4,
      title: '物理竞赛提高班',
      progress: 0.25,
      completedLessons: 2,
      totalLessons: 8,
      color: '#722ED1',
    },
  ],

  // 学习成就
  achievements: [
    {
      id: 1,
      title: '学习启航',
      description: '完成第一门课程的学习',
      icon: BookOpen,
      color: '#4080FF',
      unlocked: true,
    },
    {
      id: 2,
      title: '坚持不懈',
      description: '连续打卡15天',
      icon: Calendar,
      color: '#52C41A',
      unlocked: true,
    },
    {
      id: 3,
      title: '知识博览',
      description: '学习3个不同类别的课程',
      icon: Brain,
      color: '#722ED1',
      unlocked: true,
    },
    {
      id: 4,
      title: '学习达人',
      description: '单日学习时长超过2小时',
      icon: Medal,
      color: '#FF6B00',
      unlocked: true,
    },
    {
      id: 5,
      title: '精通大师',
      description: '完成10门课程的学习',
      icon: Trophy,
      color: '#FAAD14',
      unlocked: false,
    },
    {
      id: 6,
      title: '全速前进',
      description: '一周内完成一门课程',
      icon: Zap,
      color: '#13C2C2',
      unlocked: false,
    },
  ],

  // 学习建议
  learningTips: [
    '根据您的学习数据，建议增加物理学科的学习时间',
    '您在周三和周四的学习效率最高，可以安排较难的课程在这两天',
    '建议您尝试番茄工作法来提高学习专注度',
    '已经连续打卡16天，继续保持！',
  ],
};

// 模拟数据 - 推荐课程
const recommendedCourses = [
  {
    id: 1,
    title: '高考物理重难点突破',
    thumbnail:
      'https://images.pexels.com/photos/3729557/pexels-photo-3729557.jpeg',
    category: '物理',
    rating: 4.9,
    students: 1280,
  },
  {
    id: 2,
    title: 'Python数据分析实战',
    thumbnail:
      'https://images.pexels.com/photos/1181671/pexels-photo-1181671.jpeg',
    category: '编程',
    rating: 4.8,
    students: 2150,
  },
  {
    id: 3,
    title: '高中英语写作进阶',
    thumbnail:
      'https://images.pexels.com/photos/256417/pexels-photo-256417.jpeg',
    category: '英语',
    rating: 4.7,
    students: 1820,
  },
];

// 学习报告页面组件
export default function LearningReportScreen() {
  const router = useRouter();
  const [expandedSection, setExpandedSection] = useState('progress');

  // 动画值
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(50)).current;

  // 启动进入动画
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // 周学习时长数据
  const weeklyChartData = {
    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    values: studyReportData.weekdayLearningTime,
    highlightIndex: 2, // 高亮显示周三
  };

  // 月学习时长数据
  const monthlyChartData = {
    labels: ['第1周', '第2周', '第3周', '本周'],
    values: studyReportData.monthlyLearningTime,
    highlightIndex: 3, // 高亮显示本周
  };

  // 切换展开/折叠区域
  const toggleSection = (section) => {
    if (expandedSection === section) {
      setExpandedSection('');
    } else {
      setExpandedSection(section);
    }
  };

  // 渲染区域标题和切换按钮
  const renderSectionHeader = (title, section, icon) => {
    const IconComponent = icon;
    const isExpanded = expandedSection === section;

    return (
      <TouchableOpacity
        style={styles.sectionHeader}
        onPress={() => toggleSection(section)}
      >
        <View style={styles.sectionHeaderLeft}>
          <IconComponent size={20} color="#4080FF" />
          <Text style={styles.sectionTitle}>{title}</Text>
        </View>
        {isExpanded ? (
          <ChevronUp size={20} color="#666666" />
        ) : (
          <ChevronDown size={20} color="#666666" />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <LinearGradient
        colors={['#4080FF', '#6E9CFF']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <ArrowLeft size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>学习报告</Text>
          <TouchableOpacity style={styles.shareButton}>
            <FileText size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.summaryContainer}>
          <View style={styles.summaryLeft}>
            <View style={styles.mainStatCard}>
              <Clock size={20} color="#FFFFFF" />
              <Text style={styles.mainStatLabel}>总学习时长</Text>
              <Text style={styles.mainStatValue}>
                {Math.floor(studyReportData.totalLearningTime / 60)}小时
                {studyReportData.totalLearningTime % 60}分钟
              </Text>
            </View>
          </View>

          <View style={styles.summarySeparator} />

          <View style={styles.summaryRight}>
            <View style={styles.subStatRow}>
              <View style={styles.subStatItem}>
                <Calendar size={16} color="#FFFFFF" />
                <Text style={styles.subStatValue}>
                  {studyReportData.continuousCheckinDays}天
                </Text>
                <Text style={styles.subStatLabel}>连续打卡</Text>
              </View>
              <View style={styles.subStatItem}>
                <Award size={16} color="#FFFFFF" />
                <Text style={styles.subStatValue}>
                  {studyReportData.completedCourses}
                </Text>
                <Text style={styles.subStatLabel}>完成课程</Text>
              </View>
            </View>
            <View style={styles.subStatRow}>
              <View style={styles.subStatItem}>
                <BookOpen size={16} color="#FFFFFF" />
                <Text style={styles.subStatValue}>
                  {studyReportData.completedLessons}
                </Text>
                <Text style={styles.subStatLabel}>完成课时</Text>
              </View>
              <View style={styles.subStatItem}>
                <Star size={16} color="#FFFFFF" />
                <Text style={styles.subStatValue}>
                  {studyReportData.starPoints}
                </Text>
                <Text style={styles.subStatLabel}>学习星点</Text>
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>

      <Animated.ScrollView
        style={[
          styles.contentContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* 拟人化学习助手 */}
        <LearningAssistantBubble
          message="你本周学习时间比上周增加了25%，继续保持！💪"
          position="left"
        />

        {/* 学习进度区域 */}
        <Card style={styles.sectionCard}>
          {renderSectionHeader('学习进度追踪', 'progress', BookOpen)}

          {expandedSection === 'progress' && (
            <View style={styles.sectionContent}>
              <Text style={styles.sectionSubtitle}>当前学习的课程</Text>

              {studyReportData.currentCourses.map((course) => (
                <ProgressBar
                  key={course.id}
                  progress={course.progress}
                  color={course.color}
                  label={course.title}
                  value={course.completedLessons}
                  maxValue={course.totalLessons}
                />
              ))}

              <TouchableOpacity style={styles.viewMoreButton}>
                <Text style={styles.viewMoreText}>查看全部课程</Text>
                <ChevronRight size={16} color="#4080FF" />
              </TouchableOpacity>
            </View>
          )}
        </Card>

        {/* 学习时间分析区域 */}
        <Card style={styles.sectionCard}>
          {renderSectionHeader('学习时间分析', 'timeAnalysis', Clock)}

          {expandedSection === 'timeAnalysis' && (
            <View style={styles.sectionContent}>
              <View style={styles.learningTimeStatsRow}>
                <View style={styles.learningTimeStat}>
                  <Text style={styles.learningTimeStatValue}>
                    {studyReportData.weeklyLearningTime}
                  </Text>
                  <Text style={styles.learningTimeStatLabel}>
                    本周学习(分钟)
                  </Text>
                </View>
                <View style={styles.learningTimeStatDivider} />
                <View style={styles.learningTimeStat}>
                  <Text style={styles.learningTimeStatValue}>
                    {studyReportData.dailyAverage}
                  </Text>
                  <Text style={styles.learningTimeStatLabel}>
                    日均学习(分钟)
                  </Text>
                </View>
              </View>

              <Text style={styles.chartSectionTitle}>本周学习时间分布</Text>
              <LearningChart
                data={weeklyChartData}
                title="每日学习时长(分钟)"
              />

              <Text style={styles.chartSectionTitle}>本月学习时间趋势</Text>
              <LearningChart
                data={monthlyChartData}
                title="每周学习时长(分钟)"
              />

              <LearningAssistantBubble
                message="你在周三的学习时间最长，学习效率也最高，建议安排重要内容在这天学习！📚"
                position="right"
              />
            </View>
          )}
        </Card>


        {/* 推荐课程区域 */}
        <Card style={styles.sectionCard}>
          {renderSectionHeader('推荐课程', 'recommendations', BookOpen)}

          {expandedSection === 'recommendations' && (
            <View style={styles.sectionContent}>
              <Text style={styles.sectionSubtitle}>根据你的学习习惯推荐</Text>

              <View style={styles.recommendedCoursesContainer}>
                {recommendedCourses.map((course) => (
                  <RecommendedCourseCard key={course.id} course={course} />
                ))}
              </View>

              <TouchableOpacity style={styles.viewMoreButton}>
                <Text style={styles.viewMoreText}>浏览更多课程</Text>
                <ChevronRight size={16} color="#4080FF" />
              </TouchableOpacity>
            </View>
          )}
        </Card>
      </Animated.ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // 基础样式
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  headerGradient: {
    paddingTop: 45,
    paddingBottom: 30,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
  },
  shareButton: {
    padding: 5,
  },

  // 顶部统计摘要
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  summaryLeft: {
    flex: 1,
    justifyContent: 'center',
  },
  summarySeparator: {
    width: 1,
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 20,
  },
  summaryRight: {
    flex: 1,
    justifyContent: 'center',
  },
  mainStatCard: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
  },
  mainStatLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 8,
    fontFamily: 'NotoSansSC-Regular',
  },
  mainStatValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 4,
    fontFamily: 'NotoSansSC-Bold',
  },
  subStatRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  subStatItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 10,
    marginHorizontal: 4,
  },
  subStatValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 4,
    fontFamily: 'NotoSansSC-Bold',
  },
  subStatLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 2,
    fontFamily: 'NotoSansSC-Regular',
  },

  // 内容区域
  contentContainer: {
    flex: 1,
    marginTop: -15,
  },
  scrollContent: {
    padding: 15,
    paddingBottom: 30,
  },

  // 区域卡片样式
  sectionCard: {
    marginBottom: 15,
    padding: 0,
    overflow: 'hidden',
    borderRadius: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  sectionContent: {
    padding: 15,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
    marginBottom: 12,
    fontFamily: 'NotoSansSC-Medium',
  },

  // 进度条组件样式
  progressBarContainer: {
    marginBottom: 15,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  progressLabel: {
    fontSize: 14,
    color: '#333333',
    fontFamily: 'NotoSansSC-Regular',
  },
  progressValue: {
    fontSize: 12,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
  },
  progressTrack: {
    height: 8,
    backgroundColor: '#F5F5F5',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },

  // 查看更多按钮
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
    paddingVertical: 8,
  },
  viewMoreText: {
    fontSize: 14,
    color: '#4080FF',
    marginRight: 4,
    fontFamily: 'NotoSansSC-Regular',
  },

  // 学习时间分析样式
  learningTimeStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  learningTimeStat: {
    alignItems: 'center',
  },
  learningTimeStatValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#4080FF',
    fontFamily: 'NotoSansSC-Bold',
  },
  learningTimeStatLabel: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
    fontFamily: 'NotoSansSC-Regular',
  },
  learningTimeStatDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#EEEEEE',
  },

  // 图表样式
  chartSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 15,
    fontFamily: 'NotoSansSC-Medium',
  },
  chartContainer: {
    marginBottom: 20,
  },
  chartTitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 15,
    textAlign: 'center',
    fontFamily: 'NotoSansSC-Regular',
  },
  chartContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 150,
    paddingTop: 10,
  },
  chartBarContainer: {
    alignItems: 'center',
    width: (width - 90) / 7,
    position: 'relative',
  },
  chartBar: {
    width: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  chartBarLabel: {
    fontSize: 12,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
  },
  chartBarHighlight: {
    position: 'absolute',
    top: -25,
    backgroundColor: '#FF6B00',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  chartBarHighlightText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
  },

  // 成就样式
  achievementsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  achievementCard: {
    width: '48%',
    marginBottom: 15,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  lockedAchievement: {
    opacity: 0.6,
  },
  achievementIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    position: 'relative',
  },
  achievementContent: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
    fontFamily: 'NotoSansSC-Medium',
  },
  achievementDescription: {
    fontSize: 12,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
  },
  lockOverlay: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
  },
  achievementBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
  },

  // 学习建议样式
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
    backgroundColor: '#FFFBF0',
    borderRadius: 8,
    padding: 12,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
    marginLeft: 10,
    lineHeight: 20,
    fontFamily: 'NotoSansSC-Regular',
  },

  // 学习能力评估样式
  assessmentItem: {
    marginBottom: 20,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 15,
  },
  assessmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  assessmentTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'NotoSansSC-Medium',
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  assessmentDescription: {
    fontSize: 13,
    color: '#666666',
    lineHeight: 18,
    fontFamily: 'NotoSansSC-Regular',
  },

  // 助手气泡样式
  bubbleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 20,
    marginTop: 20,
  },
  bubbleLeft: {
    justifyContent: 'flex-start',
  },
  bubbleRight: {
    justifyContent: 'flex-end',
  },
  assistantAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  bubble: {
    maxWidth: '75%',
    padding: 12,
    borderRadius: 18,
    marginHorizontal: 8,
  },
  bubbleLeftArrow: {
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 0,
  },
  bubbleRightArrow: {
    backgroundColor: '#E8F3FF',
    borderBottomRightRadius: 0,
  },
  bubbleText: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
    fontFamily: 'NotoSansSC-Regular',
  },

  // 推荐课程卡片样式
  recommendedCourseCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  courseImage: {
    width: 80,
    height: 80,
    borderRadius: 4,
    marginRight: 12,
  },
  courseInfoContainer: {
    flex: 1,
  },
  courseTagContainer: {
    backgroundColor: '#FFB800',
    borderRadius: 4,
    padding: 4,
    marginBottom: 8,
  },
  courseTag: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  courseStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  courseRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  courseRatingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 4,
    fontFamily: 'NotoSansSC-Medium',
  },
  courseStudents: {
    fontSize: 14,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
  },

  // 推荐课程区域样式
  recommendedCoursesContainer: {
    marginBottom: 20,
  },
});
