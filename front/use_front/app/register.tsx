import React, {useEffect, useState} from 'react';
import {
  KeyboardAvoidingView,
  Modal,
  Picker,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {useRouter} from 'expo-router';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import {ArrowLeft, Eye, EyeOff, MapPin, User,} from 'lucide-react-native';
import {showGlobalToast} from '@/components/common/toast/ToastProvider';
// 导入中国地区数据
import locationData from '@/assets/data/location.json';

// 添加类型定义
type LocationItem = {
  code: string;
  name: string;
  cities?: {
    [key: string]: {
      code: string;
      name: string;
      districts: {
        [key: string]: string;
      };
    };
  };
};

type DistrictItem = {
  code: string;
  name: string;
};

type LocationPickerProps = {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: LocationInfo) => void;
};

type LocationInfo = {
  province: string;
  city: string;
  district: string;
  school: string;
};

type UserInfoPickerProps = {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: UserInfoData) => void;
};

type UserInfoData = {
  ageGroup: string;
  subjects: string[];
  grade: string;
};

// 地区选择弹窗组件
const LocationPicker = ({
  visible,
  onClose,
  onSubmit,
}: LocationPickerProps) => {
  const [provinceCode, setProvinceCode] = useState('');
  const [provinceName, setProvinceName] = useState('');
  const [cityCode, setCityCode] = useState('');
  const [cityName, setCityName] = useState('');
  const [districtCode, setDistrictCode] = useState('');
  const [districtName, setDistrictName] = useState('');
  const [school, setSchool] = useState('');

  // 获取所有省份
  const getProvinces = () => {
    return Object.values(locationData);
  };

  // 根据选择的省份获取城市列表
  const getCities = () => {
    if (!provinceCode || !locationData[provinceCode]) return [];

    return Object.values(locationData[provinceCode].cities || {});
  };

  // 根据选择的城市获取区县列表
  const getDistricts = () => {
    if (
      !provinceCode ||
      !cityCode ||
      !locationData[provinceCode].cities ||
      !locationData[provinceCode].cities[cityCode]
    )
      return [];

    const districts =
      locationData[provinceCode].cities[cityCode].districts || {};
    return Object.entries(districts).map(([code, name]) => ({
      code,
      name,
    }));
  };

  // 处理省份变化
  const handleProvinceChange = (code: string) => {
    if (code === '') {
      setProvinceCode('');
      setProvinceName('');
      setCityCode('');
      setCityName('');
      setDistrictCode('');
      setDistrictName('');
      return;
    }

    const province = locationData[code];
    if (province) {
      setProvinceCode(code);
      setProvinceName(province.name);
      // 重置城市和区县
      setCityCode('');
      setCityName('');
      setDistrictCode('');
      setDistrictName('');
    }
  };

  // 处理城市变化
  const handleCityChange = (code: string) => {
    if (code === '') {
      setCityCode('');
      setCityName('');
      setDistrictCode('');
      setDistrictName('');
      return;
    }

    if (provinceCode && locationData[provinceCode].cities) {
      const city = locationData[provinceCode].cities[code];
      if (city) {
        setCityCode(code);
        setCityName(city.name);
        // 重置区县
        setDistrictCode('');
        setDistrictName('');
      }
    }
  };

  // 处理区县变化
  const handleDistrictChange = (code: string) => {
    if (code === '') {
      setDistrictCode('');
      setDistrictName('');
      return;
    }

    if (
      provinceCode &&
      cityCode &&
      locationData[provinceCode].cities &&
      locationData[provinceCode].cities[cityCode] &&
      locationData[provinceCode].cities[cityCode].districts
    ) {
      const districtName =
        locationData[provinceCode].cities[cityCode].districts[code];
      if (districtName) {
        setDistrictCode(code);
        setDistrictName(districtName);
      }
    }
  };

  const handleSubmit = () => {
    if (!provinceName || !cityName || !districtName || !school) {
      showGlobalToast({
        message: '请填写完整的地址信息',
        type: 'warning',
        duration: 2000,
      });
      return;
    }

    onSubmit({
      province: provinceName,
      city: cityName,
      district: districtName,
      school,
    });
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, styles.locationContainer]}>
          <View style={styles.modalHeader}>
            <View style={styles.iconContainer}>
              <MapPin size={28} color="#4080FF" />
            </View>
            <Text style={styles.modalTitle}>地址信息</Text>
            <Text style={styles.modalSubtitle}>请选择您所在的地区和学校</Text>
          </View>

          <ScrollView
            style={styles.modalScrollContent}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            <View style={styles.decorationElement} />

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>01.</Text> 省份
              </Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={provinceCode}
                  onValueChange={handleProvinceChange}
                  style={styles.picker}
                  dropdownIconColor="#4080FF"
                  mode="dropdown"
                >
                  <Picker.Item label="请选择省份" value="" />
                  {getProvinces().map((province) => (
                    <Picker.Item
                      key={province.code}
                      label={province.name}
                      value={province.code}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>02.</Text> 城市
              </Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={cityCode}
                  onValueChange={handleCityChange}
                  style={styles.picker}
                  enabled={!!provinceCode}
                  dropdownIconColor="#4080FF"
                  mode="dropdown"
                >
                  <Picker.Item label="请选择城市" value="" />
                  {getCities().map((city) => (
                    <Picker.Item
                      key={city.code}
                      label={city.name}
                      value={city.code}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>03.</Text> 区/县
              </Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={districtCode}
                  onValueChange={handleDistrictChange}
                  style={styles.picker}
                  enabled={!!cityCode}
                  dropdownIconColor="#4080FF"
                  mode="dropdown"
                >
                  <Picker.Item label="请选择区/县" value="" />
                  {getDistricts().map((district) => (
                    <Picker.Item
                      key={district.code}
                      label={district.name}
                      value={district.code}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>04.</Text> 学校名称
              </Text>
              <View style={styles.schoolInputContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="请输入学校名称"
                  value={school}
                  onChangeText={setSchool}
                />
                <MapPin size={20} color="#4080FF" style={styles.schoolIcon} />
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modalButton, styles.confirmButton]}
              onPress={handleSubmit}
            >
              <Text style={styles.confirmButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// 用户信息填写弹窗组件
const UserInfoPicker = ({
  visible,
  onClose,
  onSubmit,
}: UserInfoPickerProps) => {
  const [ageGroup, setAgeGroup] = useState('');
  const [subjects, setSubjects] = useState<string[]>([]);
  const [grade, setGrade] = useState('');

  // 年龄段列表 (8个)
  const ageGroups = [
    '6-7岁',
    '8-9岁',
    '10-11岁',
    '12-13岁',
    '14-15岁',
    '16-17岁',
    '18-19岁',
    '20岁以上',
  ];

  // 年级列表（从一年级到高三）
  const grades = [
    '小学一年级',
    '小学二年级',
    '小学三年级',
    '小学四年级',
    '小学五年级',
    '小学六年级',
    '初中一年级',
    '初中二年级',
    '初中三年级',
    '高中一年级',
    '高中二年级',
    '高中三年级',
  ];

  // 科目列表
  const allSubjects = [
    '语文',
    '数学',
    '英语',
    '物理',
    '化学',
    '生物',
    '历史',
    '地理',
    '政治',
  ];

  const toggleSubject = (subject: string) => {
    if (subjects.includes(subject)) {
      setSubjects(subjects.filter((item) => item !== subject));
    } else {
      setSubjects([...subjects, subject]);
    }
  };

  const handleSubmit = () => {
    if (!ageGroup || subjects.length === 0 || !grade) {
      showGlobalToast({
        message: '请填写完整的个人信息',
        type: 'warning',
        duration: 2000,
      });
      return;
    }

    onSubmit({
      ageGroup,
      subjects,
      grade,
    });
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, styles.userInfoContainer]}>
          <View style={styles.modalHeader}>
            <View style={[styles.iconContainer, styles.userIconContainer]}>
              <User size={24} color="#4080FF" />
            </View>
            <Text style={styles.modalTitle}>个人信息</Text>
            <Text style={styles.modalSubtitle}>
              请完善您的个人信息，帮助我们提供更好的服务
            </Text>
          </View>

          <ScrollView
            style={styles.modalScrollContent}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
          >
            <View style={[styles.decorationElement, styles.decorationRight]} />

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>01.</Text> 年龄段
              </Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={ageGroup}
                  onValueChange={(value) => setAgeGroup(value)}
                  style={styles.picker}
                  dropdownIconColor="#4080FF"
                  mode="dropdown"
                >
                  <Picker.Item label="请选择年龄段" value="" />
                  {ageGroups.map((age) => (
                    <Picker.Item key={age} label={age} value={age} />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>02.</Text> 年级
              </Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={grade}
                  onValueChange={(value) => setGrade(value)}
                  style={styles.picker}
                  dropdownIconColor="#4080FF"
                  mode="dropdown"
                >
                  <Picker.Item label="请选择年级" value="" />
                  {grades.map((g) => (
                    <Picker.Item key={g} label={g} value={g} />
                  ))}
                </Picker>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                <Text style={styles.labelNumber}>03.</Text>{' '}
                需要补习的科目（可多选）
              </Text>
              <View style={styles.subjectsContainer}>
                {allSubjects.map((subject) => (
                  <TouchableOpacity
                    key={subject}
                    style={[
                      styles.subjectButton,
                      subjects.includes(subject) &&
                        styles.subjectButtonSelected,
                    ]}
                    onPress={() => toggleSubject(subject)}
                  >
                    <Text
                      style={[
                        styles.subjectButtonText,
                        subjects.includes(subject) &&
                          styles.subjectButtonTextSelected,
                      ]}
                    >
                      {subject}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modalButton, styles.confirmButton]}
              onPress={handleSubmit}
            >
              <Text style={styles.confirmButtonText}>确定</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default function RegisterScreen() {
  const router = useRouter();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // 弹窗状态
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [showUserInfoPicker, setShowUserInfoPicker] = useState(false);

  // 表单数据
  const [locationInfo, setLocationInfo] = useState<LocationInfo | null>(null);
  const [userInfo, setUserInfo] = useState<UserInfoData | null>(null);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleRegister = () => {
    // 表单验证
    if (
      !username.trim() ||
      !password.trim() ||
      !confirmPassword.trim() ||
      !inviteCode.trim()
    ) {
      setError('请填写所有必填项');
      return;
    }

    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    if (inviteCode !== '123456') {
      // 模拟邀请码验证
      setError('邀请码无效');
      return;
    }

    // 开始加载
    setLoading(true);
    setError('');

    // 模拟网络请求延迟
    setTimeout(() => {
      setLoading(false);

      // 注册成功，显示地址选择弹窗
      showGlobalToast({
        message: '账号创建成功，请完成个人信息',
        type: 'success',
        duration: 2000,
      });

      setShowLocationPicker(true);
    }, 1000);
  };

  const handleLocationSubmit = (data: LocationInfo) => {
    setLocationInfo(data);
    setShowLocationPicker(false);

    // 地址提交成功后显示用户信息弹窗
    setTimeout(() => {
      setShowUserInfoPicker(true);
    }, 500);
  };

  const handleUserInfoSubmit = (data: UserInfoData) => {
    setUserInfo(data);
    setShowUserInfoPicker(false);

    // 模拟注册完成，自动登录
    setLoading(true);

    setTimeout(() => {
      // 创建用户信息
      if (locationInfo) {
        const userInfo = {
          name: username,
          avatarUrl:
            'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
          level: data.grade,
          id: Math.floor(10000000 + Math.random() * 90000000).toString(),
          role: data.ageGroup,
          school: locationInfo.school,
          isLoggedIn: true,
        };

        // 保存用户信息到全局变量
        global.userInfo = userInfo;

        setLoading(false);

        // 注册并登录成功提示
        showGlobalToast({
          message: '注册成功并已登录',
          type: 'success',
          duration: 2000,
        });

        // 修改：跳转到首页而不是返回
        setTimeout(() => {
          router.replace('/');
        }, 500);
      }
    }, 1500);
  };

  // 确保在Web环境下Picker正确显示
  useEffect(() => {
    // 处理移动端和Web端的兼容性
    if (Platform.OS === 'web') {
      // 针对Web平台的特殊处理
      const styleSheet = document.createElement('style');
      styleSheet.textContent = `
        select {
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234080FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
          background-repeat: no-repeat;
          background-position: right 10px center;
          background-size: 16px;
          padding-right: 30px !important;
          border: none !important;
          outline: none !important;
        }
      `;
      document.head.appendChild(styleSheet);

      return () => {
        document.head.removeChild(styleSheet);
      };
    }
  }, []);

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        >
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="#333" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>注册账号</Text>
          </View>

          <Card style={styles.formCard}>
            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            <View style={styles.inputContainer}>
              <Text style={styles.label}>用户名</Text>
              <TextInput
                style={styles.input}
                placeholder="请设置用户名（用于登录）"
                placeholderTextColor="#999"
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>密码</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="请设置密码"
                  placeholderTextColor="#999"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOff size={20} color="#666" />
                  ) : (
                    <Eye size={20} color="#666" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>确认密码</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="请再次输入密码"
                  placeholderTextColor="#999"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? (
                    <EyeOff size={20} color="#666" />
                  ) : (
                    <Eye size={20} color="#666" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>邀请码</Text>
              <TextInput
                style={styles.input}
                placeholder="请输入邀请码"
                placeholderTextColor="#999"
                value={inviteCode}
                onChangeText={setInviteCode}
              />
              <Text style={styles.tipText}>
                * 注册需要邀请码，请联系老师获取
              </Text>
            </View>

            <Button
              title="注册"
              onPress={handleRegister}
              style={styles.registerButton}
              size="lg"
              loading={loading}
            />

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>已有账号? </Text>
              <TouchableOpacity onPress={() => router.push('/login')}>
                <Text style={styles.loginLink}>立即登录</Text>
              </TouchableOpacity>
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* 地址选择弹窗 */}
      <LocationPicker
        visible={showLocationPicker}
        onClose={() => setShowLocationPicker(false)}
        onSubmit={handleLocationSubmit}
      />

      {/* 用户信息弹窗 */}
      <UserInfoPicker
        visible={showUserInfoPicker}
        onClose={() => setShowUserInfoPicker(false)}
        onSubmit={handleUserInfoSubmit}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 25,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
    fontFamily: 'NotoSansSC-Bold',
  },
  formCard: {
    padding: 24,
    borderRadius: 16,
  },
  errorText: {
    color: '#FF3B30',
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 14,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 15,
    color: '#333',
    marginBottom: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  input: {
    flex: 1,
    backgroundColor: '#F5F7FA',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333',
    borderWidth: 0, // 移除边框
  },
  tipText: {
    fontSize: 12,
    color: '#999',
    marginTop: 6,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 15,
    color: '#333',
  },
  eyeIcon: {
    padding: 10,
  },
  registerButton: {
    marginTop: 10,
    marginBottom: 25,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  loginText: {
    color: '#666',
    fontSize: 14,
  },
  loginLink: {
    color: '#4080FF',
    fontSize: 14,
    fontWeight: '500',
  },
  // 弹窗样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    backgroundColor: '#FFF',
    borderRadius: 20,
    padding: 24,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  locationContainer: {
    maxHeight: '80%',
  },
  userInfoContainer: {
    maxHeight: '80%',
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: 'NotoSansSC-Bold',
  },
  modalSubtitle: {
    color: '#666',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  modalScrollContent: {
    maxHeight: '60%',
  },
  pickerContainer: {
    backgroundColor: '#F5F7FA',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    overflow: 'hidden',
  },
  picker: {
    width: '100%',
    height: 50,
    color: '#333',
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  schoolInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F7FA',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingRight: 10,
  },
  schoolIcon: {
    marginRight: 6,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 16,
  },
  modalButton: {
    flex: 1,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cancelButton: {
    backgroundColor: '#F0F2F5',
  },
  confirmButton: {
    backgroundColor: '#4080FF',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
  subjectsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  subjectButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: '#F5F7FA',
    marginRight: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  subjectButtonSelected: {
    backgroundColor: '#EBF2FF',
    borderColor: '#4080FF',
  },
  subjectButtonText: {
    color: '#666',
    fontSize: 14,
  },
  subjectButtonTextSelected: {
    color: '#4080FF',
    fontWeight: '500',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#EBF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  userIconContainer: {
    backgroundColor: '#E6F7FF',
  },
  decorationElement: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(64, 128, 255, 0.05)',
    top: -30,
    left: -30,
    zIndex: -1,
  },
  decorationRight: {
    left: 'auto',
    right: -30,
    top: 50,
    backgroundColor: 'rgba(64, 128, 255, 0.03)',
  },
  labelNumber: {
    color: '#4080FF',
    fontWeight: 'bold',
    marginRight: 4,
  },
});
