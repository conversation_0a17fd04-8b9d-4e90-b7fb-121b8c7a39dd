import React from 'react';
import {FlatList, Image, SafeAreaView, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {useRouter} from 'expo-router';
import {Calendar, ChevronLeft, Clock, Star, Users} from 'lucide-react-native';

// 模拟数据 - 特训营课程
const SPECIAL_TRAINING_COURSES = [
  {
    id: 1,
    title: '高考数学冲刺特训营',
    imageUrl:
      'https://images.pexels.com/photos/6238021/pexels-photo-6238021.jpeg',
    trainer: '李明教授',
    rating: 4.9,
    studentCount: 328,
    startDate: '2023-11-15',
    endDate: '2023-12-30',
    price: '1999.00',
    originalPrice: '2999.00',
    duration: '45天',
    totalHours: 90,
    classSize: 20,
    tags: ['高考数学', '冲刺提分', '小班教学'],
    description:
      '专为高三学生设计的数学冲刺课程，覆盖高考重点难点，配合专业学习规划和定期测试，助力高考数学成绩提升。',
  },
  {
    id: 2,
    title: 'Python全栈开发特训班',
    imageUrl:
      'https://images.pexels.com/photos/574071/pexels-photo-574071.jpeg',
    trainer: '陈俊',
    rating: 4.8,
    studentCount: 215,
    startDate: '2023-12-01',
    endDate: '2024-02-28',
    price: '2999.00',
    originalPrice: '3999.00',
    duration: '90天',
    totalHours: 180,
    classSize: 15,
    tags: ['Python', '全栈开发', '项目实战'],
    description:
      '从零基础到全栈工程师，涵盖前后端开发、数据库、部署等全流程技能，配合实战项目，全方位提升编程能力。',
  },
  {
    id: 3,
    title: '雅思口语突破特训营',
    imageUrl:
      'https://images.pexels.com/photos/6147369/pexels-photo-6147369.jpeg',
    trainer: '王芳',
    rating: 4.9,
    studentCount: 186,
    startDate: '2023-11-20',
    endDate: '2024-01-10',
    price: '2499.00',
    originalPrice: '3299.00',
    duration: '50天',
    totalHours: 100,
    classSize: 10,
    tags: ['雅思', '口语', '小班教学'],
    description:
      '针对雅思口语部分的专项训练，外教小班教学，配合模拟考试和个性化反馈，快速提升口语表达和应试能力。',
  },
];

// 课程卡片组件
const TrainingCourseCard = ({ course, onPress }) => (
  <TouchableOpacity style={styles.courseCard} onPress={onPress}>
    <Image source={{ uri: course.imageUrl }} style={styles.courseImage} />
    <View style={styles.courseContent}>
      <Text style={styles.courseTitle}>{course.title}</Text>

      <View style={styles.trainerRow}>
        <Text style={styles.trainerLabel}>导师：</Text>
        <Text style={styles.trainerName}>{course.trainer}</Text>
        <View style={styles.ratingContainer}>
          <Star size={14} color="#FFB800" fill="#FFB800" />
          <Text style={styles.ratingText}>{course.rating}</Text>
        </View>
      </View>

      <View style={styles.infoRow}>
        <View style={styles.infoItem}>
          <Calendar size={14} color="#666666" />
          <Text style={styles.infoText}>
            {course.startDate.substring(5)} 至 {course.endDate.substring(5)}
          </Text>
        </View>
        <View style={styles.infoItem}>
          <Clock size={14} color="#666666" />
          <Text style={styles.infoText}>{course.totalHours}课时</Text>
        </View>
        <View style={styles.infoItem}>
          <Users size={14} color="#666666" />
          <Text style={styles.infoText}>限{course.classSize}人</Text>
        </View>
      </View>

      <View style={styles.tagContainer}>
        {course.tags.map((tag, index) => (
          <View key={index} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </View>

      <Text style={styles.description} numberOfLines={2}>
        {course.description}
      </Text>

      <View style={styles.priceRow}>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>¥{course.price}</Text>
          <Text style={styles.originalPrice}>¥{course.originalPrice}</Text>
        </View>
        <TouchableOpacity style={styles.enrollButton}>
          <Text style={styles.enrollButtonText}>立即报名</Text>
        </TouchableOpacity>
      </View>
    </View>
  </TouchableOpacity>
);

export default function SpecialTrainingScreen() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ChevronLeft size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>特训营</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.container}>
        <Text style={styles.sectionTitle}>集训提分 · 专项突破</Text>
        <Text style={styles.description}>
          特训营采用小班制教学，配合专业学习规划和阶段性测试，
          帮助学生在短期内集中突破特定学科或技能。
        </Text>

        <FlatList
          data={SPECIAL_TRAINING_COURSES}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <TrainingCourseCard
              course={item}
              onPress={() => router.push(`/special-training/${item.id}` as any)}
            />
          )}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.courseList}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  placeholder: {
    width: 32,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 16,
  },
  courseList: {
    paddingBottom: 16,
  },
  courseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  courseImage: {
    width: '100%',
    height: 160,
    resizeMode: 'cover',
  },
  courseContent: {
    padding: 16,
  },
  courseTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  trainerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  trainerLabel: {
    fontSize: 14,
    color: '#666666',
  },
  trainerName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFB800',
    marginLeft: 4,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  infoText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  tag: {
    backgroundColor: '#F5F5F5',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#666666',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF6B00',
    marginRight: 8,
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  enrollButton: {
    backgroundColor: '#4080FF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  enrollButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
