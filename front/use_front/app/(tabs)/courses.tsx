import React, {useState} from 'react';
import {SafeAreaView, ScrollView, StyleSheet, Text, View} from 'react-native';
import SearchBar from '@/components/ui/SearchBar';
import CategoryButton from '@/components/ui/CategoryButton';
import CourseCard from '@/components/ui/CourseCard';

export default function CoursesScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('全部');
  const [selectedAgeGroup, setSelectedAgeGroup] = useState('全部');

  // 学科分类
  const subjects = ['全部', '设计', '编程', '语言', '数学', '物理'];

  // 年龄段
  const ageGroups = ['全部', '青少年', '大学生', '成人'];

  // 课程列表
  const courses = [
    {
      id: 1,
      title: '设计思维与创新方法论',
      imageUrl:
        'https://images.pexels.com/photos/3184306/pexels-photo-3184306.jpeg',
      teacher: '李教授',
      price: '499.00',
      studentCount: 328,
      duration: '12',
      tag: '新课',
      subject: '设计',
      ageGroup: '大学生',
    },
    {
      id: 2,
      title: 'Python基础课程',
      imageUrl:
        'https://images.pexels.com/photos/1181271/pexels-photo-1181271.jpeg',
      teacher: '张教授',
      price: '399.00',
      studentCount: 568,
      duration: '16',
      subject: '编程',
      ageGroup: '青少年',
    },
    {
      id: 3,
      title: '英语口语进阶',
      imageUrl:
        'https://images.pexels.com/photos/4050315/pexels-photo-4050315.jpeg',
      teacher: '王老师',
      price: '349.00',
      studentCount: 421,
      duration: '20',
      subject: '语言',
      ageGroup: '成人',
    },
    {
      id: 4,
      title: '高等数学精讲',
      imageUrl:
        'https://images.pexels.com/photos/6238297/pexels-photo-6238297.jpeg',
      teacher: '刘教授',
      price: '549.00',
      studentCount: 286,
      duration: '24',
      subject: '数学',
      ageGroup: '大学生',
    },
  ];

  // 根据选择的分类过滤课程
  const filteredCourses = courses.filter((course) => {
    const matchesSubject =
      selectedSubject === '全部' || course.subject === selectedSubject;
    const matchesAgeGroup =
      selectedAgeGroup === '全部' || course.ageGroup === selectedAgeGroup;
    const matchesSearch =
      searchQuery === '' ||
      course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.teacher.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSubject && matchesAgeGroup && matchesSearch;
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>科目分类</Text>
          </View>

          <View style={styles.searchContainer}>
            <SearchBar
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="搜索课程、讲师..."
            />
          </View>

          <View style={styles.categoriesContainer}>
            <Text style={styles.categoryTitle}>年龄段</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.categoryScroll}
            >
              {ageGroups.map((age, index) => (
                <CategoryButton
                  key={index}
                  title={age}
                  isActive={selectedAgeGroup === age}
                  onPress={() => setSelectedAgeGroup(age)}
                />
              ))}
            </ScrollView>
          </View>

          <View style={styles.categoriesContainer}>
            <Text style={styles.categoryTitle}>科目分类</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.categoryScroll}
            >
              {subjects.map((subject, index) => (
                <CategoryButton
                  key={index}
                  title={subject}
                  isActive={selectedSubject === subject}
                  onPress={() => setSelectedSubject(subject)}
                />
              ))}
            </ScrollView>
          </View>

          <View style={styles.coursesSection}>
            <Text style={styles.sectionTitle}>最新上架</Text>
            <View style={styles.courseList}>
              {filteredCourses.map((item) => (
                <CourseCard
                  key={item.id}
                  title={item.title}
                  imageUrl={item.imageUrl}
                  teacher={item.teacher}
                  price={item.price}
                  studentCount={item.studentCount}
                  duration={item.duration}
                  tag={item.subject}
                  onPress={() => {}}
                />
              ))}
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    fontFamily: 'NotoSansSC-Bold',
  },
  searchContainer: {
    marginBottom: 20,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
    fontFamily: 'NotoSansSC-Medium',
  },
  categoryScroll: {
    flexDirection: 'row',
  },
  coursesSection: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
    fontFamily: 'NotoSansSC-Medium',
  },
  courseList: {
    paddingBottom: 16,
  },
});
