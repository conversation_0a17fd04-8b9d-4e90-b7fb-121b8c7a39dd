import React from 'react';
import { Tabs } from 'expo-router';
import { StyleSheet, Text, View } from 'react-native';
import {
  BookOpen,
  Calendar,
  LayoutDashboard,
  LucideIcon,
  User,
  Video,
} from 'lucide-react-native';

// 定义标签图标组件的属性接口
interface TabBarIconProps {
  color: string;
  size: number;
  icon: LucideIcon;
  label: string;
}

// 自定义标签组件，确保图标和文字都显示
function TabBarIcon({ color, size, icon: Icon, label }: TabBarIconProps) {
  return (
    <View style={styles.tabIconContainer}>
      <Icon size={size - 2} color={color} />
      <Text style={[styles.tabBarLabelCustom, { color }]}>{label}</Text>
    </View>
  );
}

export default function TabsLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#4080FF',
        tabBarInactiveTintColor: '#999999',
        tabBarStyle: styles.tabBar,
        headerShown: false,
        tabBarLabelStyle: styles.tabBarLabel,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: '首页',
          tabBarIcon: ({ color, size }) => (
            <TabBarIcon
              color={color}
              size={size}
              icon={LayoutDashboard}
              label="首页"
            />
          ),
        }}
      />
      <Tabs.Screen
        name="courses"
        options={{
          title: '课程',
          tabBarIcon: ({ color, size }) => (
            <TabBarIcon
              color={color}
              size={size}
              icon={BookOpen}
              label="课程"
            />
          ),
        }}
      />
      <Tabs.Screen
        name="live"
        options={{
          title: '直播',
          tabBarIcon: ({ color, size }) => (
            <TabBarIcon color={color} size={size} icon={Video} label="直播" />
          ),
        }}
      />
      <Tabs.Screen
        name="checkin"
        options={{
          title: '打卡',
          tabBarIcon: ({ color, size }) => (
            <TabBarIcon
              color={color}
              size={size}
              icon={Calendar}
              label="打卡"
            />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: '我的',
          tabBarIcon: ({ color, size }) => (
            <TabBarIcon color={color} size={size} icon={User} label="我的" />
          ),
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    height: 64,
    paddingBottom: 8,
    paddingTop: 8,
  },
  tabBarLabel: {
    display: 'none',
  },
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabBarLabelCustom: {
    fontSize: 12,
    marginTop: 4,
  },
});
