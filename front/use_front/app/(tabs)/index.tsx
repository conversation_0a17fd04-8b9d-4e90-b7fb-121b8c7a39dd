import React from 'react';
import {Image, SafeAreaView, ScrollView, StyleSheet, Text, View,} from 'react-native';
import SearchBar from '@/components/ui/SearchBar';
import FeatureButton from '@/components/home/<USER>';
import SectionHeader from '@/components/home/<USER>';
import LivePreview from '@/components/home/<USER>';
import CourseCard from '@/components/ui/CourseCard';
import {Flame, GraduationCap, Users} from 'lucide-react-native';
import {useRouter} from 'expo-router';

export default function HomeScreen() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const router = useRouter();

  const featuredImage =
    'https://images.pexels.com/photos/267885/pexels-photo-267885.jpeg';

  const features = [
    {
      id: 1,
      title: '特训营',
      icon: <Flame size={24} color="#FFFFFF" />,
      color: '#4080FF',
      route: '/special-training',
    },
    {
      id: 2,
      title: '名师课',
      icon: <GraduationCap size={24} color="#FFFFFF" />,
      color: '#FF6B00',
      route: '/master-courses',
    },
    {
      id: 3,
      title: '一对一',
      icon: <Users size={24} color="#FFFFFF" />,
      color: '#9747FF',
      route: '/(tabs)/one-on-one',
    },
  ];

  const livePreview = {
    title: 'AI绘画进阶技巧直播课',
    time: '今天 19:30',
    teacher: '王教授',
    imageUrl:
      'https://images.pexels.com/photos/7303154/pexels-photo-7303154.jpeg',
    registrationCount: 268,
  };

  const recommendedCourses = [
    {
      id: 1,
      title: '设计思维与创新方法论',
      imageUrl:
        'https://images.pexels.com/photos/3184306/pexels-photo-3184306.jpeg',
      teacher: '李教授',
      price: '499.00',
      studentCount: 328,
      duration: '12',
      tag: '新课',
    },
    {
      id: 2,
      title: '学设计，上覃师',
      imageUrl:
        'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg',
      teacher: '覃师傅',
      price: '299.00',
      studentCount: 1280,
      duration: '24',
      tag: 'CG动画',
    },
  ];

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.logo}>鼎峰课堂</Text>
        </View>

        <View style={styles.searchContainer}>
          <SearchBar value={searchQuery} onChangeText={setSearchQuery} />
        </View>

        <View style={styles.bannerContainer}>
          <Image source={{ uri: featuredImage }} style={styles.bannerImage} />
        </View>

        <View style={styles.featuresContainer}>
          {features.map((feature) => (
            <FeatureButton
              key={feature.id}
              title={feature.title}
              icon={feature.icon}
              color={feature.color}
              onPress={() => router.push(feature.route as any)}
            />
          ))}
        </View>

        <View style={styles.liveSection}>
          <SectionHeader title="直播预告" onViewMore={() => {}} />
          <LivePreview
            title={livePreview.title}
            time={livePreview.time}
            teacher={livePreview.teacher}
            imageUrl={livePreview.imageUrl}
            registrationCount={livePreview.registrationCount}
            onPress={() => {}}
          />
        </View>

        <View style={styles.recommendedSection}>
          <SectionHeader title="优秀课程推荐" onViewMore={() => {}} />
          {recommendedCourses.map((course) => (
            <CourseCard
              key={course.id}
              title={course.title}
              imageUrl={course.imageUrl}
              teacher={course.teacher}
              price={course.price}
              studentCount={course.studentCount}
              duration={course.duration}
              tag={course.tag}
              onPress={() => router.push(`/course/${course.id}` as any)}
            />
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  logo: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    fontFamily: 'NotoSansSC-Bold',
  },
  searchContainer: {
    marginBottom: 16,
  },
  bannerContainer: {
    marginBottom: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  bannerImage: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  liveSection: {
    marginBottom: 24,
  },
  recommendedSection: {
    marginBottom: 24,
  },
});
