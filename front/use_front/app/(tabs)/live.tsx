import React, { useState } from 'react';
import {
  FlatList,
  Image,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useRouter } from 'expo-router';
import {
  ChevronDown,
  Search,
  Users,
  Clock,
  ExternalLink,
  Video,
  Calendar,
} from 'lucide-react-native';

// 定义直播类型接口
interface LiveStream {
  id: number;
  title: string;
  teacher: string;
  teacherAvatar: string;
  cover: string;
  platform: string;
  url: string;
  startTime: string;
  duration: string;
  status: 'upcoming' | 'live' | 'ended';
  viewerCount: number;
  category: string;
  description: string;
}

// 模拟数据 - 直播列表
const LIVE_STREAMS: LiveStream[] = [
  {
    id: 1,
    title: '高等数学基础入门课程',
    teacher: '李明教授',
    teacherAvatar:
      'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
    cover: 'https://images.pexels.com/photos/5427648/pexels-photo-5427648.jpeg',
    platform: '腾讯会议',
    url: 'https://meeting.tencent.com/dm/abc123',
    startTime: '2024-03-15 19:00',
    duration: '2小时',
    status: 'upcoming',
    viewerCount: 0,
    category: '数学',
    description:
      '本次直播将讲解高等数学的基础概念，包括极限、导数、微分等核心知识点。适合大一新生和需要复习的同学参加。',
  },
  {
    id: 2,
    title: '英语口语实战训练',
    teacher: '王芳老师',
    teacherAvatar:
      'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg',
    cover: 'https://images.pexels.com/photos/5428833/pexels-photo-5428833.jpeg',
    platform: 'Zoom',
    url: 'https://zoom.us/j/123456789',
    startTime: '2024-03-14 20:00',
    duration: '1.5小时',
    status: 'live',
    viewerCount: 156,
    category: '英语',
    description:
      '通过真实场景对话练习，提升英语口语表达能力。本次课程将重点训练日常生活和商务场景的对话技巧。',
  },
  {
    id: 3,
    title: '物理实验演示课',
    teacher: '张伟博士',
    teacherAvatar:
      'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg',
    cover: 'https://images.pexels.com/photos/5553063/pexels-photo-5553063.jpeg',
    platform: 'B站直播',
    url: 'https://live.bilibili.com/12345',
    startTime: '2024-03-13 15:30',
    duration: '2.5小时',
    status: 'ended',
    viewerCount: 432,
    category: '物理',
    description:
      '通过有趣的物理实验演示，帮助学生理解抽象的物理概念。包括电磁学、光学等多个实验项目。',
  },
  {
    id: 4,
    title: 'Python编程入门实战',
    teacher: '陈俊老师',
    teacherAvatar:
      'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',
    cover: 'https://images.pexels.com/photos/4164418/pexels-photo-4164418.jpeg',
    platform: '钉钉',
    url: 'https://dingding.com/live/abc123',
    startTime: '2024-03-16 14:00',
    duration: '3小时',
    status: 'upcoming',
    viewerCount: 0,
    category: '编程',
    description:
      '从零开始学习Python编程，包括基础语法、数据结构、函数等核心概念，并通过实际项目练习巩固知识。',
  },
  {
    id: 5,
    title: '化学分子结构解析',
    teacher: '刘倩教授',
    teacherAvatar:
      'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg',
    cover: 'https://images.pexels.com/photos/2280549/pexels-photo-2280549.jpeg',
    platform: '腾讯课堂',
    url: 'https://ke.qq.com/course/12345',
    startTime: '2024-03-17 16:30',
    duration: '1.5小时',
    status: 'upcoming',
    viewerCount: 0,
    category: '化学',
    description:
      '深入讲解有机化合物的分子结构，通过3D模型演示帮助学生理解复杂的化学键和分子构型。',
  },
];

// 模拟数据 - 筛选选项
const CATEGORIES = [
  '数学',
  '英语',
  '物理',
  '化学',
  '编程',
  '历史',
  '地理',
  '生物',
];

// 定义直播卡片组件的属性接口
interface LiveStreamCardProps {
  liveStream: LiveStream;
  onPress: () => void;
}

// 获取状态显示信息
const getStatusInfo = (status: LiveStream['status'], viewerCount: number) => {
  switch (status) {
    case 'live':
      return {
        text: `🔴 直播中`,
        color: '#FF4444',
        bgColor: '#FFE6E6',
      };
    case 'upcoming':
      return {
        text: '📅 即将开始',
        color: '#4080FF',
        bgColor: '#E6F7FF',
      };
    case 'ended':
      return {
        text: '⏹️ 已结束',
        color: '#999999',
        bgColor: '#F5F5F5',
      };
  }
};

const LiveStreamCard: React.FC<LiveStreamCardProps> = ({
  liveStream,
  onPress,
}) => {
  const statusInfo = getStatusInfo(liveStream.status, liveStream.viewerCount);

  return (
    <TouchableOpacity style={styles.liveCard} onPress={onPress}>
      <View style={styles.coverContainer}>
        <Image source={{ uri: liveStream.cover }} style={styles.coverImage} />
        <View
          style={[styles.statusBadge, { backgroundColor: statusInfo.bgColor }]}
        >
          <Text style={[styles.statusText, { color: statusInfo.color }]}>
            {statusInfo.text}
          </Text>
        </View>
      </View>

      <View style={styles.liveInfo}>
        <Text style={styles.liveTitle} numberOfLines={2}>
          {liveStream.title}
        </Text>

        <View style={styles.teacherContainer}>
          <Image
            source={{ uri: liveStream.teacherAvatar }}
            style={styles.teacherAvatarSmall}
          />
          <Text style={styles.teacherName}>{liveStream.teacher}</Text>
        </View>

        <View style={styles.liveFooter}>
          <View style={styles.timeContainer}>
            <Clock size={14} color="#666666" />
            <Text style={styles.timeText}>{liveStream.startTime}</Text>
          </View>

          <View style={styles.categoryContainer}>
            <Text style={styles.categoryText}>{liveStream.category}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// 直播详情弹窗组件
interface LiveDetailModalProps {
  visible: boolean;
  liveStream: LiveStream | null;
  onClose: () => void;
}

const LiveDetailModal: React.FC<LiveDetailModalProps> = ({
  visible,
  liveStream,
  onClose,
}) => {
  if (!liveStream) return null;

  const statusInfo = getStatusInfo(liveStream.status, liveStream.viewerCount);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>直播详情</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            <Image
              source={{ uri: liveStream.cover }}
              style={styles.modalCoverImage}
            />

            <View style={styles.modalInfo}>
              <Text style={styles.modalLiveTitle}>{liveStream.title}</Text>

              <View style={styles.modalTeacherContainer}>
                <Image
                  source={{ uri: liveStream.teacherAvatar }}
                  style={styles.modalTeacherAvatar}
                />
                <View style={styles.modalTeacherInfo}>
                  <Text style={styles.modalTeacherName}>
                    {liveStream.teacher}
                  </Text>
                  <View
                    style={[
                      styles.modalStatusBadge,
                      { backgroundColor: statusInfo.bgColor },
                    ]}
                  >
                    <Text
                      style={[
                        styles.modalStatusText,
                        { color: statusInfo.color },
                      ]}
                    >
                      {statusInfo.text}
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.modalDetailRow}>
                <Video size={16} color="#666666" />
                <Text style={styles.modalDetailLabel}>直播平台：</Text>
                <Text style={styles.modalDetailValue}>
                  {liveStream.platform}
                </Text>
              </View>

              <View style={styles.modalDetailRow}>
                <Calendar size={16} color="#666666" />
                <Text style={styles.modalDetailLabel}>开始时间：</Text>
                <Text style={styles.modalDetailValue}>
                  {liveStream.startTime}
                </Text>
              </View>

              <View style={styles.modalDetailRow}>
                <Clock size={16} color="#666666" />
                <Text style={styles.modalDetailLabel}>课程时长：</Text>
                <Text style={styles.modalDetailValue}>
                  {liveStream.duration}
                </Text>
              </View>

              <View style={styles.modalDetailRow}>
                <ExternalLink size={16} color="#666666" />
                <Text style={styles.modalDetailLabel}>直播地址：</Text>
                <TouchableOpacity>
                  <Text style={styles.modalLinkText} numberOfLines={1}>
                    {liveStream.url}
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.modalDescription}>
                <Text style={styles.modalDescriptionTitle}>课程介绍</Text>
                <Text style={styles.modalDescriptionText}>
                  {liveStream.description}
                </Text>
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalActions}>
            {liveStream.status === 'live' && (
              <TouchableOpacity style={styles.joinButton}>
                <Video size={16} color="#FFFFFF" />
                <Text style={styles.joinButtonText}>加入直播</Text>
              </TouchableOpacity>
            )}
            {liveStream.status === 'upcoming' && (
              <TouchableOpacity style={styles.remindButton}>
                <Calendar size={16} color="#4080FF" />
                <Text style={styles.remindButtonText}>复制地址</Text>
              </TouchableOpacity>
            )}
            {liveStream.status === 'ended' && (
              <TouchableOpacity style={styles.replayButton}>
                <Video size={16} color="#666666" />
                <Text style={styles.replayButtonText}>查看回放</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default function LiveStreamScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部分类');
  const [liveStreams, setLiveStreams] = useState<LiveStream[]>(LIVE_STREAMS);
  const [selectedLiveStream, setSelectedLiveStream] =
    useState<LiveStream | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  // 处理卡片点击
  const handleCardPress = (liveStream: LiveStream) => {
    setSelectedLiveStream(liveStream);
    setModalVisible(true);
  };

  // 渲染直播卡片
  const renderLiveStreamCard = (liveStream: LiveStream) => (
    <LiveStreamCard
      liveStream={liveStream}
      onPress={() => handleCardPress(liveStream)}
    />
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <ChevronDown size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>直播课堂</Text>
        <View style={styles.headerSpacer} />
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Search size={20} color="#999999" style={styles.searchIcon} />
          <Text style={styles.searchPlaceholder}>搜索直播课程或老师</Text>
        </View>
      </View>

      <View style={styles.categoryFilterContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryScroll}
        >
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === '全部分类' && styles.selectedCategoryButton,
            ]}
            onPress={() => setSelectedCategory('全部分类')}
          >
            <Text
              style={[
                styles.categoryText,
                selectedCategory === '全部分类' && styles.selectedCategoryText,
              ]}
            >
              全部分类
            </Text>
          </TouchableOpacity>
          {CATEGORIES.map((category, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.categoryButton,
                selectedCategory === category && styles.selectedCategoryButton,
              ]}
              onPress={() => setSelectedCategory(category)}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category && styles.selectedCategoryText,
                ]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <FlatList
        data={liveStreams}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => renderLiveStreamCard(item)}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.liveStreamList}
        numColumns={2}
        columnWrapperStyle={styles.row}
      />

      <LiveDetailModal
        visible={modalVisible}
        liveStream={selectedLiveStream}
        onClose={() => setModalVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  headerSpacer: {
    width: 32,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchPlaceholder: {
    color: '#999999',
    fontSize: 14,
  },
  categoryFilterContainer: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoryScroll: {
    paddingHorizontal: 12,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
  },
  selectedCategoryButton: {
    backgroundColor: '#4080FF',
  },
  categoryText: {
    fontSize: 14,
    color: '#666666',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  liveStreamList: {
    padding: 16,
  },
  row: {
    justifyContent: 'space-between',
  },
  liveCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    width: '48%',
  },
  coverContainer: {
    position: 'relative',
  },
  coverImage: {
    width: '100%',
    height: 100,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  statusBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '500',
  },
  liveInfo: {
    padding: 12,
  },
  liveTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    lineHeight: 18,
  },
  teacherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  teacherAvatarSmall: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 6,
  },
  teacherName: {
    fontSize: 12,
    color: '#666666',
  },
  liveFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  timeText: {
    fontSize: 11,
    color: '#666666',
    marginLeft: 4,
  },
  categoryContainer: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  categoryText: {
    fontSize: 10,
    color: '#666666',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  closeButton: {
    padding: 4,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666666',
  },
  modalCoverImage: {
    width: '100%',
    height: 200,
  },
  modalInfo: {
    padding: 16,
  },
  modalLiveTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
    lineHeight: 24,
  },
  modalTeacherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTeacherAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  modalTeacherInfo: {
    flex: 1,
  },
  modalTeacherName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  modalStatusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  modalStatusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  modalDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  modalDetailLabel: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
    minWidth: 80,
  },
  modalDetailValue: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  modalLinkText: {
    fontSize: 14,
    color: '#4080FF',
    textDecorationLine: 'underline',
    flex: 1,
  },
  modalDescription: {
    marginTop: 20,
  },
  modalDescriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  modalDescriptionText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  modalActions: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  joinButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF4444',
    paddingVertical: 12,
    borderRadius: 8,
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  remindButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E6F7FF',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4080FF',
  },
  remindButtonText: {
    color: '#4080FF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  replayButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    paddingVertical: 12,
    borderRadius: 8,
  },
  replayButtonText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
