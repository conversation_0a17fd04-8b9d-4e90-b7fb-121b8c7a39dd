import React, {useState} from 'react';
import {SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import CheckinCard from '@/components/checkin/CheckinCard';
import CalendarDay from '@/components/checkin/CalendarDay';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import {ChevronLeft, ChevronRight} from 'lucide-react-native';

export default function CheckinScreen() {
  const [isCheckedInToday, setIsCheckedInToday] = useState(false);
  
  const handleCheckIn = () => {
    setIsCheckedInToday(true);
    // 可以添加动画或者其他反馈
  };

  // 模拟打卡数据
  const checkinData = {
    currentStreak: 28,
    totalCheckins: 42,
    avgStudyHours: 4.5,
  };

  // 日历相关状态
  const [selectedDate, setSelectedDate] = useState(new Date().getDate());
  
  // 模拟当月已打卡的日期
  const checkedInDates = [1, 2, 3, 5, 7, 10, 12, 15, 16];
  
  // 星期几的头部
  const weekDays = ['一', '二', '三', '四', '五', '六', '日'];

  // 生成当月日历数据
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay() || 7; // 0 (Sunday) => 7
  
  // 生成日历网格
  const calendarDays = [];
  
  // 填充前面的空白
  for (let i = 1; i < firstDayOfMonth; i++) {
    calendarDays.push(null);
  }
  
  // 填充日期
  for (let i = 1; i <= daysInMonth; i++) {
    calendarDays.push(i);
  }

  // 今日学习任务
  const todayTask = {
    id: 1,
    title: 'Python基础课程',
    progress: 65,
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerText}>每日打卡</Text>
          <Text style={styles.subtitle}>坚持学习，持续进步</Text>
        </View>

        <CheckinCard
          currentStreak={checkinData.currentStreak}
          totalCheckins={checkinData.totalCheckins}
          avgStudyHours={checkinData.avgStudyHours}
          onCheckIn={handleCheckIn}
          isCheckedInToday={isCheckedInToday}
        />

        <View style={styles.calendarSection}>
          <View style={styles.calendarHeader}>
            <Text style={styles.calendarTitle}>9月打卡记录</Text>
            <View style={styles.navigationButtons}>
              <TouchableOpacity style={styles.navButton}>
                <ChevronLeft size={20} color="#666666" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.navButton}>
                <ChevronRight size={20} color="#666666" />
              </TouchableOpacity>
            </View>
          </View>

          <Card style={styles.calendarCard}>
            <View style={styles.weekDaysRow}>
              {weekDays.map((day, index) => (
                <View key={index} style={styles.weekDayCell}>
                  <Text style={styles.weekDayText}>{day}</Text>
                </View>
              ))}
            </View>

            <View style={styles.calendarGrid}>
              {calendarDays.map((day, index) => {
                if (day === null) {
                  return <View key={`empty-${index}`} style={styles.emptyCell} />;
                }
                
                const isToday = day === new Date().getDate();
                const isSelected = day === selectedDate;
                const isCheckedIn = checkedInDates.includes(day);
                
                return (
                  <CalendarDay
                    key={day}
                    date={day}
                    isToday={isToday}
                    isSelected={isSelected}
                    isCheckedIn={isCheckedIn}
                    onPress={setSelectedDate}
                  />
                );
              })}
            </View>
          </Card>
        </View>

        <View style={styles.taskSection}>
          <Text style={styles.sectionTitle}>今日学习任务</Text>
          <Card style={styles.taskCard}>
            <View style={styles.taskHeader}>
              <View style={styles.taskIcon}>
                <Text style={styles.taskIconText}>P</Text>
              </View>
              <Text style={styles.taskTitle}>{todayTask.title}</Text>
            </View>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${todayTask.progress}%` }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>{todayTask.progress}%</Text>
            </View>
            <Button 
              title="继续" 
              onPress={() => {}} 
              type="primary" 
              style={styles.continueButton}
            />
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 20,
  },
  headerText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
    fontFamily: 'NotoSansSC-Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
  },
  calendarSection: {
    marginBottom: 24,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  calendarTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'NotoSansSC-Medium',
  },
  navigationButtons: {
    flexDirection: 'row',
  },
  navButton: {
    padding: 4,
  },
  calendarCard: {
    padding: 12,
  },
  weekDaysRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  weekDayCell: {
    width: 35,
    alignItems: 'center',
  },
  weekDayText: {
    fontSize: 14,
    color: '#666666',
    fontFamily: 'NotoSansSC-Regular',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  emptyCell: {
    width: 35,
    height: 35,
    margin: 2,
  },
  taskSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
    fontFamily: 'NotoSansSC-Medium',
  },
  taskCard: {
    padding: 16,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  taskIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4080FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  taskIconText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'NotoSansSC-Medium',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#EEEEEE',
    borderRadius: 4,
    marginRight: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4080FF',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666666',
    width: 40,
    textAlign: 'right',
  },
  continueButton: {
    alignSelf: 'flex-end',
  },
});