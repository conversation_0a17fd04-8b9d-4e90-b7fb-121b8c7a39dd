import React, {useCallback, useEffect, useState} from 'react';
import {Image, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {useFocusEffect, useRouter} from 'expo-router';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import StatCard from '@/components/profile/StatCard';
import MenuOption from '@/components/profile/MenuOption';
import ContactModal from '@/components/common/ContactModal';
import {showGlobalToast} from '@/components/common/toast/ToastProvider';
import {
  Award,
  Book,
  Bookmark,
  Clock as ClockIcon,
  FileText,
  GraduationCap,
  Heart,
  Lock,
  MessageCircle,
  Settings,
  User as User2,
  UserCircle,
} from 'lucide-react-native';
import {LinearGradient} from 'expo-linear-gradient';

// 导入用户信息类型
interface UserInfo {
  name: string;
  avatarUrl: string;
  level: string;
  id: string;
  role: string;
  school: string;
  isLoggedIn: boolean;
}

export default function ProfileScreen() {
  const [contactModalVisible, setContactModalVisible] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const router = useRouter();

  // 检查登录状态 - 页面挂载时执行
  useEffect(() => {
    checkLoginStatus();
  }, []);

  // 页面每次获取焦点时检查登录状态
  useFocusEffect(
    useCallback(() => {
      checkLoginStatus();
    }, [])
  );

  // 检查登录状态的函数
  const checkLoginStatus = () => {
    // 实际项目中应该从AsyncStorage或其他存储中读取用户信息
    // 这里简化处理，从全局变量读取
    if (global.userInfo && global.userInfo.isLoggedIn) {
      if (!isLoggedIn) {
        setIsLoggedIn(true);
        setUserInfo(global.userInfo);
      }
    } else {
      if (isLoggedIn) {
        setIsLoggedIn(false);
        setUserInfo(null);
      }
    }
  };

  // 联系方式信息
  const contactInfo = {
    phone: '18888888888',
    wechat: 'dfkt2024',
    qrcode:
      'https://images.pexels.com/photos/4126684/pexels-photo-4126684.jpeg',
  };

  // 默认用户信息（已登录时使用）
  const defaultUserInfo: UserInfo = {
    name: '张小鹿',
    avatarUrl:
      'https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg',
    level: '青少年',
    id: '12345678',
    role: '高中一年级',
    school: '未来科技中学',
    isLoggedIn: true,
  };

  // 使用userInfo或默认值
  const user = userInfo || defaultUserInfo;

  // 学习统计
  const stats = [
    {
      id: 1,
      value: 12,
      label: '收藏课程',
      icon: <Bookmark size={16} color="#FF6B00" />,
    },
    {
      id: 2,
      value: 36,
      label: '学习课时',
      icon: <ClockIcon size={16} color="#4080FF" />,
    },
    {
      id: 3,
      value: 8,
      label: '已完成课程',
      icon: <Award size={16} color="#52C41A" />,
    },
  ];

  // 处理登录
  const handleLogin = () => {
    router.push('/login');
  };

  // 处理登出
  const handleLogout = () => {
    // 清除用户登录状态
    global.userInfo = null;
    setIsLoggedIn(false);
    setUserInfo(null);

    // 显示登出提示
    showGlobalToast({
      message: '已退出登录',
      type: 'info',
      duration: 2000,
      position: 'top',
    });
  };

  // 未登录时的个人页面
  const renderNotLoggedIn = () => {
    return (
      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <LinearGradient
            colors={['#4080FF', '#6E9CFF']}
            style={styles.headerGradient}
          >
            <View style={styles.notLoggedInContainer}>
              <View style={styles.avatarPlaceholder}>
                <UserCircle size={48} color="#FFFFFF" />
              </View>
              <Text style={styles.loginPromptText}>登录账号以使用更多功能</Text>
              <Button
                title="登录/注册"
                onPress={handleLogin}
                type="outline"
                style={styles.loginButton}
                textStyle={styles.loginButtonText}
              />
            </View>
          </LinearGradient>

          <View style={styles.contentContainer}>
            <View style={styles.sectionContainer}>
              <Card style={[styles.menuCard, styles.supportCard]}>
                <MenuOption
                  icon={<MessageCircle size={22} color="#FF6B00" />}
                  title="客服咨询"
                  onPress={() => setContactModalVisible(true)}
                  customStyle={styles.supportMenuOption}
                />
              </Card>
            </View>

            <View style={styles.sectionContainer}>
              <View style={styles.profileOptionsHeader}>
                <Lock size={18} color="#4080FF" />
                <Text style={styles.profileOptionsTitle}>账号与安全</Text>
              </View>
              <View style={styles.profileOptionsContainer}>
                <Card
                  style={[
                    styles.menuCard,
                    styles.singleOptionCard,
                    styles.firstCard,
                  ]}
                >
                  <MenuOption
                    icon={<Settings size={22} color="#4080FF" />}
                    title="设置"
                    onPress={() => {}}
                    customStyle={styles.singleMenuOption}
                  />
                </Card>
              </View>
            </View>
          </View>
        </ScrollView>
        <ContactModal
          visible={contactModalVisible}
          onClose={() => setContactModalVisible(false)}
          contactInfo={contactInfo}
          title="客服咨询"
          subtitle="有任何问题或需要帮助，请随时联系我们的客服团队"
        />
      </SafeAreaView>
    );
  };

  // 已登录时的个人页面
  const renderLoggedIn = () => {
    return (
      <SafeAreaView style={styles.safeArea}>
        <ContactModal
          visible={contactModalVisible}
          onClose={() => setContactModalVisible(false)}
          contactInfo={contactInfo}
          title="客服咨询"
          subtitle="有任何问题或需要帮助，请随时联系我们的客服团队"
        />
        <ScrollView
          style={styles.container}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <LinearGradient
            colors={['#4080FF', '#6E9CFF']}
            style={styles.headerGradient}
          >
            <View style={styles.profileCard}>
              <View style={styles.profileHeader}>
                <Image source={{ uri: user.avatarUrl }} style={styles.avatar} />
                <View style={styles.profileInfo}>
                  <Text style={styles.userName}>{user.name}</Text>
                  <View style={styles.badgeContainer}>
                    <View style={styles.levelBadge}>
                      <GraduationCap size={12} color="#FFFFFF" />
                      <Text style={styles.badgeText}>{user.level}</Text>
                    </View>
                    <Text style={styles.userId}>ID: {user.id}</Text>
                  </View>
                  <Text style={styles.schoolInfo}>
                    <Book size={12} color="#FFFFFF" style={styles.schoolIcon} />
                    {user.school} · {user.role}
                  </Text>
                </View>
              </View>
              <TouchableOpacity style={styles.editButton}>
                <Text style={styles.editButtonText}>编辑资料</Text>
              </TouchableOpacity>
            </View>
          </LinearGradient>

          <View style={styles.contentContainer}>
            <Card style={styles.statsCard}>
              <View style={styles.statsContainer}>
                {stats.map((stat) => (
                  <View key={stat.id} style={styles.statItem}>
                    <StatCard
                      value={stat.value}
                      label={stat.label}
                      icon={stat.icon}
                    />
                    {stat.id < stats.length && (
                      <View style={styles.statDivider} />
                    )}
                  </View>
                ))}
              </View>
            </Card>

            <View style={styles.sectionContainer}>
              <Card style={[styles.menuCard, styles.supportCard]}>
                <MenuOption
                  icon={<MessageCircle size={22} color="#FF6B00" />}
                  title="客服咨询"
                  onPress={() => setContactModalVisible(true)}
                  customStyle={styles.supportMenuOption}
                />
              </Card>
            </View>

            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <GraduationCap size={18} color="#4080FF" />
                <Text style={styles.sectionTitle}>学习管理</Text>
              </View>
              <Card style={styles.menuCard}>
                <MenuOption
                  icon={<Heart size={20} color="#FF6B00" />}
                  title="我的收藏"
                  onPress={() => router.push('/favorites')}
                />
                <MenuOption
                  icon={<ClockIcon size={20} color="#4080FF" />}
                  title="学习记录"
                  onPress={() => router.push('/learning-records')}
                />
                <MenuOption
                  icon={<FileText size={20} color="#52C41A" />}
                  title="学习报告"
                  onPress={() => router.push('/learning-report')}
                />
              </Card>
            </View>

            <View style={styles.sectionContainer}>
              <View style={styles.profileOptionsHeader}>
                <Lock size={18} color="#4080FF" />
                <Text style={styles.profileOptionsTitle}>账号与安全</Text>
              </View>
              <View style={styles.profileOptionsContainer}>
                <Card
                  style={[
                    styles.menuCard,
                    styles.singleOptionCard,
                    styles.firstCard,
                  ]}
                >
                  <MenuOption
                    icon={<User2 size={22} color="#722ED1" />}
                    title="个人资料"
                    onPress={() => {}}
                    customStyle={styles.singleMenuOption}
                  />
                  <MenuOption
                    icon={<Settings size={22} color="#4080FF" />}
                    title="设置"
                    onPress={() => {}}
                    customStyle={styles.singleMenuOption}
                  />
                </Card>
              </View>
            </View>

            <Button
              title="退出登录"
              onPress={handleLogout}
              type="outline"
              style={styles.logoutButton}
              textStyle={{ color: '#FF3B30' }}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  };

  return isLoggedIn ? renderLoggedIn() : renderNotLoggedIn();
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  headerGradient: {
    paddingTop: 45,
    paddingBottom: 40,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    marginBottom: 5,
  },
  // 已登录的样式
  profileCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginRight: 16,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  profileInfo: {
    justifyContent: 'center',
  },
  userName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 6,
    fontFamily: 'NotoSansSC-Bold',
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  levelBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 3,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  badgeText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
    marginLeft: 4,
  },
  userId: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: 'NotoSansSC-Regular',
  },
  schoolInfo: {
    fontSize: 12,
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Regular',
    flexDirection: 'row',
    alignItems: 'center',
  },
  schoolIcon: {
    marginRight: 4,
  },
  editButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  editButtonText: {
    fontSize: 13,
    color: '#FFFFFF',
    fontFamily: 'NotoSansSC-Medium',
  },
  // 未登录的样式
  notLoggedInContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  loginPromptText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 16,
    fontFamily: 'NotoSansSC-Medium',
  },
  loginButton: {
    backgroundColor: 'transparent',
    borderColor: '#FFFFFF',
    paddingHorizontal: 32,
    paddingVertical: 10,
    borderRadius: 20,
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
  },
  // 共用样式
  contentContainer: {
    padding: 10,
    marginTop: -55,
  },
  statsCard: {
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    paddingVertical: 8,
    borderRadius: 15,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statDivider: {
    width: 1,
    height: 45,
    backgroundColor: '#EEEEEE',
    marginHorizontal: 8,
  },
  sectionContainer: {
    marginTop: -20,
    marginBottom: 40,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
    fontFamily: 'NotoSansSC-Medium',
  },
  menuCard: {
    padding: 0,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderRadius: 15,
  },
  logoutButton: {
    marginBottom: 32,
    marginTop: 20,
    borderRadius: 12,
    paddingVertical: 12,
    borderColor: '#FF3B30',
  },
  profileOptionsContainer: {
    marginTop: 10,
  },
  profileOptionsHeader: {
    paddingHorizontal: 4,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileOptionsTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'NotoSansSC-Medium',
    marginLeft: 8,
  },
  singleOptionCard: {
    borderRadius: 15,
    marginHorizontal: 2,
  },
  singleMenuOption: {
    paddingVertical: 16,
    borderBottomWidth: 0,
  },
  firstCard: {
    marginBottom: 12,
  },
  secondCard: {
    marginBottom: 5,
  },
  supportCard: {
    marginTop: 10,
    marginHorizontal: 2,
  },
  supportMenuOption: {
    paddingVertical: 16,
    borderBottomWidth: 0,
  },
});
