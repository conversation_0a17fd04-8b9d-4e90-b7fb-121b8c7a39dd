import {Link, Stack} from 'expo-router';
import React from 'react';
import {StyleSheet, Text, View} from 'react-native';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <View style={styles.container}>
        <Text style={styles.text}>此页面不存在。</Text>
        <Link href="/" style={styles.link}>
          <Text style={styles.linkText}>返回首页!</Text>
        </Link>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  text: {
    fontSize: 20,
    fontWeight: '600',
    fontFamily: 'NotoSansSC-Medium',
    color: '#333333',
    marginBottom: 16,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
  linkText: {
    fontSize: 16,
    color: '#4080FF',
    fontFamily: 'NotoSansSC-Regular',
  }
});