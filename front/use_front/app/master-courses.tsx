import React, {useState} from 'react';
import {FlatList, Image, SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {useRouter} from 'expo-router';
import {BadgeCheck, ChevronLeft, Clock, Star,} from 'lucide-react-native';

// 模拟数据 - 名师课程
const MASTER_COURSES = [
  {
    id: 1,
    title: '高中物理难点突破',
    imageUrl:
      'https://images.pexels.com/photos/714699/pexels-photo-714699.jpeg',
    masterName: '张伟教授',
    masterTitle: '北京大学物理学博士',
    masterAvatar:
      'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg',
    rating: 4.9,
    studentCount: 3256,
    totalLessons: 32,
    totalHours: 48,
    price: '599.00',
    originalPrice: '899.00',
    category: '物理',
    level: '高中',
    tags: ['名师', '物理', '重难点'],
    description:
      '张伟教授深入浅出讲解高中物理重难点，从力学到电磁学，全面覆盖高考物理考点，帮助学生攻克物理难关。',
    highlight: ['北京大学物理学博士', '全国优秀教师', '15年高中物理教学经验'],
  },
  {
    id: 2,
    title: '考研英语精讲班',
    imageUrl:
      'https://images.pexels.com/photos/267669/pexels-photo-267669.jpeg',
    masterName: '王芳老师',
    masterTitle: '剑桥大学语言学硕士',
    masterAvatar:
      'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg',
    rating: 4.8,
    studentCount: 5128,
    totalLessons: 48,
    totalHours: 72,
    price: '799.00',
    originalPrice: '1099.00',
    category: '英语',
    level: '考研',
    tags: ['考研英语', '阅读', '写作'],
    description:
      '王芳老师结合多年考研英语教学经验，针对性讲解考研英语重难点，包括阅读理解、翻译、写作等各部分应试技巧。',
    highlight: [
      '剑桥大学语言学硕士',
      '新东方英语名师',
      '著有《考研英语高分突破》',
    ],
  },
  {
    id: 3,
    title: 'Web前端开发实战课',
    imageUrl:
      'https://images.pexels.com/photos/270360/pexels-photo-270360.jpeg',
    masterName: '陈俊讲师',
    masterTitle: '前BAT高级前端工程师',
    masterAvatar:
      'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg',
    rating: 4.9,
    studentCount: 4235,
    totalLessons: 56,
    totalHours: 84,
    price: '899.00',
    originalPrice: '1299.00',
    category: '编程',
    level: '进阶',
    tags: ['前端开发', 'React', 'Vue'],
    description:
      '陈俊老师从实战角度出发，系统讲解前端开发技术栈，包括HTML5、CSS3、JavaScript、React和Vue等，并带领学生开发多个实战项目。',
    highlight: ['前BAT高级前端工程师', '10年前端开发经验', '开源项目作者'],
  },
  {
    id: 4,
    title: '高考作文写作技巧',
    imageUrl:
      'https://images.pexels.com/photos/261510/pexels-photo-261510.jpeg',
    masterName: '李明教授',
    masterTitle: '清华大学中文系教授',
    masterAvatar:
      'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
    rating: 4.7,
    studentCount: 6789,
    totalLessons: 24,
    totalHours: 36,
    price: '499.00',
    originalPrice: '699.00',
    category: '语文',
    level: '高中',
    tags: ['高考作文', '写作技巧', '素材积累'],
    description:
      '李明教授深入浅出讲解高考作文写作技巧，包括立意、结构、素材积累等方面，帮助学生在高考中取得高分。',
    highlight: [
      '清华大学中文系教授',
      '曾任高考阅卷组长',
      '著有《高考作文写作指南》',
    ],
  },
];

// 名师课程卡片组件
const MasterCourseCard = ({ course, onPress }) => (
  <TouchableOpacity style={styles.courseCard} onPress={onPress}>
    <Image source={{ uri: course.imageUrl }} style={styles.courseImage} />

    <View style={styles.masterAvatarContainer}>
      <Image
        source={{ uri: course.masterAvatar }}
        style={styles.masterAvatar}
      />
    </View>

    <View style={styles.courseContent}>
      <View style={styles.titleRow}>
        <View style={styles.categoryBadge}>
          <Text style={styles.categoryText}>{course.category}</Text>
        </View>
        <Text style={styles.levelBadge}>{course.level}</Text>
      </View>

      <Text style={styles.courseTitle}>{course.title}</Text>

      <View style={styles.masterInfo}>
        <Text style={styles.masterName}>{course.masterName}</Text>
        <Text style={styles.masterTitle}>{course.masterTitle}</Text>
      </View>

      <View style={styles.statsRow}>
        <View style={styles.ratingContainer}>
          <Star size={14} color="#FFB800" fill="#FFB800" />
          <Text style={styles.ratingText}>{course.rating}</Text>
          <Text style={styles.studentCount}>({course.studentCount}人学习)</Text>
        </View>

        <View style={styles.lessonInfo}>
          <Clock size={14} color="#666666" />
          <Text style={styles.lessonText}>{course.totalHours}课时</Text>
        </View>
      </View>

      <View style={styles.highlightContainer}>
        {course.highlight.slice(0, 2).map((item, index) => (
          <View key={index} style={styles.highlightItem}>
            <BadgeCheck size={14} color="#4080FF" />
            <Text style={styles.highlightText}>{item}</Text>
          </View>
        ))}
      </View>

      <View style={styles.priceRow}>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>¥{course.price}</Text>
          <Text style={styles.originalPrice}>¥{course.originalPrice}</Text>
        </View>
        <TouchableOpacity style={styles.enrollButton}>
          <Text style={styles.enrollButtonText}>立即购买</Text>
        </TouchableOpacity>
      </View>
    </View>
  </TouchableOpacity>
);

export default function MasterCoursesScreen() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('全部');

  const categories = ['全部', '数学', '英语', '物理', '化学', '语文', '编程'];

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ChevronLeft size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>名师课堂</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.categoryFilter}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {categories.map((category, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.categoryButton,
                selectedCategory === category && styles.selectedCategoryButton,
              ]}
              onPress={() => setSelectedCategory(category)}
            >
              <Text
                style={[
                  styles.categoryButtonText,
                  selectedCategory === category && styles.selectedCategoryText,
                ]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.container}>
        <Text style={styles.sectionTitle}>名师授课 · 精品课程</Text>
        <Text style={styles.sectionDescription}>
          汇聚国内外知名教授、行业专家和资深讲师，
          提供高品质、有深度的精品课程。
        </Text>

        <FlatList
          data={MASTER_COURSES}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <MasterCourseCard
              course={item}
              onPress={() => router.push(`/master-courses/${item.id}` as any)}
            />
          )}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.courseList}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  placeholder: {
    width: 32,
  },
  categoryFilter: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    marginLeft: 8,
  },
  selectedCategoryButton: {
    backgroundColor: '#4080FF',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#666666',
  },
  selectedCategoryText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 16,
  },
  courseList: {
    paddingBottom: 16,
  },
  courseCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  courseImage: {
    width: '100%',
    height: 160,
    resizeMode: 'cover',
  },
  masterAvatarContainer: {
    position: 'absolute',
    top: 210,
    left: 6,
    backgroundColor: '#FFFFFF',
    padding: 2,
    borderRadius: 30,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  masterAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  courseContent: {
    padding: 16,
    paddingTop: 24,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryBadge: {
    backgroundColor: '#E6F7FF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 12,
    color: '#1890FF',
  },
  levelBadge: {
    fontSize: 12,
    color: '#666666',
  },
  courseTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    marginLeft: 58,
  },
  masterInfo: {
    marginBottom: 8,
    marginLeft: 58,
  },
  masterName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
  },
  masterTitle: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFB800',
    marginLeft: 4,
  },
  studentCount: {
    fontSize: 12,
    color: '#999999',
    marginLeft: 4,
  },
  lessonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lessonText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  highlightContainer: {
    marginBottom: 12,
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  highlightText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FF6B00',
    marginRight: 8,
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  enrollButton: {
    backgroundColor: '#4080FF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  enrollButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
