// 获取环境变量
const APP_ENV = process.env.APP_ENV || 'development';

// 环境配置
const envConfig = {
  development: {
    name: "鼎峰课堂 - 开发版",
    slug: "dianfeng-classroom-dev",
    version: "1.0.0-dev",
    apiUrl: "http://localhost:8082/api",
    scheme: "dianfeng-dev",
  },
  test: {
    name: "鼎峰课堂 - 测试版",
    slug: "dianfeng-classroom-test", 
    version: "1.0.0-test",
    apiUrl: "http://test-api.dianfeng.com/api",
    scheme: "dianfeng-test",
  },
  staging: {
    name: "鼎峰课堂 - 预发布版",
    slug: "dianfeng-classroom-staging",
    version: "1.0.0-staging", 
    apiUrl: "http://staging-api.dianfeng.com/api",
    scheme: "dianfeng-staging",
  },
  production: {
    name: "鼎峰课堂",
    slug: "dianfeng-classroom",
    version: "1.0.0",
    apiUrl: "https://api.dianfeng.com/api",
    scheme: "dianfeng",
  }
};

const config = envConfig[APP_ENV] || envConfig.development;

export default {
  expo: {
    name: config.name,
    slug: config.slug,
    version: config.version,
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: `com.dianfeng.classroom.${APP_ENV}`,
      buildNumber: "1.0.0"
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      package: `com.dianfeng.classroom.${APP_ENV}`,
      versionCode: 1
    },
    web: {
      favicon: "./assets/favicon.png",
      bundler: "metro"
    },
    scheme: config.scheme,
    extra: {
      apiUrl: config.apiUrl,
      environment: APP_ENV,
      eas: {
        projectId: "your-project-id-here"
      }
    },
    plugins: [
      "expo-router"
    ]
  }
};
