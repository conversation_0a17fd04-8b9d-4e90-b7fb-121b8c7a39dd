{"name": "dingfeng-classroom", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start --dev-client", "dev:test": "EXPO_NO_TELEMETRY=1 APP_ENV=test expo start --dev-client", "dev:staging": "EXPO_NO_TELEMETRY=1 APP_ENV=staging expo start --dev-client", "build:web": "expo export --platform web", "build:web:dev": "APP_ENV=development expo export --platform web", "build:web:test": "APP_ENV=test expo export --platform web", "build:web:staging": "APP_ENV=staging expo export --platform web", "build:web:prod": "APP_ENV=production expo export --platform web", "build:android": "expo build:android", "build:android:dev": "APP_ENV=development expo build:android", "build:android:test": "APP_ENV=test expo build:android", "build:android:staging": "APP_ENV=staging expo build:android", "build:android:prod": "APP_ENV=production expo build:android", "build:ios": "expo build:ios", "build:ios:dev": "APP_ENV=development expo build:ios", "build:ios:test": "APP_ENV=test expo build:ios", "build:ios:staging": "APP_ENV=staging expo build:ios", "build:ios:prod": "APP_ENV=production expo build:ios", "lint": "expo lint"}, "dependencies": {"@expo-google-fonts/noto-sans-sc": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@gorhom/portal": "^1.0.14", "@lucide/lab": "^0.1.2", "@react-native/normalize-colors": "^0.79.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "china-location": "^2.1.0", "expo": "^53.0.0", "expo-blur": "~14.1.4", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~5.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-china-location": "^1.1.1", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}