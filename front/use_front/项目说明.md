# 鼎峰课堂 - 在线教育平台

## 项目概述

"鼎峰课堂"是一个面向各年龄段学习者的在线教育平台，采用 React Native + Expo 开发的跨平台移动应用。平台提供丰富的在线课程，涵盖设计、编程、语言、数学、物理等多个学科领域，并针对青少年、大学生和成人等不同年龄段用户提供个性化学习体验。

## 功能模块

### 1. 用户账户管理
- **登录功能**：支持账号密码登录，提供密码显示/隐藏功能
- **注册功能**：新用户注册流程
- **个人资料管理**：查看和编辑个人信息
- **账户安全设置**：密码修改等安全功能

### 2. 首页功能
- **课程推荐**：精选优质课程展示
- **特训营/名师课/一对一**：特色服务入口
- **搜索功能**：支持课程和讲师搜索
- **直播预告**：即将开始的直播课程提醒

### 3. 课程浏览与筛选
- **课程分类**：按学科分类（设计、编程、语言、数学、物理等）
- **年龄段筛选**：支持按目标受众（青少年、大学生、成人）筛选
- **搜索功能**：支持关键词搜索课程和讲师
- **最新上架**：展示最新发布的课程

### 4. 课程详情
- **课程概述**：包含课程标题、介绍、价格、学习人数等信息
- **课程大纲**：章节列表和课时安排
- **讲师信息**：讲师介绍、教学经验和专业背景
- **学员评价**：课程评分和学员反馈
- **相关服务**：课程咨询和联系方式

### 5. 学习追踪
- **每日打卡**：学习打卡记录功能
- **打卡日历**：可视化展示月度打卡情况
- **学习统计**：累计打卡天数、连续打卡天数、平均学习时长等
- **学习任务**：当日学习任务和进度追踪

### 6. 个人中心
- **个人信息展示**：用户资料、学习级别等
- **学习统计**：收藏课程数、学习课时、已完成课程数
- **功能入口**：我的课程、订单管理、收藏夹、优惠券等
- **设置**：账号设置、通知设置、关于我们、帮助中心
- **客服支持**：客服咨询通道

### 7. 课程学习
- **视频播放**：课程视频内容观看
- **进度记录**：自动记录学习进度
- **学习打卡**：完成学习内容后打卡确认

## 技术栈

- **前端框架**：React Native, Expo
- **路由导航**：Expo Router
- **UI组件**：自定义UI组件库
- **状态管理**：React Hooks (useState, useEffect)
- **样式管理**：React Native StyleSheet
- **图标库**：Lucide React Native
- **UI增强**：Expo LinearGradient

## 项目结构

```
/app                           # 应用主要页面
  /(tabs)/                     # 底部导航标签页
    index.tsx                  # 首页
    courses.tsx                # 课程页
    checkin.tsx                # 打卡页
    profile.tsx                # 个人中心
    _layout.tsx                # 标签页布局
  /course/                     # 课程相关页面
    [id].tsx                   # 课程详情页
  login.tsx                    # 登录页
  register.tsx                 # 注册页
  _layout.tsx                  # 应用主布局

/components                    # 组件库
  /ui/                         # 通用UI组件
    Button.tsx                 # 按钮组件
    Card.tsx                   # 卡片组件
    CategoryButton.tsx         # 分类按钮
    CourseCard.tsx             # 课程卡片
    SearchBar.tsx              # 搜索栏
  /common/                     # 通用业务组件
    ContactModal.tsx           # 联系方式弹窗
    /toast/                    # 提示组件
  /home/<USER>
  /profile/                    # 个人中心组件
  /checkin/                    # 打卡页面组件

/assets                        # 静态资源文件
/hooks                         # 自定义hooks
/types                         # TypeScript类型定义
```

## 项目特点

1. **用户体验**：精心设计的UI界面，流畅的交互体验
2. **完整功能**：涵盖在线教育平台核心功能模块
3. **目标导向**：以学习追踪和打卡系统促进用户持续学习
4. **多元化内容**：支持多学科、多年龄段的课程分类
5. **个性化推荐**：根据用户特点推荐合适课程

## 使用说明

1. 首次使用需注册账号
2. 浏览首页或课程页查找感兴趣的课程
3. 在课程详情页了解课程信息并进行选择
4. 通过每日打卡功能记录学习轨迹
5. 在个人中心管理学习记录和个人信息 


1、后端对象存储
2、java 17 + Mybatis-plus + mysql