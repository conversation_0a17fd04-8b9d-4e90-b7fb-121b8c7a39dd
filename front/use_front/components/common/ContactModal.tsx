import React from 'react';
import {
  Clipboard,
  Dimensions,
  Image,
  Linking,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {Copy, ExternalLink, MessageSquare, Phone, X,} from 'lucide-react-native';
import {showGlobalToast} from './toast/ToastProvider';

interface ContactInfo {
  phone: string;
  wechat: string;
  qrcode: string;
}

interface ContactModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  contactInfo: ContactInfo;
  phoneLabel?: string;
  wechatLabel?: string;
  qrcodeLabel?: string;
  noteText?: string;
}

const ContactModal: React.FC<ContactModalProps> = ({
  visible,
  onClose,
  title = '联系咨询',
  subtitle = '欢迎咨询相关信息，我们将为您提供专业解答',
  contactInfo,
  phoneLabel = '电话咨询',
  wechatLabel = '微信咨询',
  qrcodeLabel = '微信扫码添加咨询',
  noteText = '温馨提示：工作时间 9:00-21:00，咨询时请说明来自"鼎峰课堂App"',
}) => {
  // 处理拨打电话
  const handleCallPhone = () => {
    Linking.openURL(`tel:${contactInfo.phone}`).catch((err) => {
      showGlobalToast({
        type: 'error',
        message: '无法拨打电话，请稍后再试或手动拨打',
        duration: 2000,
      });
    });
  };

  // 处理复制微信号
  const handleCopyWechat = () => {
    Clipboard.setString(contactInfo.wechat);
    showGlobalToast({
      type: 'success',
      message: `微信号已复制: ${contactInfo.wechat}`,
      duration: 2000,
    });
  };

  const renderModalContent = () => (
    <View style={styles.modalOverlay}>
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{title}</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <X size={20} color="#666" />
          </TouchableOpacity>
        </View>

        <Text style={styles.modalSubtitle}>{subtitle}</Text>

        <View style={styles.contactOptions}>
          <TouchableOpacity
            style={styles.contactOption}
            onPress={handleCallPhone}
            activeOpacity={0.7}
          >
            <View
              style={[styles.contactIconBg, { backgroundColor: '#4080FF' }]}
            >
              <Phone size={20} color="#FFFFFF" />
            </View>
            <View style={styles.contactContentWrapper}>
              <Text style={styles.contactLabel}>{phoneLabel}</Text>
              <View style={styles.phoneContainer}>
                <Text style={styles.contactValue}>{contactInfo.phone}</Text>
                <ExternalLink
                  size={14}
                  color="#4080FF"
                  style={styles.phoneIcon}
                />
              </View>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.contactOption}
            onPress={handleCopyWechat}
            activeOpacity={0.7}
          >
            <View
              style={[styles.contactIconBg, { backgroundColor: '#52C41A' }]}
            >
              <MessageSquare size={20} color="#FFFFFF" />
            </View>
            <View style={styles.contactContentWrapper}>
              <Text style={styles.contactLabel}>{wechatLabel}</Text>
              <View style={styles.wechatContainer}>
                <Text style={styles.contactValue}>{contactInfo.wechat}</Text>
                <View style={styles.copyButton}>
                  <Copy size={12} color="#52C41A" style={styles.copyIcon} />
                  <Text style={styles.copyText}>复制</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>

        <View style={styles.qrcodeWrapper}>
          <Text style={styles.qrcodeLabel}>{qrcodeLabel}</Text>
          <View style={styles.qrcodeImageContainer}>
            <Image
              source={{ uri: contactInfo.qrcode }}
              style={styles.qrcodeImage}
            />
          </View>
        </View>

        <View style={styles.contactNote}>
          <Text style={styles.noteText}>{noteText}</Text>
        </View>
      </View>
    </View>
  );

  if (!visible) return null;

  // 使用正常的Modal，但是Toast会通过全局系统显示
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      {renderModalContent()}
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    elevation: 999,
  },
  modalContainer: {
    width: width * 0.88,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    fontFamily: 'NotoSansSC-Bold',
  },
  closeButton: {
    padding: 6,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
    lineHeight: 20,
  },
  contactOptions: {
    marginBottom: 20,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  contactContentWrapper: {
    flex: 1,
    justifyContent: 'center',
  },
  contactIconBg: {
    width: 42,
    height: 42,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 6,
    fontFamily: 'NotoSansSC-Medium',
  },
  contactValue: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  phoneIcon: {
    marginLeft: 6,
  },
  wechatContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(82, 196, 26, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  copyIcon: {
    marginRight: 4,
  },
  copyText: {
    fontSize: 12,
    color: '#52C41A',
    fontWeight: '500',
  },
  qrcodeWrapper: {
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 24,
  },
  qrcodeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  qrcodeImageContainer: {
    padding: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  qrcodeImage: {
    width: 160,
    height: 160,
    borderRadius: 4,
  },
  contactNote: {
    backgroundColor: '#F7F9FC',
    borderRadius: 12,
    padding: 14,
  },
  noteText: {
    fontSize: 13,
    color: '#888',
    lineHeight: 18,
    textAlign: 'center',
  },
});

export default ContactModal;
