import React, {useEffect, useRef} from 'react';
import {Animated, Dimensions, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View,} from 'react-native';
import {AlertCircle, AlertTriangle, CheckCircle, Info, X,} from 'lucide-react-native';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

interface ToastMessageProps {
  visible: boolean;
  type: ToastType;
  message: string;
  duration?: number;
  onClose?: () => void;
  position?: 'top' | 'bottom';
  showClose?: boolean;
}

const ToastMessage: React.FC<ToastMessageProps> = ({
  visible,
  type = 'info',
  message,
  duration = 3000,
  onClose,
  position = 'top',
  showClose = true,
}) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(
    new Animated.Value(position === 'top' ? -100 : 100)
  ).current;
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (visible) {
      // 显示Toast
      showToast();

      // 设置自动关闭
      if (duration > 0) {
        timeoutRef.current = setTimeout(() => {
          hideToast();
        }, duration);
      }
    } else {
      // 隐藏Toast
      hideToast();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [visible, duration]);

  // 显示动画
  const showToast = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // 隐藏动画
  const hideToast = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: position === 'top' ? -100 : 100,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (onClose) {
        onClose();
      }
    });
  };

  // 根据类型获取颜色
  const getTypeColor = () => {
    switch (type) {
      case 'success':
        return '#52C41A';
      case 'error':
        return '#FF4D4F';
      case 'warning':
        return '#FAAD14';
      case 'info':
      default:
        return '#4080FF';
    }
  };

  // 根据类型获取图标
  const getTypeIcon = () => {
    const color = getTypeColor();
    const size = 20;

    switch (type) {
      case 'success':
        return <CheckCircle size={size} color={color} />;
      case 'error':
        return <AlertCircle size={size} color={color} />;
      case 'warning':
        return <AlertTriangle size={size} color={color} />;
      case 'info':
      default:
        return <Info size={size} color={color} />;
    }
  };

  // 计算位置样式
  const getPositionStyle = () => {
    const statusBarHeight = StatusBar.currentHeight || 0;
    const topOffset = Platform.OS === 'ios' ? 50 : statusBarHeight + 20;

    return {
      [position]: position === 'top' ? topOffset : 60,
    };
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        getPositionStyle(),
        {
          opacity,
          transform: [{ translateY }],
        },
      ]}
      pointerEvents="box-none"
    >
      <View
        style={[
          styles.toast,
          {
            borderLeftColor: getTypeColor(),
            backgroundColor: type === 'warning' ? '#FFFBE6' : '#FFFFFF',
          },
        ]}
      >
        <View style={styles.iconContainer}>{getTypeIcon()}</View>
        <Text style={styles.message}>{message}</Text>
        {showClose && (
          <TouchableOpacity
            style={styles.closeButton}
            onPress={hideToast}
            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
          >
            <X size={16} color="#999999" />
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 9999999,
    elevation: 9999999,
    pointerEvents: 'box-none',
  },
  toast: {
    flexDirection: 'row',
    alignItems: 'center',
    width: width * 0.9,
    maxWidth: 450,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    backgroundColor: '#FFFFFF',
    ...Platform.select({
      ios: {
        shadowColor: 'rgba(0,0,0,0.5)',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.5,
        shadowRadius: 5,
      },
      android: {
        elevation: 25,
      },
    }),
  },
  iconContainer: {
    marginRight: 12,
  },
  message: {
    flex: 1,
    fontSize: 14,
    color: '#333333',
    fontFamily: 'NotoSansSC-Regular',
  },
  closeButton: {
    marginLeft: 12,
    padding: 4,
  },
});

export default ToastMessage;
