import React, {createContext, useContext, useEffect, useRef, useState,} from 'react';
import {AppState, AppStateStatus, StyleSheet, View} from 'react-native';
import {PortalProvider} from '@gorhom/portal';
import ToastMessage, {ToastType} from './ToastMessage';

interface ToastOptions {
  message: string;
  type?: ToastType;
  duration?: number;
  position?: 'top' | 'bottom';
  showClose?: boolean;
}

interface ToastContextProps {
  showToast: (options: ToastOptions) => void;
}

// 创建Context
const ToastContext = createContext<ToastContextProps | undefined>(undefined);

// 创建全局Toast实例，确保在任何Modal之上显示
let globalToastQueue: ToastOptions[] = [];
let isDisplayingGlobalToast = false;
let globalShowToastCallback: ((options: ToastOptions) => void) | null = null;

// 全局显示Toast的函数，可以在任何地方调用
export const showGlobalToast = (options: ToastOptions) => {
  if (globalShowToastCallback) {
    globalShowToastCallback(options);
  } else {
    // 如果回调未设置，将消息添加到队列
    globalToastQueue.push(options);
  }
};

// 提供者组件
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<ToastType>('info');
  const [duration, setDuration] = useState(3000);
  const [position, setPosition] = useState<'top' | 'bottom'>('top');
  const [showClose, setShowClose] = useState(true);

  // 使用ref跟踪队列中的toast
  const toastQueue = useRef<ToastOptions[]>([]);
  const isDisplaying = useRef(false);

  // 监听应用状态变化
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        nextAppState === 'active' &&
        toastQueue.current.length > 0 &&
        !isDisplaying.current
      ) {
        displayNextToast();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => {
      subscription.remove();
    };
  }, []);

  // 处理全局队列
  useEffect(() => {
    // 设置全局回调
    globalShowToastCallback = showToast;

    // 检查是否有排队的消息
    if (globalToastQueue.length > 0 && !isDisplayingGlobalToast) {
      const pendingToasts = [...globalToastQueue];
      globalToastQueue = [];

      // 添加到本地队列
      pendingToasts.forEach((toast) => {
        toastQueue.current.push(toast);
      });

      // 如果当前没有显示任何toast，显示下一个
      if (!isDisplaying.current) {
        displayNextToast();
      }
    }

    return () => {
      globalShowToastCallback = null;
    };
  }, []);

  // 关闭当前toast并从队列中移除
  const handleClose = () => {
    setVisible(false);
    isDisplaying.current = false;
    isDisplayingGlobalToast = false;

    // 延迟后检查队列
    setTimeout(() => {
      if (toastQueue.current.length > 0) {
        // 显示队列中的下一个toast
        displayNextToast();
      }
    }, 300); // 等待关闭动画结束
  };

  // 显示队列中的下一个toast
  const displayNextToast = () => {
    if (toastQueue.current.length === 0 || isDisplaying.current) return;

    const nextToast = toastQueue.current.shift();
    if (nextToast) {
      setMessage(nextToast.message);
      setType(nextToast.type || 'info');
      setDuration(nextToast.duration !== undefined ? nextToast.duration : 3000);
      setPosition(nextToast.position || 'top');
      setShowClose(
        nextToast.showClose !== undefined ? nextToast.showClose : true
      );
      setVisible(true);
      isDisplaying.current = true;
      isDisplayingGlobalToast = true;
    }
  };

  // 显示toast
  const showToast = (options: ToastOptions) => {
    // 添加到队列
    toastQueue.current.push(options);

    // 如果当前没有显示任何toast，则立即显示
    if (!isDisplaying.current) {
      displayNextToast();
    }
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      <PortalProvider>
        {children}
        <View style={styles.rootToastContainer} pointerEvents="box-none">
          <ToastMessage
            visible={visible}
            message={message}
            type={type}
            duration={duration}
            position={position}
            showClose={showClose}
            onClose={handleClose}
          />
        </View>
      </PortalProvider>
    </ToastContext.Provider>
  );
};

const styles = StyleSheet.create({
  rootToastContainer: {
    ...StyleSheet.absoluteFillObject,
    pointerEvents: 'box-none',
  },
});

// 自定义Hook
export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export default ToastProvider;
