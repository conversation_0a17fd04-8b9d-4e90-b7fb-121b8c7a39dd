import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import Card from '../ui/Card';
import Button from '../ui/Button';

interface LivePreviewProps {
  title: string;
  time: string;
  teacher: string;
  imageUrl: string;
  registrationCount: number;
  onPress: () => void;
}

export default function LivePreview({
  title,
  time,
  teacher,
  imageUrl,
  registrationCount,
  onPress
}: LivePreviewProps) {
  return (
    <Card style={styles.card}>
      <View style={styles.header}>
        <View style={styles.liveIndicator}>
          <View style={styles.liveIndicatorDot} />
          <Text style={styles.liveText}>直播</Text>
        </View>
        <Text style={styles.time}>{time} | {teacher}</Text>
      </View>

      <View style={styles.content}>
        <Image source={{ uri: imageUrl }} style={styles.image} />
        <View style={styles.detailsContainer}>
          <Text style={styles.title} numberOfLines={2}>{title}</Text>
          <Text style={styles.registrationCount}>已预约 {registrationCount}</Text>
          <Button 
            title="预约" 
            onPress={onPress} 
            type="primary" 
            size="sm"
            style={styles.button}
          />
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  liveIndicatorDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF4D4F',
    marginRight: 4,
  },
  liveText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF4D4F',
  },
  time: {
    fontSize: 12,
    color: '#666666',
  },
  content: {
    flexDirection: 'row',
  },
  image: {
    width: 100,
    height: 70,
    borderRadius: 4,
    marginRight: 12,
  },
  detailsContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  registrationCount: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 4,
  },
  button: {
    alignSelf: 'flex-start',
  },
});