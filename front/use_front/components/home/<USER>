import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

interface FeatureButtonProps {
  title: string;
  icon: React.ReactNode;
  onPress: () => void;
  color?: string;
}

export default function FeatureButton({ 
  title, 
  icon, 
  onPress, 
  color = '#4080FF' 
}: FeatureButtonProps) {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={[styles.iconContainer, { backgroundColor: color }]}>
        {icon}
      </View>
      <Text style={styles.title}>{title}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginHorizontal: 8,
    width: 70,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 12,
    color: '#333333',
    textAlign: 'center',
  },
});