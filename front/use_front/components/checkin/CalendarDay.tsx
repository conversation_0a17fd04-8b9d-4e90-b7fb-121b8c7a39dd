import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

interface CalendarDayProps {
  date: number;
  isToday?: boolean;
  isSelected?: boolean;
  isCheckedIn?: boolean;
  onPress: (date: number) => void;
  disabled?: boolean;
}

export default function CalendarDay({
  date,
  isToday = false,
  isSelected = false,
  isCheckedIn = false,
  onPress,
  disabled = false
}: CalendarDayProps) {
  const handlePress = () => {
    if (!disabled) {
      onPress(date);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isToday && styles.today,
        isSelected && styles.selected,
        isCheckedIn && styles.checkedIn,
        disabled && styles.disabled
      ]}
      onPress={handlePress}
      disabled={disabled}
    >
      <Text 
        style={[
          styles.text,
          isSelected && styles.selectedText,
          isToday && styles.todayText,
          disabled && styles.disabledText
        ]}
      >
        {date}
      </Text>
      {isCheckedIn && <View style={styles.checkedInDot} />}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 17.5,
    margin: 2,
  },
  today: {
    borderWidth: 1,
    borderColor: '#4080FF',
  },
  selected: {
    backgroundColor: '#4080FF',
  },
  checkedIn: {
    backgroundColor: '#E6F0FF',
  },
  disabled: {
    opacity: 0.3,
  },
  text: {
    fontSize: 14,
    color: '#333333',
  },
  selectedText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  todayText: {
    color: '#4080FF',
    fontWeight: '600',
  },
  disabledText: {
    color: '#999999',
  },
  checkedInDot: {
    position: 'absolute',
    bottom: 2,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#4080FF',
  },
});