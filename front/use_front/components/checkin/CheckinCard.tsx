import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import Card from '../ui/Card';
import Button from '../ui/Button';
import {CalendarDays} from 'lucide-react-native';

interface CheckinCardProps {
  currentStreak: number;
  totalCheckins: number;
  avgStudyHours: number;
  onCheckIn: () => void;
  isCheckedInToday: boolean;
}

export default function CheckinCard({
  currentStreak,
  totalCheckins,
  avgStudyHours,
  onCheckIn,
  isCheckedInToday
}: CheckinCardProps) {
  return (
    <Card style={styles.card}>
      <View style={styles.headerContainer}>
        <View>
          <Text style={styles.title}>今日打卡</Text>
          <Text style={styles.subtitle}>
            已连续打卡 {currentStreak} 天
          </Text>
        </View>
        <Button
          title={isCheckedInToday ? "已打卡" : "打卡"}
          onPress={onCheckIn}
          disabled={isCheckedInToday}
          type="primary"
          icon={<CalendarDays size={16} color="#FFFFFF" />}
        />
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{currentStreak}</Text>
          <Text style={styles.statLabel}>连续天数</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{totalCheckins}</Text>
          <Text style={styles.statLabel}>累计天数</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{avgStudyHours}</Text>
          <Text style={styles.statLabel}>平均时长(h)</Text>
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    color: '#666666',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#F7F9FC',
    borderRadius: 8,
    paddingVertical: 16,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4080FF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
  },
  divider: {
    height: 30,
    width: 1,
    backgroundColor: '#E5E5E5',
  },
});