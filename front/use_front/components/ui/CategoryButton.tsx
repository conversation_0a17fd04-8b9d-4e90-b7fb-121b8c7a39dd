import React from 'react';
import {StyleProp, StyleSheet, Text, TouchableOpacity, ViewStyle} from 'react-native';

interface CategoryButtonProps {
  title: string;
  onPress: () => void;
  isActive?: boolean;
  style?: StyleProp<ViewStyle>;
}

export default function CategoryButton({ 
  title, 
  onPress, 
  isActive = false,
  style 
}: CategoryButtonProps) {
  return (
    <TouchableOpacity 
      style={[
        styles.button, 
        isActive ? styles.activeButton : styles.inactiveButton,
        style
      ]} 
      onPress={onPress}
    >
      <Text 
        style={[
          styles.text,
          isActive ? styles.activeText : styles.inactiveText
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  activeButton: {
    backgroundColor: '#4080FF',
  },
  inactiveButton: {
    backgroundColor: '#F0F0F0',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
  activeText: {
    color: '#FFFFFF',
  },
  inactiveText: {
    color: '#666666',
  },
});