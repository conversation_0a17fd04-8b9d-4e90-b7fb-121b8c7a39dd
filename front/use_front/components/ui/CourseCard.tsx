import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {User} from 'lucide-react-native';
import Card from './Card';

interface CourseCardProps {
  title: string;
  imageUrl: string;
  teacher?: string;
  price?: string;
  discount?: string;
  studentCount?: number;
  duration?: string;
  tag?: string;
  isLive?: boolean;
  onPress?: () => void;
}

export default function CourseCard({
  title,
  imageUrl,
  teacher,
  price,
  discount,
  studentCount,
  duration,
  tag,
  isLive = false,
  onPress
}: CourseCardProps) {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <Card style={styles.card}>
        <View style={styles.imageContainer}>
          <Image source={{ uri: imageUrl }} style={styles.image} />
          {tag && <View style={styles.tagContainer}><Text style={styles.tag}>{tag}</Text></View>}
          {isLive && <View style={styles.liveTag}><Text style={styles.liveText}>直播</Text></View>}
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.title} numberOfLines={2}>{title}</Text>
          
          {teacher && (
            <View style={styles.teacherContainer}>
              <User size={12} color="#666666" />
              <Text style={styles.teacherText}>{teacher}</Text>
              {duration && <Text style={styles.durationText}>共{duration}课时</Text>}
            </View>
          )}
          
          <View style={styles.bottomContainer}>
            {price && (
              <View style={styles.priceContainer}>
                <Text style={styles.price}>¥{price}</Text>
                {discount && <Text style={styles.discount}>¥{discount}</Text>}
              </View>
            )}
            
            {studentCount !== undefined && (
              <Text style={styles.studentCount}>{studentCount}人已学</Text>
            )}
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 0,
    overflow: 'hidden',
    width: '100%',
    borderRadius: 8,
    marginBottom: 16,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    height: 120,
    width: '100%',
    resizeMode: 'cover',
  },
  tagContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#4080FF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  tag: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  liveTag: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FF4D4F',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  liveText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  contentContainer: {
    padding: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333333',
  },
  teacherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  teacherText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  durationText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 8,
  },
  bottomContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B00',
  },
  discount: {
    fontSize: 12,
    color: '#999999',
    textDecorationLine: 'line-through',
    marginLeft: 4,
  },
  studentCount: {
    fontSize: 12,
    color: '#999999',
  },
});