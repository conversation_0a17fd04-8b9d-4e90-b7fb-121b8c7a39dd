import React from 'react';
import {StyleProp, StyleSheet, Text, TouchableOpacity, View, ViewStyle,} from 'react-native';
import {ChevronRight} from 'lucide-react-native';

interface MenuOptionProps {
  icon: React.ReactNode;
  title: string;
  onPress: () => void;
  showBadge?: boolean;
  badgeCount?: number;
  customStyle?: StyleProp<ViewStyle>;
}

export default function MenuOption({
  icon,
  title,
  onPress,
  showBadge = false,
  badgeCount,
  customStyle,
}: MenuOptionProps) {
  return (
    <TouchableOpacity style={[styles.container, customStyle]} onPress={onPress}>
      <View style={styles.leftSection}>
        <View style={styles.iconContainer}>{icon}</View>
        <Text style={styles.title}>{title}</Text>
      </View>
      <View style={styles.rightSection}>
        {showBadge && badgeCount && badgeCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>
              {badgeCount > 99 ? '99+' : badgeCount}
            </Text>
          </View>
        )}
        <ChevronRight size={20} color="#CCCCCC" />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 14,
  },
  title: {
    fontSize: 15,
    color: '#333333',
    fontWeight: '500',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    backgroundColor: '#FF4D4F',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: 10,
  },
  badgeText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
