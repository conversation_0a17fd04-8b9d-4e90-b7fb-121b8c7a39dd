#!/bin/bash

# 后端构建脚本
# 使用方法: ./build-backend.sh [环境]
# 环境: dev, test, staging, prod

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
ENV=${1:-dev}

# 验证环境参数
if [[ ! "$ENV" =~ ^(dev|test|staging|prod)$ ]]; then
    echo -e "${RED}错误: 无效的环境参数 '$ENV'${NC}"
    echo -e "${YELLOW}支持的环境: dev, test, staging, prod${NC}"
    exit 1
fi

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  鼎峰课堂后端构建脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}环境: $ENV${NC}"
echo -e "${BLUE}========================================${NC}"

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACK_DIR="$PROJECT_ROOT/back"

# 检查Maven
if ! command -v mvn &> /dev/null; then
    # 尝试使用项目配置的Maven路径
    MAVEN_HOME="$HOME/Dev/env/apache-maven-3.9.5"
    if [ -f "$MAVEN_HOME/bin/mvn" ]; then
        export PATH="$MAVEN_HOME/bin:$PATH"
        echo -e "${YELLOW}使用Maven: $MAVEN_HOME/bin/mvn${NC}"
    else
        echo -e "${RED}错误: 未找到Maven，请安装Maven或设置正确的路径${NC}"
        exit 1
    fi
fi

# 检查Java
if ! command -v java &> /dev/null; then
    # 尝试使用项目配置的Java路径
    JAVA_HOME="/Library/Java/JavaVirtualMachines/graalvm-17.jdk/Contents/Home"
    if [ -f "$JAVA_HOME/bin/java" ]; then
        export JAVA_HOME="$JAVA_HOME"
        export PATH="$JAVA_HOME/bin:$PATH"
        echo -e "${YELLOW}使用Java: $JAVA_HOME/bin/java${NC}"
    else
        echo -e "${RED}错误: 未找到Java，请安装Java 17或设置正确的路径${NC}"
        exit 1
    fi
fi

# 显示版本信息
echo -e "${YELLOW}Maven版本:${NC}"
mvn -version
echo ""

cd "$BACK_DIR"

# 清理之前的构建
echo -e "${YELLOW}清理之前的构建...${NC}"
mvn clean

# 编译和打包
echo -e "${YELLOW}开始构建 - 环境: $ENV${NC}"
case $ENV in
    dev)
        mvn package -Pdev -DskipTests=false
        ;;
    test)
        mvn package -Ptest -DskipTests=false
        ;;
    staging)
        mvn package -Pstaging -DskipTests=true
        ;;
    prod)
        mvn package -Pprod -DskipTests=true
        ;;
esac

# 检查构建结果
JAR_FILE="$BACK_DIR/target/back-0.0.1-SNAPSHOT-$ENV.jar"
if [ -f "$JAR_FILE" ]; then
    JAR_SIZE=$(du -sh "$JAR_FILE" | cut -f1)
    echo -e "${GREEN}构建成功! 🎉${NC}"
    echo -e "${GREEN}JAR文件: $JAR_FILE${NC}"
    echo -e "${GREEN}文件大小: $JAR_SIZE${NC}"
    
    # 创建启动脚本
    START_SCRIPT="$BACK_DIR/start-$ENV.sh"
    cat > "$START_SCRIPT" << EOF
#!/bin/bash

# 鼎峰课堂后端启动脚本 - $ENV环境
# 生成时间: $(date)

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 配置
JAR_FILE="$JAR_FILE"
LOG_FILE="logs/dianfeng-class-$ENV.log"
PID_FILE="dianfeng-class-$ENV.pid"

# 检查Java
if ! command -v java &> /dev/null; then
    echo -e "\${RED}错误: 未找到Java，请安装Java 17\${NC}"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动应用
echo -e "\${YELLOW}启动鼎峰课堂后端 - $ENV环境...\${NC}"
echo -e "\${YELLOW}JAR文件: \$JAR_FILE\${NC}"
echo -e "\${YELLOW}日志文件: \$LOG_FILE\${NC}"

# 设置JVM参数
JVM_OPTS="-Xms512m -Xmx1024m"
if [ "$ENV" = "prod" ]; then
    JVM_OPTS="-Xms1024m -Xmx2048m"
fi

# 启动应用
nohup java \$JVM_OPTS \\
    -Dspring.profiles.active=$ENV \\
    -Dfile.encoding=UTF-8 \\
    -Djava.awt.headless=true \\
    -jar "\$JAR_FILE" \\
    > "\$LOG_FILE" 2>&1 &

# 保存PID
echo \$! > "\$PID_FILE"

echo -e "\${GREEN}应用启动成功! PID: \$(cat \$PID_FILE)\${NC}"
echo -e "\${GREEN}查看日志: tail -f \$LOG_FILE\${NC}"
echo -e "\${GREEN}停止应用: kill \$(cat \$PID_FILE)\${NC}"
EOF
    
    chmod +x "$START_SCRIPT"
    echo -e "${GREEN}启动脚本: $START_SCRIPT${NC}"
    
else
    echo -e "${RED}构建失败! 未找到JAR文件${NC}"
    exit 1
fi

echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}后端构建完成! 🎉${NC}"
echo -e "${BLUE}========================================${NC}"
