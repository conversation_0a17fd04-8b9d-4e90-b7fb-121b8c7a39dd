#!/bin/bash

# 全栈构建脚本
# 使用方法: ./build-all.sh [环境] [组件]
# 环境: dev, test, staging, prod
# 组件: frontend, backend, all

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认参数
ENV=${1:-dev}
COMPONENT=${2:-all}

# 验证环境参数
if [[ ! "$ENV" =~ ^(dev|test|staging|prod)$ ]]; then
    echo -e "${RED}错误: 无效的环境参数 '$ENV'${NC}"
    echo -e "${YELLOW}支持的环境: dev, test, staging, prod${NC}"
    exit 1
fi

# 验证组件参数
if [[ ! "$COMPONENT" =~ ^(frontend|backend|all)$ ]]; then
    echo -e "${RED}错误: 无效的组件参数 '$COMPONENT'${NC}"
    echo -e "${YELLOW}支持的组件: frontend, backend, all${NC}"
    exit 1
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示构建信息
echo -e "${PURPLE}╔══════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║        鼎峰课堂全栈构建脚本          ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════╝${NC}"
echo ""
echo -e "${CYAN}🏗️  构建配置:${NC}"
echo -e "${GREEN}   环境: $ENV${NC}"
echo -e "${GREEN}   组件: $COMPONENT${NC}"
echo -e "${GREEN}   时间: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
echo ""

# 记录开始时间
START_TIME=$(date +%s)

# 构建前端
build_frontend() {
    echo -e "${BLUE}🎨 开始构建前端...${NC}"
    if [ -f "$SCRIPT_DIR/build-frontend.sh" ]; then
        chmod +x "$SCRIPT_DIR/build-frontend.sh"
        "$SCRIPT_DIR/build-frontend.sh" "$ENV" "all"
    else
        echo -e "${RED}错误: 未找到前端构建脚本${NC}"
        return 1
    fi
}

# 构建后端
build_backend() {
    echo -e "${BLUE}⚙️  开始构建后端...${NC}"
    if [ -f "$SCRIPT_DIR/build-backend.sh" ]; then
        chmod +x "$SCRIPT_DIR/build-backend.sh"
        "$SCRIPT_DIR/build-backend.sh" "$ENV"
    else
        echo -e "${RED}错误: 未找到后端构建脚本${NC}"
        return 1
    fi
}

# 执行构建
case $COMPONENT in
    frontend)
        build_frontend
        ;;
    backend)
        build_backend
        ;;
    all)
        build_frontend
        echo ""
        build_backend
        ;;
esac

# 计算构建时间
END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))
MINUTES=$((BUILD_TIME / 60))
SECONDS=$((BUILD_TIME % 60))

echo ""
echo -e "${PURPLE}╔══════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║            构建完成报告              ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════╝${NC}"
echo ""
echo -e "${GREEN}✅ 构建成功完成!${NC}"
echo -e "${CYAN}📊 构建统计:${NC}"
echo -e "${GREEN}   环境: $ENV${NC}"
echo -e "${GREEN}   组件: $COMPONENT${NC}"
echo -e "${GREEN}   耗时: ${MINUTES}分${SECONDS}秒${NC}"
echo ""

# 显示构建产物
echo -e "${CYAN}📦 构建产物:${NC}"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

if [[ "$COMPONENT" == "frontend" || "$COMPONENT" == "all" ]]; then
    # 管理后台
    ADMIN_DIST="$PROJECT_ROOT/front/admin_front/dist-$ENV"
    if [ -d "$ADMIN_DIST" ]; then
        ADMIN_SIZE=$(du -sh "$ADMIN_DIST" | cut -f1)
        echo -e "${GREEN}   🖥️  管理后台: $ADMIN_SIZE${NC}"
    fi
    
    # 移动端
    MOBILE_DIST="$PROJECT_ROOT/front/use_front/dist"
    if [ -d "$MOBILE_DIST" ]; then
        MOBILE_SIZE=$(du -sh "$MOBILE_DIST" | cut -f1)
        echo -e "${GREEN}   📱 移动端: $MOBILE_SIZE${NC}"
    fi
fi

if [[ "$COMPONENT" == "backend" || "$COMPONENT" == "all" ]]; then
    # 后端JAR
    BACKEND_JAR="$PROJECT_ROOT/back/target/back-0.0.1-SNAPSHOT-$ENV.jar"
    if [ -f "$BACKEND_JAR" ]; then
        BACKEND_SIZE=$(du -sh "$BACKEND_JAR" | cut -f1)
        echo -e "${GREEN}   ⚙️  后端JAR: $BACKEND_SIZE${NC}"
    fi
fi

echo ""
echo -e "${CYAN}🚀 部署建议:${NC}"
case $ENV in
    dev)
        echo -e "${YELLOW}   开发环境已就绪，可以启动本地服务进行开发调试${NC}"
        ;;
    test)
        echo -e "${YELLOW}   测试环境构建完成，可以部署到测试服务器${NC}"
        ;;
    staging)
        echo -e "${YELLOW}   预发布环境构建完成，建议先进行功能验证${NC}"
        ;;
    prod)
        echo -e "${YELLOW}   生产环境构建完成，请谨慎部署到生产服务器${NC}"
        echo -e "${RED}   ⚠️  部署前请确保已备份数据库和配置文件${NC}"
        ;;
esac

echo ""
echo -e "${CYAN}📋 下一步操作:${NC}"
if [[ "$COMPONENT" == "backend" || "$COMPONENT" == "all" ]]; then
    START_SCRIPT="$PROJECT_ROOT/back/start-$ENV.sh"
    if [ -f "$START_SCRIPT" ]; then
        echo -e "${GREEN}   启动后端: $START_SCRIPT${NC}"
    fi
fi

if [ -f "$SCRIPT_DIR/docker-compose.$ENV.yml" ]; then
    echo -e "${GREEN}   Docker部署: docker-compose -f deploy/docker-compose.$ENV.yml up -d${NC}"
fi

echo ""
echo -e "${PURPLE}╔══════════════════════════════════════╗${NC}"
echo -e "${PURPLE}║          构建脚本执行完毕            ║${NC}"
echo -e "${PURPLE}╚══════════════════════════════════════╝${NC}"
