# 生产环境 Docker Compose 配置
version: '3.8'

services:
  # 后端应用
  backend-prod:
    build:
      context: ../back
      dockerfile: Dockerfile.prod
    container_name: dianfeng-backend-prod
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: -Xms1024m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200
    ports:
      - "8082:8082"
    volumes:
      - ../back/logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - dianfeng-prod

  # 前端 - 管理后台
  frontend-admin-prod:
    build:
      context: ../front/admin_front
      dockerfile: Dockerfile.prod
    container_name: dianfeng-admin-prod
    restart: unless-stopped
    ports:
      - "8088:80"
    volumes:
      - ../front/admin_front/dist-prod:/usr/share/nginx/html:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - dianfeng-prod

  # 前端 - 移动端
  frontend-mobile-prod:
    build:
      context: ../front/use_front
      dockerfile: Dockerfile.prod
    container_name: dianfeng-mobile-prod
    restart: unless-stopped
    ports:
      - "8089:80"
    volumes:
      - ../front/use_front/dist:/usr/share/nginx/html:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - dianfeng-prod

  # Nginx 反向代理
  nginx-prod:
    image: nginx:alpine
    container_name: dianfeng-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - backend-prod
      - frontend-admin-prod
      - frontend-mobile-prod
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - dianfeng-prod

  # 日志收集 (可选)
  filebeat-prod:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: dianfeng-filebeat-prod
    restart: unless-stopped
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ../back/logs:/var/log/app:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTICSEARCH_HOSTS=elasticsearch:9200
    networks:
      - dianfeng-prod
    profiles:
      - monitoring

  # 监控 (可选)
  prometheus-prod:
    image: prom/prometheus:latest
    container_name: dianfeng-prometheus-prod
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - dianfeng-prod
    profiles:
      - monitoring

  # Grafana 监控面板 (可选)
  grafana-prod:
    image: grafana/grafana:latest
    container_name: dianfeng-grafana-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - dianfeng-prod
    profiles:
      - monitoring

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  dianfeng-prod:
    driver: bridge
