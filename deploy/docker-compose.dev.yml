# 开发环境 Docker Compose 配置
version: '3.8'

services:
  # MySQL 数据库
  mysql-dev:
    image: mysql:8.0
    container_name: dianfeng-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: dianfeng_class_dev
      MYSQL_USER: dianfeng_dev
      MYSQL_PASSWORD: dev123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - dianfeng-dev

  # Redis 缓存
  redis-dev:
    image: redis:7-alpine
    container_name: dianfeng-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    networks:
      - dianfeng-dev

  # 后端应用
  backend-dev:
    build:
      context: ../back
      dockerfile: Dockerfile.dev
    container_name: dianfeng-backend-dev
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: dev
      JAVA_OPTS: -Xms512m -Xmx1024m
    ports:
      - "8082:8082"
    volumes:
      - ../back/logs:/app/logs
    depends_on:
      - mysql-dev
      - redis-dev
    networks:
      - dianfeng-dev

  # 前端 - 管理后台
  frontend-admin-dev:
    build:
      context: ../front/admin_front
      dockerfile: Dockerfile.dev
    container_name: dianfeng-admin-dev
    restart: unless-stopped
    ports:
      - "8088:80"
    volumes:
      - ../front/admin_front/dist-dev:/usr/share/nginx/html
    networks:
      - dianfeng-dev

  # Nginx 反向代理
  nginx-dev:
    image: nginx:alpine
    container_name: dianfeng-nginx-dev
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/dev.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend-dev
      - frontend-admin-dev
    networks:
      - dianfeng-dev

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  dianfeng-dev:
    driver: bridge
