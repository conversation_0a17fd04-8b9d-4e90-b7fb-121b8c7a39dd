#!/bin/bash

# 前端构建脚本
# 使用方法: ./build-frontend.sh [环境] [项目]
# 环境: dev, test, staging, prod
# 项目: admin, mobile, all

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
ENV=${1:-dev}
PROJECT=${2:-all}

# 验证环境参数
if [[ ! "$ENV" =~ ^(dev|test|staging|prod)$ ]]; then
    echo -e "${RED}错误: 无效的环境参数 '$ENV'${NC}"
    echo -e "${YELLOW}支持的环境: dev, test, staging, prod${NC}"
    exit 1
fi

# 验证项目参数
if [[ ! "$PROJECT" =~ ^(admin|mobile|all)$ ]]; then
    echo -e "${RED}错误: 无效的项目参数 '$PROJECT'${NC}"
    echo -e "${YELLOW}支持的项目: admin, mobile, all${NC}"
    exit 1
fi

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  鼎峰课堂前端构建脚本${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}环境: $ENV${NC}"
echo -e "${GREEN}项目: $PROJECT${NC}"
echo -e "${BLUE}========================================${NC}"

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FRONT_DIR="$PROJECT_ROOT/front"

# 构建管理后台
build_admin() {
    echo -e "${YELLOW}开始构建管理后台...${NC}"
    cd "$FRONT_DIR/admin_front"
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装依赖...${NC}"
        pnpm install
    fi
    
    # 构建
    echo -e "${YELLOW}构建环境: $ENV${NC}"
    case $ENV in
        dev)
            pnpm run build:dev
            ;;
        test)
            pnpm run build:test
            ;;
        staging)
            pnpm run build:staging
            ;;
        prod)
            pnpm run build:prod
            ;;
    esac
    
    echo -e "${GREEN}管理后台构建完成!${NC}"
    echo -e "${GREEN}输出目录: $FRONT_DIR/admin_front/dist-$ENV${NC}"
}

# 构建移动端
build_mobile() {
    echo -e "${YELLOW}开始构建移动端...${NC}"
    cd "$FRONT_DIR/use_front"
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装依赖...${NC}"
        pnpm install
    fi
    
    # 构建Web版本
    echo -e "${YELLOW}构建Web版本 - 环境: $ENV${NC}"
    case $ENV in
        dev)
            pnpm run build:web:dev
            ;;
        test)
            pnpm run build:web:test
            ;;
        staging)
            pnpm run build:web:staging
            ;;
        prod)
            pnpm run build:web:prod
            ;;
    esac
    
    echo -e "${GREEN}移动端构建完成!${NC}"
    echo -e "${GREEN}输出目录: $FRONT_DIR/use_front/dist${NC}"
}

# 执行构建
case $PROJECT in
    admin)
        build_admin
        ;;
    mobile)
        build_mobile
        ;;
    all)
        build_admin
        echo ""
        build_mobile
        ;;
esac

echo -e "${BLUE}========================================${NC}"
echo -e "${GREEN}前端构建完成! 🎉${NC}"
echo -e "${BLUE}========================================${NC}"

# 显示构建结果
echo -e "${YELLOW}构建结果:${NC}"
if [[ "$PROJECT" == "admin" || "$PROJECT" == "all" ]]; then
    if [ -d "$FRONT_DIR/admin_front/dist-$ENV" ]; then
        ADMIN_SIZE=$(du -sh "$FRONT_DIR/admin_front/dist-$ENV" | cut -f1)
        echo -e "${GREEN}✓ 管理后台: $ADMIN_SIZE${NC}"
    fi
fi

if [[ "$PROJECT" == "mobile" || "$PROJECT" == "all" ]]; then
    if [ -d "$FRONT_DIR/use_front/dist" ]; then
        MOBILE_SIZE=$(du -sh "$FRONT_DIR/use_front/dist" | cut -f1)
        echo -e "${GREEN}✓ 移动端: $MOBILE_SIZE${NC}"
    fi
fi

echo -e "${BLUE}========================================${NC}"
