/*
 Navicat Premium Dump SQL

 Source Server         : docker-8.0
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : dianfeng_class

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 04/06/2025 21:01:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admins
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '管理员ID，主键',
  `username` varchar(50) NOT NULL COMMENT '管理员账号',
  `password` varchar(255) NOT NULL COMMENT '密码，加密存储',
  `real_name` varchar(50) DEFAULT NULL COMMENT '管理员真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role` tinyint(1) DEFAULT '2' COMMENT '角色：1-超级管理员，2-课程管理员，3-运营管理员',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `permissions` text COMMENT '权限列表，JSON格式存储',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int DEFAULT '0' COMMENT '登录次数',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_by` int DEFAULT NULL COMMENT '创建者ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员账户表';

-- ----------------------------
-- Records of admins
-- ----------------------------
BEGIN;
INSERT INTO `admins` (`id`, `username`, `password`, `real_name`, `avatar`, `role`, `phone`, `email`, `permissions`, `last_login_ip`, `last_login_time`, `login_count`, `remark`, `status`, `created_by`, `created_at`, `updated_at`) VALUES (1, 'admin', '14e1b600b1fd579f47433b88e8d85291', '系统管理员', NULL, 1, NULL, NULL, NULL, '127.0.0.1', '2025-06-04 20:35:15', 13, NULL, 1, NULL, '2025-05-28 10:39:26', '2025-05-28 10:48:59');
COMMIT;

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID，主键',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` int DEFAULT '0' COMMENT '父分类ID，0表示一级分类',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程分类表';

-- ----------------------------
-- Records of categories
-- ----------------------------
BEGIN;
INSERT INTO `categories` (`id`, `name`, `parent_id`, `icon`, `sort_order`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (1, '计算机', 0, '', 0, 1, '2025-05-28 10:50:28', 0, '2025-05-28 10:50:28');
COMMIT;

-- ----------------------------
-- Table structure for course_chapters
-- ----------------------------
DROP TABLE IF EXISTS `course_chapters`;
CREATE TABLE `course_chapters` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '章节ID，主键',
  `course_id` int NOT NULL COMMENT '课程ID',
  `title` varchar(255) NOT NULL COMMENT '章节标题',
  `description` text COMMENT '章节描述',
  `sort_order` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程章节表';

-- ----------------------------
-- Records of course_chapters
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for course_lessons
-- ----------------------------
DROP TABLE IF EXISTS `course_lessons`;
CREATE TABLE `course_lessons` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '课时ID，主键',
  `course_id` int NOT NULL COMMENT '课程ID',
  `chapter_id` int NOT NULL COMMENT '章节ID',
  `title` varchar(255) NOT NULL COMMENT '课时标题',
  `description` text COMMENT '课时描述',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频URL',
  `duration` int DEFAULT '0' COMMENT '视频时长（秒）',
  `is_free` tinyint(1) DEFAULT '0' COMMENT '是否免费：0-否，1-是',
  `sort_order` int DEFAULT '0' COMMENT '排序值，越小越靠前',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课时信息表';

-- ----------------------------
-- Records of course_lessons
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for course_reviews
-- ----------------------------
DROP TABLE IF EXISTS `course_reviews`;
CREATE TABLE `course_reviews` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '评价ID，主键',
  `course_id` int NOT NULL COMMENT '课程ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `rating` tinyint(1) NOT NULL COMMENT '评分，1-5分',
  `content` text COMMENT '评价内容',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程评价表';

-- ----------------------------
-- Records of course_reviews
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for courses
-- ----------------------------
DROP TABLE IF EXISTS `courses`;
CREATE TABLE `courses` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '课程ID，主键',
  `title` varchar(255) NOT NULL COMMENT '课程标题',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '课程副标题',
  `description` text COMMENT '课程描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '课程封面图片URL',
  `teacher_id` int NOT NULL COMMENT '讲师ID',
  `category_id` int NOT NULL COMMENT '分类ID',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '课程价格',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '课程原价',
  `level` tinyint(1) DEFAULT '1' COMMENT '课程难度：1-入门，2-初级，3-中级，4-高级',
  `age_group` tinyint(1) DEFAULT NULL COMMENT '适合年龄段：1-青少年，2-大学生，3-成人，0-不限',
  `duration` int DEFAULT '0' COMMENT '课程总时长（分钟）',
  `lesson_count` int DEFAULT '0' COMMENT '课时总数',
  `student_count` int DEFAULT '0' COMMENT '学习人数',
  `rating` decimal(2,1) DEFAULT '5.0' COMMENT '课程评分',
  `review_count` int DEFAULT '0' COMMENT '评价数量',
  `is_live` tinyint(1) DEFAULT '0' COMMENT '是否直播课：0-录播，1-直播',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐课程：0-否，1-是',
  `is_special_training` tinyint(1) DEFAULT '0' COMMENT '是否特训营：0-否，1-是',
  `is_one_on_one` tinyint(1) DEFAULT '0' COMMENT '是否一对一：0-否，1-是',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-下架，1-上架',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `contact_info_phone` varchar(20) DEFAULT NULL COMMENT '联系信息-手机号',
  `contact_info_wechat` varchar(20) DEFAULT NULL COMMENT '联系信息-微信',
  `contact_info_remark` text COMMENT '联系信息-备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课程信息表';

-- ----------------------------
-- Records of courses
-- ----------------------------
BEGIN;
INSERT INTO `courses` (`id`, `title`, `subtitle`, `description`, `cover_image`, `teacher_id`, `category_id`, `price`, `original_price`, `level`, `age_group`, `duration`, `lesson_count`, `student_count`, `rating`, `review_count`, `is_live`, `is_featured`, `is_special_training`, `is_one_on_one`, `status`, `is_del`, `contact_info_phone`, `contact_info_wechat`, `contact_info_remark`, `created_at`, `updated_at`) VALUES (1, '添加课程', '', '', '', 3, 1, 99.00, 999.00, 1, 0, 0, 0, 0, 5.0, 0, 0, 0, 0, 0, 1, 0, '', '', '', '2025-05-31 12:09:21', '2025-05-31 12:09:21');
COMMIT;

-- ----------------------------
-- Table structure for customer_service_contacts
-- ----------------------------
DROP TABLE IF EXISTS `customer_service_contacts`;
CREATE TABLE `customer_service_contacts` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '客服ID，主键',
  `name` varchar(50) NOT NULL COMMENT '客服姓名',
  `phone` varchar(20) NOT NULL COMMENT '客服手机号',
  `wechat` varchar(20) NOT NULL COMMENT '客服微信',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客服联系信息表';

-- ----------------------------
-- Records of customer_service_contacts
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for live_courses
-- ----------------------------
DROP TABLE IF EXISTS `live_courses`;
CREATE TABLE `live_courses` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '直播ID，主键',
  `title` varchar(255) NOT NULL COMMENT '直播标题',
  `description` text COMMENT '直播描述',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `live_url` varchar(255) DEFAULT NULL COMMENT '直播地址',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态：0-未开始，1-直播中，2-已结束',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='直播通知表';

-- ----------------------------
-- Records of live_courses
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for teachers
-- ----------------------------
DROP TABLE IF EXISTS `teachers`;
CREATE TABLE `teachers` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '讲师ID，主键',
  `name` varchar(50) NOT NULL COMMENT '讲师姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '讲师头像URL',
  `title` varchar(100) DEFAULT NULL COMMENT '讲师职称/头衔',
  `introduction` text COMMENT '讲师简介',
  `experience` text COMMENT '教学经验',
  `expertise` varchar(255) DEFAULT NULL COMMENT '专业领域',
  `location` varchar(100) DEFAULT NULL COMMENT '讲师所在地',
  `rating` decimal(2,1) DEFAULT '5.0' COMMENT '讲师评分',
  `student_count` int DEFAULT '0' COMMENT '学生总数',
  `course_count` int DEFAULT '0' COMMENT '课程总数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='讲师信息表';

-- ----------------------------
-- Records of teachers
-- ----------------------------
BEGIN;
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (1, '王明', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '高级数学讲师', '毕业于清华大学数学系，拥有十年高等数学教学经验。专注于微积分和线性代数的教学，善于将复杂的数学概念简化，让学生轻松掌握。', '曾在多家知名教育机构任教，帮助上千名学生提高数学成绩，多名学生在数学竞赛中获奖。', '微积分、线性代数、数学建模', '北京市', 4.9, 1250, 8, 0, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (2, '李芳', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '英语语言专家', '英国剑桥大学语言学博士，拥有TESOL国际资格证书，专注于英语口语和写作培训。', '有八年英语教学经验，曾在国际学校和语言培训中心任教，开发了多套英语学习教材。', '英语口语、写作、语法、雅思托福备考', '上海市', 4.8, 980, 6, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (3, '张强', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '物理学教授', '北京大学物理学博士，研究方向为量子物理和理论力学。以生动有趣的教学方式著称，让抽象的物理概念变得直观易懂。', '十五年物理教学经验，编写过多本物理教材，曾带领学生参加全国物理竞赛并获得优异成绩。', '力学、电磁学、量子物理、物理竞赛辅导', '广东省广州市', 5.0, 1560, 10, 0, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (4, '陈静', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '化学实验指导专家', '中国科学院化学系硕士，专注于高中化学教育和实验教学。', '拥有七年化学教学经验，曾在重点中学任教，擅长实验演示和解析，帮助学生理解化学反应原理。', '有机化学、无机化学、化学实验指导', '四川省成都市', 4.7, 820, 5, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (5, '刘伟', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '计算机科学教师', '复旦大学计算机科学博士，前互联网公司技术总监，现专注于编程教育。', '十二年软件开发和教学经验，带领学生参加过多次ACM编程竞赛，开发了多门编程课程。', 'Java编程、Python数据分析、算法与数据结构', '广东省深圳市', 4.9, 2100, 12, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (6, '赵雪', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '生物学研究员', '上海交通大学生命科学博士，专注于分子生物学和遗传学研究与教学。', '八年生物学教育经验，曾参与国家重点研究项目，将最新研究成果融入教学中。', '分子生物学、遗传学、细胞生物学', '湖北省武汉市', 4.8, 750, 6, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (7, '杨光', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '历史文化学者', '北京师范大学历史学博士，专注于中国古代史和文化研究。', '十年历史教学经验，著有多部历史教材和文化研究专著，善于讲述历史故事，让历史课生动有趣。', '中国古代史、世界史、文化研究', '陕西省西安市', 4.6, 930, 7, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (8, '周丽', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '艺术设计导师', '中央美术学院设计系硕士，资深平面设计师，专注于视觉艺术和设计教育。', '十五年设计和教学经验，曾在多家设计公司担任创意总监，作品获得多个国际设计大奖。', '平面设计、UI/UX设计、创意思维', '浙江省杭州市', 4.9, 1800, 9, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (9, '吴健', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '体育健康教练', '北京体育大学运动训练专业，国家一级健身教练，前国家队运动员。', '十二年体育教学和训练经验，专注于青少年体能发展和运动技能培训，培养了多名优秀运动员。', '体能训练、篮球教学、健康管理', '江苏省南京市', 4.7, 1050, 8, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (10, '郑华', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '经济学教授', '中国人民大学经济学博士，研究方向为宏观经济政策和金融市场分析。', '十六年经济学教学和研究经验，曾担任多家金融机构顾问，著有多部经济学教材和研究著作。', '宏观经济学、金融市场分析、投资理论', '天津市', 4.8, 1350, 10, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (11, '孙萍', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '心理咨询师', '北京师范大学心理学博士，国家二级心理咨询师，专注于青少年心理健康教育。', '九年心理咨询和教学经验，擅长青少年心理问题干预和情绪管理指导，帮助众多学生克服学习压力和心理困扰。', '青少年心理健康、情绪管理、压力应对', '重庆市', 4.9, 890, 7, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (12, '胡军', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '音乐理论教师', '中央音乐学院作曲系硕士，专业钢琴演奏家，专注于音乐理论和钢琴教学。', '十四年音乐教学经验，培养了多名专业音乐人才，编写了系列音乐教材，举办过多场个人钢琴独奏会。', '音乐理论、钢琴演奏、作曲技巧', '江苏省苏州市', 4.7, 780, 6, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (13, '唐雨', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '语文教育专家', '北京大学中文系博士，专注于中国古代文学和现代语文教育。', '十一年语文教学经验，善于激发学生阅读兴趣和写作能力，开发了系列阅读理解和写作技巧课程。', '古代文学、现代文学、写作技巧、阅读理解', '湖南省长沙市', 4.8, 1100, 9, 1, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
INSERT INTO `teachers` (`id`, `name`, `avatar`, `title`, `introduction`, `experience`, `expertise`, `location`, `rating`, `student_count`, `course_count`, `status`, `created_at`, `is_del`, `updated_at`) VALUES (14, '马超', 'https://p5.itc.cn/q_70/images03/20200730/e2898f83956b412eb43968cb4528fd0d.jpeg', '人工智能研究员', '浙江大学计算机科学博士，研究方向为机器学习和自然语言处理。', '八年AI研究和教学经验，曾在国际顶级科技公司担任研究员，参与多个国家级AI项目，将前沿技术融入教学。', '机器学习、自然语言处理、深度学习、计算机视觉', '山东省青岛市', 5.0, 1650, 11, 0, '2025-05-31 10:07:06', 0, '2025-05-31 10:07:06');
COMMIT;

-- ----------------------------
-- Table structure for user_checkins
-- ----------------------------
DROP TABLE IF EXISTS `user_checkins`;
CREATE TABLE `user_checkins` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '打卡ID，主键',
  `user_id` int NOT NULL COMMENT '用户ID',
  `checkin_date` date NOT NULL COMMENT '打卡日期',
  `checkin_time` datetime NOT NULL COMMENT '打卡时间',
  `learn_duration` int DEFAULT '0' COMMENT '学习时长（分钟）',
  `remark` varchar(255) DEFAULT NULL COMMENT '打卡备注',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户打卡记录表';

-- ----------------------------
-- Records of user_checkins
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_favorites
-- ----------------------------
DROP TABLE IF EXISTS `user_favorites`;
CREATE TABLE `user_favorites` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '收藏ID，主键',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户收藏表';

-- ----------------------------
-- Records of user_favorites
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_course_access
-- ----------------------------
DROP TABLE IF EXISTS `user_course_access`;
CREATE TABLE `user_course_access` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '权限记录ID，主键',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `chapter_id` int DEFAULT NULL COMMENT '章节ID，null表示整个课程权限',
  `lesson_id` int DEFAULT NULL COMMENT '课时ID，null表示章节权限',
  `access_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '权限类型：1-课程，2-章节，3-课时',
  `acquire_method` tinyint(1) NOT NULL DEFAULT '1' COMMENT '获取方式：1-购买，2-免费，3-积分兑换，4-优惠券兑换，5-管理员赠送，6-推广活动',
  `is_buyout` tinyint(1) DEFAULT '0' COMMENT '是否买断：0-否，1-是（买断权限优先级最高）',
  `price_paid` decimal(10,2) DEFAULT '0.00' COMMENT '实际支付金额',
  `original_price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式：wechat-微信，alipay-支付宝，points-积分，coupon-优惠券，gift-赠送',
  `order_id` varchar(100) DEFAULT NULL COMMENT '订单ID（如果是购买获得）',
  `coupon_id` int DEFAULT NULL COMMENT '使用的优惠券ID',
  `points_used` int DEFAULT '0' COMMENT '使用的积分数量',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间，null表示永久有效',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活：0-未激活，1-已激活',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-已失效，1-有效，2-已退款',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `admin_id` int DEFAULT NULL COMMENT '操作管理员ID（赠送时记录）',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_course_chapter_lesson` (`user_id`,`course_id`,`chapter_id`,`lesson_id`,`is_del`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_lesson_id` (`lesson_id`),
  KEY `idx_access_type` (`access_type`),
  KEY `idx_acquire_method` (`acquire_method`),
  KEY `idx_is_buyout` (`is_buyout`),
  KEY `idx_status_active` (`status`,`is_active`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户课程权限表';

-- ----------------------------
-- Records of user_course_access
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_learning_records
-- ----------------------------
DROP TABLE IF EXISTS `user_learning_records`;
CREATE TABLE `user_learning_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID，主键',
  `user_id` int NOT NULL COMMENT '用户ID',
  `course_id` int NOT NULL COMMENT '课程ID',
  `lesson_id` int NOT NULL COMMENT '课时ID',
  `progress` int DEFAULT '0' COMMENT '学习进度（秒）',
  `duration` int DEFAULT '0' COMMENT '课时总时长（秒）',
  `progress_rate` decimal(5,2) DEFAULT '0.00' COMMENT '完成比例，0-100%',
  `is_completed` tinyint(1) DEFAULT '0' COMMENT '是否完成：0-未完成，1-已完成',
  `last_learn_time` datetime DEFAULT NULL COMMENT '最后学习时间',
  `learn_duration` int DEFAULT '0' COMMENT '学习时长（秒）',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户学习记录表';

-- ----------------------------
-- Records of user_learning_records
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `username` varchar(50) NOT NULL COMMENT '用户名，登录账号',
  `password` varchar(255) NOT NULL COMMENT '密码，加密存储',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像URL',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别：0-未知，1-男，2-女',
  `age` int DEFAULT NULL COMMENT '年龄',
  `bio` text COMMENT '个人简介',
  `grade` int DEFAULT '0' COMMENT '年级',
  `school` varchar(100) DEFAULT NULL COMMENT '学校名称',
  `location` varchar(100) DEFAULT NULL COMMENT '用户所在地',
  `status` tinyint(1) DEFAULT '1' COMMENT '账号状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息表';

-- ----------------------------
-- Records of users
-- ----------------------------
BEGIN;
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (51, 'zhangxm', 'e10adc3949ba59abbe56e057f20f883e', '张小明', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138001', '<EMAIL>', 1, 16, '热爱学习，对数学和物理有浓厚兴趣。', 9, '北京市第一中学', '北京市海淀区', 0, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 09:42:25');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (52, 'lixh', 'e10adc3949ba59abbe56e057f20f883e', '李小红', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138002', '<EMAIL>', 2, 16, '喜欢文学和历史，梦想成为一名作家。', 10, '上海市实验中学', '上海市徐汇区', 0, NULL, '2025-05-31 08:56:31', 0, '2025-06-03 20:03:38');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (53, 'wangxg', 'e10adc3949ba59abbe56e057f20f883e', '王小刚', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138003', '<EMAIL>', 1, 14, '足球爱好者，校队主力前锋。', 8, '广州市第二中学', '广州市天河区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (54, 'zhaoxl', 'e10adc3949ba59abbe56e057f20f883e', '赵小丽', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138004', '<EMAIL>', 2, 17, '对计算机编程有浓厚兴趣，参加过多次信息学奥赛。', 11, '深圳市高级中学', '深圳市南山区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (55, 'chenxh', 'e10adc3949ba59abbe56e057f20f883e', '陈小华', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138005', '<EMAIL>', 1, 13, '喜欢绘画和设计，希望将来成为一名艺术家。', 7, '成都市第七中学', '成都市武侯区', 1, NULL, '2025-05-31 08:56:31', 1, '2025-05-31 09:00:13');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (56, 'yangxf', 'e10adc3949ba59abbe56e057f20f883e', '杨小芳', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138006', '<EMAIL>', 2, 12, '热爱音乐，擅长钢琴和小提琴。', 6, '武汉市育才小学', '武汉市江汉区', 0, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 17:24:30');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (57, 'liuxw', 'e10adc3949ba59abbe56e057f20f883e', '刘小伟', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138007', '<EMAIL>', 1, 10, '好奇心强，喜欢探索自然科学。', 4, '南京市实验小学', '南京市玄武区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (58, 'zhouxl', 'e10adc3949ba59abbe56e057f20f883e', '周小琳', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138008', '<EMAIL>', 2, 11, '性格活泼，班级文艺委员，组织过多次班级活动。', 5, '杭州市求知小学', '杭州市西湖区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (59, 'wuxq', 'e10adc3949ba59abbe56e057f20f883e', '吴小强', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138009', '<EMAIL>', 1, 18, '理科生，对物理有独特见解，希望将来从事科研工作。', 12, '天津市南开中学', '天津市南开区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (60, 'zhengxt', 'e10adc3949ba59abbe56e057f20f883e', '郑小婷', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138010', '<EMAIL>', 2, 19, '已考入北京大学中文系，热爱写作。', 13, '北京大学', '北京市海淀区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (61, 'zhuxl', 'e10adc3949ba59abbe56e057f20f883e', '朱小龙', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138011', '<EMAIL>', 1, 8, '活泼好动，喜欢各种体育活动。', 2, '重庆市人民小学', '重庆市渝中区', 0, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 17:24:32');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (62, 'sunxy', 'e10adc3949ba59abbe56e057f20f883e', '孙小燕', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138012', '<EMAIL>', 2, 9, '性格温和，喜欢阅读儿童文学。', 3, '西安市育才小学', '西安市雁塔区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (63, 'maxj', 'e10adc3949ba59abbe56e057f20f883e', '马小军', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138013', '<EMAIL>', 1, 16, '篮球爱好者，校篮球队队长。', 10, '长沙市一中', '长沙市岳麓区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (64, 'huxm', 'e10adc3949ba59abbe56e057f20f883e', '胡小梅', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138014', '<EMAIL>', 2, 15, '舞蹈特长生，多次参加市级比赛。', 9, '济南市实验中学', '济南市历下区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (65, 'linxf', 'e10adc3949ba59abbe56e057f20f883e', '林小峰', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138015', '<EMAIL>', 1, 7, '对自然科学充满好奇，喜欢看科普读物。', 1, '福州市实验小学', '福州市鼓楼区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (66, 'guoxm', 'e10adc3949ba59abbe56e057f20f883e', '郭小敏', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138016', '<EMAIL>', 2, 17, '学习成绩优异，班级学习委员。', 11, '青岛市第二中学', '青岛市市南区', 0, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 17:24:57');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (67, 'hex y', 'e10adc3949ba59abbe56e057f20f883e', '何小勇', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138017', '<EMAIL>', 1, 14, '科技创新小组成员，热爱编程和机器人制作。', 8, '大连市第八中学', '大连市西岗区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (68, 'xuxy', 'e10adc3949ba59abbe56e057f20f883e', '徐小雨', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138018', '<EMAIL>', 2, 10, '热爱画画，多次在少儿绘画比赛中获奖。', 4, '厦门市实验小学', '厦门市思明区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (69, 'gaoxb', 'e10adc3949ba59abbe56e057f20f883e', '高小博', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138019', '<EMAIL>', 1, 18, '英语特长生，希望将来从事国际交流工作。', 12, '苏州市第一中学', '苏州市姑苏区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
INSERT INTO `users` (`id`, `username`, `password`, `nickname`, `avatar`, `phone`, `email`, `gender`, `age`, `bio`, `grade`, `school`, `location`, `status`, `last_login_time`, `created_at`, `is_del`, `updated_at`) VALUES (70, 'huangxj', 'e10adc3949ba59abbe56e057f20f883e', '黄小洁', 'https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg', '13800138020', '<EMAIL>', 2, 13, '喜欢文学和历史，经常在作文比赛中获奖。', 7, '宁波市实验中学', '宁波市海曙区', 1, NULL, '2025-05-31 08:56:31', 0, '2025-05-31 08:56:31');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
